{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationDir": "./dist/types", "outDir": "./dist/types", "baseUrl": "./src", "paths": {"@/engine/*": ["engine/*"], "@/agents/*": ["agents/*"], "@/tools/*": ["tools/*"], "@/graphs/*": ["graphs/*"], "@/services/*": ["services/*"], "@/knowledge/*": ["knowledge/*"], "@/persistence/*": ["persistence/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**/*"]}