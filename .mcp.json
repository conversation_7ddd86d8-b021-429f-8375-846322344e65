{"mcpServers": {"claudepoint": {"type": "stdio", "command": "claude<PERSON>", "args": [], "env": {}}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************", "GOOGLE_API_KEY": "AIzaSyDtz5xTg3B_zdH-2momgfwjacCV8V36uE4"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}