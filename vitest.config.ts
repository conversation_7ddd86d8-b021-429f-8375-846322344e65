/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/__tests__/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,ts}'],
    exclude: [
      'node_modules/**',
      'dist/**',
      '.git/**',
      'examples/**',
      '_exemples/**'
    ],
    typecheck: {
      tsconfig: './tsconfig.test.json'
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['src/**/*.ts'],
      exclude: [
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/__tests__/**',
        'src/**/__tests__/**',
        'src/**/types/**',
        'src/index.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    // Test timeout for complex operations
    testTimeout: 30000,
    hookTimeout: 30000,
  },
  resolve: {
    alias: {
      '@/engine': path.resolve(__dirname, './src/engine'),
      '@/agents': path.resolve(__dirname, './src/agents'),
      '@/tools': path.resolve(__dirname, './src/tools'),
      '@/graphs': path.resolve(__dirname, './src/graphs'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/knowledge': path.resolve(__dirname, './src/knowledge'),
      '@/persistence': path.resolve(__dirname, './src/persistence'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/utils': path.resolve(__dirname, './src/utils'),
    },
  },
  esbuild: {
    target: 'node18'
  }
}); 