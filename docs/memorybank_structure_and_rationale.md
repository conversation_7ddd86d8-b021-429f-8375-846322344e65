**Proposed Optimal Memory Bank Structure (V4.0 - "The Rosetta Stone")**

**Rationale for this Structure:**

This structure aims to balance:

-   **Findability & Navigability:** Clear, numbered top-level directories for logical grouping.

    -   **Comprehensiveness:** Dedicated sections for every type of information we've discussed (foundational docs, architecture, decisions, components, patterns, current state, AI workflow, meta).

    -   **AI & Human Readability:** Markdown format, with templates for consistency.

    -   **Maintainability:** Clear ownership for updates (though collaborative).

    -   **Evolution Tracking:** Specific sections for ADRs and versioning of ideas.

    -   **Minimizing Redundancy while Maximizing Clarity:** Each document has a clear purpose. Component docs will summarize relevant ADRs but not duplicate their full content.

    -   **Scalability:** Can easily accommodate new components, decisions, patterns.

    -   **Direct Support for AI-to-AI Workflow:** Includes sections for task templates, review templates, and communication protocols.

**Directory Structure:**

```
.memory-bank/
├── 00_README_MEMORY_BANK.md      # Instructions on using/maintaining this memory bank.

├── 01_PROJECT_FOUNDATION/
│   ├── 01_ProjectBrief.md          # (As per template) Mission, high-level goals, V1 scope (critical "in/out" scope).
│   ├── 02_ProductContext.md        # (As per template) User personas, user stories, detailed UX vision, problem statement.
│   └── 03_Glossary.md              # Definitions of key terms, acronyms, n8n-specific concepts used.

├── 02_ARCHITECTURE_AND_DESIGN/
│   ├── 01_SystemOverview.md        # High-level architecture (V3.3.1), conceptual layers, key component interactions (Mermaid diagram included).
│   ├── 02_TechStack.md             # Core technologies (TypeScript, Vercel AI SDK, RxDB, Zod, etc.) and rationale for choices.
│   ├── 03_DataArchitecture.md      # RxDB setup, collection schemas (high-level, linking to detailed Zod), data flow patterns.
│   ├── 04_DesignPatterns.md        # Key software design patterns employed (Repository, Service, Builder, Agent, etc.) with project-specific examples.
│   └── 05_ArchitecturalStyle.md    # Description of our decoupled, service-oriented, engine-based approach.

├── 03_ARCHITECTURE_DECISION_RECORDS/ (ADRs)
│   ├── ADR_TEMPLATE.md             # Template for new ADRs (Status, Context, Decision, Rationale, Consequences, Alternatives, Notes).
│   ├── ADR_001_CustomGraphOrchestration.md
│   ├── ADR_002_RxDBForPersistence.md
│   ├── ADR_003_VercelAISDKIntegration.md
│   ├── ADR_004_DecoupledPromptSubsystem.md
│   ├── ADR_005_AgentInfrastructure.md
│   └── ... (New ADRs added chronologically as major decisions are made)

├── 04_COMPONENTS/                  # Detailed documentation for each implemented/planned component.
│   ├── COMPONENT_TEMPLATE.md       # Template for component documentation.
│   ├── engine/
│   │   ├── graph/
│   │   │   ├── GraphBuilder.md
│   │   │   └── GraphExecutor.md
│   │   ├── llm/
│   │   │   └── LLMService.md
│   │   ├── prompts/                # Documentation for the PROMPT ENGINEERING SUBSYSTEM as a whole
│   │   │   ├── GenericPromptBuilder.md
│   │   │   ├── AgentPromptBuilder.md
│   │   │   ├── ConversationHistoryService.md
│   │   │   ├── RuleSelectorService.md
│   │   │   └── micro_utilities/    # Overview of Formatters, Normalizers, Mergers
│   │   │       ├── Formatters.md
│   │   │       ├── Normalizers.md
│   │   │       └── Mergers.md
│   │   └── agents/
│   │       ├── BaseAgent.md
│   │       └── agentRegistry.md
│   ├── agents/                     # Concrete Agents (e.g., N8nArchitectAgent.md)
│   │   └── N8nArchitectAgent.md
│   │   └── ...
│   ├── app/
│   │   └── OrchestratorService.md
│   ├── persistence/
│   │   ├── rxdbSetup.md
│   │   └── repositories/         # (e.g., UserSettingsRepository.md, TaskSessionRepository.md)
│   │       └── UserSettingsRepository.md
│   │       └── ...
│   ├── services/                   # Top-level application services
│   │   ├── UserSettingsService.md
│   │   ├── TaskSessionManager.md
│   │   └── NodeSpecService.md
│   ├── tools/
│   │   ├── ToolExecutorService.md
│   │   └── toolRegistry.md
│   │   └── specific_tools/       # (e.g., SearchDocumentationTool.md)
│   └── types/                      # Overview of key global Zod schemas and TypeScript types (linking to source for full detail).
│       └── DomainModelOverview.md

├── 05_PATTERNS_AND_CONVENTIONS/
│   ├── 01_CodingConventions.md     # TypeScript style, naming, linting, formatting.
│   ├── 02_TestingStrategy.md       # Unit, integration, E2E approaches, coverage goals, mocking.
│   ├── 03_ErrorHandlingPatterns.md # Standardized error types, propagation, logging.
│   └── 04_PromptEngineeringGuidelines.md # Best practices for writing agent prompts, using prompt parts.

├── 06_PROJECT_STATE_AND_ROADMAP/
│   ├── 01_CurrentPhase.md          # Detailed description of the current development phase.
│   ├── 02_Roadmap.md               # V1, V2, Future features and milestones.
│   ├── 03_ProgressStatus.md        # (Replaces old progress.md & tasks.md completed) Kanban-style: ToDo, InProgress (with current activeContext.md content), Done.
│   ├── 04_KnownIssuesAndRisks.md
│   └── 05_ReleaseNotes.md          # Changelog for library versions.

├── 07_AI_COLLABORATION_WORKFLOW/
│   ├── 01_WorkflowOverview.md      # The AI-to-AI workflow diagram and explanation (as you provided).
│   ├── 02_ImplementationTaskTemplate.md # (As you provided) For AI Studio to give to Claude Code.
│   ├── 03_ReviewResponseTemplate.md   # (As you provided) For AI Studio to respond to Claude Code.
│   └── 04_MemoryBankMaintenance.md # Protocols for updating THIS memory bank, responsibilities.

├── 08_TECHNICAL_CONTEXT_AND_SETUP/
│   ├── 01_DevEnvironment.md        # Setup, build/test commands, tooling (from techContext.md).
│   ├── 02_ProjectStructure.md      # V3.3.1 file structure explanation (can link to a diagram or use `tree` output).
│   └── 03_Dependencies.md          # Key external libraries and their versions/roles.

├── 09_KNOWLEDGE_BASE/              # Specific RAG-related content and configurations
│   ├── 01_BaseN8nRules.md          # Human-readable, detailed explanations of each rule in `baseN8nRules.config.ts`.
│   ├── 02_ModelAndPricingInfo.md   # Details from `llmModels.config.ts`.
│   ├── 03_NodeSnapshotInfo.md      # Info about `nodes.snapshot.json`.
│   └── 04_RAG_Sources.md           # List of sources for RAG (n8n docs, GitHub examples, Context7).

└── 10_ARCHIVE_AND_HISTORY/
    ├── DecisionEvolution.md        # Traces how key decisions evolved over time (critical for context continuity).
    ├── ImplementationReportArchive/ # Claude Code's reports.
    │   └── YYYY-MM-DD_TaskName.md
    └── BrainstormingSessionLogs/   # Summaries or links to key brainstorming logs (like "N8N Agent System Brainstorm.txt").
```

content_copydownload

Use code [with caution](https://support.google.com/legal/answer/13505487).

**Key Features of this Structure:**

-   **Numbered Top-Level Folders:** For clear progression and organization.

    -   **:** Essential guide.

    -   **:** High-level "what and why."

    -   **:** The "how" at a system level.

    -   **:** Immutable log of key decisions. Standard ADR format.

    -   **:** Granular documentation per component, following a template. This is where the details extracted from our implementation plans will primarily reside, summarized for the PRD context. It's organized mirroring the src structure for easy correlation.

    -   **:** Cross-cutting concerns for quality and consistency.

    -   **:** Dynamic project management documents. ProgressStatus.md will be the main "current work" tracker, absorbing activeContext.md.

    -   **:** Defines how we (AI Studio and Claude Code) work.

    -   **:** Practical info for developers.

    -   **:** Specific data/configs used by the AI, with explanations.

    -   **:** Crucial for context continuity. DecisionEvolution.md explicitly tracks how we arrived at current decisions, fulfilling your critical requirement for prioritizing latest versions while retaining history.

**Template Files (**

-   ADR_TEMPLATE.md: Standard ADR format.

    -   COMPONENT_TEMPLATE.md: Standard sections for each component doc (Purpose, Responsibilities, Key Features, API/Interface, Dependencies, Dependents, Design Rationale, Current Status, Implementation Notes, Test Strategy, Future Considerations).

This structure provides dedicated places for both high-level overviews and deep dives, distinguishes between static design and dynamic project state, and explicitly supports the AI-to-AI workflow.

* * * * *

**Part 2: How to Effectively Extract ALL Information**

This is the most intensive part. The goal is **lossless transfer of your current contextual understanding into this structure, prioritizing final decisions while noting significant evolutionary paths.**

**Process (Systematic & Iterative):**

-   **Preparation:**

    -   Have the V3.3.1 file structure map handy.

        -   Have the new Memory Bank structure (above) as a reference.

        -   Access to our full conversation history (both this chat and "N8N Agent System Brainstorm.txt").

    -   **Extraction Strategy - By Memory Bank Section:**\
    I will go through each top-level directory of the proposed Memory Bank structure and populate its files by synthesizing information from our entire discussion.

    -   **:**

        -   ProjectBrief.md: Synthesize from PRD Section 1 you provided (latest version).

            -   ProductContext.md: Synthesize from relevant sections of your "memory bank" proposals and PRD Section 1.

            -   Glossary.md: As I extract, I will identify key terms unique to our system (e.g., "Focused Design Spike," "AgentPromptBuilder," "GraphExecutionContextSnapshot") and define them.

        -   **:**

        -   SystemOverview.md: Based on PRD Section 2 & 3.1 (Orchestration Engine Overview). Include a high-level Mermaid diagram of key component clusters and their main interactions.

            -   TechStack.md: From PRD Section 2.3.

            -   DataArchitecture.md: From PRD Section 3.4 (RxDB, collections, schemas overview).

            -   DesignPatterns.md: Identify and list patterns used (Repository, Service, Builder, Agent, Fluent API, Decoupled Micro-Utilities for Prompts).

            -   ArchitecturalStyle.md: Describe our specific style (decoupled, service-oriented, TypeScript-first, custom engine).

        -   **:**

        -   **This is CRITICAL.** For every major architectural decision we made (e.g., choosing RxDB, custom graph engine, Vercel AI SDK, the specific V3.3.1 prompt subsystem design, agent infrastructure design):

            -   Create a new ADR_XXX_DecisionTitle.md file.

                -   Fill it using the ADR template, pulling context, rationale, alternatives considered (if discussed), and consequences from our conversations.

                -   I will explicitly look for points where we debated options (e.g., different loop mechanisms, different prompt builder responsibilities).

        -   **:**

        -   For every single component in the V3.3.1 structure (e.g., GraphBuilder.ts, LLMService.ts, N8nArchitectAgent.ts, TaskSessionRepository.ts, each Formatter/Normalizer/Merger):

            -   Create its .md file (e.g., components/engine/graph/GraphBuilder.md).

                -   Fill it using the COMPONENT_TEMPLATE.md.

                -   **Content will be a focused summary derived from its detailed implementation plan we generated/validated.** It will emphasize purpose, key responsibilities, public API/interface, dependencies, and key design choices specific to that component. It will not be as verbose as the full implementation plan but will be the canonical description of that component within the system.

        -   **:** Extract our discussions on testing, error handling, prompt engineering guidelines.

        -   **:**

        -   CurrentPhase.md: "Phase 0: Architectural Alignment & V3.3.1 Refactoring" is complete. We are about to start "Phase 1: Implementation of New Prompting Micro-Utilities & Revised Builders" (or whatever the true current next step is after this memory bank creation).

            -   Roadmap.md: Extract the multi-phase implementation roadmap we just outlined (from my previous messages).

            -   ProgressStatus.md: Initialize with "ToDo" for all upcoming phases/tasks. Mark Phase 0 as "Done."

        -   **:** Populate with the templates and workflow you provided.

        -   **:** Extract from techContext.md and our discussions.

        -   **:** Summarize contents/purpose of baseN8nRules.config.ts, etc.

        -   **:**

        -   DecisionEvolution.md: **CRITICAL.** For key evolving topics (like PromptBuilderService responsibilities, loop handling in GraphExecutor, agent interaction with services), I will trace the discussion path, highlighting initial ideas, challenges, refinements, and the final decision, explicitly stating what was superseded. This addresses your "prioritize FINAL/LATEST version ... note what it superseded" instruction.

            -   BrainstormingSessionLogs/N8N_Agent_System_Brainstorm_Summary.md: I will create a structured summary of the key outcomes and decisions from the "N8N Agent System Brainstorm.txt" file, focusing on elements that informed our current V3.3.1 architecture.

    -   **Prioritizing Latest Decisions:**

    -   Throughout the extraction, if I find an earlier idea (e.g., TemplateRegistry) that was later replaced (e.g., by static prompt parts and refined AgentPromptBuilder), the ADR for the final decision will mention the alternative considered and why it was changed. The DecisionEvolution.md will explicitly track this change. Component documentation in 04_COMPONENTS/ will ONLY reflect the LATEST design.

    -   **Cross-Referencing:** As I generate content, I will try to create logical links between documents (e.g., a component doc might link to relevant ADRs or patterns).