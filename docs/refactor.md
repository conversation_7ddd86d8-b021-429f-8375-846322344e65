## **Final Agreed Target Project Structure (V3.3.1):**

`src/
├── 📄 index.ts
├── 📁 __tests__/
│   └── 📄 setup.ts
│
├── 📁 agents/                     // CONCRETE AI AGENT IMPLEMENTATIONS
│   ├── 📁 architect/
│   │   ├── 📄 N8nArchitectAgent.ts
│   │   └── 📄 architect.prompts.ts
│   ├── ...
│   └── 📄 index.ts
│
├── 📁 app/                        // APPLICATION-LEVEL ORCHESTRATION
│   ├── 📄 OrchestratorService.ts
│   ├── 📄 index.ts
│   └── 📁 __tests__/
│
├── 📁 config/                     // GLOBAL STATIC CONFIGURATIONS
│   ├── 📄 baseN8nRules.config.ts
│   ├── 📄 llmModels.config.ts
│   ├── 📄 nodes.snapshot.json
│   └── 📄 index.ts
│
├── 📁 engine/                     // CORE AI ENGINE INFRASTRUCTURE
│   ├── 📁 agents/                   // Agent *infrastructure*
│   │   ├── 📄 BaseAgent.ts
│   │   ├── 📄 agentRegistry.ts
│   │   ├── 📄 initializeAgents.ts
│   │   └── 📁 types/
│   │       ├── 📄 agentRegistry.types.ts
│   │       └── 📄 agents.types.ts
│   ├── 📁 graph/                    // Graph execution engine
│   │   ├── 📄 GraphBuilder.ts
│   │   ├── 📄 GraphExecutor.ts
│   │   └── 📁 types/
│   │       ├── 📄 builder.types.ts
│   │       ├── 📄 execution.types.ts
│   │       └── 📄 graph.types.ts
│   ├── 📁 llm/                      // LLM Interaction Subsystem
│   │   ├── 📄 LLMService.ts
│   │   └── 📁 types/
│   │       └── 📄 llmService.types.ts
│   ├── 📁 prompts/                  // PROMPT ENGINEERING SUBSYSTEM (Utilities & Services)
│   │   ├── 📁 formatters/           // (SystemInstructionFormatter.ts, etc.)
│   │   ├── 📁 normalizers/            // (MessageNormalizer.ts, etc.)
│   │   ├── 📁 mergers/                // (MessageMerger.ts)
│   │   ├── 📁 parts/                  // (system.parts.ts, etc. - Universal prompt snippets)
│   │   ├── 📁 services/               // Prompt *orchestration* services WITHIN THE ENGINE
│   │   │   ├── 📄 GenericPromptBuilder.ts
│   │   │   ├── 📄 AgentPromptBuilder.ts
│   │   │   ├── 📄 ConversationHistoryService.ts
│   │   │   ├── 📄 RuleSelectorService.ts
│   │   │   └── 📁 types/              // Types for these prompting services
│   │   ├── 📄 index.ts
│   │   └── 📁 types/
│   │       └── 📄 prompting.types.ts    // Common types for this subsystem (PromptComponent, etc.)
│   └── 📄 index.ts                   // Exports key engine infrastructure
│
├── 📁 graphs/                     // User-defined GRAPH DEFINITIONS
│   ├── 📄 createNewWorkflow.graph.ts
│   └── 📄 index.ts
│
├── 📁 knowledge/                  // RAG System
│   ├── 📄 EmbeddingService.ts
│   └── ...
│
├── 📁 persistence/                // DATA PERSISTENCE LAYER (RxDB)
│   ├── 📄 rxdbSetup.ts
│   ├── 📁 repositories/
│   └── ...
│
├── 📁 services/                   // HIGHER-LEVEL APPLICATION SERVICES
│   ├── 📄 UserSettingsService.ts
│   ├── 📄 TaskSessionManager.ts
│   ├── 📄 NodeSpecService.ts        // Domain data service
│   ├── 📄 KnowledgeService.ts (Future)
│   └── 📁 types/                   // Types specific to these application services (e.g. nodeSpecService.types.ts)
│
├── 📄 index.ts                    // Main library barrel export
│
├── 📁 tools/                      // TOOL DEFINITIONS, REGISTRY & EXECUTOR
│   ├── 📄 toolRegistry.ts
│   ├── 📄 ToolExecutorService.ts
│   └── ...
│
├── 📁 types/                      // GLOBAL SHARED DOMAIN ZOD SCHEMAS & TYPES
│   ├── 📄 nodes.types.ts           // NodeDescriptionSchema, NodeSelectorDescriptor (used by NodeSpecService)
│   └── ...
│
└── 📁 utils/
    └── ...`

*(Note: __tests__ subdirectories are implied for most modules and omitted here for brevity but should be present as per the original structure you provided).*

## **Holistic Architectural Analysis & Refactoring Roadmap Generation**

**Objective:**

1. Analyze the source code and implementation against our target V3.3.1 architecture.
2. Identify all necessary refactoring tasks (renaming, moving files, splitting components, creating new ones).
3. Define a clear, dependency-ordered implementation roadmap that the developer LLM can follow to transform the current state into the target V3.3.1 architecture, implementing new components and refactoring existing ones.

## IMPLEMENTATION ROADMAP

This section outlines the phased implementation plan to realize the V1 n8n Agentic AI "Brain" Library. It begins with a detailed refactoring of the existing codebase (as per the current implementation state in src/) to align strictly with our V3.3.1 target architecture. This is followed by the development of new components and the refinement of existing ones where necessary. This roadmap is designed for execution by Claude Code, adhering to the guidelines in CLAUDE.md.

### 7.1 Development Phases & Milestones

- **Phase 0: Architectural Alignment & V3.3.1 Refactoring (Immediate & Critical Focus)**
    - **Goal:** Transform the CIS codebase into the V3.3.1 structure. This involves precise file/directory moves, creating new module structures (especially for the engine/prompts/ subsystem), updating all imports, ensuring existing validated logic is preserved, and adapting components to their new roles and dependencies as per our final designs.
    - **Key Activities:** Strategic file moves (mv), new directory creation (mkdir -p), deletion of obsolete files/directories (rm -rf), comprehensive import path updates across the entire codebase, minor code adaptations in existing files to align with new dependency locations or slightly altered interfaces from the refactoring. Re-running existing tests and fixing any issues arising from the refactor.
    - **Milestone:** Codebase perfectly mirrors the V3.3.1 file structure. All components from CIS that are retained are correctly relocated. All existing tests pass. The foundation for implementing new, decoupled prompt utilities is established.
- **Phase 1: Implementation of New Prompting Micro-Utilities & Revised Builders**
    - **Goal:** Implement the new, highly decoupled prompt engineering micro-utilities (Formatters, Normalizers, Mergers) and refactor/implement the GenericPromptBuilder and AgentPromptBuilder to use them.
    - **Key Activities:** Create and test each Formatter, Normalizer, and Merger. Implement the revised (leaner) GenericPromptBuilder. Implement the new AgentPromptBuilder.
    - **Milestone:** The complete prompt engineering subsystem (src/engine/prompts/) is functional and well-tested.
- **Phase 2: Implementation of Core Conversation & Task State Management**
    - **Goal:** Implement services for managing conversation history (including summarization) and task session lifecycle.
    - **Key Activities:** Implement ConversationHistoryService (using the new prompt builders for summarizer prompts). Implement TaskSessionManager (using TaskSessionRepository and integrating with ConversationHistoryService for summary updates).
    - **Milestone:** Robust management of task-specific conversational context and overall task state is functional.
- **Phase 3: Implementation of Remaining Core Engine Components & Agent Infrastructure**
    - **Goal:** Ensure GraphExecutor, BaseAgent, and agentRegistry are fully implemented and tested according to their detailed plans.
    - **Key Activities:**
        - **GraphExecutor.ts:** Full implementation based on its validated plan (generic execution, HIL, error handling).
        - **BaseAgent.ts:** Full implementation (static properties, DI, response helpers, prepareLlmMessages using AgentPromptBuilder).
        - **agentRegistry.ts:** Populate AGENT_REGISTRY_CONFIG with *shell entries* for all V1 agents, ensuring it correctly uses static agent properties.
    - **Milestone:** The core graph execution engine and agent infrastructure are complete and tested.
- **Phase 4: V1 Concrete Agent & Tool Logic Implementation**
    - **Goal:** Implement the core execute() logic for all V1 specialized AI agents and their required tools.
    - **Key Activities:** Develop agent-specific prompt parts (*.parts.ts in each agent's src/agents/{agentName}/prompts/ dir). Implement execute() methods, getFinalOutputSchema() for each V1 agent. Implement V1 tools (mocking n8n-instance interactions for library V1).
    - **Milestone:** All V1 agents are functional.
- **Phase 5: V1 Graph Definition & End-to-End Integration**
    - **Goal:** Define and test the V1 graphs (createNewWorkflowGraph_v1, updateExistingWorkflowGraph_v1, analyzeWorkflowGraph_v1) using the mature GraphBuilder.
    - **Key Activities:** Write graph definition files in src/graphs/. Conduct integration testing of full graph executions with (partially mocked) agents and tools. Implement RAG tool interactions (Context7, GitHub querier).
    - **Milestone:** V1 library is feature-complete for its defined scope.
- **Phase 6: Final Documentation, Testing & V1 Release Prep**
    - **Goal:** Comprehensive documentation, maximized test coverage, performance review, and packaging.
    - **Milestone:** V1 "Brain" Library ready.

### 7.2 Component Implementation Order & Detailed Refactoring Plan

**Phase 0: Architectural Alignment & V3.3.1 Refactoring**

**Objective:** Precisely transform the CIS structure into the V3.3.1 target structure. This phase requires careful analysis of each file in CIS.

- **[CC-TASK 0.1] Create Initial Checkpoint:**
    - claudepoint create "Baseline - Pre-Refactor V3.3.1"
- **[CC-TASK 0.2] Establish Target V3.3.1 Directory Structure:**
    - **Action:** Based on the V3.3.1 structure detailed in our PRD (Section "Final Agreed Project Structure (V3.3.1)"), create all new directories and subdirectories if they do not exist in CIS. For example:
        - mkdir -p src/agents/architect/prompts (and for all other V1 agents)
        - mkdir -p src/app
        - mkdir -p src/config
        - mkdir -p src/engine/agents/base
        - mkdir -p src/engine/agents/types
        - mkdir -p src/engine/graph/types
        - mkdir -p src/engine/llm/types
        - mkdir -p src/engine/prompts/formatters
        - mkdir -p src/engine/prompts/mergers
        - mkdir -p src/engine/prompts/normalizers
        - mkdir -p src/engine/prompts/parts
        - mkdir -p src/engine/prompts/services
        - mkdir -p src/engine/prompts/services/types
        - mkdir -p src/engine/prompts/types
        - mkdir -p src/graphs
        - mkdir -p src/knowledge (shell directory for now)
        - mkdir -p src/persistence/repositories/__tests__
        - mkdir -p src/persistence/schemas
        - mkdir -p src/persistence/types
        - mkdir -p src/services (top-level)
        - mkdir -p src/services/types (top-level)
        - mkdir -p src/tools (top-level, for definitions, registry, executor)
        - mkdir -p src/types (global domain types)
        - mkdir -p src/utils (already exists, confirm location)
- **[CC-TASK 0.3] Analyze and Relocate/Refactor src/config/:**
    - **models.config.ts:**
        - CIS Location: src/config/models.config.ts.
        - V3.3.1 Target: src/config/llmModels.config.ts.
        - **Action:** mv src/config/models.config.ts src/config/llmModels.config.ts. Ensure its content and exports (createEnhancedModelPricingConfig, etc.) are aligned with LLMService's needs. Content was previously validated as good.
    - **nodes.snapshot.json (Placeholder for CIS engine/config/nodes.json):**
        - CIS has src/engine/config/nodes.json.
        - V3.3.1 Target: src/config/nodes.snapshot.json.
        - **Action:** mv src/engine/config/nodes.json src/config/nodes.snapshot.json.
    - **baseN8nRules.config.ts:**
        - CIS does not have this file directly in src/config/. It was provided as compiled_baserules_src.txt.
        - V3.3.1 Target: src/config/baseN8nRules.config.ts.
        - **Action:** Create src/config/baseN8nRules.config.ts using the validated content from compiled_baserules_src.txt. Ensure it exports loadBaseN8nRules() and necessary types.
    - **userSettings.defaults.ts (Was userSettings.config.ts):**
        - CIS Location: src/services/userSettings.config.ts.
        - V3.3.1 Target: src/config/userSettings.defaults.ts.
        - **Action:** mv src/services/userSettings.config.ts src/config/userSettings.defaults.ts. Update its imports (if any) and ensure UserSettingsService can import defaults from here.
    - **index.ts for src/config/:**
        - **Action:** Create src/config/index.ts to export key configurations.
- **[CC-TASK 0.4] Analyze and Relocate/Refactor src/engine/ Submodules:**
    - **engine/agents/ (Agent Infrastructure):**
        - BaseAgent.ts:
            - CIS Location: src/engine/agents/base/BaseAgent.ts. Content from llmservice_promptbuilder_templaterepository_src_for_architect_complete_analysis_and_review.txt was validated as "Final Plan V3.3".
            - V3.3.1 Target: Same.
            - **Action:** Verify CIS content matches the "Final Plan V3.3" for BaseAgent.ts (static properties, constructor with AgentDependencies, prepareLlmMessages calling AgentPromptBuilder, invokeLlm, response helpers, getFinalOutputSchema). Make necessary code refinements if CIS version is older. Update imports.
        - agentRegistry.ts:
            - CIS Location: src/engine/agents/agentRegistry.ts. Content from same source, validated as "Final Plan V3.3".
            - V3.3.1 Target: Same.
            - **Action:** Verify CIS content matches "Final Plan V3.3" (AGENT_REGISTRY_CONFIG structure, agentRegistryMap, helper functions like createAgentInstance). Update imports to reflect new BaseAgent and agent type locations. Remove placeholder TODOs for agent imports and prepare for concrete agent class imports from src/agents/.
        - types/agents.types.ts & types/agentRegistry.types.ts:
            - CIS Location: src/engine/agents/types/. Content from same source, validated as "Final Plan V3.3".
            - V3.3.1 Target: Same.
            - **Action:** Verify contents and update any internal type references.
        - initialize.ts:
            - CIS Location: src/engine/agents/initialize.ts. This was for TemplateRegistry.
            - V3.3.1 Target: REMOVED. Agent prompt parts/instructions are handled differently now.
            - **Action:** rm -f src/engine/agents/initialize.ts.
        - index.ts for src/engine/agents/:
            - **Action:** Ensure it exports BaseAgent, agentRegistryMap, createAgentInstance, and relevant types.
    - **engine/graph/ (Graph Engine):**
        - GraphBuilder.ts, GraphExecutor.ts, types/*:
            - CIS Location: src/engine/graph/. These were the first components planned and "implemented" (code provided by developer LLM and validated by us).
            - V3.3.1 Target: Same.
            - **Action:** These files are considered foundational and **ALREADY ALIGNED** with our plans. No major code changes beyond ensuring their internal imports are correct after other files move. Confirm their tests still pass.
    - **engine/llm/ (New Submodule):**
        - LLMService.ts & types/llmService.types.ts:
            - CIS Location: src/engine/services/LLMService.ts and src/engine/services/types/llmService.types.ts. Validated.
            - V3.3.1 Target: src/engine/llm/LLMService.ts and src/engine/llm/types/llmService.types.ts.
            - **Action:**
                - mkdir -p src/engine/llm/types
                - mv src/engine/services/LLMService.ts src/engine/llm/
                - mv src/engine/services/types/llmService.types.ts src/engine/llm/types/
                - Update imports.
    - **engine/prompts/ (New Subsystem - This is a major refactor):**
        - **parts/*:**
            - CIS: src/prompts/templates/system.ts, src/prompts/templates/summarize.ts.
            - V3.3.1: src/engine/prompts/parts/system.parts.ts, src/engine/prompts/parts/summarizer.parts.ts, and new parts like inputContext.parts.ts, outputFormat.parts.ts, teamAwareness.parts.ts.
            - **Action:**
                - mkdir -p src/engine/prompts/parts
                - mv src/prompts/templates/system.ts src/engine/prompts/parts/system.parts.ts (Rename SYSTEM_PROMPT_BASE to e.g., CORE_SYSTEM_PROMPT_TEMPLATE_BASE. Ensure TEAM_AWARENESS, INPUT_CONTEXT_DEFINITION, OUTPUT_FORMAT_INSTRUCTION are defined here as exported constants/template strings).
                - mv src/prompts/templates/summarize.ts src/engine/prompts/parts/summarizer.parts.ts (Ensure SUMMARIZER_SYSTEM_INSTRUCTION_V1, SUMMARIZER_USER_TASK_TEMPLATE_V1 are defined here).
                - Create new files in engine/prompts/parts/ for any other standard prompt blocks we identified (e.g., teamAwareness.parts.ts might hold the template string for that section).
                - Remove src/prompts/templates/index.ts and src/prompts/templates/teamAwareness.ts.
        - **formatters/, normalizers/, mergers/ (ALL NEW):**
            - **Action:** These directories are empty. Implementation plans for these micro-utilities are **PENDING** and will be tackled in Phase 1.
        - **types/prompting.types.ts (Consolidates various prompt-related types):**
            - CIS: engine/services/types/genericPromptBuilder.types.ts, src/prompts/types/templates.types.ts.
            - V3.3.1: src/engine/prompts/types/prompting.types.ts.
            - **Action:** Create src/engine/prompts/types/prompting.types.ts. Consolidate PromptComponent types, AssembledPromptResult, TokenCountFunction types from CIS genericPromptBuilder.types.ts. TemplateKey, AgentTemplates from CIS templates.types.ts are **REMOVED** as TemplateRegistry is gone.
        - **services/GenericPromptBuilder.ts:**
            - CIS Location: src/engine/services/GenericPromptBuilder.ts. Its current implementation (from llmservice_promptbuilder_templaterepository_src_for_architect_complete_analysis_and_review.txt) is now considered legacy due to the micro-utility decision.
            - V3.3.1 Target: src/engine/prompts/services/GenericPromptBuilder.ts.
            - **Action:**
                - mv src/engine/services/GenericPromptBuilder.ts src/engine/prompts/services/
                - **CRITICAL CODE CHANGE:** This file needs a **full rewrite** based on our new plan for it to orchestrate the (yet to be implemented) Formatters, Normalizers, and Mergers. The existing complex logic will be distributed into those micro-utilities. The implementation plan for this revised GenericPromptBuilder is **PENDING** (Phase 1).
        - **services/AgentPromptBuilder.ts (NEW):**
            - **Action:** Implementation plan for src/engine/prompts/services/AgentPromptBuilder.ts is **PENDING** (Phase 1).
        - **services/ConversationHistoryService.ts:**
            - CIS Location: src/services/ConversationHistoryService.ts.
            - V3.3.1 Target: src/engine/prompts/services/ConversationHistoryService.ts. Its types from src/services/types/conversationHistoryService.types.ts also move.
            - **Action:**
                - mv src/services/ConversationHistoryService.ts src/engine/prompts/services/
                - mv src/services/types/conversationHistoryService.types.ts src/engine/prompts/services/types/
                - **CODE REVIEW/REFINEMENT NEEDED:** Its current implementation needs to be adapted to use the new GenericPromptBuilder (for summarizer prompts) and align with AgentPromptBuilder's role. This means its own prompt building for summarization will become simpler.
        - **services/RuleSelectorService.ts:**
            - CIS Location: src/services/RuleSelectorService.ts. Implementation validated.
            - V3.3.1 Target: src/engine/prompts/services/RuleSelectorService.ts. Its types from src/services/types/ruleSelectorService.types.ts also move.
            - **Action:**
                - mv src/services/RuleSelectorService.ts src/engine/prompts/services/
                - mv src/services/types/ruleSelectorService.types.ts src/engine/prompts/services/types/
                - Update imports.
        - **index.ts for src/engine/prompts/ and src/engine/prompts/services/:**
            - **Action:** Create/update these to export relevant items.
    - **Remove engine/config/ and engine/prompts/promptPartLoader.ts (already handled)**.
    - **Remove old engine/services/ directory and its types/:**
        - CIS has engine/services/ with LLMService, NodeSpecService, GenericPromptBuilder, PromptBuilderService and their types. All of these have been moved or are being refactored/replaced.
        - **Action:** rm -rf src/engine/services (after its contents are moved).
- **[CC-TASK 0.5] Analyze and Relocate/Refactor src/persistence/:**
    - rxdbSetup.ts, types/database.types.ts, repositories/*.
    - CIS Location: src/persistence/. All components here were validated.
    - V3.3.1 Target: Same.
    - **Action:** No major moves.
        - **Schema Directory:** Create src/persistence/schemas/. For V1, rxdbSetup.ts can continue to generate RxDB JSON schemas dynamically from Zod at runtime. We can defer populating this directory with static .json or .ts schema files.
        - Ensure CustomRulesRepository.ts and TaskSessionRepository.ts are aligned with their final validated plans/implementations.
        - ChatRepository.ts in CIS is a placeholder. Its full implementation plan is **PENDING** (Phase 1).
        - index.ts for src/persistence/ and src/persistence/repositories/.
- **[CC-TASK 0.6] Analyze and Relocate/Refactor src/services/ (Top-Level Application Services):**
    - UserSettingsService.ts:
        - CIS Location: src/services/UserSettingsService.ts. Validated.
        - V3.3.1 Target: Same.
        - **Action:** No move. Ensure its types are correctly in src/types/userSettings.types.ts (global) or src/services/types/userSettingsService.types.ts (service-specific, if any).
    - NodeSpecService.ts (Moved here from engine/services/ in Task 0.4).
    - TaskSessionManager.ts:
        - CIS Location: src/services/TaskSessionManager.ts. Validated.
        - V3.3.1 Target: Same.
        - **Action:** No move. **CODE REVIEW/REFINEMENT NEEDED:** Its interaction with PromptBuilderService (now AgentPromptBuilder) and ConversationHistoryService for summarization needs to be updated to reflect the new roles of these services. The ensureContextIsSummarized method will likely change significantly or be removed, with that logic now primarily residing in ConversationHistoryService and coordinated by AgentPromptBuilder / BaseAgent.prepareLlmMessages.
    - index.ts for src/services/.
- **[CC-TASK 0.7] Analyze and Relocate/Refactor src/types/ (Global Domain Types):**
    - CIS has chat.types.ts, index.ts, knowledge.types.ts, rules.types.ts, tasks.types.ts, userSettings.types.ts.
    - V3.3.1 Target: Same. These are global Zod schemas and TypeScript types.
    - **Action:** Review each file. Ensure they ONLY contain truly global domain types. Any types that are specific to a single service/module should have been co-located during earlier moves (e.g., llmService.types.ts is now in engine/llm/types/). TaskSessionSchema and GraphExecutionContextSnapshotSchema in tasks.types.ts are correct here as they are fundamental domain objects.
- **[CC-TASK 0.8] Prepare Other Top-Level Directories:**
    - src/agents/: Create shell subdirectories for each V1 agent (e.g., architect/, composer/) and inside each, a prompts/ subdirectory. Agent implementations are Phase 4.
    - src/app/: Create shell for OrchestratorService.ts. Implementation is Phase 3-4.
    - src/graphs/: CIS has simpleWorkflow.ts. This can remain as an example. V1 graph definitions are Phase 5.
    - src/knowledge/: Create shell for EmbeddingService.ts. Implementation is Phase 1.
    - src/tools/: Create shells for toolRegistry.ts, ToolExecutorService.ts, and types/tools.types.ts. V1 tool definitions are Phase 4.
- **[CC-TASK 0.9] Global Imports & Barrel Files (index.ts):**
    - **Action:** Meticulously update ALL import statements across the entire refactored codebase to use correct V3.3.1 paths.
    - Create/Update index.ts barrel files in *all main directories* (src/agents/, src/app/, src/config/, src/engine/ (and its submodules like src/engine/prompts/services/), src/graphs/, src/knowledge/, src/persistence/, src/services/, src/tools/, src/types/, src/utils/) to export their public APIs.
    - Update the main src/index.ts to export the public API of the "Brain" library.
- **[CC-TASK 0.10] Full Build, Type Check, Lint, Test Execution:**
    - pnpm build
    - pnpm type-check
    - pnpm lint --fix
    - pnpm test
    - **Goal:** All commands pass. All existing tests for relocated components must pass. Any test failures due to refactoring must be fixed.
- **[CC-TASK 0.11] Create Checkpoint:**
    - claudepoint create "RefactorV3.3.1Complete - Structure Aligned, Imports Fixed, Tests Passing"

---

**Phase 1 onwards: New Component Implementation & Refinement (Order based on dependencies after refactoring)**

This list prioritizes getting the new, decoupled prompting utilities built first, as they are foundational for AgentPromptBuilder, which BaseAgent relies on.

1. **Prompting Micro-Utilities (src/engine/prompts/formatters|normalizers|mergers/) - NEW IMPLEMENTATION:**
    - **Task:** Generate detailed implementation plans for, then implement and test:
        - JsonSchemaInstructionFormatter.ts
        - TaggedDataFormatter.ts
        - SystemInstructionFormatter.ts, UserMessageFormatter.ts, AssistantMessageFormatter.ts, TextBlockFormatter.ts (pattern from first two)
        - ToolContentNormalizer.ts
        - MessageNormalizer.ts (will use ToolContentNormalizer)
        - MessageMerger.ts
    - **Testing:** Pure unit tests for each utility.
2. **Revised GenericPromptBuilder.ts (src/engine/prompts/services/) - REWRITE & TEST:**
    - **Task:** Rewrite implementation to orchestrate the new micro-utilities.
    - **Testing:** Unit tests mocking the formatters, normalizers, mergers.
3. **ConversationHistoryService.ts (src/engine/prompts/services/) - REFACTOR & TEST:**
    - **Task:** Adapt to use the new GenericPromptBuilder for its internal summarizer prompts. Ensure its logic for fetching history and orchestrating summarization is robust.
    - **Testing:** Unit tests mocking TaskSessionRepo, ChatRepo, LLMService, and the new GenericPromptBuilder.
4. **AgentPromptBuilder.ts (src/engine/prompts/services/) - NEW IMPLEMENTATION:**
    - **Task:** Implement this service to use GenericPromptBuilder, ConversationHistoryService, and RuleSelectorService.
    - **Testing:** Unit tests verifying correct assembly of all prompt components for various agent scenarios.
5. **TaskSessionManager.ts (src/services/) - REFACTOR & TEST:**
    - **Task:** Refine its API and internal logic. The summarization trigger might now come from AgentPromptBuilder (via BaseAgent) throwing SummarizationRequiredError, or ConversationHistoryService could directly update the task session summary if it performs summarization. Clarify this flow. (Our last decision was CHS updates session, APB consumes session).
    - **Testing:** Unit tests mocking TaskSessionRepo, and its interaction with CHS if applicable.
6. **BaseAgent.ts (src/engine/agents/base/) - REFINE & TEST:**
    - **Task:** Ensure prepareLlmMessages() correctly uses the new AgentPromptBuilder.
    - **Testing:** Unit tests for its helper methods.
7. **GraphExecutor.ts (src/engine/graph/) - IMPLEMENT & TEST:**
    - **Task:** Full implementation based on its existing validated plan.
    - **Testing:** Comprehensive unit and integration tests as per its plan.
8. **Remaining Repositories (ChatRepository.ts, KnowledgeRepository.ts) & EmbeddingService.ts - IMPLEMENT & TEST:**
    - **Task:** Full implementation and testing for these.

**Phase 4: V1 Concrete Agent & Tool Logic Implementation**

- **Goal:** Implement the core execute() logic for all V1 specialized AI agents and their required supporting tools. This brings the "intelligence" of the system to life.
- **Prerequisites:** All components from Phases 0-3 are implemented and thoroughly tested.
- **Key Activities & Order:**
    1. **[CC-TASK 4.1] Implement V1 Tool Definitions (src/tools/definitions/):**
        - For each V1 tool identified (e.g., search_documentation_tool, search_workflow_templates_tool, get_workflow_template_json_tool, and the *conceptual/mocked* versions of n8n-interaction tools like fetch_current_workflow_json_tool, apply_changes_to_canvas_tool for library V1).
        - **Action:** Create the ToolDefinition object for each, including:
            - name, description.
            - inputSchema (Zod), outputSchema (Zod).
            - execute method:
                - For RAG tools: Implement logic to use KnowledgeRepository (and EmbeddingService if query embedding is needed here).
                - For n8n-interaction tools (V1 library): execute will log the call and its arguments, and return a pre-defined mock success or failure response. No actual browser/n8n-instance interaction.
                - For internal_await_user_input_tool: This tool's execute method is special; it signals the GraphExecutor to pause and expect user input (as discussed in GraphExecutor HIL mechanism).
        - Register all V1 tools in src/tools/toolRegistry.ts.
        - Write unit tests for each tool (mocking dependencies like KnowledgeRepository).
    2. **[CC-TASK 4.2] Implement n8n-summarizer-agent "Role" Integration:**
        - **Action:** No separate agent class. Ensure ConversationHistoryService correctly uses the n8n-summarizer-agent slug, its specific prompt parts from src/engine/prompts/parts/summarizer.parts.ts (via AgentPromptBuilder), and the UTILITY_CONVERSATION_SUMMARIZATION LLMTaskCategory when it invokes LLMService for summarization.
        - Test this summarization sub-process thoroughly via ConversationHistoryService tests.
    3. **[CC-TASK 4.3] Implement Concrete AI Agent Logic (src/agents/{agentName}/):**
        - For each V1 agent (IntentRouter, RequirementsGatherer, Architect, NodeSelector, NodeComposer, WorkflowDocumenter, ConnectionComposer, Validator, Reflection):
            - **Action (Create/Update Agent Class):** In its file (e.g., src/agents/architect/N8nArchitectAgent.ts), ensure it extends BaseAgent and defines static readonly agentSlug, static readonly CORE_SYSTEM_INSTRUCTION (imported from its co-located *.prompts.ts), static readonly defaultLlmTaskCategory.
            - **Action (Create Agent Prompts File):** Create/finalize src/agents/{agentName}/prompts/{agentName}.prompts.ts exporting the CORE_SYSTEM_INSTRUCTION and any other agent-specific prompt part constants.
            - **Action (Implement getFinalOutputSchema()):** Return the correct Zod schema for its result.payload.
            - **Action (Implement execute() method):**
                - This is the core logic. It will receive inputs from the graph.
                - It will use this.prepareLlmMessages() (from BaseAgent) to build its prompt(s) for the LLM, passing its specific subTaskUserInstruction and callSpecificData.
                - It will use this.invokeLlm() (from BaseAgent) to make the LLM call(s).
                - It will process the LLMInvocationResult.
                - If it needs to perform multiple internal LLM calls or complex logic, implement that sequence.
                - If it needs user clarification, it returns this.requestUserClarification(...).
                - If it needs graph-level tools, it returns this.requestGraphTools(...) (including intermediate_agent_state_for_resume). The graph will re-invoke it with tool results.
                - Finally, it returns this.success(...) with the Zod-validated payload, or this.failure(...).
            - **Testing:** Write comprehensive unit tests for each agent, mocking its AgentDependencies (llmService, agentPromptBuilder, nodeSpecService, etc.). Test all logical paths within execute(), including how it handles different LLM responses, HIL requests, and tool requests.
- **Milestone:** All V1 specialized AI agents and essential V1 tools are implemented with core logic and thoroughly unit-tested. The system can perform individual AI "thinking" steps.

**Phase 5: V1 Graph Definition & End-to-End Integration**

- **Goal:** Define the V1 graphs (createNewWorkflowGraph_v1, updateExistingWorkflowGraph_v1, analyzeWorkflowGraph_v1) using the GraphBuilder API, and conduct integration testing of full graph executions. Implement V1 RAG tool interactions.
- **Prerequisites:** Phase 4 completed. All agents and tools have at least their V1 logic.
- **Key Activities & Order:**
    1. **[CC-TASK 5.1] Implement RAG Tool Backend (KnowledgeRepository & EmbeddingService):**
        - **Action:** Fully implement EmbeddingService.ts (using transformers.js).
        - **Action:** Fully implement KnowledgeRepository.ts (using RxDB, EmbeddingService, and client-side vector search logic based on the "distance to samples" pattern or similar for idx fields).
        - **Action:** Create a mechanism to populate KnowledgeRepository with:
            - Processed n8n documentation (chunked, embedded). For V1 library, this might be from a pre-processed JSON file.
            - Processed n8n workflow examples (chunked, embedded from GitHub via a conceptual custom MCP server, or from a pre-processed JSON file for V1 library).
        - **Testing:** Unit tests for EmbeddingService and KnowledgeRepository (mocking RxDB for repo tests). Test vector search accuracy with a small sample dataset.
    2. **[CC-TASK 5.2] Implement MCP Client/Tool for Context7 (Conceptual for V1 Library):**
        - **Action:** Define the ToolDefinition for query_context7_n8n_docs_tool.
        - **Action:** For V1 library, its execute method might either:
            - Directly use KnowledgeRepository if Context7 data is pre-loaded there.
            - Simulate an MCP call and return mock data.
        - (Actual MCP client implementation is for when the library is in an environment that can make network requests easily).
    3. **[CC-TASK 5.3] Define createNewWorkflowGraph_v1.graph.ts:**
        - **Action:** Using the GraphBuilder API, meticulously define all nodes (agent calls, tool calls, conditionals, data transforms, loops using builder.addLoop()) and edges for the "Create New Workflow" graph, as per our detailed sketch and refinements.
        - Ensure all inputs for nodes are correctly mapped using builder.nodeOutput(), builder.graphInput(), etc., including fallbackSource for loop state.
        - Ensure all HIL pauses and re-entry points are correctly wired.
        - Define error handling paths (using .onFailure() or relying on the graph's default error handler).
        - **Testing:** "Dry run" compilation with builder.compile(). Generate Mermaid diagram and review visually.
    4. **[CC-TASK 5.4] Define updateExistingWorkflowGraph_v1.graph.ts:**
        - **Action:** Similar to 5.3, define the graph for updating existing workflows. This will involve providing the existing WorkflowSchema as an initial input. Agents like Architect and Composer will need to be prompted to understand "diffs" or "modifications."
        - **Testing:** Dry run, Mermaid diagram.
    5. **[CC-TASK 5.5] Define analyzeWorkflowGraph_v1.graph.ts:**
        - **Action:** Define the graph for analyzing workflows. This will primarily use N8nBestPracticeValidatorAgent and N8nExplainerAgent.
        - **Testing:** Dry run, Mermaid diagram.
    6. **[CC-TASK 5.6] Integration Testing (Graph Level):**
        - **Action:** For each V1 graph:
            - Write integration tests that invoke OrchestratorService.processUserRequest(...) (which will internally create/run the graph via GraphExecutor).
            - Provide sample userInputText and mock any necessary initial context.
            - Mock LLM responses at the LLMService level for key agent interactions to test specific paths through the graph (happy path, HIL path, error path, loop execution).
            - Assert that the graph executes the expected sequence of nodes.
            - Assert that the final TaskSession state (status, output, error) is correct.
            - Test HIL pauses: verify graph pauses, activeInterruptDetails are correct, and graph resumes correctly with mock user input.
            - Test loop execution: verify correct number of iterations for nodeCompositionLoop, correct accumulation.
- **Milestone:** V1 graphs are defined. Core end-to-end flows for "Create," "Update," and "Analyze" are testable at the library level with mocked LLM interactions and (for V1 library) conceptual n8n instance interactions. RAG tools provide knowledge from pre-processed sources.

**Phase 6: Final Documentation, Testing & V1 Release Prep**

- **Goal:** Ensure the V1 "Brain" Library is robust, well-documented, and ready for initial use (e.g., by a CLI test harness or for early integration experiments with a browser extension).
- **Prerequisites:** Phase 5 completed.
- **Key Activities:**
    1. **[CC-TASK 6.1] Comprehensive Code Review:** Review all V1 components for adherence to design, best practices, and clarity.
    2. **[CC-TASK 6.2] Maximize Test Coverage:** Identify any gaps in unit or integration test coverage and add tests. Aim for >85-90% coverage on critical path code.
    3. **[CC-TASK 6.3] API Documentation (TSDoc/JSDoc):** Ensure all public APIs (services, builder, agent base class, key types) are thoroughly documented.
    4. **[CC-TASK 6.4] Developer Documentation (READMEs, Architectural Overview):**
        - Update project README.md.
        - Create an ARCHITECTURE.md summarizing the V3.3.1 structure, key components, and data flow (can leverage PRD content).
        - Document how to define new agents, tools, and graphs.
    5. **[CC-TASK 6.5] Performance Profiling & Optimization (Basic):**
        - Run benchmarks for key operations (e.g., full graph execution for "create new workflow" with mocked LLM latencies, RAG query speed).
        - Identify and address any obvious performance bottlenecks in the library code (not LLM latency itself).
    6. **[CC-TASK 6.6] Refine Error Messaging:** Ensure user-facing error messages (from default error handler or graph end nodes) are clear and helpful.
    7. **[CC-TASK 6.7] Build & Packaging:** Set up build process (tsc and/or bundler like Rollup/esbuild) to produce distributable library files (e.g., CommonJS, ES Modules). Define package.json correctly.
    8. **[CC-TASK 6.8] Create Example Usage / CLI Test Harness:** Develop a simple Node.js CLI script that imports the library, initializes services, and allows invoking OrchestratorService.processUserRequest to test end-to-end flows manually.
- **Milestone:** V1 "Brain" Library is complete, well-tested, documented, and packaged. It fulfills all "In Scope" items from PRD Section 1.3 for the library itself.