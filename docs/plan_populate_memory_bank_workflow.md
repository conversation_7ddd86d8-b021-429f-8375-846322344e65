# Plan: Populate 07_AI_COLLABORATION_WORKFLOW Memory Bank Section

**Objective:** Populate the `07_AI_COLLABORATION_WORKFLOW` section of the n8n AI Assistant Project Memory Bank (V4.0 "The Rosetta Stone" structure).

**Target Files & Content Generation:**

1.  **`.memory-bank/07_AI_COLLABORATION_WORKFLOW/01_WorkflowOverview.md`**:
    *   **Content:** Extract the "AI-to-AI Development Workflow: Simplified Architecture & Process Flow" section from the user's initial "CRITICAL MISSION" prompt (the part starting with `<proposed_new_workflow>`). This includes the Mermaid diagram and the "Complete Workflow Summary for AI Studio" which details the Architect's role, the development cycle phases, and success indicators. Ensure the Mermaid diagram is correctly formatted in a code block.
2.  **`.memory-bank/07_AI_COLLABORATION_WORKFLOW/02_ImplementationTaskTemplate.md`**:
    *   **Content:** Extract the "Implementation Document Template" from the user's initial "CRITICAL MISSION" prompt (the markdown structure provided for AI Studio to give to <PERSON>).
3.  **`.memory-bank/07_AI_COLLABORATION_WORKFLOW/03_ReviewResponseTemplate.md`**:
    *   **Content:** Extract the "Review Response Template" from the user's initial "CRITICAL MISSION" prompt (the markdown structure for AI Studio to respond to Claude Code).
4.  **`.memory-bank/07_AI_COLLABORATION_WORKFLOW/04_MemoryBankMaintenance.md`**:
    *   **Content:** Generate protocols for updating and maintaining this Memory Bank. Describe:
        *   The Memory Bank's role as the single source of truth.
        *   The collaborative update process: Claude Code proposes updates during implementation; AI Studio/Architect reviews and validates these updates.
        *   The importance of keeping all sections (especially ADRs, component docs, and project state) current.
        *   How to handle conflicting information (prioritize latest decisions, document evolution in `10_ARCHIVE_AND_HISTORY/DecisionEvolution.md`).
        *   Frequency of updates (e.g., after each significant task, decision, or architectural change).
        *   Responsibilities for ensuring accuracy and completeness.

**Source Information:**
*   The user's initial "CRITICAL MISSION" prompt, specifically the section under `<proposed_new_workflow>`.
*   General best practices for documenting workflows and maintenance protocols.
*   The overall Memory Bank structure and rationale ([`docs/memorybank_structure_and_rationale.md`](docs/memorybank_structure_and_rationale.md)).

**Plan Steps:**

1.  **Create/Populate `.memory-bank/07_AI_COLLABORATION_WORKFLOW/01_WorkflowOverview.md`**:
    *   Extract the "AI-to-AI Development Workflow: Simplified Architecture & Process Flow" section, including the Mermaid diagram and the "Complete Workflow Summary for AI Studio", from the provided content.
    *   Write this extracted content to the file.

2.  **Create/Populate `.memory-bank/07_AI_COLLABORATION_WORKFLOW/02_ImplementationTaskTemplate.md`**:
    *   Extract the "Implementation Document Template" from the provided content.
    *   Write this extracted content to the file.

3.  **Create/Populate `.memory-bank/07_AI_COLLABORATION_WORKFLOW/03_ReviewResponseTemplate.md`**:
    *   Extract the "Review Response Template" from the provided content.
    *   Write this extracted content to the file.

4.  **Create/Populate `.memory-bank/07_AI_COLLABORATION_WORKFLOW/04_MemoryBankMaintenance.md`**:
    *   Generate content detailing the protocols for updating and maintaining the Memory Bank, covering:
        *   The Memory Bank's role as the single source of truth.
        *   The collaborative update process (Claude Code proposes, AI Studio/Architect reviews and validates).
        *   The importance of keeping all sections current.
        *   How to handle conflicting information.
        *   Frequency of updates.
        *   Responsibilities for accuracy and completeness.
    *   Write this generated content to the file.