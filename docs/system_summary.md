**System Summary: n8n Agentic AI "Brain" Library (V1.0 - Target Architecture V3.3.1)**

**1. Core Mission & Product Vision**

- **Mission:** To create a highly intelligent, multi-platform TypeScript library – the "AI Brain" – for an n8n assistant. It will understand natural language requests to design, generate, validate, explain, and help optimize n8n workflows, embedding best practices.
- **Target Users:** All n8n users (beginner to advanced) and developers integrating the library.
- **Value:** Accelerate workflow development, lower n8n learning curve, improve workflow quality, provide a flexible AI foundation.
- **V1 Scope Highlights:**
    - Graphs for "Create New Workflow," "Update Existing Workflow," and "Analyze Workflow."
    - Advanced RAG (n8n docs, GitHub examples via custom MCP) & Context7 integration.
    - Multi-platform (Node.js library for CLI/testing, browser for direct self-contained extension use).
    - Tools for n8n interaction will be conceptual/mocked in V1 library; actual DOM/instance interaction is by the consuming environment (e.g., extension).

**2. Overarching Architectural Principles**

- **Maximum Reasonable Decoupling:** Core philosophy. Components have single, clear responsibilities.
- **Clarity & Explicitness:** System structure, data flow, and control flow are designed to be explicit.
- **TypeScript First:** Leveraging strong typing for robustness and maintainability.
- **Layered Architecture:** Clear separation: Application Logic (app/, services/, agents/) → Engine (engine/) → Persistence (persistence/) → Configs (config/).
- **Developer Experience (DX):** Fluent APIs (GraphBuilder), clear naming, well-documented modules.

**3. Key Architectural Components & Their Roles**

- **A. Orchestration Engine (src/engine/graph/)**
    - **GraphBuilder.ts**: Fluent API for programmatically defining GraphDefinition objects. Encapsulates loop creation, conditional edge definitions (.onSuccess, .onBranch, .otherwise), input/output mapping helpers (builder.nodeOutput()), and graph validation via compile().
    - **GraphDefinition (Type)**: Declarative, JSON-serializable (if functions are registered by key) structure representing an AI workflow. Contains nodes, edges, entry points, initial input expectations, constants, and default error handler ID.
    - **GraphExecutor.ts**: Generic runtime engine that interprets and executes GraphDefinitions. Manages GraphExecutionContext, resolves node inputs (with fallbackSource logic), calls agents/tools/functions, stores versioned outputs, handles HIL pauses (based on agent signals), and manages error propagation (explicit onFailure edges or graph's default error handler). Enforces MAX_TOTAL_GRAPH_STEPS.
    - **GraphExecutionContext (Type)**: Runtime state for a single graph execution instance (status, node outputs, interrupt details, services).
    - **FunctionRegistry (Type, output by GraphBuilder.compile()):** Maps keys (stored in GraphDefinition) to actual JavaScript functions for conditional branches and data transforms.
- **B. LLM Interaction Subsystem (src/engine/llm/)**
    - **LLMService.ts**: Sole gateway to Vercel AI SDK Core v5. Manages API keys (from UserSettingsService), dynamic model selection per LLMTaskCategory (with Chrome AI preference), token counting, cost estimation, API call retries, and standardized response wrapping (LLMInvocationResult). Supports streaming of text and custom UI data parts (UIMessage with metadata).
- **C. Prompt Engineering Subsystem (src/engine/prompts/)**
    - **parts/*.parts.ts**: Static, reusable prompt snippets/templates (e.g., base system instructions, team awareness block templates).
    - **formatters/*.ts**: Micro-utilities, each formats a specific PromptComponent type (e.g., JsonSchemaInstructionFormatter) into an intermediate { role, contentString } structure.
    - **normalizers/MessageNormalizer.ts & ToolContentNormalizer.ts**: Ensure ModelMessages (especially from history or for tool interactions) conform to Vercel AI SDK v5 structure (roles, content parts).
    - **mergers/MessageMerger.ts**: Handles merging adjacent, compatible ModelMessages.
    - **services/GenericPromptBuilder.ts**: Lean orchestrator using Formatters, Normalizers, and Mergers to assemble a basic ModelMessage[] list from abstract PromptComponents.
    - **services/RuleSelectorService.ts**: Selects relevant N8nRules (base & custom from CustomRulesRepository) for prompt injection using keyword matching (V1) and rule types (ALWAYS_APPLY, CONTEXTUAL_SUGGESTION).
    - **services/ConversationHistoryService.ts**: Manages retrieval of conversation history (TaskSession.llmContextSummary from TaskSessionRepository, recent messages from ChatRepository). **Crucially, it orchestrates proactive context summarization if needed:** estimates token size, if too large, uses GenericPromptBuilder & LLMService to invoke n8n-summarizer-agent (a conceptual role), updates TaskSession summary, and then provides the managed history block.
    - **services/AgentPromptBuilder.ts**: Primary service used by BaseAgent. It orchestrates GenericPromptBuilder, ConversationHistoryService, and RuleSelectorService to assemble the complete, context-rich ModelMessage[] for a specific agent's LLM call, including injecting the agent's CORE_SYSTEM_INSTRUCTION, team awareness, input definitions, rules, history, specific task data, and output schema instructions.
- **D. Agent Infrastructure (src/engine/agents/)**
    - **base/BaseAgent.ts**: Abstract class for all AI agents. Defines static agentSlug, static CORE_SYSTEM_INSTRUCTION, static defaultLlmTaskCategory. Constructor takes AgentDependencies. Provides protected prepareLlmMessages(...) (uses AgentPromptBuilder), protected invokeLlm(...) (uses LLMService), and response helpers (success, failure, requestUserClarification, requestGraphTools). Enforces execute() and getFinalOutputSchema().
    - **agentRegistry.ts**: AGENT_REGISTRY_CONFIG array and agentRegistryMap map agentSlug strings to AgentRegistryEntry (containing constructor, static metadata like core instruction and default category, and final output Zod schema). Used by GraphBuilder (for defaults when using class constructor in addAgentNode) and GraphExecutor (to instantiate agents).
- **E. Concrete AI Agents (src/agents/{agentName}/) - V1 Set**
    - Each extends BaseAgent, has a co-located *.prompts.ts for its CORE_SYSTEM_INSTRUCTION.
    - N8nIntentRouterAgent, N8nRequirementsGatheringAgent, N8nArchitectAgent (with "Focused Design Spike" for complex steps), N8nNodeSelectorAgent, N8nNodeComposerAgent (uses NodeSpecService for live schemas), N8nWorkflowDocumenterAgent, N8nConnectionComposerAgent, N8nBestPracticeValidatorAgent, N8nReflectionAgent.
    - n8n-summarizer-agent is a conceptual role used by ConversationHistoryService.
- **F. Data Persistence Layer (src/persistence/)**
    - **rxdbSetup.ts**: Initializes RxDB (Dexie for browser, LokiJS/FS for Node.js V1), defines collections using JSON schemas derived from Zod (user_settings, task_sessions, chat_threads, conversation_messages, custom_rules, knowledge_chunks).
    - **Repositories (repositories/*.ts):** One per collection (e.g., UserSettingsRepository, TaskSessionRepository, CustomRulesRepository), abstracting RxDB calls, using Zod for validation on write, and returning plain data objects.
- **G. Application Services (src/services/)**
    - **UserSettingsService.ts**: Manages user settings (API keys, model prefs) using UserSettingsRepository, provides fallbacks.
    - **NodeSpecService.ts**: Loads/caches all n8n node specifications (from /types/nodes.json snapshot in Node.js, or pushed from live instance in browser). Provides getNodeSpec(), getAllNodeDescriptors().
    - **TaskSessionManager.ts**: Manages TaskSession lifecycle using TaskSessionRepository. Updates status, persists GraphExecutionContextSnapshot. Critically, its prepareSessionForAgentCall() method is called by BaseAgent *before* AgentPromptBuilder to allow ConversationHistoryService (which TSM might coordinate with or be called by) to trigger summarization transparently if the context (including new agent data + history) would overflow for the target model. *Self-correction: The refined flow is BaseAgent calls AgentPromptBuilder, which calls ConversationHistoryService, which handles history and summarization (calling LLMService & GenericPromptBuilder for summarizer prompt) and updates TaskSession (via TaskSessionRepository if CHS has direct access or via TSM). TaskSessionManager itself is then more focused on direct CRUD and status updates for TaskSession initiated by OrchestratorService.* Let's stick to: **TaskSessionManager is responsible for TaskSession CRUD, status updates, and snapshot persistence. ConversationHistoryService handles history fetching and summarization logic, updating TaskSession via its repository or the manager.**
    - KnowledgeService.ts & EmbeddingService.ts (in src/knowledge/): For V1 Advanced RAG. EmbeddingService uses transformers.js. KnowledgeRepository (in persistence) stores chunks/embeddings and performs vector search. KnowledgeService provides higher-level RAG API.
- **H. Tool Infrastructure (src/tools/)**
    - ToolDefinition (interface), toolRegistry.ts, ToolExecutorService.ts.
    - V1 tools focus on RAG (search_documentation, search_workflow_templates_tool, query_context7_n8n_docs_tool, query_github_workflows_mcp_tool) and conceptual/mocked n8n interactions (fetch_current_workflow_json, apply_changes_to_canvas, etc. for library V1).
    - internal_await_user_input_tool mechanism for HIL.
- **I. Configuration (src/config/)**
    - baseN8nRules.config.ts: Bundled expert n8n rules.
    - llmModels.config.ts: LLM capabilities (context windows) and pricing.
    - nodes.snapshot.json: For NodeSpecService in Node.js.
    - userSettings.defaults.ts: Default user preferences.
- **J. Graphs (src/graphs/)**
    - V1: createNewWorkflow.graph.ts, updateExistingWorkflow.graph.ts, analyzeWorkflow.graph.ts, defined using GraphBuilder.
- **K. Application Orchestration (src/app/)**
    - OrchestratorService.ts: Top-level service. Handles user requests, uses N8nIntentRouterAgent (via direct LLMService or AgentRegistry call, NOT as a graph node for initial routing), manages TaskSession lifecycle via TaskSessionManager, starts graph executions via GraphExecutor, and handles the "Apply & Review" sequence post-graph.

**4. Key Cross-Cutting Concerns & Patterns**

- **State Management:** TaskSession (managed by TaskSessionManager, persisted by TaskSessionRepository) is central for operational state. GraphExecutionContext for in-flight graph state.
- **Error Handling:** Standardized agent/tool error responses. Explicit onFailure graph edges. Graph-level default error handler. GraphExecutor has MAX_TOTAL_GRAPH_STEPS.
- **Human-in-the-Loop (HIL):** Agents signal needs_clarification. GraphExecutor pauses, stores interruptPayload. OrchestratorService manages UI interaction and resumes graph with user input.
- **V1 "Apply & Review" UX:** Graph outputs ProposedWorkflowJson. Orchestrator then (conceptually for V1 library) calls tools: checkpoint_workflow, apply_changes_to_canvas (mocked), user confirms/reverts (which calls rollback_workflow_tool).
- **Multi-Platform:** Achieved via RxDB storage adapters, NodeSpecService loading strategies, isNodeEnvironment() checks for platform-specific code, and designing tools for conceptual execution in library V1.