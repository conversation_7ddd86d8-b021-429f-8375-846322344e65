**SINGLE COMPONENT IMPLEMENTATION PLAN: NodeSpecService.ts**

**1. COMPONENT OVERVIEW**

- **Purpose:** The NodeSpecService is responsible for loading, caching, and providing access to the full specifications (schemas, descriptions, properties, defaults, etc.) of all available n8n nodes for the context in which the AI assistant is operating.
- **Scope:**
    - **Data Loading:**
        - In a Node.js environment (for library development, testing, CLI): Load node specifications from a local JSON file (e.g., a snapshot of an n8n instance's /api/v1/node-types or /types/nodes.json endpoint).
        - In a browser environment (Chrome extension): Be capable of receiving full node specifications fetched from the active n8n instance (via a tool like system_fetch_all_node_specs_tool which uses main-world.ts to call the n8n endpoint). This received data will then be cached.
    - **Caching:** Maintain an in-memory cache of the loaded node specifications to provide fast access. The cache should be keyed or invalidated appropriately if the n8n instance/version changes.
    - **Querying/Access:** Provide methods to:
        - Get the full specification for a single node type (by nodeTypeId and optionally version).
        - Get a lightweight list of all available node descriptors (e.g., typeId, displayName, description, category, group) suitable for NodeSelectorAgent.
        - (V1.5/V2) Find nodes based on query strings, categories, or groups (implementing filtering logic).
- **Dependencies (Conceptually):**
    - NodeDescriptionSchema (Zod schema defining the structure of items from /types/nodes.json). This is crucial for type safety when parsing and using the specs.
    - (For browser mode) A mechanism/tool (e.g., system_fetch_all_node_specs_tool) to trigger the fetching of node specs from the live n8n instance. NodeSpecService itself won't do the direct fetching from the n8n page but will receive the data.
    - (For Node.js mode) Filesystem access (fs.readFile) or a way to import a local JSON file.
- **Dependents:**
    - NodeSelectorAgent: Uses getAllNodeDescriptors() and potentially findNodes() to choose relevant node types.
    - NodeComposerAgent: Uses getNodeSpec() to get the detailed schema for the specific node type it is composing.
    - Other agents (e.g., ArchitectAgent, ValidatorAgent) might use it for high-level information about nodes.
    - GraphExecutionContext.services: An instance of NodeSpecService will be available here.
- **Key Design Decisions:**
    - Centralized service for all n8n node specification data.
    - Support for both Node.js (file-based) and browser (data pushed from live instance) environments.
    - In-memory caching for performance.
    - Clear API for accessing node specs.
- **Evolution Notes:**
    - Initially, we thought agents might get schemas via individual get_node_template_tool calls.
    - We refined this to loading all node specs once into a central service (NodeSpecService) for efficiency and to enable broader querying (like for NodeSelectorAgent). The get_node_template_tool's role shifted to being the mechanism that *primes* this service in the browser.

**2. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant is being built as a TypeScript library (for Node.js and browser) that powers an AI to help users with n8n workflows. It uses a graph-based engine (GraphExecutor) to run AI workflows defined by GraphDefinitions. These graphs consist of AI Agents and Tools. Agents like NodeSelectorAgent (chooses n8n nodes) and NodeComposerAgent (generates n8n node JSON) need detailed information about available n8n nodes, their parameters, types, and default values.
    
- **This Component's Role (NodeSpecService):**
    
    The NodeSpecService is the definitive source of truth within the AI "brain" for all n8n node specifications. It abstracts how these specifications are obtained (either from a local file in Node.js or from a live n8n instance in the browser) and provides a consistent API for other services and agents to query this data. It caches the specifications in memory for fast access, ensuring that agents don't need to repeatedly fetch this information.
    
- **Related Components:**
    - **NodeSelectorAgent & NodeComposerAgent:** Primary consumers.
    - **system_fetch_all_node_specs_tool (Conceptual Tool):** In browser mode, this tool is responsible for fetching the raw node specifications data from the n8n instance's API endpoint (e.g., /types/nodes.json) and passing it to NodeSpecService for loading and caching.
    - **n8nNodeDirectorySnapshot.json (Conceptual File):** A local JSON file containing a snapshot of /types/nodes.json, used by NodeSpecService when running in Node.js or for initial bootstrapping/testing.
    - **NodeDescriptionSchema (Zod Schema):** Defines the expected structure of each node specification object within the /types/nodes.json array. NodeSpecService will use this for validation if parsing raw JSON.
- **Design Philosophy:**
    - **Centralized Knowledge:** Provide a single, reliable source for n8n node specifications.
    - **Efficiency:** Cache data in memory to avoid repeated fetching or file reads.
    - **Abstraction:** Hide the details of how node specs are loaded based on the environment.
    - **Accuracy:** Aim to provide specifications that match the user's current n8n instance version as closely as possible (in browser mode).
- **Technical Constraints:**
    - Must be multi-platform (Node.js, browser).
    - Data parsing and caching should be performant.
    - Memory usage for the cache should be reasonable (though /types/nodes.json can be large, it's usually a one-time load per session/version).

**3. EXTRACTED DESIGN DECISIONS**

1. **Decision:** Centralize n8n node specification access in a dedicated service.
    - **Rationale:** Avoids each agent needing to fetch/manage this data independently. Ensures consistency.
    - **Final Form:** NodeSpecService class.
2. **Decision:** Support two primary data loading mechanisms: local file (Node.js) and data pushed from a live n8n instance (browser).
    - **Rationale:** Enables multi-platform operation and use of live data when available.
    - **Final Form:** NodeSpecService will have methods like loadSpecsFromFile(filePath) and loadSpecsFromJson(jsonData: NodeDescription[]) or loadSpecsFromJsonString(jsonString: string).
3. **Decision:** Cache loaded specifications in memory.
    - **Rationale:** Performance. Avoids re-parsing large JSON or re-reading files on every query.
    - **Final Form:** NodeSpecService will use internal private members (e.g., a Map<string, NodeDescription>) for caching.
4. **Decision:** Provide specific query methods for different agent needs.
    - **Rationale:** NodeSelector needs a lightweight list, NodeComposer needs full detail for one node.
    - **Final Form:** Methods like getNodeSpec(typeId, version?), getAllNodeDescriptors().
5. **Decision:** NodeComposerAgent will call NodeSpecService.getNodeSpec() internally to get its schema, rather than a graph tool fetching it for each composition.
    - **Rationale:** Simplifies graph definition, empowers composer agent, leverages cached data in NodeSpecService.
    - **Final Form:** This is a key interaction pattern.

**4. TECHNICAL SPECIFICATIONS**

- **TypeScript Interfaces/Types (src/background/services/nodeSpecService.types.ts or co-located):**
    
    `// From src/common/zodSchemas.ts (or graph.types.ts if already there)
    // This Zod schema MUST accurately reflect the structure of objects in n8n's /types/nodes.json
    // It will be complex, covering name, displayName, group, version, defaults, properties (which is an array of parameter defs), etc.
    export const NodePropertyOptionSchema = z.object({ // Example for options within a property
        name: z.string(),
        value: z.union([z.string(), z.number(), z.boolean()]),
        description: z.string().optional(),
        // ... other option fields
    });
    
    export const NodeParameterSchema = z.object({
        displayName: z.string(),
        name: z.string(),
        type: z.string(), // e.g., 'string', 'number', 'boolean', 'options', 'collection', 'fixedCollection', 'multiOptions', 'notice', 'json'
        default: z.any(),
        description: z.string().optional(),
        placeholder: z.string().optional(),
        required: z.boolean().optional(),
        typeOptions: z.any().optional(), // e.g., { multipleValues: true, MoodleType: 'text' }
        options: z.array(NodePropertyOptionSchema).optional(),
        // ... many other fields like noDataExpression, displayOptions.show.resource etc.
    });
    
    export const NodeDescriptionSchema = z.object({
        name: z.string(), // Internal type ID, e.g., "n8n-nodes-base.if"
        displayName: z.string(),
        icon: z.string().optional(),
        group: z.array(z.string()), // e.g., ["LOGIC", "CORE"]
        version: z.number().or(z.array(z.number())), // Can be single or array for multi-version nodes
        description: z.string().optional(),
        defaults: z.record(z.any()).optional(), // Default values for parameters
        inputs: z.array(z.string()).optional(), // e.g., ["main"] for nodes with multiple input anchors
        outputs: z.array(z.string()).optional(), // e.g., ["main"], or for IF node ["main", "main"] (true, false)
        properties: z.array(NodeParameterSchema), // The list of node parameters
        subtitle: z.string().optional(),
        // ... other fields like documentationUrl, credentials, progress, etc.
    });
    export type NodeDescription = z.infer<typeof NodeDescriptionSchema>;
    
    // Lightweight descriptor for NodeSelectorAgent
    export interface NodeSelectorDescriptor {
        typeId: string; // Corresponds to NodeDescription.name
        displayName: string;
        description?: string;
        categories?: string[]; // Derived from NodeDescription.group or other fields
        primaryGroup?: string;
        icon?: string;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **External Dependencies:**
    - Node.js: fs/promises (for readFile).
    - Zod (for NodeDescriptionSchema and runtime validation of loaded JSON).
- **Internal Dependencies:**
    - NodeDescriptionSchema, NodeSelectorDescriptor types.
- **Multi-Platform Requirements:**
    - **Node.js:** Load from file specified at construction or via a method.
    - **Browser:** Load from a JSON string or object provided to it (e.g., by OrchestratorService after a tool fetches it). No direct DOM/fetch access from NodeSpecService itself.
- **Configuration Schema:** None directly for the service, but it expects the input JSON (file or string) to conform to z.array(NodeDescriptionSchema).

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (NodeSpecService.ts)**

- **File Structure:**
    - src/background/services/nodeSpecService.ts
    - (Types in nodeSpecService.types.ts or src/common/zodSchemas.ts)
- **Import Strategy:**
    
    `import { promises as fs } from 'fs'; // For Node.js file loading
    import { z } from 'zod';
    import { NodeDescriptionSchema, NodeDescription, NodeSelectorDescriptor } from './types/nodeSpecService.types'; // Or from common types
    import { isNodeEnvironment } from '@/common/utils/environment'; // Assumed utility`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **NodeSpecService Class Definition:**
    
    `export class NodeSpecService {
      private nodeSpecsMap: Map<string, NodeDescription> = new Map(); // Key: typeId (name)
      private nodeDescriptors: NodeSelectorDescriptor[] = [];
      private isInitialized: boolean = false;
      private currentDataSourceInfo: string = "No data loaded"; // e.g., file path or "live instance data"
    
      constructor() {
        // Optionally, can take a default file path for Node.js environment
        // or be initialized lazily.
      }
    
      // --- Public Initialization Methods ---
      public async loadSpecsFromFile(filePath: string): Promise<void> { /* ... */ }
      public async loadSpecsFromJsonString(jsonString: string, sourceName?: string): Promise<void> { /* ... */ }
      public loadSpecsFromArray(specsArray: NodeDescription[], sourceName?: string): void { /* ... */ }
    
      // --- Private Data Processing ---
      private processAndCacheSpecs(specs: NodeDescription[], sourceName: string): void { /* ... */ }
      private createSelectorDescriptors(specs: NodeDescription[]): NodeSelectorDescriptor[] { /* ... */ }
    
      // --- Public Accessor Methods ---
      public async getNodeSpec(typeId: string, version?: number): Promise<NodeDescription | null> { /* ... */ }
      public async getAllNodeDescriptors(): Promise<NodeSelectorDescriptor[]> { /* ... */ }
      public async findNodes(query: string, options?: { limit?: number }): Promise<NodeSelectorDescriptor[]> { /* ... V1.5/V2 fuzzy search ... */ }
      public getDataSourceInfo(): string { return this.currentDataSourceInfo; }
      public getIsInitialized(): boolean { return this.isInitialized; }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **Core Logic Flow for Initialization Methods:**
    - **loadSpecsFromFile(filePath: string):**
        1. If !isNodeEnvironment(), throw error or log warning (this method is Node.js specific).
        2. jsonString = await fs.readFile(filePath, 'utf-8').
        3. Call this.loadSpecsFromJsonString(jsonString, \File: ${filePath}`)`.
    - **loadSpecsFromJsonString(jsonString: string, sourceName: string = "JSON String"):**
        1. rawData = JSON.parse(jsonString). Catch parsing errors.
        2. validationResult = z.array(NodeDescriptionSchema).safeParse(rawData).
        3. If !validationResult.success, throw validation error detailing issues (validationResult.error.format()).
        4. Call this.processAndCacheSpecs(validationResult.data, sourceName).
    - **loadSpecsFromArray(specsArray: NodeDescription[], sourceName: string = "Direct Array"):**
        1. (Optional but recommended) rawData = JSON.parse(JSON.stringify(specsArray)) to ensure deep copy and allow Zod validation on a plain object structure if specsArray elements are class instances.
        2. validationResult = z.array(NodeDescriptionSchema).safeParse(rawData).
        3. If !validationResult.success, throw validation error.
        4. Call this.processAndCacheSpecs(validationResult.data, sourceName).
- **processAndCacheSpecs(specs: NodeDescription[], sourceName: string) (Private):**
    1. Clear existing cache: this.nodeSpecsMap.clear(), this.nodeDescriptors = [].
    2. Iterate specs:
        - For each spec: NodeDescription:
            - this.nodeSpecsMap.set(spec.name, spec); (Handles multi-version nodes by simply overwriting with the last one encountered for a given name, or if versioning is critical, nodeSpecsMap needs to be Map<string, NodeDescription[]>).
            - **For V1, assume /types/nodes.json provides the "latest" or default version for each unique spec.name.** If n8n API provides multiple versions for the same name in this flat list, the last one wins. Advanced version handling for getNodeSpec can be V2.
    3. this.nodeDescriptors = this.createSelectorDescriptors(specs).
    4. this.isInitialized = true.
    5. this.currentDataSourceInfo = sourceName.
    6. Log success: "NodeSpecService initialized with X specs from Y."
- **createSelectorDescriptors(specs: NodeDescription[]) (Private):**
    1. Initialize descriptors: NodeSelectorDescriptor[] = [].
    2. Iterate specs:
        - For each spec: NodeDescription:
            - descriptor: NodeSelectorDescriptor = { typeId: spec.name, displayName: spec.displayName, description: spec.description, categories: spec.group, primaryGroup: spec.group?.[0], icon: spec.icon }.
            - Add to descriptors.
    3. Return descriptors.
- **Core Logic Flow for Accessor Methods:**
    - **getNodeSpec(typeId: string, version?: number):**
        1. If !this.isInitialized, throw error "Service not initialized." or return null.
        2. If version is provided: (V2 feature) search for the specific version. For V1, ignore version and return latest.
        3. Return this.nodeSpecsMap.get(typeId) || null.
    - **getAllNodeDescriptors():**
        1. If !this.isInitialized, return [] or throw.
        2. Return [...this.nodeDescriptors] (a copy).
    - **findNodes(query: string, options?) (V1.5/V2 - Basic V1 can be simple filter):**
        1. If !this.isInitialized, return [].
        2. lowerQuery = query.toLowerCase().
        3. Filter this.nodeDescriptors:
            - desc.displayName.toLowerCase().includes(lowerQuery) OR
            - desc.typeId.toLowerCase().includes(lowerQuery) OR
            - desc.description?.toLowerCase().includes(lowerQuery) OR
            - desc.categories?.some(cat => cat.toLowerCase().includes(lowerQuery)).
        4. Apply options.limit if provided.
        5. Return filtered list.
- **Error Handling:**
    - File read errors in loadSpecsFromFile.
    - JSON parsing errors in loadSpecsFromJsonString.
    - Zod validation errors if loaded data doesn't match NodeDescriptionSchema.
    - Errors if trying to access specs before initialization.
- **State Management:** Internal state consists of nodeSpecsMap, nodeDescriptors, isInitialized, currentDataSourceInfo. These are set by loading methods.
- **Async Patterns:** loadSpecsFromFile is async. loadSpecsFromJsonString and loadSpecsFromArray can be sync if JSON parsing/Zod validation is sync, but making them async allows for future async validation. Accessor methods can be async if they might trigger lazy loading in future, but for V1 with in-memory cache, they can be sync after init (return Promise.resolve(...)). It's generally safer to make them async from the start if there's any chance of I/O later.
- **Platform Differences:** loadSpecsFromFile is Node.js specific. In browser, data is pushed via loadSpecsFromJsonString or loadSpecsFromArray.

**6. INTEGRATION SPECIFICATIONS**

- **Input Contracts:**
    - loadSpecsFromFile: filePath: string.
    - loadSpecsFromJsonString: jsonString: string, sourceName?: string.
    - loadSpecsFromArray: specsArray: NodeDescription[], sourceName?: string.
    - getNodeSpec: typeId: string, version?: number.
- **Output Contracts:**
    - getNodeSpec: Promise<NodeDescription | null>.
    - getAllNodeDescriptors: Promise<NodeSelectorDescriptor[]>.
    - findNodes: Promise<NodeSelectorDescriptor[]>.
- **Service Injection:** An instance of NodeSpecService will be created by the main application setup (e.g., in background/index.ts) and injected into AgentDependencies, which are then available to agents via this.deps.nodeSpecService or graphContext.services.nodeSpecService.
- **Initialization:** The OrchestratorService (or main app setup) is responsible for calling one of the loadSpecs... methods at startup or when new spec data is available.
    - Node.js: Call loadSpecsFromFile with path to n8nNodeDirectorySnapshot.json.
    - Browser: When system_fetch_all_node_specs_tool gets data from n8n instance, OrchestratorService calls nodeSpecService.loadSpecsFromJsonString(fetchedData).

**7. QUALITY ASSURANCE REQUIREMENTS**

- **Unit Testing Strategy:**
    - Mock fs/promises for loadSpecsFromFile.
    - Provide valid and invalid sample nodes.json data (strings and arrays of objects).
    - **Test loadSpecsFromFile:**
        - Successful load and cache population.
        - File not found error.
        - Invalid JSON in file error.
    - **Test loadSpecsFromJsonString:**
        - Successful load with valid JSON.
        - Invalid JSON string error.
        - Data not matching z.array(NodeDescriptionSchema) error (test with Zod safeParse).
    - **Test loadSpecsFromArray:** Similar to JSON string, test with valid/invalid arrays.
    - **Test processAndCacheSpecs (indirectly):** Verify nodeSpecsMap and nodeDescriptors are correctly populated after a successful load. Check isInitialized and currentDataSourceInfo.
    - **Test getNodeSpec:**
        - Existing type ID.
        - Non-existent type ID.
        - Called before initialization.
        - (V2) Version handling.
    - **Test getAllNodeDescriptors:**
        - Returns all expected descriptors.
        - Correct mapping from NodeDescription to NodeSelectorDescriptor.
        - Called before initialization.
    - **Test findNodes (V1 simple filter):**
        - Queries matching display name, type ID, description, category. Case-insensitivity.
        - No results found.
- **Edge Case Handling:** Empty nodes.json data, specs with missing optional fields, very large nodes.json.
- **Performance Criteria (Conceptual):** getNodeSpec and getAllNodeDescriptors should be very fast (O(1) or O(N) for descriptors list copy) after initialization due to in-memory cache. Loading/parsing large JSON might take time but is a one-off.
- **Validation Rules:** Primary validation is against NodeDescriptionSchema during loading.
- **Logging Strategy:** Log successful initialization, source of data, number of specs loaded. Log errors during loading/parsing/validation.

**8. VALIDATION CRITERIA**

- Service initializes correctly in both Node.js (from file) and browser (from pushed data) modes.
- Node specifications are correctly parsed, validated against NodeDescriptionSchema, and cached.
- getNodeSpec returns the correct full NodeDescription or null.
- getAllNodeDescriptors returns an array of correctly formatted NodeSelectorDescriptors.
- (V1.5/V2) findNodes accurately filters descriptors based on query.
- Service handles malformed input data or file errors gracefully.
- Accessor methods are performant after initialization.