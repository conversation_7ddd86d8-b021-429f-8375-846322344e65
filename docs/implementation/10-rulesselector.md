**COMBINED IMPLEMENTATION PLAN: CustomRulesRepository.ts & RuleSelectorService.ts (V1)**

**Part 1: CustomRulesRepository.ts**

**1. COMPONENT OVERVIEW (CustomRulesRepository.ts)**

- **Purpose:** To provide a data access layer for interacting with the custom_rules RxDB collection. It encapsulates all direct RxDB CRUD operations for user-defined N8nRules.
- **Scope:**
    - Creating, reading, updating, and deleting custom rules.
    - Querying custom rules (e.g., by ID, by type, by active status, by agent applicability).
    - Handles direct interaction with the custom_rules RxDB collection.
- **Dependencies:**
    - N8nAidDatabase instance (from getDb() in rxdbSetup.ts).
    - CustomRuleDocType, CustomRuleDocument, CustomRuleCollection (from src/persistence/types/database.types.ts).
    - N8nRule, N8nRuleSchema (Zod schema from src/types/rules.types.ts) for creating/validating rule data.
- **Dependents:**
    - RuleSelectorService: Will use this repository to fetch user-defined custom rules.
    - UI components (in the future Chrome extension) for managing custom rules.
- **Key Design Decisions:**
    - Repository pattern for abstracting database operations.
    - Standard CRUD methods.
    - Methods for common querying patterns (e.g., get all active rules, get rules for specific agents).
- **Evolution Notes:** This repository allows users to extend and customize the AI's behavior by adding their own operational guidelines or best practices.

**2. COMPLETE CONTEXT PACKAGE (CustomRulesRepository.ts)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant uses RxDB for persistent local storage. The custom_rules collection is designed to store N8nRule objects that users define to guide the AI's behavior or enforce specific workflow design patterns.
    
- **This Component's Role (CustomRulesRepository):**
    
    The CustomRulesRepository is the dedicated data access layer for the custom_rules collection. It provides a clean API for creating new rules, retrieving existing ones (individually or in bulk, with filtering), updating them, and deleting them. It ensures all interactions with the custom_rules data in RxDB are done correctly and consistently. It will be primarily used by the RuleSelectorService to load user rules and by future UI components that allow users to manage these rules.
    
- **Related Components:**
    - **rxdbSetup.ts / getDb():** Provides the initialized N8nAidDatabase instance.
    - **CustomRuleDocType (from database.types.ts):** Defines the structure of the data stored.
    - **N8nRuleSchema (Zod schema):** Used for validating data before insertion/update.
    - **RuleSelectorService:** Consumes this repository.
- **Design Philosophy:**
    - **Data Access Abstraction:** Isolate database interaction logic for custom rules.
    - **Standard Repository Pattern:** Provide clear CRUD and query methods.
- **Technical Constraints:** Must use RxDB APIs. Operations should be asynchronous.

**3. EXTRACTED DESIGN DECISIONS (CustomRulesRepository.ts)**

1. **Decision:** Implement as a class-based repository.
    - **Rationale:** Standard OOP, dependency injection of DB instance.
    - **Final Form:** export class CustomRulesRepository { ... }.
2. **Decision:** Use id (likely UUID) as the primary key for N8nRule objects.
    - **Rationale:** Standard practice for identifying individual rule documents.
    - **Final Form:** N8nRuleSchema includes id: z.string().
3. **Decision:** Repository methods will take and return plain N8nRule data objects (Zod-derived types), not raw RxDB documents.
    - **Rationale:** Decouples consumers from RxDB-specific document types. Repository handles toJSON() and parse() internally.
    - **Final Form:** Methods like getRuleById(id: string): Promise<N8nRule | null>.
4. **Decision:** Use Zod validation (N8nRuleSchema.parse()) before inserting or updating rules.
    - **Rationale:** Ensure data integrity before writing to the database, complementing RxDB's JSON schema validation.
    - **Final Form:** Data passed to addRule or updateRule will be validated.

**4. TECHNICAL SPECIFICATIONS (CustomRulesRepository.ts)**

- **TypeScript Interfaces/Types:**
    - Uses N8nAidDatabase, CustomRuleCollection, CustomRuleDocument, CustomRuleDocType from src/persistence/types/database.types.ts.
    - Uses N8nRule, N8nRuleSchema from src/types/rules.types.ts.
- **CustomRulesRepository Class:**
    
    `// src/persistence/repositories/CustomRulesRepository.ts
    import { N8nAidDatabase, CustomRuleCollection, CustomRuleDocument, CustomRuleDocType } from '../types/database.types';
    import { N8nRule, N8nRuleSchema } from '@/types/rules.types.ts'; // Adjusted path
    import { getDb } from '../rxdbSetup';
    import { generateUUID } from '@/utils/uuid'; // For new rule IDs
    
    export interface RuleQueryOptions {
      isActive?: boolean;
      type?: N8nRule['type'];
      appliesToAgent?: string; // Find rules applicable to a specific agent
      // Add other filter options as needed
    }
    
    export class CustomRulesRepository {
      private dbPromise: Promise<N8nAidDatabase>;
    
      constructor(dbPromise?: Promise<N8nAidDatabase>) {
        this.dbPromise = dbPromise || getDb();
      }
    
      private async getCollection(): Promise<CustomRuleCollection> { /* ... */ }
    
      public async addRule(ruleData: Omit<N8nRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<N8nRule> { /* ... */ }
      public async getRuleById(id: string): Promise<N8nRule | null> { /* ... */ }
      public async updateRule(id: string, updates: Partial<Omit<N8nRule, 'id' | 'createdAt'>>): Promise<N8nRule | null> { /* ... */ }
      public async deleteRule(id: string): Promise<boolean> { /* ... */ }
      public async getAllRules(options?: RuleQueryOptions): Promise<N8nRule[]> { /* ... */ }
      public async getActiveRulesForAgent(agentSlug: string): Promise<N8nRule[]> { /* ... */ }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (CustomRulesRepository.ts)**

- **File Structure:** src/persistence/repositories/CustomRulesRepository.ts
- **Constructor:** Takes optional dbPromise, defaults to getDb().
- **getCollection(): Promise<CustomRuleCollection> (Private):** Awaits dbPromise, returns db.custom_rules.
- **addRule(ruleData: Omit<N8nRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<N8nRule>:**
    1. const collection = await this.getCollection();
    2. Create full rule object:
        
        `const newRule: N8nRuleDocType = {
          id: generateUUID(),
          ...ruleData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };`
        
        **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
        
    3. Validate: const validatedRule = N8nRuleSchema.parse(newRule); (Catch Zod errors, re-throw as repository error).
    4. const doc = await collection.insert(validatedRule);
    5. Return doc.toJSON().
- **getRuleById(id: string): Promise<N8nRule | null>:**
    1. const collection = await this.getCollection();
    2. const doc = await collection.findOne(id).exec();
    3. Return doc ? doc.toJSON() : null;
- **updateRule(id: string, updates: Partial<Omit<N8nRule, 'id' | 'createdAt'>>): Promise<N8nRule | null>:**
    1. const collection = await this.getCollection();
    2. const doc = await collection.findOne(id).exec();
    3. If !doc, return null (or throw RuleNotFoundError).
    4. Prepare update object: const patchData = { ...updates, updatedAt: new Date() };
    5. Validate patchData against a partial Zod schema if possible, or rely on RxDB's schema validation. For robustness, it's good to validate what you control:
        
        const currentData = doc.toJSON(); const dataToValidate = { ...currentData, ...patchData }; N8nRuleSchema.parse(dataToValidate); // Validates the would-be state.
        
    6. const updatedDoc = await doc.incrementalPatch(patchData);
    7. Return updatedDoc.toJSON().
- **deleteRule(id: string): Promise<boolean>:**
    1. const collection = await this.getCollection();
    2. const doc = await collection.findOne(id).exec();
    3. If !doc, return false.
    4. await doc.remove();
    5. Return true.
- **getAllRules(options?: RuleQueryOptions): Promise<N8nRule[]>:**
    1. const collection = await this.getCollection();
    2. Build RxDB Mango Query based on options:
        
        `let query = collection.find();
        if (options?.isActive !== undefined) {
          query = query.where('isActive').eq(options.isActive);
        }
        if (options?.type) {
          query = query.where('type').eq(options.type);
        }
        if (options?.appliesToAgent) {
          // 'appliesToAgents' is an array, so use $elemMatch or $in
          query = query.where('appliesToAgents').elemMatch({ $eq: options.appliesToAgent });
        }
        // ... other filters`
        
        **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
        
    3. const docs = await query.exec();
    4. Return docs.map(doc => doc.toJSON());
- **getActiveRulesForAgent(agentSlug: string): Promise<N8nRule[]>:**
    1. Simply call this.getAllRules({ isActive: true, appliesToAgent: agentSlug });

---

**Part 2: RuleSelectorService.ts**

**1. COMPONENT OVERVIEW (RuleSelectorService.ts)**

- **Purpose:** To select and provide a relevant subset of N8nRules (both bundled base rules and user-defined custom rules) to the PromptBuilderService for injection into an agent's prompt.
- **Scope:**
    - Load bundled base n8n rules (e.g., from a JSON file).
    - Fetch active, user-defined custom rules from CustomRulesRepository.
    - Combine base and custom rules.
    - Filter rules based on the current agentSlug.
    - For CONTEXTUAL_SUGGESTION rules, perform a selection mechanism (V1: keyword matching; V2: Chrome AI or semantic search) to pick the most relevant ones based on the current task context.
    - Provide a final list of rule descriptions for the PromptBuilderService.
- **Dependencies:**
    - CustomRulesRepository: To get user-defined rules.
    - N8nRule type.
    - (V1) Bundled baseN8nRules.json (or .ts module exporting the array).
    - (V2) ChromeAIService or KnowledgeRepository (for semantic search if keyword matching is insufficient).
    - Tokenizer (for keyword extraction from task description).
- **Dependents:**
    - PromptBuilderService: Primary consumer.
- **Key Design Decisions:**
    - Layered rule selection: ALWAYS_APPLY rules first, then relevant CONTEXTUAL_SUGGESTION rules.
    - V1 uses keyword matching for contextual selection for simplicity.
    - Clear separation from rule storage (repository).
- **Evolution Notes:** This service is key to making the AI "knowledgeable" about n8n best practices and adaptable to user customizations. V2 could enhance contextual selection significantly.

**2. COMPLETE CONTEXT PACKAGE (RuleSelectorService.ts)**

- **System Architecture Overview:** (Same as for Repository). AI Agents are guided by rules. Some rules are built-in (base rules), and users can add their own (custom rules via CustomRulesRepository).
- **This Component's Role (RuleSelectorService):**
    
    When an AI Agent is about to perform a task, the PromptBuilderService needs to provide it with relevant rules and guidelines. The RuleSelectorService is responsible for figuring out *which* rules are relevant for the current agent and its specific task. It fetches always-applicable rules for that agent, user-defined custom rules from the database (via CustomRulesRepository), and then intelligently selects a few additional contextual rules that seem most pertinent to what the agent is trying to achieve (e.g., based on keywords in the task description). It then provides a concise list of these rules' descriptions to the PromptBuilderService.
    
- **Related Components:** CustomRulesRepository, PromptBuilderService, N8nRule type, bundled base rules file.
- **Design Philosophy:** Relevance, conciseness (don't overwhelm LLM with too many rules), combination of static and dynamic rule selection.
- **Technical Constraints:** Selection logic should be reasonably fast.

**3. EXTRACTED DESIGN DECISIONS (RuleSelectorService.ts)**

1. **Decision:** Combine bundled base rules with user-defined custom rules.
    - **Rationale:** Provide a comprehensive set of guidelines.
    - **Final Form:** Service loads base rules and fetches custom rules from repository.
2. **Decision:** Filter rules based on agentSlug applicability (rule.appliesToAgents).
    - **Rationale:** Ensure agents only see rules relevant to their role.
    - **Final Form:** Filtering step in getRulesForAgentPrompt.
3. **Decision:** Implement two main types of rule application: ALWAYS_APPLY and CONTEXTUAL_SUGGESTION.
    - **Rationale:** Some rules are universally important for an agent, others only in specific situations.
    - **Final Form:** Logic differentiates based on rule.type.
4. **Decision (V1):** Use keyword matching for selecting CONTEXTUAL_SUGGESTION rules.
    - **Rationale:** Simpler to implement for V1 than full semantic search or LLM-based selection.
    - **Alternatives:** Semantic search (V2), LLM-based relevance ranking (V2).
    - **Final Form:** Extract keywords from task description, match against rule.triggerKeywordsOrContext.
5. **Decision:** Limit the number of contextual rules returned.
    - **Rationale:** Avoid prompt stuffing and keep LLM focused.
    - **Final Form:** Return top N (e.g., 3-5) contextual rules.

**4. TECHNICAL SPECIFICATIONS (RuleSelectorService.ts)**

- **TypeScript Interfaces/Types (src/services/types/ruleSelectorService.types.ts or co-located):**
    
    `// src/services/types/ruleSelectorService.types.ts
    import { N8nRule } from '@/types/rules.types'; // Adjusted path
    import { GraphExecutionContext } from '@/engine/graph/types/execution.types';
    import { TaskSession } from '@/common/types/tasks.types';
    
    export interface RuleSelectionContext {
      agentSlug: string;
      taskDescription?: string; // From agent's current specific task
      // Optional full context for more advanced selection in V2
      // currentGraphContext?: GraphExecutionContext; 
      // currentTaskSession?: TaskSession;
    }
    
    export interface SelectedRules {
      alwaysApplyRules: N8nRule[];
      contextualRules: N8nRule[];
      // Combined and formatted string for prompt injection
      // formattedRuleDescriptions: string; 
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **RuleSelectorService Class:**
    
    `// src/services/RuleSelectorService.ts
    import { CustomRulesRepository } from '@/persistence/repositories/CustomRulesRepository';
    import { N8nRule } from '@/types/rules.types';
    import { RuleSelectionContext, SelectedRules } from './types/ruleSelectorService.types';
    import { loadBaseN8nRules } from '@/common/config/baseN8nRules'; // Assumed loader for bundled rules
    // import { countTokens } from 'gpt-tokenizer'; // Or simple word counter for keywords
    
    export class RuleSelectorService {
      private customRulesRepo: CustomRulesRepository;
      private baseRules: N8nRule[];
      private readonly maxContextualRules = 5; // Configurable
    
      constructor(customRulesRepo: CustomRulesRepository) {
        this.customRulesRepo = customRulesRepo;
        this.baseRules = loadBaseN8nRules(); // Load bundled rules once
      }
    
      public async getRulesForAgentPrompt(context: RuleSelectionContext): Promise<N8nRule[]> { /* ... */ }
      private extractKeywords(text?: string, count?: number): string[] { /* ... */ }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **src/config/baseN8nRules.ts (New file):**
    
    `// src/config/baseN8nRules.ts
    import { N8nRule } from '@/types/rules.types';
    
    const baseRulesData: Omit<N8nRule, 'id' | 'createdAt' | 'updatedAt' | 'source'>[] = [
        // ... (Manually curated array of base rule objects, without DB-generated fields)
        // Example:
        // {
        //   name: "Use Global Error Workflow",
        //   description: "Ensures unhandled errors are caught and reported.",
        //   descriptionForLLM: "CRITICAL: For production workflows, always define and set a global Error Workflow...",
        //   type: 'ALWAYS_APPLY',
        //   priority: 'critical',
        //   appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
        //   triggerKeywordsOrContext: ['error', 'production', 'robust'],
        //   recommendation: "Define an Error Trigger node workflow...",
        //   category: 'workflow-design',
        //   version: 1, isActive: true,
        // }
    ];
    
    let loadedBaseRules: N8nRule[] | null = null;
    
    export function loadBaseN8nRules(): N8nRule[] {
        if (loadedBaseRules) return loadedBaseRules;
        // Simulate DB-like structure by adding id, timestamps, source
        loadedBaseRules = baseRulesData.map((rule, index) => ({
            ...rule,
            id: `base-rule-${index + 1}`,
            source: 'builtin',
            createdAt: new Date('2024-01-01'), // Fixed date for base rules
            updatedAt: new Date('2024-01-01'),
        }));
        return loadedBaseRules;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (RuleSelectorService.ts)**

- **File Structure:** src/services/RuleSelectorService.ts
- **Constructor:**
    1. Takes customRulesRepo: CustomRulesRepository.
    2. Calls this.baseRules = loadBaseN8nRules(); to load bundled rules.
- **getRulesForAgentPrompt(context: RuleSelectionContext): Promise<N8nRule[]>:**
    1. Destructure agentSlug, taskDescription from context.
    2. Fetch active custom rules for the agent: const customUserRules = await this.customRulesRepo.getActiveRulesForAgent(agentSlug);
    3. Filter base rules for the agent: const applicableBaseRules = this.baseRules.filter(rule => rule.isActive && rule.appliesToAgents.includes(agentSlug));
    4. Combine: const allApplicableRules = [...applicableBaseRules, ...customUserRules]; (Handle potential duplicates by ID if necessary, though base and custom should have different ID schemes).
    5. Separate ALWAYS_APPLY rules: const alwaysRules = allApplicableRules.filter(r => r.type === 'ALWAYS_APPLY');
    6. Separate potential CONTEXTUAL_SUGGESTION rules: const potentialContextualRules = allApplicableRules.filter(r => r.type === 'CONTEXTUAL_SUGGESTION');
    7. **Keyword-based Contextual Selection (V1):**
        - If taskDescription and potentialContextualRules exist:
            - taskKeywords = this.extractKeywords(taskDescription, 10); (Get top 10 unique, non-stop-word keywords).
            - Score each rule in potentialContextualRules:
                - score = 0;
                - For each keyword in taskKeywords: if rule.triggerKeywordsOrContext?.includes(keyword) (case-insensitive), increment score.
                - Prioritize by rule.priority (critical > high > ...). Higher score wins, then higher priority.
            - Sort scored rules by score (desc), then priority (desc).
            - selectedContextualRules = sortedScoredRules.slice(0, this.maxContextualRules);
        - Else: selectedContextualRules = [].
    8. Combine and return: const finalRules = [...alwaysRules, ...selectedContextualRules];
        - (Optional) Deduplicate finalRules by id if alwaysRules could overlap with selected contextual ones (unlikely if types are distinct).
        - Return finalRules.
- **extractKeywords(text?: string, count: number = 10): string[] (Private):**
    1. If no text, return [].
    2. Lowercase text.
    3. Split into words.
    4. Filter out common stop words (maintain a small list of English stop words).
    5. Filter out very short words (e.g., < 3 chars).
    6. Count word frequencies.
    7. Return top count most frequent words. (This is a basic TF approach, more advanced TF-IDF is V2).

**6. INTEGRATION SPECIFICATIONS (Both Components)**

- CustomRulesRepository uses N8nAidDatabase from getDb().
- RuleSelectorService constructor takes CustomRulesRepository.
- PromptBuilderService constructor takes RuleSelectorService.
- PromptBuilderService.buildPromptMessagesForAgentCall calls ruleSelectorService.getRulesForAgentPrompt().

**7. QUALITY ASSURANCE REQUIREMENTS (Both Components)**

- **CustomRulesRepository.ts Unit Tests:**
    - Mock RxDB collection.
    - Test addRule (validates, adds ID/timestamps, inserts).
    - Test getRuleById (found, not found).
    - Test updateRule (updates, sets updatedAt, validates, handles not found).
    - Test deleteRule (removes, handles not found).
    - Test getAllRules with various RuleQueryOptions (isActive, type, appliesToAgent).
    - Test getActiveRulesForAgent.
- **RuleSelectorService.ts Unit Tests:**
    - Mock CustomRulesRepository and loadBaseN8nRules().
    - Test getRulesForAgentPrompt:
        - Scenario 1: Only ALWAYS_APPLY rules (base and custom) for the agent are returned.
        - Scenario 2: ALWAYS_APPLY + relevant CONTEXTUAL_SUGGESTION rules (mock repo/base rules and taskDescription to trigger keyword matches).
        - Scenario 3: Correct filtering by agentSlug.
        - Scenario 4: No rules applicable.
        - Scenario 5: Max contextual rules limit is respected.
        - Scenario 6: Priority influences contextual rule selection if scores are equal.
    - Test extractKeywords helper with sample text and stop words.
- **No LLM calls in these tests.**

**8. VALIDATION CRITERIA (Both Components)**

- CustomRulesRepository correctly performs CRUD and query operations on (mocked) custom_rules collection, including Zod validation.
- RuleSelectorService correctly loads base rules and fetches custom rules.
- RuleSelectorService correctly filters rules by agentSlug and rule.type.
- V1 keyword matching logic for CONTEXTUAL_SUGGESTION rules in RuleSelectorService is functional.
- The number of returned contextual rules is limited.
- Both services handle edge cases (no rules, empty inputs) gracefully.