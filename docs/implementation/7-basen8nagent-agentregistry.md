**COMBINED IMPLEMENTATION PLAN: BaseAgent.ts & agentRegistry.ts**

**Part 1: BaseAgent.ts**

**1. COMPONENT OVERVIEW (BaseAgent.ts)**

- **Purpose:** To serve as the abstract base class for all specialized AI agents within the n8n AI Assistant system. It provides common functionalities, standardizes agent structure, and enforces essential contracts.
- **Scope:**
    - Define the core interface for an agent's execution logic.
    - Provide static properties for agent identification (agentSlug) and default operational parameters (defaultLlmTaskCategory).
    - Manage injected dependencies (core services like LLMService, PromptBuilderService, NodeSpecService, ToolExecutorService, RxDB Repositories via AgentDependencies).
    - Offer protected helper methods for agents to:
        - Construct standardized success responses (this.success(...)).
        - Signal failures (this.failure(...)).
        - Request user clarification from the GraphExecutor (this.requestUserClarification(...)).
        - Request graph-level tool calls from the GraphExecutor (this.requestGraphTools(...)).
        - Utility for getting JSON schema strings from Zod schemas (for internal multi-step LLM calls that require structured output).
    - Enforce implementation of an execute() method and getFinalOutputSchema() by concrete subclasses.
- **Dependencies (Services to be Injected via AgentDependencies):**
    - LLMService: For any internal LLM calls an agent might make (if an agent orchestrates its own multi-step LLM interactions).
    - PromptBuilderService: For agents to construct ModelMessage[] for their LLM calls.
    - NodeSpecService: For agents like Composer/Selector to get node information.
    - ToolExecutorService: *Not typically called directly by agents*. Agents request tools via requestGraphTools(), which signals the GraphExecutor (which uses ToolExecutorService). However, it might be in AgentDependencies if an agent needed to, for example, get a list of available tools for planning.
    - (Potentially) RxDB Repositories: If a highly stateful agent needs direct, complex data access beyond what its inputs provide. (Use with caution to maintain agent focus).
    - ZodTypeAny from zod (for getFinalOutputSchema()).
    - zod-to-json-schema (for getJsonSchemaString() helper).
- **Dependents:**
    - All concrete AI agent classes (e.g., N8nArchitectAgent, N8nNodeComposerAgent) will extend BaseAgent.
    - AgentRegistry: Will store and manage constructors of classes derived from BaseAgent.
    - GraphExecutor: Will instantiate agents (derived from BaseAgent) and call their execute() method.
- **Key Design Decisions:**
    - Abstract class to enforce common structure and provide shared utilities.
    - Static properties for declarative agent metadata (agentSlug, defaultLlmTaskCategory).
    - Constructor-based dependency injection for services.
    - Standardized helper methods for returning specific AgentExecutionResult structures, simplifying agent logic for signaling different outcomes to the GraphExecutor.
- **Evolution Notes:**
    - Evolved from simple agent functions to a class-based approach for better organization, state management (if ever needed within an agent instance for a single execute call), and provision of helper utilities.
    - The helper methods for responses (success, failure, etc.) were added to reduce boilerplate in concrete agents and ensure consistent output structures.

**2. COMPLETE CONTEXT PACKAGE (BaseAgent.ts)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant uses a graph-based engine (GraphExecutor) to run AI workflows. These workflows are sequences of AI Agents and Tools. Agents are specialized TypeScript classes responsible for specific cognitive tasks (planning, composing, validating) and typically involve one or more LLM interactions.
    
- **This Component's Role (BaseAgent):**
    
    BaseAgent is the foundational abstract class that all concrete AI agents (like N8nArchitectAgent, N8nNodeComposerAgent) must extend. It establishes a common contract for how agents are structured, how they receive dependencies (like LLMService and PromptBuilderService), how they execute their primary task, and how they report their results (success, failure, need for user input, need for tool execution by the graph) back to the GraphExecutor. It also provides utility methods to simplify these common agent operations.
    
- **Related Components:**
    - **Concrete Agent Classes:** Inherit from BaseAgent.
    - **AgentDependencies (Interface):** Defines the set of services (e.g., LLMService, PromptBuilderService) that are injected into every agent instance.
    - **AgentExecutionResult (Interface):** The standardized structure agents return from their execute() method, signaling outcomes to the GraphExecutor.
    - **GraphExecutor:** Instantiates concrete agents (which are BaseAgent subclasses) and calls their execute() method, passing resolved inputs and context.
    - **PromptBuilderService & LLMService:** Agents use these (via this.deps) for their internal LLM call cycles.
- **Design Philosophy:**
    - **Inheritance for Shared Behavior:** Use OOP inheritance to provide common utilities and enforce a standard agent interface.
    - **Convention over Configuration (for metadata):** Static properties like agentSlug provide declarative metadata.
    - **Dependency Injection:** Make services available to agents in a clean, testable way.
    - **Abstraction of Executor Communication:** Helper methods (success, requestUserClarification) abstract the specific JSON structures agents need to return to signal outcomes to the GraphExecutor.
- **Technical Constraints:** TypeScript class. Must be abstract.

**3. EXTRACTED DESIGN DECISIONS (BaseAgent.ts)**

1. **Decision:** Use an abstract base class for all agents.
    - **Rationale:** Enforce common methods (execute, getFinalOutputSchema), provide shared utilities, and manage dependency injection consistently.
    - **Final Form:** export abstract class BaseAgent<TInput, TOutputPayload> { ... }.
2. **Decision:** Agents define static readonly agentSlug and static readonly defaultLlmTaskCategory.
    - **Rationale:** Declarative metadata associated with the agent class itself. Used by AgentRegistry and GraphBuilder.
    - **Final Form:** These static properties are mandatory for subclasses.
3. **Decision:** Dependencies (services) are injected via constructor using an AgentDependencies interface.
    - **Rationale:** Clear, type-safe dependency management. Facilitates testing with mock services.
    - **Final Form:** constructor(protected readonly deps: AgentDependencies).
4. **Decision:** Provide protected helper methods for standardized responses (success, failure, requestUserClarification, requestGraphTools).
    - **Rationale:** Reduce boilerplate in agent implementations, ensure consistent AgentExecutionResult structures for the GraphExecutor.
    - **Final Form:** Methods like protected success(data: SuccessPayload<TOutputPayload>): AgentExecutionResult<TOutputPayload>.
5. **Decision:** Agents must implement abstract execute(...): Promise<AgentExecutionResult<TOutputPayload>>.
    - **Rationale:** This is the core method called by the GraphExecutor.
    - **Final Form:** Abstract method definition.
6. **Decision:** Agents must implement abstract getFinalOutputSchema(): ZodTypeAny (returning z.ZodType<TOutputPayload>).
    - **Rationale:** Allows PromptBuilderService to dynamically fetch the agent's final output Zod schema to generate JSON schema instructions for the LLM.
    - **Final Form:** Abstract method definition.
7. **Decision:** Provide a helper getJsonSchemaString(zodSchema, schemaName?) for agents needing to describe schemas for internal LLM calls.
    - **Rationale:** Utility for agents that have multi-step internal LLM logic requiring structured JSON for intermediate steps.
    - **Final Form:** Protected helper method using zod-to-json-schema.

**4. TECHNICAL SPECIFICATIONS (BaseAgent.ts)**

- **TypeScript Interfaces/Types (src/background/agents/base/baseAgent.types.ts or co-located if preferred, but AgentDependencies and AgentExecutionResult might be more global):**
    
    `// src/common/types/agents.types.ts (or similar global types location)
    import { z, ZodTypeAny } from 'zod';
    import { LLMTokenUsage } from '@/engine/services/types/llmService.types'; // Assumed path
    import { ModelMessage } from 'ai'; // For TaskSession relevant history
    
    // Services injected into every agent
    export interface AgentDependencies {
      llmService: LLMService;
      promptBuilder: PromptBuilderService;
      nodeSpecService: NodeSpecService;
      toolExecutor?: ToolExecutorService; // Optional if not all agents need to know about tools
      // Potentially other common services or repositories
      // Example:
      // taskSessionManager?: TaskSessionManager; // If agent needs to update TaskSession directly (rare)
    }
    
    export interface SuccessPayload<TOutput> {
      thought: string;
      payload: TOutput;
    }
    
    export interface ClarificationRequestDetails {
      interruptId?: string; // Optional: agent can suggest an ID, or Orchestrator generates one
      question_for_user: string;
      suggested_replies?: string[];
      expected_response_schema_id?: string; // Key to a registered Zod schema for user's response
      // Any other data to help UI render the clarification request
    }
    
    export interface ToolCallRequestDetails {
      needed_tool_calls: {
        tool_name: string;
        tool_args: Record<string, any>;
        // Optional: A unique ID for this specific tool call request within the agent's turn
        // Useful if agent requests multiple tools and needs to map results back.
        tool_call_request_id?: string; 
      }[];
      reason_for_tool_call?: string; // Why the agent needs these tools
      // State the agent wants to preserve to resume its logic after tool execution
      intermediate_agent_state_for_resume?: any; 
    }
    
    // Standardized output structure from an agent's execute method
    export interface AgentExecutionResult<TOutputPayload = any> {
      status: 'success' | 'failure' | 'needs_clarification' | 'needs_tool_calls';
      // For 'success'
      result?: {
        thought: string;
        payload: TOutputPayload; // Must match agent's getFinalOutputSchema()
      };
      // For 'failure'
      errorDetails?: {
        message: string;
        type?: string; // Agent-specific error type
        originalError?: any;
      };
      // For 'needs_clarification' or 'needs_tool_calls'
      // Re-using a common structure for both to simplify
      clarificationOrToolRequestDetails?: ClarificationRequestDetails | ToolCallRequestDetails;
      
      // Common across all results (populated by LLMService via agent's use)
      tokenUsage?: LLMTokenUsage; 
      cost?: number;
    }
    
    // For TaskSession context passed to PromptBuilderService by agents
    export interface AgentTaskSessionView {
        id: string;
        llmContextSummary?: string;
        llmContextRelevantHistory?: ModelMessage[];
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **BaseAgent Abstract Class:**
    
    `// src/background/agents/base/BaseAgent.ts
    import { z, ZodTypeAny } from 'zod';
    import { zodToJsonSchema } from 'zod-to-json-schema';
    import { 
        AgentDependencies, 
        AgentExecutionResult, 
        SuccessPayload,
        ClarificationRequestDetails,
        ToolCallRequestDetails
    } from '@/common/types/agents.types'; // Adjusted path
    import { GraphExecutionContext } from '@/engine/graph/types/execution.types'; // For execute method signature
    import { TaskSession } from '@/common/types/tasks.types'; // For execute method signature
    
    export abstract class BaseAgent<TInputParams, TOutputPayloadType extends ZodTypeAny> {
      // Static properties to be defined by concrete subclasses
      static readonly agentSlug: string = 'override_me_in_subclass';
      static readonly defaultLlmTaskCategory: LLMTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING; // Example default
      static readonly description?: string; // Optional static description
      static readonly roleDescription?: string; // Optional static role description
    
      public readonly agentSlug: string;
      protected readonly deps: AgentDependencies;
    
      constructor(dependencies: AgentDependencies) {
        const staticThis = this.constructor as typeof BaseAgent;
        if (staticThis.agentSlug === 'override_me_in_subclass') {
            throw new Error(`Agent class ${staticThis.name} must override static readonly agentSlug.`);
        }
        this.agentSlug = staticThis.agentSlug;
        this.deps = dependencies;
      }
    
      // Core method to be implemented by all agents
      abstract execute(
        inputs: TInputParams, 
        graphContext: GraphExecutionContext, // Provides access to graph-level state and services
        taskSession: TaskSession // Provides access to overall task state, history
      ): Promise<AgentExecutionResult<z.infer<TOutputPayloadType>>>;
    
      // Concrete agents must define the Zod schema for their successful 'payload'
      abstract getFinalOutputSchema(): TOutputPayloadType;
    
      // --- Protected Helper Methods for Standardized Responses ---
      protected success(data: SuccessPayload<z.infer<TOutputPayloadType>>): AgentExecutionResult<z.infer<TOutputPayloadType>> {
        // Runtime validation against the agent's declared output schema
        try {
          this.getFinalOutputSchema().parse(data.payload);
        } catch (error) {
          console.error(`Agent ${this.agentSlug} SUCCESS payload validation failed:`, error);
          return this.failure({
            reason: `Internal: Agent ${this.agentSlug} produced a success payload that failed its own schema validation.`,
            errorType: 'OutputSchemaValidationError',
            originalError: error,
          });
        }
        return {
          status: 'success',
          result: {
            thought: data.thought,
            payload: data.payload,
          },
        };
      }
    
      protected failure(details: { reason: string; errorType?: string; originalError?: any }): AgentExecutionResult<never> {
        return {
          status: 'failure',
          errorDetails: {
            message: details.reason,
            type: details.errorType || 'AgentProcessingError',
            originalError: details.originalError,
          },
        };
      }
    
      protected requestUserClarification(details: ClarificationRequestDetails): AgentExecutionResult<never> {
        return {
          status: 'needs_clarification',
          clarificationOrToolRequestDetails: details,
        };
      }
    
      protected requestGraphTools(details: ToolCallRequestDetails): AgentExecutionResult<never> {
        return {
          status: 'needs_tool_calls',
          clarificationOrToolRequestDetails: details,
        };
      }
      
      protected getJsonSchemaString(zodSchema: ZodTypeAny, schemaName?: string): string {
        try {
            return JSON.stringify(zodToJsonSchema(zodSchema, schemaName || 'InternalSchema'), null, 2);
        } catch (error) {
            console.error("Error converting Zod schema to JSON schema string:", error);
            // Fallback to a very basic schema string or throw
            return JSON.stringify({ type: "object", description: "Schema conversion failed" });
        }
      }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **External Dependencies (for BaseAgent):** zod, zod-to-json-schema.
- **Internal Dependencies:** AgentDependencies, AgentExecutionResult, LLMTaskCategory, GraphExecutionContext, TaskSession.

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (BaseAgent.ts)**

- **File Structure:** src/background/agents/base/BaseAgent.ts
- **Class Definition:** As specified in "TypeScript Interfaces" above.
    - **Static Properties:** agentSlug must be overridden by subclasses. defaultLlmTaskCategory can be.
    - **Constructor:** Takes AgentDependencies, stores them in protected readonly deps. Sets instance agentSlug. Includes a check that agentSlug was overridden.
    - **execute Method:** Abstract. Signature: (inputs: TInputParams, graphContext: GraphExecutionContext, taskSession: TaskSession) => Promise<AgentExecutionResult<z.infer<TOutputPayloadType>>>.
        - TInputParams: Generic type for the agent's specific resolved inputs from the graph.
        - TOutputPayloadType: Generic Zod schema type for the agent's successful payload.
        - graphContext: Gives access to graphContext.services (like NodeSpecService if needed directly beyond this.deps) and other graph run state if absolutely necessary (though agents should primarily use their direct inputs).
        - taskSession: Passed through so agents can give it to PromptBuilderService for history/summary access.
    - **getFinalOutputSchema Method:** Abstract. Signature: () => TOutputPayloadType.
    - **Protected Helper Methods:**
        - success(data: SuccessPayload<z.infer<TOutputPayloadType>>):
            1. Takes thought: string and payload: z.infer<TOutputPayloadType>.
            2. **Critically: Performs runtime validation of data.payload against this.getFinalOutputSchema().parse(data.payload).** If validation fails, it should log an error and return a this.failure() result indicating an internal agent error.
            3. Returns AgentExecutionResult with status: 'success' and structured result.
        - failure(details: { reason: string; errorType?: string; originalError?: any }): Returns AgentExecutionResult with status: 'failure' and structured errorDetails.
        - requestUserClarification(details: ClarificationRequestDetails): Returns AgentExecutionResult with status: 'needs_clarification' and clarificationOrToolRequestDetails set to details.
        - requestGraphTools(details: ToolCallRequestDetails): Returns AgentExecutionResult with status: 'needs_tool_calls' and clarificationOrToolRequestDetails set to details.
        - getJsonSchemaString(zodSchema: ZodTypeAny, schemaName?: string): Uses zodToJsonSchema and JSON.stringify. Includes basic error handling for conversion.

**Part 2: agentRegistry.ts**

**1. COMPONENT OVERVIEW (agentRegistry.ts)**

- **Purpose:** To provide a central registry for all available AI agent classes. This allows the GraphBuilder to validate agent references and the GraphExecutor to dynamically instantiate the correct agent class based on an agentSlug string stored in the GraphDefinition.
- **Scope:**
    - Define the structure for an agent registry entry (AgentRegistryEntry).
    - Maintain a configuration array (AGENT_REGISTRY_CONFIG) listing all known agent classes, their slugs, and default operational parameters (like defaultLlmTaskCategory).
    - Provide a readily accessible Map (agentRegistryMap) for efficient lookup of agent configurations by slug.
- **Dependencies:**
    - All concrete agent class constructors (e.g., N8nArchitectAgent, N8nNodeComposerAgent).
    - BaseAgent type (for constructor typing).
    - LLMTaskCategory enum.
    - AgentDependencies interface.
- **Dependents:**
    - GraphBuilder: Uses AGENT_REGISTRY_CONFIG (or agentRegistryMap) to verify agent classes passed to addAgentNode and to fetch default llmTaskCategory and agentSlug if addAgentNode takes class constructor.
    - GraphExecutor: Uses agentRegistryMap to get the constructor or factory for an agentSlug to instantiate the agent at runtime.
- **Key Design Decisions:**
    - Registry maps string slugs to class constructors and metadata.
    - Static configuration array (AGENT_REGISTRY_CONFIG) as the single source of truth for registered agents.
    - Derived map (agentRegistryMap) for efficient runtime lookups.
- **Evolution Notes:** Essential for decoupling graph definitions (which store slugs) from concrete agent class implementations.

**2. COMPLETE CONTEXT PACKAGE (agentRegistry.ts)**

- **System Architecture Overview:** (Same as for BaseAgent).
- **This Component's Role (agentRegistry):** The agentRegistry acts as a directory or service locator for AI agents. When a graph is defined, it refers to agents by a unique string identifier (their agentSlug). At runtime, the GraphExecutor needs to turn this slug into an actual, executable agent instance. The registry provides this mapping from slug to the agent's class constructor and any default configuration. It's essential for dynamic agent instantiation.
- **Related Components:** BaseAgent, concrete agent classes, GraphBuilder, GraphExecutor.
- **Design Philosophy:** Centralized registration, decoupling, runtime lookup.
- **Technical Constraints:** Must be easily updatable as new agents are added. Efficient lookup by slug.

**3. EXTRACTED DESIGN DECISIONS (agentRegistry.ts)**

1. **Decision:** Use string slugs to identify agents in GraphDefinition.
    - **Rationale:** Makes graph definitions serializable and decoupled from class paths.
    - **Final Form:** AgentCallNodeDef.agentSlug: string.
2. **Decision:** Maintain a static configuration (AGENT_REGISTRY_CONFIG) listing all agents.
    - **Rationale:** Single source of truth for what agents are available.
    - **Final Form:** An array of AgentRegistryEntry objects.
3. **Decision:** Provide a Map for efficient runtime lookup (agentRegistryMap).
    - **Rationale:** Fast O(1) lookup by slug for GraphExecutor.
    - **Final Form:** agentRegistryMap: Map<string, AgentRegistryEntry>.

**4. TECHNICAL SPECIFICATIONS (agentRegistry.ts)**

- **TypeScript Interfaces (src/background/agents/agentRegistry.types.ts or co-located):**
    
    `// src/background/agents/agentRegistry.types.ts (or with BaseAgent types)
    import { BaseAgent, AgentDependencies } from './base/BaseAgent'; // Adjust path
    import { LLMTaskCategory } from '@/engine/graph/types/graph.types';
    
    export type AgentConstructor<TInput = any, TOutput = any> = 
      new (dependencies: AgentDependencies) => BaseAgent<TInput, TOutput>;
    
    export interface AgentRegistryEntry<TInput = any, TOutput = any> {
      slug: string;
      description?: string; // For "Team Awareness" and UI
      roleDescription?: string; // More detailed role for prompts
      constructor: AgentConstructor<TInput, TOutput>;
      defaultLlmTaskCategory: LLMTaskCategory;
      // Optional factory for more complex instantiation or if agent needs specific setup beyond standard deps
      factory?: (dependencies: AgentDependencies) => BaseAgent<TInput, TOutput>;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **No external dependencies beyond TypeScript and internal types.**

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (agentRegistry.ts)**

- **File Structure:** src/background/agents/agentRegistry.ts
- **Import Strategy:**
    
    `// src/background/agents/agentRegistry.ts
    import { AgentRegistryEntry, AgentConstructor } from './agentRegistry.types'; // Or from shared types
    import { LLMTaskCategory } from '@/engine/graph/types/graph.types';
    import { AgentDependencies } from './base/BaseAgent'; // Or from shared types
    
    // Import ALL concrete agent classes
    import { N8nArchitectAgent } from './architect/N8nArchitectAgent'; // Example
    import { N8nNodeComposerAgent } from './composer/N8nNodeComposerAgent'; // Example
    // ... import all other agent classes`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **AGENT_REGISTRY_CONFIG Definition:**
    
    `export const AGENT_REGISTRY_CONFIG: AgentRegistryEntry[] = [
      {
        slug: N8nArchitectAgent.agentSlug, // Assumes static slug on class
        description: N8nArchitectAgent.description || "Designs n8n workflow architecture.",
        roleDescription: N8nArchitectAgent.roleDescription || "As an n8n Solution Architect, your role is to analyze requirements and design a high-level plan...",
        constructor: N8nArchitectAgent as AgentConstructor,
        defaultLlmTaskCategory: N8nArchitectAgent.defaultLlmTaskCategory,
      },
      {
        slug: N8nNodeComposerAgent.agentSlug,
        description: N8nNodeComposerAgent.description || "Composes n8n node JSON.",
        roleDescription: N8nNodeComposerAgent.roleDescription || "As an n8n Node Composer, your role is to generate valid JSON for specific n8n nodes...",
        constructor: N8nNodeComposerAgent as AgentConstructor,
        defaultLlmTaskCategory: N8nNodeComposerAgent.defaultLlmTaskCategory,
      },
      // ... entries for ALL other agents
      // Example for an agent that might use a factory for special setup:
      // {
      //   slug: 'special-stateful-agent',
      //   description: 'Agent with custom initialization',
      //   constructor: SpecialStatefulAgent, // Still provide constructor for type compatibility
      //   defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
      //   factory: (deps: AgentDependencies) => {
      //     const instance = new SpecialStatefulAgent(deps);
      //     instance.performCustomSetup(); // Example custom setup
      //     return instance;
      //   }
      // }
    ];`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **agentRegistryMap Derivation:**
    
    `export const agentRegistryMap = new Map<string, AgentRegistryEntry>();
    AGENT_REGISTRY_CONFIG.forEach(entry => {
      if (agentRegistryMap.has(entry.slug)) {
        console.warn(`AgentRegistry: Duplicate agent slug detected: ${entry.slug}. Overwriting.`);
      }
      agentRegistryMap.set(entry.slug, entry);
    });
    
    // Optional: Freeze the map after population for runtime immutability
    Object.freeze(agentRegistryMap);`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **Utility Function (Optional but Recommended):**
    
    `export function getAgentConfig(slug: string): AgentRegistryEntry | undefined {
        return agentRegistryMap.get(slug);
    }
    
    export function createAgentInstance(slug: string, dependencies: AgentDependencies): BaseAgent<any, any> {
        const entry = agentRegistryMap.get(slug);
        if (!entry) {
            throw new Error(`Agent with slug "${slug}" not found in registry.`);
        }
        if (entry.factory) {
            return entry.factory(dependencies);
        }
        return new entry.constructor(dependencies);
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    

**6. INTEGRATION SPECIFICATIONS (Both Components)**

- **BaseAgent:**
    - Concrete agents inherit and implement execute() and getFinalOutputSchema().
    - GraphExecutor instantiates agents using constructors from agentRegistryMap and injects AgentDependencies.
    - GraphExecutor calls agent.execute(resolvedInputs, graphContext, taskSession).
    - PromptBuilderService calls agent.getFinalOutputSchema() (on an instance or by getting the Zod schema from AgentConfigEntry which could also store it) to get Zod schema for prompt generation.
- **agentRegistry:**
    - GraphBuilder (during addAgentNode(id, AgentClass, ...)):
        - Accesses AgentClass.agentSlug and AgentClass.defaultLlmTaskCategory (static properties).
        - Could potentially cross-reference AgentClass with AGENT_REGISTRY_CONFIG for validation or to get the canonical slug/description.
    - GraphExecutor (during executeNode for an AgentCallNodeDef):
        - Uses agentSlug from AgentCallNodeDef.
        - Calls createAgentInstance(agentSlug, assembledAgentDependencies) to get an agent instance.
    - PromptBuilderService (for "Team Awareness"):
        - Iterates currentGraphDefinition.nodes. For each agentSlug, it can call getAgentConfig(agentSlug) to get its description or roleDescription.

**7. QUALITY ASSURANCE REQUIREMENTS (Both Components)**

- **BaseAgent.ts Unit Tests:**
    - Test static property access (e.g., N8nArchitectAgent.agentSlug).
    - Test constructor correctly stores deps and sets instance agentSlug.
    - Test each response helper (success, failure, requestUserClarification, requestGraphTools):
        - Verify they return the correct AgentExecutionResult structure and status.
        - For success, mock getFinalOutputSchema() on a test subclass. Test that payload is validated against it. Test that it returns a failure status if validation fails.
    - Test getJsonSchemaString() helper with a sample Zod schema.
- **agentRegistry.ts Unit Tests:**
    - Test AGENT_REGISTRY_CONFIG has entries for all expected V1 agents.
    - Test agentRegistryMap is correctly populated from AGENT_REGISTRY_CONFIG.
    - Test for duplicate slug detection (if console.warn is used, spy on it).
    - Test getAgentConfig(slug) returns correct entry or undefined.
    - Test createAgentInstance(slug, deps):
        - Successfully creates an instance of the correct agent.
        - Verifies dependencies are passed to the constructor.
        - Tests it uses factory if defined in AgentRegistryEntry.
        - Throws error for unknown slug.
- **No LLM calls needed for these tests.** All dependencies are structural or mocked.

**8. VALIDATION CRITERIA (Both Components)**

- BaseAgent correctly provides abstract methods and functional helper methods.
- Static properties on concrete agents are accessible and used by agentRegistry.
- Dependencies are correctly injectable into BaseAgent.
- Response helpers produce valid AgentExecutionResult objects, including payload validation in success().
- agentRegistry correctly stores and retrieves agent configurations/constructors.
- GraphExecutor can successfully use agentRegistry to instantiate and execute agents.
- GraphBuilder can successfully use agent class static properties when defining AgentCallNodeDef.