**SINGLE COMPONENT IMPLEMENTATION PLAN: TaskSessionManager.ts**

**1. COMPONENT OVERVIEW**

- **Purpose:** The TaskSessionManager is a high-level service responsible for the business logic and lifecycle management of TaskSession objects. It acts as the primary interface for other services (like OrchestratorService and PromptBuilderService) to create, retrieve, update, and manage task-specific state and context.
- **Scope:**
    - Creating new TaskSession instances (using TaskSessionRepository).
    - Retrieving existing TaskSessions.
    - Updating TaskSession status (e.g., from 'RUNNING' to 'PAUSED_FOR_USER_INPUT').
    - Managing the llmContextSummary within a TaskSession:
        - Providing a mechanism for PromptBuilderService to request summarization.
        - Invoking the n8n-summarizer-agent (via LLMService) when summarization is needed.
        - Updating the TaskSession with the new summary (via TaskSessionRepository).
    - Managing the persistence of GraphExecutionContextSnapshot within a TaskSession (saving snapshots when a graph pauses or completes, loading snapshots for resumption).
    - (Potentially) Enforcing limits (e.g., max number of active tasks per chat, though this might be Orchestrator's role).
    - (Future) Archiving or cleaning up old/completed task sessions.
- **Dependencies:**
    - TaskSessionRepository.ts: For all database interactions related to TaskSessions. (Implementation & Tests DONE)
    - LLMService.ts: To invoke the n8n-summarizer-agent for context summarization. (Implementation & Tests DONE)
    - AgentRegistry.ts: To get the configuration for n8n-summarizer-agent (its slug and default LLMTaskCategory). (Plan DONE, assumes AGENT_REGISTRY_CONFIG available)
    - PromptBuilderService.ts (Conceptual for summarization): It will need to construct the prompt for the n8n-summarizer-agent. The TaskSessionManager will provide the necessary history and current summary to the PromptBuilderService for this specific summarization task.
    - TaskSessionSchema, TaskSession type, TaskSessionStatus enum (from src/types/tasks.types.ts - DONE).
    - GraphExecutionContextSnapshotSchema type (from src/types/tasks.types.ts - DONE).
    - ModelMessageSchema (from src/types/chat.types.ts or Vercel AI SDK - for history used in summarization).
- **Dependents:**
    - OrchestratorService.ts: Will be the primary consumer for creating new task sessions and managing their lifecycle during graph execution (pause, resume, complete, fail).
    - PromptBuilderService.ts: Will interact with TaskSessionManager to get current llmContextSummary and llmContextRelevantHistory (or manager provides methods for this), and to signal when summarization is needed (manager then handles the summarization call).
    - GraphExecutor.ts (indirectly via OrchestratorService): The snapshot it produces will be saved via the TaskSessionManager.
- **Key Design Decisions:**
    - Service layer abstracts repository details and encapsulates task lifecycle logic.
    - Central point for managing task-specific LLM context summarization.
    - Handles persistence of graph execution state.
    - Provides a clean API for other services to interact with task sessions.
- **Evolution Notes:**
    - The role of managing llmContextSummary and triggering summarization was identified as a key responsibility for ensuring context windows aren't overflowed. This manager is the natural place for that logic.
    - Persistence of GraphExecutionContextSnapshot became crucial for pause/resume functionality.

**2. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant uses an OrchestratorService to manage user requests. Each significant request (e.g., "create a workflow") is handled as a TaskSession. This TaskSession object stores all state for that task, including its current status, the history of conversation relevant to it, and snapshots of any AI graph executions (GraphExecutionContextSnapshot) involved. These TaskSession objects are persisted in an RxDB database via TaskSessionRepository. Agents use an LLMService for AI calls and a PromptBuilderService to construct prompts.
    
- **This Component's Role (TaskSessionManager.ts):**
    
    The TaskSessionManager is a crucial service that provides the business logic for managing the entire lifecycle of TaskSession objects. It sits between the OrchestratorService (which decides when to start or resume tasks) and the TaskSessionRepository (which handles raw database storage). Key responsibilities include creating new task sessions, fetching existing ones, updating their status (e.g., when a graph pauses or completes), and, very importantly, managing the LLM context summary for each task. If the conversation history for a task grows too long, this manager will coordinate with the PromptBuilderService and LLMService to invoke a specialized n8n-summarizer-agent to compress the history and update the stored summary. It also handles saving and retrieving the detailed GraphExecutionContextSnapshot associated with a task, enabling features like pausing and resuming complex AI graph operations.
    
- **Related Components:**
    - **TaskSessionRepository:** The data access layer used by this manager for all DB operations.
    - **LLMService:** Used by this manager to call the n8n-summarizer-agent.
    - **PromptBuilderService:** Used by this manager to prepare the specific prompt for the n8n-summarizer-agent. It also signals to this manager when summarization might be needed for an ongoing agent's main prompt.
    - **OrchestratorService:** The primary client of TaskSessionManager, requesting creation, updates, and retrieval of task sessions.
    - **TaskSession Zod Schema & Type:** Defines the data structure this manager works with.
    - **GraphExecutionContextSnapshot Zod Schema & Type:** Defines the structure of graph state this manager helps persist.
- **Design Philosophy:**
    - **Service Layer Abstraction:** Encapsulate business logic related to task sessions.
    - **State Lifecycle Management:** Provide clear methods for progressing task states.
    - **Contextual Integrity:** Ensure LLM context summaries are managed effectively and kept up-to-date.
    - **Decoupling:** Isolate other services from the direct need to manage summarization processes or raw repository calls for common operations.
- **Technical Constraints:**
    - Must operate asynchronously.
    - Needs to handle potential failures during summarization LLM calls.
    - Assumes TaskSessionRepository provides reliable data persistence.

**3. EXTRACTED DESIGN DECISIONS**

1. **Decision:** Create a dedicated TaskSessionManager service.
    - **Rationale:** Separates task lifecycle business logic from raw data access (Repository) and high-level orchestration (Orchestrator).
    - **Final Form:** export class TaskSessionManager { ... }.
2. **Decision:** TaskSessionManager will be responsible for triggering and managing context summarization.
    - **Rationale:** Centralizes the logic for keeping llmContextSummary fresh and managing context window pressure for tasks.
    - **Evolution:** This responsibility was explicitly assigned here to avoid PromptBuilderService directly updating TaskSession state or having too many cross-dependencies.
    - **Final Form:** Method summarizeContextForTaskSession(sessionId, fullHistoryForSummarization) which uses PromptBuilderService (for summarizer prompt) and LLMService (to call n8n-summarizer-agent), then updates TaskSession via repository.
3. **Decision:** TaskSessionManager will handle saving and loading GraphExecutionContextSnapshot within TaskSession.
    - **Rationale:** Provides a clear API for OrchestratorService / GraphExecutor to persist and retrieve graph state.
    - **Final Form:** Methods like updateGraphSnapshot(sessionId, snapshot) and getGraphSnapshot(sessionId).
4. **Decision:** TaskSessionManager provides methods to create sessions with initial defaults and update specific fields like status.
    - **Rationale:** Simplifies common operations for OrchestratorService.
    - **Final Form:** Methods like createSession(chatId, originalUserInput, initialTitle?), updateSessionStatus(sessionId, status).
5. **Decision:** TaskSessionManager will use TaskSessionRepository for all DB interactions.
    - **Rationale:** Adherence to layered architecture.
    - **Final Form:** Repository instance injected via constructor.

**4. TECHNICAL SPECIFICATIONS**

- **TypeScript Interfaces/Types (src/services/types/taskSessionManager.types.ts or co-located):**
    
    `// src/services/types/taskSessionManager.types.ts
    import { TaskSession, TaskSessionStatus, GraphExecutionContextSnapshot } from '@/types/tasks.types';
    import { ModelMessage } from 'ai'; // Or our own ChatMessage equivalent for history
    
    export interface CreateTaskSessionData {
      chatId: string;
      originalUserInput: string;
      title?: string; // Optional, manager can generate default
      initialStatus?: TaskSessionStatus;
      // Any other initial fields that Orchestrator might want to set
      initialGraphDefinitionId?: string; 
    }
    
    export interface SummarizationResult {
      newSummary: string;
      tokensUsed?: number; // For the summarization call
      cost?: number;
    }
    
    // This is what PromptBuilderService might pass to TaskSessionManager when requesting summarization
    export interface SummarizationRequestContext {
        taskSessionId: string;
        // History to be summarized - PromptBuilder prepares this from ConversationStore/ChatRepository
        messagesToSummarize: ModelMessage[]; 
        currentLlmContextSummary?: string; // The existing summary to be built upon
        targetModelContextWindow: number; // Model for which context is being managed
        summarizerAgentSlug: string; // e.g., 'n8n-summarizer-agent'
        summarizerTaskCategory: LLMTaskCategory;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **TaskSessionManager Class:**
    
    `// src/services/TaskSessionManager.ts
    import { TaskSessionRepository } from '@/persistence/repositories/TaskSessionRepository';
    import { LLMService } from '@/engine/services/LLMService';
    import { PromptBuilderService } from '@/engine/services/PromptBuilderService';
    import { AgentRegistryEntry, getAgentConfig } from '@/engine/agents/agentRegistry'; // For summarizer agent
    import { TaskSession, TaskSessionStatus, GraphExecutionContextSnapshot } from '@/types/tasks.types';
    import { ModelMessage } from 'ai'; // For history array type
    import { CreateTaskSessionData, SummarizationResult, SummarizationRequestContext } from './types/taskSessionManager.types';
    
    export class TaskSessionManager {
      private taskSessionRepo: TaskSessionRepository;
      private llmService: LLMService;
      private promptBuilderService: PromptBuilderService; // To build prompt for summarizer
    
      constructor(
        taskSessionRepo: TaskSessionRepository,
        llmService: LLMService,
        promptBuilderService: PromptBuilderService
      ) { /* ... assign dependencies ... */ }
    
      public async createSession(data: CreateTaskSessionData): Promise<TaskSession> { /* ... */ }
      public async getSession(sessionId: string): Promise<TaskSession | null> { /* ... */ }
      public async updateSessionStatus(sessionId: string, status: TaskSessionStatus): Promise<TaskSession | null> { /* ... */ }
      
      public async updateLlmContextSummary(sessionId: string, newSummary: string): Promise<TaskSession | null> { /* ... */ }
      public async getLlmContextSummary(sessionId: string): Promise<string | undefined> { /* ... */ }
    
      // Key method for proactive context management
      public async ensureContextIsSummarized(context: SummarizationRequestContext): Promise<{
          taskSession: TaskSession | null;
          summaryUpdated: boolean;
          newSummary?: string;
      }> { /* ... */ }
      
      public async saveGraphSnapshot(sessionId: string, snapshot: GraphExecutionContextSnapshot): Promise<TaskSession | null> { /* ... */ }
      public async getGraphSnapshot(sessionId: string): Promise<GraphExecutionContextSnapshot | undefined> { /* ... */ }
      public async clearGraphSnapshot(sessionId: string): Promise<TaskSession | null> { /* ... */ }
    
      public async markAsCompleted(sessionId: string, finalOutput?: any): Promise<TaskSession | null> { /* ... */ }
      public async markAsFailed(sessionId: string, errorDetails: any): Promise<TaskSession | null> { /* ... */ }
      public async markAsAborted(sessionId: string): Promise<TaskSession | null> { /* ... */ }
      
      // ... other utility methods like findActiveSessionsForChat, etc.
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **External Dependencies:** None beyond what's passed in constructor.
- **Internal Dependencies:** TaskSessionRepository, LLMService, PromptBuilderService, AgentRegistry.
- **Configuration:** Thresholds for triggering summarization (e.g., "if task-specific history token count > X% of target model's context window"). This might be part of PromptBuilderOptions or internal to TaskSessionManager.

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (TaskSessionManager.ts)**

- **File Structure:** src/services/TaskSessionManager.ts (This is a higher-level application service).
- **Constructor:**
    - Takes taskSessionRepo: TaskSessionRepository, llmService: LLMService, promptBuilderService: PromptBuilderService.
    - Assigns them to private readonly members.
- **createSession(data: CreateTaskSessionData): Promise<TaskSession>:**
    1. Call this.taskSessionRepo.createSession({ chatId: data.chatId, originalUserInput: data.originalUserInput, title: data.title, status: data.initialStatus || 'initializing', /* map other initial fields */ }).
    2. Return the result.
- **getSession(sessionId: string): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.getSessionById(sessionId);
- **updateSessionStatus(sessionId: string, status: TaskSessionStatus): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { status });
- **updateLlmContextSummary(sessionId: string, newSummary: string): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { llmContextSummary: newSummary });
- **getLlmContextSummary(sessionId: string): Promise<string | undefined>:**
    1. const session = await this.getSession(sessionId);
    2. Return session?.llmContextSummary;
- **ensureContextIsSummarized(context: SummarizationRequestContext): Promise<{ taskSession: TaskSession | null; summaryUpdated: boolean; newSummary?: string; }>:**
    1. Destructure context: sessionId, messagesToSummarize, currentLlmContextSummary, targetModelContextWindow, summarizerAgentSlug, summarizerTaskCategory.
    2. Get summarizerAgentConfig = getAgentConfig(summarizerAgentSlug) from agentRegistry. Throw if not found.
    3. **Prepare data for summarizer prompt:**
        - This might involve creating a specificDataForThisLLMCall object like:
            
            `const summarizerInputData: AgentCallSpecificData = {
                _CORE_AGENT_TASK_DESCRIPTION_: "Summarize the following conversation history concisely, building upon the previous summary if provided, to ensure the total context remains manageable.",
                _PREVIOUS_SUMMARY_IF_ANY_: currentLlmContextSummary,
                _CONVERSATION_HISTORY_TO_SUMMARIZE_: messagesToSummarize // Array of ModelMessage
            };`
            
            **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
            
    4. **Build prompt for summarizer agent:**
        - const { messages: summarizerPromptMessages } = await this.promptBuilderService.buildPromptMessagesForAgentCall(summarizerAgentSlug, null /* no current graph for summarizer */, { id: sessionId, /* only essential TaskSession fields needed by PromptBuilder for this call, like ID, no full history */ } as TaskSession, summarizerInputData, { /* options for summarizer's output schema if it's structured, or just text */ });
    5. **Invoke summarizer agent via LLMService:**
        - const summarizationLlmResult = await this.llmService.invokeModel(summarizerTaskCategory, summarizerPromptMessages, { responseFormat: { type: 'text' } /* Assuming summarizer returns text */ });
    6. If summarizationLlmResult.status === 'success' and summarizationLlmResult.content (the new summary string) exists:
        - const newSummary = summarizationLlmResult.content as string;
        - const updatedSession = await this.updateLlmContextSummary(sessionId, newSummary);
        - Return { taskSession: updatedSession, summaryUpdated: true, newSummary }.
    7. Else (summarization failed):
        - Log error: "Summarization failed for TaskSession ${sessionId}".
        - const currentSession = await this.getSession(sessionId);
        - Return { taskSession: currentSession, summaryUpdated: false }.
- **saveGraphSnapshot(sessionId: string, snapshot: GraphExecutionContextSnapshot): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { graphExecutionContextSnapshot: snapshot, activeGraphExecutionId: snapshot.currentGraphRunId });
- **getGraphSnapshot(sessionId: string): Promise<GraphExecutionContextSnapshot | undefined>:**
    1. const session = await this.getSession(sessionId);
    2. Return session?.graphExecutionContextSnapshot;
- **clearGraphSnapshot(sessionId: string): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { graphExecutionContextSnapshot: undefined, activeGraphExecutionId: undefined });
- **markAsCompleted(sessionId: string, finalOutput?: any): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { status: 'completed', finalOutput, completedAt: new Date(), graphExecutionContextSnapshot: undefined, activeGraphExecutionId: undefined }); (Clear snapshot on final completion).
- **markAsFailed(sessionId: string, errorDetails: any): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { status: 'failed', lastError: errorDetails, completedAt: new Date(), graphExecutionContextSnapshot: undefined, activeGraphExecutionId: undefined });
- **markAsAborted(sessionId: string): Promise<TaskSession | null>:**
    1. Return this.taskSessionRepo.updateSession(sessionId, { status: 'cancelled', completedAt: new Date(), graphExecutionContextSnapshot: undefined, activeGraphExecutionId: undefined });

**6. INTEGRATION SPECIFICATIONS**

- Constructor injection: TaskSessionRepository, LLMService, PromptBuilderService.
- OrchestratorService will call createSession, updateSessionStatus, saveGraphSnapshot, getGraphSnapshot, markAsCompleted/Failed/Aborted.
- PromptBuilderService (or agents through it) will call ensureContextIsSummarized (or a method that triggers it if TaskSessionManager detects the need based on info from PromptBuilderService). This interaction needs to be smooth: PromptBuilderService might calculate current prompt size, if too big, it tells TaskSessionManager "Summarize this history for session X for target model Y", then TaskSessionManager does its work and PromptBuilderService re-fetches the updated summary.

**7. QUALITY ASSURANCE REQUIREMENTS**

- **Unit Testing Strategy:**
    - Mock TaskSessionRepository, LLMService, PromptBuilderService, agentRegistry (getAgentConfig).
    - **Test createSession:** Verify repository is called with correct defaults.
    - **Test getSession:** Verify repository call.
    - **Test updateSessionStatus:** Verify repository call.
    - **Test updateLlmContextSummary & getLlmContextSummary:** Verify repository calls.
    - **Test ensureContextIsSummarized:**
        - Scenario 1: Summarization not needed (mock PromptBuilderService to indicate current context is small). Verify no LLM call made.
        - Scenario 2: Summarization needed.
            - Verify getAgentConfig called for summarizer.
            - Verify PromptBuilderService.buildPromptMessagesForAgentCall called with correct data for summarizer.
            - Verify LLMService.invokeModel called with summarizer slug, category, and prompt.
            - Mock LLMService success: Verify TaskSessionRepository.updateSession is called with the new summary.
            - Mock LLMService failure: Verify error is logged and original session state is maintained.
    - **Test saveGraphSnapshot, getGraphSnapshot, clearGraphSnapshot:** Verify repository calls with correct snapshot data or undefined.
    - **Test markAsCompleted, markAsFailed, markAsAborted:** Verify status, completedAt, finalOutput/lastError are set, and snapshot is cleared.
- **Error Handling:** Test failures from repository calls or LLMService (for summarization) are handled gracefully.
- **No direct RxDB or Vercel AI SDK calls in these tests; all through mocked dependencies.**

**8. VALIDATION CRITERIA**

- All public methods are implemented and match their TypeScript signatures.
- Correctly uses TaskSessionRepository for all persistence.
- ensureContextIsSummarized correctly orchestrates calls to PromptBuilderService (for prompt) and LLMService (for summarizer agent), and updates TaskSession via repository.
- State transitions for status, completedAt, finalOutput, lastError are correct.
- GraphExecutionContextSnapshot is correctly saved, retrieved, and cleared.
- Handles errors from dependent services gracefully.

---