**SINGLE COMPONENT IMPLEMENTATION PLAN: rxdbSetup.ts (RxDB Initialization and Core Schemas/Collections - V1)**

**1. COMPONENT OVERVIEW**

- **Purpose:** To initialize and configure the RxDB database instance, define the necessary data collections (tables) with their schemas, and provide a way to access the database instance throughout the application. This component is the cornerstone of our multi-platform persistence layer.
- **Scope:**
    - Detect the environment (Node.js or Browser).
    - Select and configure the appropriate RxDB storage adapter:
        - Browser: getRxStorageDexie() (using IndexedDB).
        - Node.js: getRxStorageLoki({ adapter: new LokiFsStructuredAdapter() }) (using LokiJS with filesystem persistence for testing/CLI) or potentially getRxStorageSQLite() if native SQLite is preferred for Node.js robustness.
    - Define RxDB JSON schemas for all V1 core data collections:
        - user_settings
        - task_sessions
        - chat_threads
        - conversation_messages
        - custom_rules (for user-defined rules)
        - knowledge_chunks (for RAG, including embeddings)
    - Create these collections in the RxDB database instance.
    - (Potentially) Handle schema migrations if versions change in the future (V2, but good to note).
    - Export a function to get the initialized database instance (likely a singleton promise).
- **Dependencies:**
    - rxdb (core library).
    - rxdb/plugins/storage-dexie (for IndexedDB adapter in browser).
    - rxdb/plugins/storage-lokijs and lokijs/src/loki-fs-structured-adapter (for Node.js LokiJS adapter).
    - (Optional) rxdb/plugins/storage-sqlite and a SQLite adapter if chosen for Node.js.
    - RxDB Plugins: RxDBDevModePlugin (for development), RxDBQueryBuilderPlugin (for Mango queries), RxDBSchemaCheckPlugin. Consider RxDBLeaderElectionPlugin (for browser if multiple tabs access DB) and RxDBAttachmentsPlugin (if needed for future binary data).
    - Zod schemas for our core data types (e.g., UserSettingsSchema, TaskSessionSchema, etc.). These will be converted to RxDB-compatible JSON schemas.
    - zod-to-json-schema: Utility to convert Zod schemas.
- **Dependents:**
    - All Repository classes (e.g., UserSettingsRepository, TaskSessionRepository) will depend on the initialized RxDB database instance provided by this setup.
    - Main application/extension initialization logic.
- **Key Design Decisions:**
    - RxDB as the chosen local-first, multi-platform database.
    - Environment-specific storage adapters.
    - Schema definition using JSON schema (derived from Zod).
    - Centralized database initialization.
- **Evolution Notes:**
    - We decided to use RxDB to overcome chrome.storage.local limitations regarding storage size, querying, and Node.js compatibility.
    - The choice of storage adapters (Dexie for browser, LokiJS/FS for Node) provides good defaults.
    - The initial list of collections covers V1 needs for user settings, task/chat state, rules, and RAG.

**2. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant is a TypeScript-based AI "brain" library designed for Node.js and browser (Chrome extension) environments. It features a graph-based orchestration engine for AI workflows. A critical requirement is persistent storage for user settings (like API keys, model preferences), ongoing task states, conversation histories, user-defined rules, and a knowledge base for Retrieval Augmented Generation (RAG).
    
- **This Component's Role (rxdbSetup.ts):**
    
    This module is responsible for setting up and initializing our chosen database solution, RxDB. It will detect whether the code is running in a Node.js or browser environment and configure RxDB with an appropriate storage adapter (filesystem-based for Node.js, IndexedDB for the browser). It will also define the structure (schemas) for all the necessary data "tables" (collections in RxDB terms) that the application will use. Finally, it will provide a standardized way for the rest of the application, particularly data repository classes, to get a hold of the initialized RxDB database instance. This ensures all data interactions go through a properly configured and consistent database layer.
    
- **Related Components:**
    - **RxDB Repositories (e.g., UserSettingsRepository):** These are the primary consumers. They will use the RxDB database instance initialized by this module to perform CRUD operations on specific collections.
    - **Zod Schemas (e.g., UserSettingsSchema from userSettings.types.ts):** Our application defines data structures using Zod. This module will convert these Zod schemas into the JSON schema format required by RxDB for defining collection structures.
    - **Storage Adapters (from RxDB plugins):** RxDBStorageDexie (for IndexedDB), RxDBStorageLoki (for LokiJS).
    - **Main Application Startup Logic:** Will call the initialization function from this module to set up the database.
- **Design Philosophy:**
    - **Local-First:** Data should be stored and accessible locally for offline capabilities (in browser) and independent operation (Node.js).
    - **Multi-Platform:** A single database setup approach that works across different JavaScript environments.
    - **Schema-Driven:** Enforce data integrity through well-defined schemas for collections.
    - **Centralized Initialization:** Ensure the database is set up consistently.
- **Technical Constraints:**
    - Must use RxDB.
    - Must select appropriate storage adapters for browser (IndexedDB) and Node.js (filesystem).
    - Collection schemas must be compatible with RxDB's JSON schema requirements.

**3. EXTRACTED DESIGN DECISIONS**

1. **Decision:** Use RxDB as the primary database solution.
    - **Rationale:** Multi-platform, local-first, reactive, good querying, schema support, plugin ecosystem, suitable for storing JSON documents and potentially vector embeddings. Addresses limitations of chrome.storage.local.
    - **Final Form:** Core database technology chosen.
2. **Decision:** Use environment-specific storage adapters for RxDB.
    - **Rationale:** Dexie (IndexedDB) is standard and robust for browsers. LokiJS with a filesystem adapter is suitable for Node.js environments for development, testing, and potential CLI usage.
    - **Alternatives:** OPFS for browser (newer, potentially better performance but less universal support), native SQLite adapter for Node.js (more robust but adds native dependency).
    - **Final Form:** RxDBStorageDexie for browser, RxDBStorageLoki with LokiFsStructuredAdapter for Node.js as V1 choice.
3. **Decision:** Define RxDB collection schemas using JSON schema, derived from primary Zod schemas.
    - **Rationale:** Keeps Zod as the single source of truth for data shapes while providing RxDB with its required schema format. Ensures type safety from application layer down to DB.
    - **Final Form:** Use zod-to-json-schema utility for conversion.
4. **Decision:** Centralize database initialization in rxdbSetup.ts.
    - **Rationale:** Ensures database is configured once with correct plugins and schemas. Provides a singleton-like access pattern to the DB instance.
    - **Final Form:** An exported async function initializeDb() that returns Promise<RxDatabase>.
5. **Decision:** Include specific V1 collections: user_settings, task_sessions, chat_threads, conversation_messages, custom_rules, knowledge_chunks.
    - **Rationale:** These cover the core data persistence needs identified for V1 functionality.
    - **Final Form:** These collections will be defined with their respective schemas.

**4. TECHNICAL SPECIFICATIONS**

- **TypeScript Interfaces/Types (src/common/database/rxdb.types.ts or co-located with setup):**
    
    `// src/common/database/rxdb.types.ts
    import { RxDatabase, RxCollection, RxDocument } from 'rxdb';
    import { UserSettings } from '@/common/types/userSettings.types'; // Zod-derived type
    import { TaskSession } from '@/common/types/tasks.types'; // Zod-derived type
    import { ChatThread, ChatMessage } from '@/common/types/chat.types.ts'; // Zod-derived types
    import { N8nRule } from '@/common/types/rules.types.ts'; // Zod-derived type
    import { KnowledgeChunk } from '@/common/types/knowledge.types.ts'; // Zod-derived type
    
    // Define types for RxDB documents by extending base types with RxDocument methods/props
    export type UserSettingsDocType = UserSettings; // Assuming UserSettings is plain data
    export type UserSettingsDocument = RxDocument<UserSettingsDocType>;
    export type UserSettingsCollection = RxCollection<UserSettingsDocType>;
    
    export type TaskSessionDocType = TaskSession;
    export type TaskSessionDocument = RxDocument<TaskSessionDocType>;
    export type TaskSessionCollection = RxCollection<TaskSessionDocType>;
    
    export type ChatThreadDocType = ChatThread;
    export type ChatThreadDocument = RxDocument<ChatThreadDocType>;
    export type ChatThreadCollection = RxCollection<ChatThreadDocType>;
    
    export type ChatMessageDocType = ChatMessage;
    export type ChatMessageDocument = RxDocument<ChatMessageDocType>;
    export type ChatMessageCollection = RxCollection<ChatMessageDocType>;
    
    export type CustomRuleDocType = N8nRule;
    export type CustomRuleDocument = RxDocument<CustomRuleDocType>;
    export type CustomRuleCollection = RxCollection<CustomRuleDocType>;
    
    export type KnowledgeChunkDocType = KnowledgeChunk;
    export type KnowledgeChunkDocument = RxDocument<KnowledgeChunkDocType>;
    export type KnowledgeChunkCollection = RxCollection<KnowledgeChunkDocType>;
    
    // Define the structure of our database collections
    export interface N8nAidDbCollections {
      user_settings: UserSettingsCollection;
      task_sessions: TaskSessionCollection;
      chat_threads: ChatThreadCollection;
      conversation_messages: ChatMessageCollection;
      custom_rules: CustomRuleCollection;
      knowledge_chunks: KnowledgeChunkCollection;
    }
    
    // Define the type for our RxDatabase instance
    export type N8nAidDatabase = RxDatabase<N8nAidDbCollections>;`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **External Dependencies:**
    - rxdb
    - rxdb/plugins/dev-mode (for RxDBDevModePlugin)
    - rxdb/plugins/query-builder (for RxDBQueryBuilderPlugin)
    - rxdb/plugins/schema-check (for RxDBSchemaCheckPlugin)
    - rxdb/plugins/leader-election (for RxDBLeaderElectionPlugin - browser)
    - rxdb/plugins/storage-dexie (for browser)
    - rxdb/plugins/storage-lokijs, lokijs/src/loki-fs-structured-adapter (for Node.js)
    - (Optional) rxdb/plugins/migration (for RxDBMigrationPlugin - V2)
    - zod (for defining original data shapes)
    - zod-to-json-schema (for converting Zod to RxDB JSON schemas)
- **Internal Dependencies:**
    - Zod schemas for all data types to be stored (e.g., UserSettingsSchema, TaskSessionSchema from their respective type definition files).
    - isNodeEnvironment() utility.
- **Multi-Platform Requirements:** Key focus. Use isNodeEnvironment() to select storage adapter. Filesystem paths for LokiJS need to be configurable or use a sensible default for dev/testing.
- **Configuration:** Database name (e.g., n8naiddb).

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (rxdbSetup.ts)**

- **File Structure:** src/common/database/rxdbSetup.ts
- **Import Strategy:**
    
    `// src/common/database/rxdbSetup.ts
    import { createRxDatabase, addRxPlugin, RxDatabase } from 'rxdb';
    import { RxDBDevModePlugin } from 'rxdb/plugins/dev-mode';
    import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
    import { RxDBSchemaCheckPlugin } from 'rxdb/plugins/schema-check';
    import { RxDBLeaderElectionPlugin } from 'rxdb/plugins/leader-election'; // For browser
    import { getRxStorageDexie } from 'rxdb/plugins/storage-dexie';         // For browser
    import { getRxStorageLoki } from 'rxdb/plugins/storage-lokijs';         // For Node.js
    import LokiFsStructuredAdapter from 'lokijs/src/loki-fs-structured-adapter'; // Node.js FS adapter
    import { zodToJsonSchema } from 'zod-to-json-schema';
    
    import { isNodeEnvironment } from '@/common/utils/environment'; // Assumed utility
    
    // Import ALL Zod schemas for collections
    import { UserSettingsSchema } from '@/common/types/userSettings.types';
    import { TaskSessionSchema } from '@/common/types/tasks.types';
    import { ChatThreadSchema, ChatMessageSchema } from '@/common/types/chat.types';
    import { N8nRuleSchema } from '@/common/types/rules.types';
    import { KnowledgeChunkSchema } from '@/common/types/knowledge.types';
    
    import type { N8nAidDatabase, N8nAidDbCollections } from './rxdb.types';
    
    // Ensure plugins are added only once
    if (process.env.NODE_ENV === 'development') {
        addRxPlugin(RxDBDevModePlugin);
    }
    addRxPlugin(RxDBQueryBuilderPlugin);
    addRxPlugin(RxDBSchemaCheckPlugin);
    if (!isNodeEnvironment()) { // Leader election is typically for browser tabs
        addRxPlugin(RxDBLeaderElectionPlugin);
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **Global Database Instance Variable (Singleton Pattern):**
    
    `let n8nAidDbPromise: Promise<N8nAidDatabase> | null = null;`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **initializeDb() Function:**
    
    `export function initializeDb(dbName: string = 'n8naiddb'): Promise<N8nAidDatabase> {
      if (n8nAidDbPromise) {
        return n8nAidDbPromise;
      }
    
      n8nAidDbPromise = (async () => {
        try {
          const storageAdapter = isNodeEnvironment()
            ? getRxStorageLoki({ adapter: new LokiFsStructuredAdapter() }) // Uses fs in node
            : getRxStorageDexie(); // Uses IndexedDB in browser
    
          const db: N8nAidDatabase = await createRxDatabase<N8nAidDbCollections>({
            name: dbName,
            storage: storageAdapter,
            password: process.env.RXDB_PASSWORD, // Optional: for encrypted DB (consider for API keys)
            multiInstance: !isNodeEnvironment(), // True for browser (leader election), false for Node.js typical use
            eventReduce: true, // Recommended for performance
            cleanupPolicy: {} // Optional: configure cleanup for soft-deleted documents
          });
    
          // --- Define Collection Schemas (using zodToJsonSchema) ---
          // Note: RxDB JSON schema requires 'version' and 'primaryKey'
          // 'version' is for schema migrations. Start with 0.
          // 'primaryKey' must be a top-level string property in the document.
    
          const userSettingsJsonSchema = {
            ...zodToJsonSchema(UserSettingsSchema, "UserSettingsSchema"),
            title: 'User Settings',
            version: 0,
            primaryKey: 'id', // UserSettingsSchema needs an 'id' field, or use a fixed ID for singleton
            properties: {
              id: { type: 'string', const: 'singleton_user_settings' }, // Enforce single doc for settings
              ...(zodToJsonSchema(UserSettingsSchema, "UserSettingsSchema").properties || {}),
            }
          };
          // For UserSettings, ensure 'id' is defined in Zod schema or handle singleton pattern.
          // If UserSettingsSchema doesn't have `id`, modify above to make `id` const.
    
          const taskSessionsJsonSchema = {
            ...zodToJsonSchema(TaskSessionSchema, "TaskSessionSchema"),
            title: 'Task Sessions', version: 0, primaryKey: 'id',
            indexes: ['status', 'chatId', 'updatedAt'] // Example indexes
          };
    
          const chatThreadsJsonSchema = {
            ...zodToJsonSchema(ChatThreadSchema, "ChatThreadSchema"),
            title: 'Chat Threads', version: 0, primaryKey: 'id',
            indexes: ['lastUpdatedAt']
          };
    
          const conversationMessagesJsonSchema = {
            ...zodToJsonSchema(ChatMessageSchema, "ChatMessageSchema"),
            title: 'Conversation Messages', version: 0, primaryKey: 'id',
            indexes: ['chatId', 'timestamp', ['chatId', 'timestamp']] // Composite index
          };
          
          const customRulesJsonSchema = {
            ...zodToJsonSchema(N8nRuleSchema, "N8nRuleSchema"),
            title: 'Custom Rules', version: 0, primaryKey: 'id',
            indexes: ['type', 'appliesToAgents']
          };
    
          const knowledgeChunksJsonSchema = {
            ...zodToJsonSchema(KnowledgeChunkSchema, "KnowledgeChunkSchema"),
            title: 'Knowledge Chunks', version: 0, primaryKey: 'id',
            indexes: ['sourceType', /* 'embedding' fields if custom indexed */ 'idx0', 'idx1', 'idx2', 'idx3', 'idx4'],
            // For embeddings, ensure the Zod schema defines `embedding: z.array(z.number())`
            // and the custom index fields for vector search (`idx0` etc.)
          };
          
          // --- Add Collections to Database ---
          await db.addCollections({
            user_settings: { schema: userSettingsJsonSchema },
            task_sessions: { schema: taskSessionsJsonSchema },
            chat_threads: { schema: chatThreadsJsonSchema },
            conversation_messages: { schema: conversationMessagesJsonSchema },
            custom_rules: { schema: customRulesJsonSchema },
            knowledge_chunks: { schema: knowledgeChunksJsonSchema },
          });
    
          // Optional: Database-level hooks or event subscriptions
          // db. γεγονότα$.subscribe(ev => console.log('DB Event:', ev));
    
          console.log('N8nAidDatabase initialized successfully.');
          return db;
    
        } catch (error) {
          console.error('N8nAidDatabase initialization failed:', error);
          n8nAidDbPromise = null; // Reset promise so next call can retry
          throw error; // Re-throw for upstream handling
        }
      })();
      return n8nAidDbPromise;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **getDb() Exported Function:**
    
    `export async function getDb(): Promise<N8nAidDatabase> {
      if (!n8nAidDbPromise) {
        throw new Error("Database not initialized. Call initializeDb() first.");
      }
      return n8nAidDbPromise;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **Schema Definitions for RxDB:**
    - For each Zod schema (e.g., UserSettingsSchema):
        1. Convert to JSON schema: const jsonSchema = zodToJsonSchema(UserSettingsSchema, "UserSettingsSchemaName");
        2. **Augment with RxDB requirements:**
            - Add title: string (human-readable).
            - Add version: number (start at 0 for schema migrations).
            - Define primaryKey: string (must be a top-level string property in the Zod schema, e.g., id: z.string()). For user_settings, if it's a singleton document, the id can be a constant value like 'singleton_user_settings', and the schema for id should be { type: 'string', const: 'singleton_user_settings' }.
            - Define indexes: string[] | string[][] for properties that will be frequently queried. For example, task_sessions might be indexed on status or chatId. knowledge_chunks will need indexes on idx0 through idx4 for vector search.
    - Ensure all Zod schemas that will become collections have a suitable top-level string field to act as primaryKey (typically id: z.string().uuid() or similar).
- **Error Handling:** Wrap createRxDatabase and addCollections in try...catch. If initialization fails, reset n8nAidDbPromise to null so subsequent calls to initializeDb() can retry.
- **Singleton Pattern:** The n8nAidDbPromise variable ensures createRxDatabase is only called once. Subsequent calls to initializeDb() return the existing promise. getDb() provides access after initialization.

**6. INTEGRATION SPECIFICATIONS**

- **Usage:** Other parts of the application (primarily Repository classes) will call await getDb() to obtain the database instance.
- **Initialization Call:** The main application startup sequence (e.g., in background/index.ts for extension, or main script for Node.js app) MUST call await initializeDb() once at the beginning.
- **Error Handling:** If initializeDb() throws, the application should handle this critical failure (e.g., log, notify user, potentially disable features).

**7. QUALITY ASSURANCE REQUIREMENTS**

- **Unit Testing Strategy:**
    - Mock isNodeEnvironment() to test both browser and Node.js adapter selection paths.
    - Mock RxDB createRxDatabase and addCollections methods to verify they are called with correct parameters (DB name, storage adapter, schemas).
    - Test JSON schema generation: For each Zod schema, verify that the generated RxDB JSON schema includes title, version, primaryKey, and correct properties.
    - Test singleton pattern: initializeDb() called multiple times returns the same promise/instance.
    - Test getDb() throws if initializeDb() hasn't been successfully called.
    - Test error handling during initialization (e.g., if createRxDatabase mock throws).
- **Environment-Specific Tests (Conceptual, might need actual browser/Node test runners):**
    - A simple test that initializes the DB in a Node.js environment and verifies a file is created (if using LokiFS with persistence).
    - A simple test that initializes the DB in a browser-like environment (jsdom with IndexedDB mock or actual browser test) and verifies IndexedDB is used.
- **No actual data insertion/querying tests here.** That's the responsibility of Repository tests. This module only sets up the DB.

**8. VALIDATION CRITERIA**

- initializeDb() correctly selects RxDBStorageDexie in browser, RxDBStorageLoki in Node.js.
- All V1 collections (user_settings, task_sessions, etc.) are defined with valid RxDB JSON schemas, including version: 0 and a primaryKey.
- Zod schemas are correctly transformed into RxDB JSON schemas.
- Required RxDB plugins (DevMode, QueryBuilder, SchemaCheck, LeaderElection for browser) are added.
- The database initializes successfully in both mock browser and mock Node.js environments.
- getDb() returns the initialized database instance or throws if not initialized.
- Singleton pattern for DB instance is correctly implemented.

**Proposed File Structure for RxDB Setup & Repositories (within src/persistence/):**

`src/
└── 📁 persistence/
    ├── 📄 index.ts               // Re-exports key items from this module
    ├── 📄 rxdbSetup.ts          // Our current implementation plan target (RxDB initialization)
    ├── 📁 __tests__/
    │   └── 📄 rxdbSetup.test.ts   // Tests for rxdbSetup.ts
    ├── 📁 types/
    │   └── 📄 database.types.ts   // N8nAidDatabase, N8nAidDbCollections, DocTypes (UserSettingsDocument, etc.)
    │   └── 📄 repository.types.ts // Optional: Base repository interfaces if we define one
    ├── 📁 repositories/
    │   ├── 📄 UserSettingsRepository.ts
    │   ├── 📄 TaskSessionRepository.ts
    │   ├── 📄 ChatRepository.ts
    │   ├── 📄 CustomRulesRepository.ts
    │   ├── 📄 KnowledgeRepository.ts
    │   └── 📁 __tests__/
    │       ├── 📄 UserSettingsRepository.test.ts
    │       └── 📄 ... (other repository tests) ...
    └── 📁 schemas/                 // To store RxDB JSON schema definitions (derived from Zod)
        ├── 📄 userSettings.rxdb.schema.json // Or .ts exporting the object
        ├── 📄 taskSessions.rxdb.schema.json
        └── 📄 ... (other collection schemas) ...`