Following the `Core Graph Structure Types` and the `GraphBuilder` API, the next essential components are:

1. **`GraphExecutionContext`**: This is the runtime state object for a graph execution. Its structure is paramount.
2. **Function Registry & Function Invocation**: How dynamically referenced functions (`conditionFunctionKey`, `transformFunctionKey`) are managed and called.
3. **`GraphExecutor`**: The engine that runs the graphs.

Let's start with the `GraphExecutionContext`.

---

**Implementation Plan: `GraphExecutionContext`**

**1. Component Overview**

- **Purpose:** To define and manage the complete runtime state for a single execution instance of a `GraphDefinition`. This object is created by the `OrchestratorService` (or a similar top-level controller) and passed to, and mutated by, the `GraphExecutor` during a graph run.
- **Scope:** This component is primarily a data structure (TypeScript interface/type) with well-defined fields. It doesn't contain execution logic itself but is the central repository for all state related to one graph traversal.
- **Core Design Principles:**
    - **Comprehensive:** Must hold all information needed to run, pause, resume, and debug a graph execution.
    - **Serializable (Partially/Strategically):** Key parts must be serializable to JSON for persistence (e.g., via RxDB for `TaskSession`) if a graph needs to be paused and resumed across sessions. Non-serializable parts (like live service instances) will be re-injected on rehydration.
    - **Clear Structure:** Logically organized fields for easy understanding and access by the `GraphExecutor` and potentially by agents/tools (in a controlled manner).
    - **Support for V1 Features:** Must accommodate versioned node outputs, loop counters, HIL pause details, and error information.

**2. Dependencies**

- **Internal Types/Interfaces (from `src/common/types/`):**
    - `GraphDefinition` (referenced by ID and version).
    - `NodeOutputVersion` (for storing node execution results).
    - `LLMTaskCategory` (if any state is category-specific, unlikely for context itself).
    - `ClarificationDetails`, `StagedCanvasChangeOperation` (or similar types for HIL payloads - to be defined more precisely with HIL implementation).
    - `AgentDependencies` (type for the `services` object).
- **External Libraries:** None directly for the type definition.

**3. File Structure**

- `src/common/types/graph-execution.types.ts` (Or co-located in `graph.types.ts` if preferred, but separation might be good as it's runtime-specific).

**4. `NodeOutputVersion` Interface (Reiteration & Finalization)**

- **Purpose:** To store the result of a single execution of a graph node, including metadata for tracing and debugging.
- **Location:** `src/common/types/graph-execution.types.ts`

```tsx
import { ZodTypeAny } from 'zod'; // For schema of actual output if validated

export interface LLMTokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  modelName?: string; // Model used for this specific call
  cost?: number;      // Calculated cost for this specific call
}

export interface NodeOutputVersion {
  runId: string; // Unique ID for this specific execution/version of this node's output
  nodeId: string; // ID of the node that produced this output
  timestamp: number; // When this output was generated

  status: 'success' | 'failure' | 'interrupted_for_user_input' | 'in_progress_long_running'; // Added more granular statuses

  output?: any; // The actual data payload from the node (agent's Zod-validated payload, tool's result, transform's output)
                // This should ideally conform to a Zod schema known by the consuming nodes or the graph definition.

  error?: { // Present if status is 'failure'
    message: string;
    type?: string; // e.g., 'LLMError', 'ToolExecutionError', 'ValidationError', 'TimeoutError'
    stack?: string;
    details?: any; // Any additional structured error info
  };

  thought?: string; // Optional: reasoning from an agent, or notes from a tool/transform

  llmTokenUsage?: LLMTokenUsage; // If the node involved an LLM call directly (e.g., AgentCallNode)

  // For 'interrupted_for_user_input' status (payload defined by the interrupting agent)
  interruptPayload?: {
    interruptId: string; // Unique ID for this specific interruption instance
    type: string; // e.g., 'user_clarification_needed', 'user_approval_required'
    data: any; // Data needed by UI to present to user (e.g., question, options, diff)
    // Optional Zod schema (or its key) for validating the user's response upon resumption
    expectedResponseSchemaKey?: string;
  };

  // For 'in_progress_long_running' status (V2+)
  jobId?: string; // ID for an external long-running job
}

```

**5. `GraphExecutionContext` Interface Definition**

- **Location:** `src/common/types/graph-execution.types.ts`

```tsx
import { GraphDefinition, LLMTaskCategory } from './graph.types';
import { NodeOutputVersion, LLMTokenUsage } from './graph-execution.types'; // Assumes LLMTokenUsage is also here
import {
  LLMService, NodeSpecService, ToolExecutorService, RuleSelectorService,
  // Import types for RxDB Repositories if they are part of direct services
  // TaskSessionRepository, ChatRepository ... (or these are accessed via a higher-level DataService)
  PromptBuilderService // If agents use it directly via services
} from '@/background/services'; // Assuming path aliases for services

// Types for services injected into the execution context
// These are instances, not constructors.
export interface GraphExecutionServices {
  llmService: LLMService;
  nodeSpecService: NodeSpecService;
  toolExecutor: ToolExecutorService;
  ruleSelector: RuleSelectorService;
  promptBuilder: PromptBuilderService; // Making it available if agents need to build complex internal prompts
  // Potentially a generic dataService to access RxDB repositories if needed by transforms/conditions
  // dataService: { taskSessions: TaskSessionRepository, ... }
  // For V1, keep direct DB access out of here; tools/agents request data via specific mechanisms.

  // Function registry for condition/transform functions
  functionRegistry: ReadonlyMap<string /*functionKey*/, Function>;
}

export interface GraphExecutionContext {
  // --- Immutable Graph & Task Identifiers ---
  readonly graphDefinitionId: string;
  readonly graphDefinitionVersion: string;
  readonly taskSessionId: string; // Links back to the parent TaskSession managing this execution
  readonly parentGraphRunId?: string; // If this is a sub-graph execution

  // --- Unique ID for this specific run/instance of the graph ---
  readonly currentGraphRunId: string;

  // --- Overall Status of this Graph Execution Instance ---
  status:
    | 'initializing'
    | 'running'
    | 'paused_for_user_input'
    | 'paused_for_long_running_tool' // V2+
    | 'completed_success'
    | 'completed_failure'
    | 'terminated_by_user' // If user cancels
    | 'terminated_max_steps'; // If MAX_TOTAL_GRAPH_STEPS exceeded

  // --- Inputs & Constants ---
  readonly initialInputs: Readonly<Record<string, any>>; // Inputs provided when the graph execution started
  readonly graphConstants: Readonly<Record<string, any>>; // Constants defined in the GraphDefinition

  // --- Core State: Node Outputs (Versioned) ---
  // Map: Node ID -> Array of all execution attempts/versions for that node in this graph run.
  // The GraphExecutor always appends to this array. 'latest' outputVersion resolves to the last item.
  nodeOutputs: Map<string /* nodeId */, NodeOutputVersion[]>;

  // --- Execution Flow Control State (Managed by GraphExecutor) ---
  // Queue of node IDs to be processed next.
  // This is the primary driver for the executor's main loop.
  processingQueue: string[];
  // Set of node IDs that are currently being processed or have finished processing in the current "tick"
  // Used to detect if all prerequisites for a join-like node are met (V2+). For V1, queue is mostly sequential.
  // visitedInCurrentTick: Set<string>;

  // Total steps executed in this graph run (incremented by GraphExecutor for each node processed)
  totalStepsExecuted: number;

  // --- Loop Management State (Managed by GraphExecutor based on graph structure) ---
  // Map: loopId (defined in graph, e.g., by GraphBuilder.addLoop) -> current iteration counter
  loopCounters: Map<string /* loopId */, number>;
  // Map: loopId -> max iterations allowed for this loop instance (from GraphDefinition or addLoop override)
  maxLoopIterations: Map<string /* loopId */, number>;

  // --- Human-in-the-Loop (HIL) Pause State ---
  // Stores details when status is 'paused_for_user_input'
  activeInterruptDetails?: {
    interruptingNodeId: string; // ID of the node that signaled the interrupt
    interruptId: string;       // Unique ID of this specific interruption instance
    type: string;              // e.g., 'user_clarification_needed', 'user_approval_required'
    dataForUser: any;          // Data to display to the user (question, options, diff etc.)
    expectedResponseSchemaKey?: string; // Key to a Zod schema for validating user's response
  };

  // --- Error State ---
  // Stores the last critical error that led to graph failure or is being processed by an error path.
  lastCriticalError?: {
    failedNodeId: string;
    error: NodeOutputVersion['error']; // The structured error object from NodeOutputVersion
    timestamp: number;
  };

  // --- Injected Services (Read-only references) ---
  // These are live service instances provided by the OrchestratorService when creating the context.
  // They are NOT serialized if the context is persisted. Re-injected on rehydration.
  readonly services: GraphExecutionServices;

  // --- Sub-graph specific state (V2+) ---
  // If this context is for a sub-graph, this field might hold values it needs to return to its parent.
  // subGraphReturnValues?: Record<string, any>;

  // --- Mutable Scratchpad (Use with extreme caution, prefer explicit state via nodeOutputs) ---
  // For very specific, advanced scenarios where nodes might need to share transient, mutable state
  // within a single graph run without it being a formal versioned output.
  // For V1, aim to AVOID using this. Keep state flow through nodeOutputs.
  // scratchpad?: Record<string, any>;
}

```

**6. Key Design Choices & Rationale for `GraphExecutionContext`**

- **`nodeOutputs` as `Map<string, NodeOutputVersion[]>`:**
    - **Rationale:** This provides full history/versioning of every node's execution attempts within this graph run. Essential for debugging, for `outputVersion` resolution (latest, specific runId), and for complex retry/rollback logic if an agent needs to see its own prior output. `GraphExecutor` always appends to the array for a given `nodeId`.
- **`processingQueue: string[]`:**
    - **Rationale:** Explicitly models the GraphExecutor's agenda. Makes the control flow easier to understand and manage for the executor. Supports sequential execution primarily for V1. For V2, if parallel branches are introduced, this might evolve or be supplemented.
- **`loopCounters` and `maxLoopIterations`:**
    - **Rationale:** Centralized management of loop iterations by the `GraphExecutor`, driven by `loopId`s defined in the graph (e.g., by `GraphBuilder.addLoop`). Prevents infinite loops within structured loop constructs.
- **`activeInterruptDetails`:**
    - **Rationale:** Centralizes information for a HIL pause. The `GraphExecutor` populates this when an agent's output signals an interruption. The `OrchestratorService` reads this to interact with the UI.
- **`services: GraphExecutionServices`:**
    - **Rationale:** Clear dependency injection. Agents/tools/functions within the graph execution access shared services via this context field, not global imports. These are live instances, marked `readonly` on the context (though the services themselves might have state).
- **`lastCriticalError`:**
    - **Rationale:** Stores context about the error that caused a graph to enter a failure state or an error handling path. Useful for `defaultErrorHandlerNodeId` and for final graph result reporting.
- **No `loopVariables` directly on context (Revisiting from previous discussion):**
    - **Rationale:** We decided that loop-specific state (like accumulators, current item for processing) should flow through `nodeOutputs` using the explicit loop structures created by `GraphBuilder.addLoop` (which uses `DataTransformNode`s internally for state updates). This keeps `GraphExecutionContext` cleaner and data flow more traceable via `nodeOutputs`, rather than having a separate mutable `loopVariables` map. The `GraphBuilder.addLoop` will create `DataTransformNode`s whose outputs represent the evolving loop state.
- **Serialization:**
    - Fields like `services` (and any functions within `functionRegistry` if it were part of context) are not serializable. When persisting a `GraphExecutionContext` (as part of a `TaskSession`), these would be omitted and re-injected by the `OrchestratorService` upon rehydration.
    - `nodeOutputs` (containing potentially large data payloads) should be serializable.

**7. Testing Strategy**

- As this is primarily a type definition, testing involves ensuring its fields are correctly populated and used by:
    - `OrchestratorService` (when creating/rehydrating context).
    - `GraphExecutor` (when reading/mutating state during execution).
    - Agents/Tools (when accessing read-only parts like `services` or `initialInputs`).
- Validate that `GraphExecutionContext` can be (partially) serialized and deserialized correctly for pause/resume scenarios (test omitting and re-injecting `services`).

**8. Integration Points**

- Instantiated and managed by `OrchestratorService`.
- Passed to and heavily used/mutated by `GraphExecutor`.
- Read by agents, tools, condition functions, and transform functions to get their inputs and access services.
- Parts of it are persisted as part of `TaskSession` when the graph is paused or completed.