

**Implementation Plan: Core Graph Structure Types (`graph.types.ts`)**

**1. Component Overview**

*   **Purpose:** To define the fundamental TypeScript interfaces and types that represent the structure of an executable graph, its nodes, edges, and how nodes receive inputs.
*   **Scope:** These are pure type definitions. They do not contain any logic, only data structures.
*   **Core Responsibilities:**
    *   Define the overall `GraphDefinition`.
    *   Define the various types of `GraphNodeDefinition` (agent call, tool call, conditional, etc.).
    *   Define how node inputs are sourced via `GraphInputValueSource`.
    *   Define `GraphEdgeDefinition` for connections.
    *   Define related enums or utility types like `LLMTaskCategory`.

**2. Dependencies**

*   **External Libraries:**
    *   `zod` (optional, but highly recommended for schemas that will be used for runtime validation later, and for `GraphInputValueSource` to hint at expected data types). For pure type definitions, Zod isn't strictly necessary, but for a robust system, it's good to define Zod schemas alongside or as the source of these types.
*   **Internal Types/Interfaces:** None at this foundational level (this IS the foundation).

**3. File Structure**

*   `src/common/types/graph.types.ts` (Or simply `graph.ts` if it's the primary export for this module)

**4. Type/Interface Definitions (Step-by-Step)**

We'll build these up logically.

*   **A. `LLMTaskCategory` Enum (Already Discussed, Re-confirming):**
    ```typescript
    // src/common/types/llm.types.ts (or graph.types.ts if co-located)
    export enum LLMTaskCategory {
      CORE_ORCHESTRATION_AND_PLANNING = 'CORE_ORCHESTRATION_AND_PLANNING',
      N8N_CODE_GENERATION_COMPOSITION = 'N8N_CODE_GENERATION_COMPOSITION',
      KNOWLEDGE_AUGMENTED_EXPLANATION = 'KNOWLEDGE_AUGMENTED_EXPLANATION',
      ANALYTICAL_VALIDATION_CRITIQUE = 'ANALYTICAL_VALIDATION_CRITIQUE',
      UTILITY_CLASSIFICATION_ROUTING = 'UTILITY_CLASSIFICATION_ROUTING', // on_device_preferred potential
      UTILITY_CONVERSATION_SUMMARIZATION = 'UTILITY_CONVERSATION_SUMMARIZATION', // on_device_preferred potential
      // Add more categories as needed
    }
    ```

*   **B. `GraphInputValueSource` Type:**
    *   This is crucial as it defines how nodes get their data. We need to ensure the `fallbackSource` is well-defined here if the `GraphBuilder` is to use it.
    ```typescript
    // src/common/types/graph.types.ts
    import { z, ZodTypeAny } from 'zod'; // Assuming we use Zod for schema hinting

    // Forward declaration for recursive fallback type
    interface GraphInputValueSource_NodeOutput_Recursive; 

    export type GraphInputValueSource =
      | { 
          type: 'fromGraphInitialInput'; 
          path: string; // Key of the initial input
          zodSchema?: ZodTypeAny; // Optional Zod schema for this input
          defaultValue?: any; 
        }
      | { 
          type: 'fromNodeOutput'; 
          nodeId: string; // ID of the source node
          path?: string; // Optional path to a specific field in the source node's output object (e.g., 'payload.user_name')
          outputVersion?: 'latest' | number | string /* runId */; // Default to 'latest'
          zodSchema?: ZodTypeAny; // Optional Zod schema for the expected data type from this source
          defaultValue?: any; 
          fallbackSource?: GraphInputValueSource_NodeOutput_Recursive; // Fallback if primary source fails/is undefined
        }
      | { 
          type: 'fromGraphConstant'; 
          constantKey: string; // Key of a graph-level constant
          zodSchema?: ZodTypeAny;
        }
      | { 
          type: 'staticValue'; 
          value: any; 
          zodSchema?: ZodTypeAny;
        }
      // V2: Potentially 'fromGraphContextDirect' if we have a shared mutable context for some things,
      // but for V1, explicit node outputs are cleaner.
      // V2: Potentially 'fromMultipleNodeOutputs' if executor supports merging. Builder can abstract this.
      ;

    // Define the recursive part for fallbackSource to avoid TS errors
    interface GraphInputValueSource_NodeOutput_Recursive {
      type: 'fromNodeOutput'; 
      nodeId: string; 
      path?: string; 
      outputVersion?: 'latest' | number | string;
      zodSchema?: ZodTypeAny; 
      defaultValue?: any;
      // No deeper fallback from a fallback for simplicity
    }

    export interface GraphNodeInputMapping {
      [inputFieldName: string]: GraphInputValueSource;
    }
    ```

*   **C. `GraphNodeDefinition` Base and Specific Subtypes:**
    ```typescript
    // src/common/types/graph.types.ts
    // ... (imports and LLMTaskCategory, GraphInputValueSource)

    export interface BaseGraphNodeDefinition {
      id: string; // Unique within the graph
      description?: string;
      inputs?: GraphNodeInputMapping; // How this node receives its inputs
      timeoutMs?: number; // Optional timeout for this node's execution
    }

    export interface StartNodeDef extends BaseGraphNodeDefinition {
      type: 'start';
    }

    export interface EndNodeDef extends BaseGraphNodeDefinition {
      type: 'end';
      isErrorEndNode?: boolean; // If true, signifies this is a terminal error state for the graph
      // The 'inputs' to an EndNode define the final output of the graph if this EndNode is reached.
    }

    export interface AgentCallNodeDef extends BaseGraphNodeDefinition {
      type: 'agentCall';
      agentSlug: string; // Unique slug to identify the agent logic/class
      llmTaskCategory: LLMTaskCategory; // Specifies which LLM configuration to use
      // The specific Zod schema for this agent's output payload is not part of GraphDefinition
      // but is known by the agent itself and the PromptBuilderService for prompt injection.
    }

    export interface ToolCallNodeDef extends BaseGraphNodeDefinition {
      type: 'toolCall';
      toolName: string; // Unique name of the registered tool
    }

    export interface ConditionalBranchNodeDef extends BaseGraphNodeDefinition {
      type: 'conditionalBranch';
      // The actual conditionFunction (JS function) is not stored in the serializable GraphDefinition.
      // Instead, a key or reference to a registered function is stored.
      // The GraphExecutor will resolve this key to the actual function at runtime.
      conditionFunctionKey: string; 
      // The function signature would be: 
      // (inputs: Record<string, any>, graphContext: GraphExecutionContext) => string | string[];
      // Returns one or more edge labels to follow.
    }

    // For V1, loopCondition might be a specialized use of conditionalBranch
    // or the GraphBuilder's addLoop creates a structure using conditionalBranch internally.
    // We decided addLoop creates standard nodes, so no distinct LoopConditionNodeDef type needed here.

    export interface DataTransformNodeDef extends BaseGraphNodeDefinition {
      type: 'dataTransform';
      // Similar to conditionFunctionKey, stores a key to a registered transform function.
      // The function signature: 
      // (inputs: Record<string, any>, graphContext: GraphExecutionContext) => Record<string, any>;
      transformFunctionKey: string; 
    }
    
    // V2: SubGraphCallNodeDef
    // export interface SubGraphCallNodeDef extends BaseGraphNodeDefinition {
    //   type: 'subGraphCall';
    //   subGraphId: string; // ID of another GraphDefinition to execute
    // }

    export type GraphNodeDefinition =
      | StartNodeDef
      | EndNodeDef
      | AgentCallNodeDef
      | ToolCallNodeDef
      | ConditionalBranchNodeDef
      | DataTransformNodeDef
      // | SubGraphCallNodeDef; // For V2
      ;
    ```
    *   **Note on `conditionFunctionKey` and `transformFunctionKey`:** For the `GraphDefinition` to be truly serializable (e.g., to JSON), we cannot store actual JavaScript functions directly. Instead, we store string keys. The `GraphExecutor` (or a service it uses) will need a "Function Registry" to map these keys to the actual executable functions.
        *   `FunctionRegistry: Map<string, (...args: any[]) => any>`
        *   The `GraphBuilder` would register these functions with the registry when `addConditionalNode` or `addDataTransformNode` is called, or the user of the builder is responsible for ensuring functions are registered.

*   **D. `GraphEdgeDefinition` Interface:**
    ```typescript
    // src/common/types/graph.types.ts

    export interface GraphEdgeDefinition {
      id?: string; // Optional unique ID for the edge itself
      from: string; // Source GraphNodeDefinition.id
      to: string;   // Target GraphNodeDefinition.id
      label?: string; // e.g., 'onSuccess', 'onFailure', '__otherwise__', custom branch label from ConditionalBranchNodeDef
      description?: string;
      priority?: number; // Optional, for conditional branches if multiple edges could be chosen
    }
    ```

*   **E. `GraphDefinition` Interface (The Top-Level Structure):**
    ```typescript
    // src/common/types/graph.types.ts
    // ... (imports and other definitions)

    export interface GraphDefinition {
      id: string; // Unique ID for the graph type (e.g., 'createNewWorkflowGraph_v1')
      description: string;
      version: string; // For managing updates to graph structures

      entryNodeId: string; // ID of a node with type 'start'

      nodes: GraphNodeDefinition[];
      edges: GraphEdgeDefinition[];

      // Metadata for defining expected inputs when this graph is called
      expectedInitialInputs?: {
        [inputKey: string]: {
          description: string;
          zodSchema?: ZodTypeAny; // For validation by OrchestratorService/GraphExecutor
          isOptional?: boolean;
        };
      };

      // Static constants available throughout this graph's execution via GraphInputValueSource
      graphConstants?: {
        [constantKey: string]: any;
      };
      
      // Loop control defaults
      defaultMaxIterationsForAllLoops?: number;
      defaultMaxLoopIterations?: { // Per specific loopId defined in the graph
        [loopId: string]: number;
      };

      // Default error handling
      defaultErrorHandlerNodeId?: string; // ID of a node to route to if a node fails and has no specific onFailure edge
      // The mapping of error data to this default handler's inputs is simpler if GraphExecutor uses a standard error object.
      // defaultErrorInputMapping?: GraphNodeInputMapping; // This might be too complex for V1 default handling.
    }
    ```

**5. Testing Strategy (for these types)**

*   These are type definitions, so testing primarily involves ensuring they are used correctly by other components (like `GraphBuilder` and `GraphExecutor`).
*   If Zod schemas are defined alongside, unit tests for those Zod schemas can validate example data structures.
*   Ensure TypeScript compiles without errors when these types are used.

**6. Integration Points**

*   **`GraphBuilder`:** Consumes these types to construct `GraphDefinition` objects. Its fluent API methods will map to creating instances of these interfaces.
*   **`GraphExecutor`:** Takes a `GraphDefinition` as input and uses its structure to drive execution. It will read `GraphNodeDefinition`, `GraphEdgeDefinition`, and `GraphInputValueSource`.
*   **Serialization/Deserialization (Future):** If graph definitions are ever stored as JSON, these TypeScript types will guide the structure.
*   **Visualization Utility:** The `generateMermaidDiagram` function will consume a `GraphDefinition`.

