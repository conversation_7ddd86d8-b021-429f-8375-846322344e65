**SINGLE COMPONENT IMPLEMENTATION PLAN: LLMService.ts**

**1. COMPONENT OVERVIEW**

- **Purpose:** The LLMService is a centralized service responsible for all interactions with Large Language Models (LLMs) via the Vercel AI SDK Core (v5 alpha). It abstracts the complexities of LLM provider integration, model selection, prompt construction, context window management, token counting, cost estimation, error handling, and streaming standardized responses.
- **Scope:**
    - Handles all outgoing requests to LLMs.
    - Manages API key usage (retrieved from UserSettingsService).
    - Selects appropriate LLM models based on LLMTaskCategory and user preferences.
    - Integrates with Chrome AI (chrome.ai) for suitable tasks with fallback.
    - Converts internal message formats (UIMessage / TaskSession history) to Vercel AI SDK ModelMessage[].
    - Manages token counting for prompts and completions.
    - Estimates costs based on token usage and model pricing.
    - Implements retry logic for transient LLM API errors.
    - Provides standardized output wrappers for LLM responses (including content, tool calls, thoughts, errors, token usage).
    - Supports streaming of responses, including custom data parts for UI updates (e.g., agent thoughts, step progress).
    - Manages AbortController for cancellable LLM requests.
- **Dependencies (Conceptually, as these will be implemented later or mocked for unit testing LLMService):**
    - **Vercel AI SDK Core v5 alpha (@ai-sdk/core, specific model provider packages like @ai-sdk/openai, @ai-sdk/anthropic, etc.):** External library for LLM interaction.
    - **UserSettingsService (or direct access to UserSettingsRepository):** To retrieve user's API keys and model preferences per LLMTaskCategory.
    - **ChromeAIService (Conceptual wrapper around chrome.ai):** To check availability and make calls to on-device models like Gemini Nano.
    - **Tokenizer Library (e.g., gpt-tokenizer or a WASM-based tiktoken):** For client-side token counting to manage context windows before making API calls.
    - **Model Pricing Data:** A configuration source (e.g., a bundled JSON file) mapping { provider: { model_name: { input_cost_per_token, output_cost_per_token } } }.
    - **Zod Schemas:** For validating structured outputs from LLMs if response_format: { type: 'json_object', schema: ... } is used with Vercel AI SDK.
    - **UIMessage / ModelMessage types:** From Vercel AI SDK or our common types.
    - **LLMTaskCategory enum:** From src/common/types/graph.types.ts.
- **Dependents:**
    - PromptBuilderService: Uses LLMService to invoke n8n-summarizer-agent if context window needs to be managed.
    - All AI Agents (via BaseN8nAgent): Will use LLMService (likely injected into BaseN8nAgent or its AgentDependencies) to make their LLM calls.
    - GraphExecutor: Does not call LLMService directly, but provides it to agents.
- **Key Design Decisions (from our brainstorming):**
    - Centralized abstraction for all LLM calls.
    - Dynamic model selection based on LLMTaskCategory and user settings.
    - Prioritize Chrome AI for specific utility tasks if available and enabled.
    - Robust retry logic for LLM API calls.
    - Comprehensive token counting and cost estimation.
    - Streaming of text and custom UI data parts via Vercel AI SDK v5 capabilities.
    - Standardized error handling for LLM interactions.
- **Evolution Notes:**
    - Initially, we just thought of "using Vercel AI SDK."
    - This evolved into needing a dedicated service to manage the complexities of multiple providers, model selection, token limits, cost, retries, and standardized streaming.
    - Integration of Chrome AI as a preferred on-device option for certain tasks was added.
    - The use of Vercel AI SDK v5's UIMessage, metadata, and custom data parts for streaming became a core part of its design for rich UI feedback.

**2. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

- **System Architecture Overview:**
    
    Our system is an n8n AI Assistant designed as a client-side browser extension (eventually) with a core AI "brain" library built in TypeScript. This library can also run in Node.js. The AI's operations are orchestrated by a graph-based engine (GraphExecutor) that executes defined workflows (GraphDefinition). These workflows consist of nodes representing AI Agents, Tool Calls, or logical operations. AI Agents are specialized TypeScript classes that perform specific tasks (e.g., planning, code generation) by interacting with LLMs.
    
- **This Component's Role (LLMService):**
    
    The LLMService is the exclusive interface between the AI system (specifically, the AI Agents) and the underlying Large Language Models. It hides the complexities of using the Vercel AI SDK, managing different LLM providers/models, handling API keys, counting tokens, estimating costs, implementing retries, and formatting responses. No other part of the system makes direct calls to the Vercel AI SDK; they all go through LLMService.
    
- **Related Components:**
    - **AI Agents (BaseN8nAgent subclasses):** The primary consumers of LLMService. They call its methods to "think" or generate content.
    - **PromptBuilderService:** Prepares the ModelMessage[] array that agents pass to LLMService. It might also use LLMService to invoke a summarization agent if context history grows too large.
    - **UserSettingsService / UserSettingsRepository:** Provides API keys and user-defined model preferences (which model to use for which LLMTaskCategory).
    - **ChromeAIService:** Used by LLMService to attempt on-device model execution.
    - **Vercel AI SDK Core v5 alpha:** The external library LLMService wraps.
- **Design Philosophy:**
    - **Abstraction:** Shield the rest of the system from the specifics of LLM provider APIs.
    - **Centralization:** Manage all LLM interaction logic in one place for consistency and maintainability.
    - **Flexibility:** Allow users to choose their preferred models and providers.
    - **Cost & Performance Awareness:** Integrate token counting, cost estimation, and intelligent model selection for different task types.
    - **Resilience:** Implement robust error handling and retry mechanisms.
    - **Rich User Experience:** Support streaming of text and structured UI updates.
- **Technical Constraints:**
    - Must use Vercel AI SDK Core v5 alpha.
    - Must be multi-platform (browser, Node.js). For browser, consider main thread blocking for token counting if synchronous.
    - API keys are user-provided and must be handled securely (not logged, sent only to intended provider).

**3. EXTRACTED DESIGN DECISIONS**

1. **Decision:** Use Vercel AI SDK Core v5 alpha for all LLM interactions.
    - **Rationale:** Modern, well-supported SDK providing a unified API for multiple LLM providers, streaming capabilities, and tool usage support.
    - **Alternatives:** Direct provider SDKs (OpenAI, Anthropic) - would lead to more complex integration logic.
    - **Final Form:** Vercel AI SDK is the chosen interface.
2. **Decision:** Centralize all LLM calls through LLMService.
    - **Rationale:** Promotes separation of concerns, easier maintenance, consistent error handling, centralized token/cost tracking.
    - **Final Form:** No direct Vercel AI SDK calls outside LLMService.
3. **Decision:** Implement dynamic model selection based on LLMTaskCategory and user settings.
    - **Rationale:** Allows optimizing cost/performance/capability by using appropriate models for different tasks. Gives users control.
    - **Evolution:** Evolved from simple model choice to granular categories (CORE_ORCHESTRATION_AND_PLANNING, UTILITY_CLASSIFICATION_ROUTING, etc.).
    - **Final Form:** LLMService.invokeModel() takes taskCategory, looks up user preferences (from UserSettingsService), and selects model/provider.
4. **Decision:** Integrate Chrome AI (chrome.ai) for on-device processing for suitable tasks.
    - **Rationale:** Reduce latency, cost, and cloud dependency for simple, frequent tasks.
    - **Evolution:** Identified UTILITY_CLASSIFICATION_ROUTING and UTILITY_CONVERSATION_SUMMARIZATION as prime candidates.
    - **Final Form:** LLMService will attempt Chrome AI first for designated task categories if user enabled and available, with fallback to cloud LLMs.
5. **Decision:** Implement client-side token counting.
    - **Rationale:** To proactively manage context window limits before sending requests to LLMs, preventing errors and optimizing prompt construction.
    - **Final Form:** LLMService (or PromptBuilderService it uses) will use a tokenizer library before making calls.
6. **Decision:** Implement cost estimation.
    - **Rationale:** Provide transparency to users about API usage costs.
    - **FinalForm:** LLMService tracks input/output tokens per call, uses bundled model pricing data, and reports cost.
7. **Decision:** Implement retry logic for transient LLM API errors.
    - **Rationale:** Improve resilience against temporary network issues or API glitches.
    - **Final Form:** LLMService will use exponential backoff for a configurable number of retries on specific error types.
8. **Decision:** Standardize LLM response wrapping.
    - **Rationale:** Provide a consistent structure (LLMOutputWrapper) for agents to consume, regardless of LLM or success/failure.
    - **Final Form:** invokeModel methods return Promise<LLMOutputWrapper>.
9. **Decision:** Support streaming of text and custom UI data parts.
    - **Rationale:** Enhance user experience with real-time feedback.
    - **Final Form:** LLMService's streaming methods will leverage Vercel AI SDK v5's result.toUIMessageStreamResponse() and message.metadata to stream text and structured custom data parts (e.g., agent_thought_chunk, tool_call_attempt).

**4. TECHNICAL SPECIFICATIONS**

- **TypeScript Interfaces (src/background/services/llmService.types.ts or co-located):**
    
    `import {
      LanguageModelV2, // from @ai-sdk/core
      ModelMessage,    // from @ai-sdk/core
      ToolCall,        // from @ai-sdk/core
      ToolResult,      // from @ai-sdk/core
      CoreTool        // from @ai-sdk/core
    } from '@ai-sdk/core';
    import { LLMTaskCategory } from '@/common/types/graph.types.ts';
    import { UserSettings_ModelPreference } from '@/common/types/userSettings.types.ts'; // Assumed type
    
    export interface LLMTokenUsage {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    }
    
    export interface LLMCallCost {
      inputTokenCost: number;
      outputTokenCost: number;
      totalCost: number;
    }
    
    export interface LLMOutputDetails {
      modelProvider: string; // e.g., 'openai', 'anthropic', 'google', 'chrome-ai'
      modelName: string;     // e.g., 'gpt-4-turbo', 'claude-3-opus', 'gemini-nano'
      tokenUsage: LLMTokenUsage;
      cost?: LLMCallCost;
      finishReason?: string;
      latencyMs?: number;
    }
    
    // Wrapper for the result of an LLMService call
    export interface LLMInvocationResult<TContent = string | object> {
      status: 'success' | 'failure' | 'error_max_retries';
      content?: TContent; // Parsed content (string, or JSON object if structured output)
      toolCalls?: ToolCall[]; // If model requests tool calls
      text?: string; // Raw text response if applicable
      error?: {
        message: string;
        type: string; // e.g., 'APIError', 'RateLimitError', 'OutputValidationError'
        originalError?: any;
      };
      details: LLMOutputDetails; // Always present, even on failure (e.g., to show which model failed)
    }
    
    // Interface for the streaming Vercel AI SDK response format
    // This is what the UI will consume directly after being processed by toUIMessageStreamResponse
    // We don't need to redefine UIMessage, but our service provides the stream.
    
    // Options for invokeModel
    export interface InvokeModelOptions {
      systemPrompt?: string; // Prepended by PromptBuilderService
      tools?: CoreTool[];
      toolChoice?: 'auto' | 'none' | { type: 'tool'; toolName: string };
      responseFormat?: { type: 'json_object'; schema?: any } | { type: 'text' }; // For structured output
      temperature?: number;
      topP?: number;
      maxRetries?: number; // Override default
      abortSignal?: AbortSignal;
      // Any other provider-specific options Vercel AI SDK might expose
    }
    
    // For streaming structured UI updates
    export interface CustomUIMessageDataPart {
        type: 'agent_thought_chunk' | 'tool_call_initiated' | 'tool_result_received' | 'agent_step_completed' | 'custom_log';
        data: any;
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/********).TypeScript
    
- **External Dependencies:**
    - @ai-sdk/core: For LanguageModelV2, ModelMessage, ToolCall, ToolResult, CoreTool, streaming utilities.
    - @ai-sdk/openai, @ai-sdk/anthropic, @ai-sdk/google (or others as needed): Provider-specific SDKs.
    - chrome.ai types (if available, or define placeholders).
    - gpt-tokenizer (or similar, e.g., tiktoken via WASM): For client-side token counting.
    - Zod: For validating structured JSON output if responseFormat.schema is used.
- **Internal Dependencies (Services/Repositories/Types):**
    - UserSettingsService / UserSettingsRepository: To get API keys and model preferences.
    - ChromeAIService (Conceptual): To interact with chrome.ai.
    - ModelPricingData (JSON/object): For cost calculation.
    - LLMTaskCategory enum.
    - UIMessage (from Vercel SDK / @ai-sdk/react if used, or our definition).
- **Multi-Platform Requirements:**
    - **Node.js:** Vercel AI SDK provider packages work. Tokenizer needs Node-compatible version. chrome.ai is unavailable; ChromeAIService calls will gracefully fail or be skipped.
    - **Browser:** Vercel AI SDK provider packages work. Tokenizer might use WASM or a JS implementation. chrome.ai is potentially available. Main thread blocking by synchronous tokenizers needs consideration (use Web Worker for tokenizer if it's slow).
- **Configuration Schema (Conceptual, managed by UserSettingsService):**
    - apiKeys: { openai?: string, anthropic?: string, google?: string }
    - modelPreferences: { [key in LLMTaskCategory]?: UserSettings_ModelPreference }
        - UserSettings_ModelPreference: { provider: string, modelId: string, temperature?: number }
    - enableChromeAI: boolean
    - defaultMaxRetries: number (for LLMService)
- **Error Handling Contract:**
    - LLMService methods will return Promise<LLMInvocationResult>.
    - LLMInvocationResult.status will indicate 'success', 'failure' (after retries but specific issue like bad input), or 'error_max_retries' (if all retries exhausted on API/network errors).
    - LLMInvocationResult.error will contain structured error info.
    - Will throw specific custom errors for critical setup issues (e.g., MissingApiKeyError, ModelNotFoundError).

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (LLMService.ts)**

- **File Structure:**
    - src/background/services/llmService.ts (Main class)
    - src/background/services/llmService.types.ts (Interfaces defined above)
    - src/common/config/modelPricing.json (Or a .ts file exporting the object)
- **Import Strategy:**
    
    `import { 
        createOpenAI, createAnthropic, createGoogle // etc.
    } from '@ai-sdk/core/providers'; // Or specific provider packages
    import { 
        LanguageModelV2, ModelMessage, ToolCall, ToolResult, CoreTool, streamToResponse, formatStreamPart, // and other core types
        generateId, readDataStream,
        experimental_generateObject, experimental_streamObject, // if using these
        generateText, streamText // for simpler text/tool use
    } from '@ai-sdk/core';
    import { UIMessage, convertToModelMessages, createUIMessageStream } from '@ai-sdk/ui-utils'; // Or our own implementations if not using react part
    import { countTokens } from 'gpt-tokenizer'; // Or other tokenizer
    // ... other internal imports (UserSettingsService, ChromeAIService, types, enums, pricing)`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/********).TypeScript
    
- **LLMService Class Definition:**
    
    `export class LLMService {
      private userSettingsService: UserSettingsService;
      private chromeAIService: ChromeAIService;
      private modelPricingData: Record<string, any>; // Loaded from JSON/config
      private defaultMaxRetries: number;
      private activeLLMClients: Map<string, LanguageModelV2> = new Map(); // Cache for LLM clients
    
      constructor(
        userSettingsService: UserSettingsService,
        chromeAIService: ChromeAIService,
        // Tokenizer can be injected or instantiated here
      ) {
        this.userSettingsService = userSettingsService;
        this.chromeAIService = chromeAIService;
        this.modelPricingData = loadModelPricingData(); // Utility to load JSON
        this.defaultMaxRetries = userSettingsService.getSetting('defaultLlmServiceMaxRetries') || 3;
      }
    
      // --- Private Helper Methods ---
      private async getModelAndProvider(taskCategory: LLMTaskCategory): Promise<{ providerName: string, modelId: string, modelInstance?: LanguageModelV2, isChromeAI: boolean, effectiveTemperature?: number }> { /* ... */ }
      private getClientForProvider(providerName: string, apiKey?: string): LanguageModelV2 { /* ... */ }
      private calculateCost(modelProvider: string, modelId: string, promptTokens: number, completionTokens: number): LLMCallCost | undefined { /* ... */ }
      private countPromptTokens(messages: ModelMessage[]): number { /* ... use tokenizer ... */ }
    
      // --- Public API Methods ---
      public async invokeModel(
        taskCategory: LLMTaskCategory,
        messages: ModelMessage[], // Already prepared by PromptBuilderService
        options?: InvokeModelOptions
      ): Promise<LLMInvocationResult> { /* ... main logic with retries ... */ }
    
      public streamModelResponse(
        taskCategory: LLMTaskCategory,
        messages: ModelMessage[], // Already prepared
        options?: InvokeModelOptions
      ): ReadableStream<UIMessage> { /* ... streaming logic ... */ }
    
      // Maybe separate methods for structured output vs. text
      public async generateStructuredObject<T extends z.ZodTypeAny>(
        taskCategory: LLMTaskCategory,
        messages: ModelMessage[],
        outputSchema: T, // Zod schema for output
        options?: InvokeModelOptions
      ): Promise<LLMInvocationResult<z.infer<T>>> { /* Uses experimental_generateObject */ }
      
      public streamStructuredObject<T extends z.ZodTypeAny>(
        taskCategory: LLMTaskCategory,
        messages: ModelMessage[],
        outputSchema: T,
        options?: InvokeModelOptions
      ): ReadableStream<UIMessage> { /* Uses experimental_streamObject and toUIMessageStreamResponse */ }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/********).TypeScript
    
- **Core Logic Flow for invokeModel (Non-Streaming Example):**
    1. **getModelAndProvider(taskCategory) (Private Helper):**
        - Get userSettings = await this.userSettingsService.getSettings().
        - Get preference = userSettings.modelPreferences[taskCategory].
        - **Chrome AI Check:** If userSettings.enableChromeAI and taskCategory is suitable (e.g., UTILITY_CLASSIFICATION_ROUTING) and await this.chromeAIService.isAvailable():
            - Return { providerName: 'chrome-ai', modelId: 'gemini-nano', isChromeAI: true, effectiveTemperature: preference?.temperature }.
        - If preference exists:
            - Return { providerName: preference.provider, modelId: preference.modelId, isChromeAI: false, effectiveTemperature: preference?.temperature }.
        - **Fallback Logic:** If no preference for category, fallback to CORE_ORCHESTRATION_AND_PLANNING pref, then to a system default model/provider if defined, or throw ModelNotConfiguredError.
    2. **Token Counting (Pre-computation):**
        - promptTokens = this.countPromptTokens(messages).
        - (If context is too large for chosen model, this method might throw ContextWindowExceededError before API call - PromptBuilderService should ideally handle this before even calling LLMService).
    3. **Retry Loop (for i < maxRetries):**
        - maxRetries = options?.maxRetries || this.defaultMaxRetries.
        - startTime = Date.now().
        - **Handle Chrome AI Path:**
            - If isChromeAI:
                - response = await this.chromeAIService.generateText({ prompt: messages, temperature: effectiveTemperature, signal: options?.abortSignal }).
                - Construct LLMOutputDetails (provider 'chrome-ai', model 'gemini-nano', estimate tokens for Nano if API doesn't provide).
                - Construct LLMInvocationResult (status 'success', content is response). Return.
                - Catch errors from chromeAIService, if retryable and not last attempt, continue loop. If fallback configured for Chrome AI failure, try cloud path next.
        - **Handle Cloud LLM Path:**
            - apiKey = await this.userSettingsService.getApiKeyForProvider(providerName). Throw MissingApiKeyError if none.
            - client = this.getClientForProvider(providerName, apiKey) (caches clients).
            - **Vercel AI SDK Call:** (Example using generateText, adapt for experimental_generateObject etc.)
                
                `const result = await generateText({
                  model: client,
                  system: options?.systemPrompt, // PromptBuilder already put it in messages for main call
                  prompt: messages, // Or just messages if system is part of it
                  tools: options?.tools,
                  toolChoice: options?.toolChoice,
                  temperature: effectiveTemperature || options?.temperature,
                  // ... other options from InvokeModelOptions
                  signal: options?.abortSignal,
                });`
                
                **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/********).TypeScript
                
            - latencyMs = Date.now() - startTime.
            - completionTokens = this.countTokens(result.text). (Or use result.usage.completionTokens if available).
            - actualPromptTokens = result.usage.promptTokens (prefer API reported).
            - callCost = this.calculateCost(...).
            - details: LLMOutputDetails = { ... }.
            - If result.toolCalls: return LLMInvocationResult with toolCalls.
            - If options.responseFormat?.type === 'json_object' and options.responseFormat.schema:
                - Parse result.text as JSON.
                - Validate against options.responseFormat.schema using Zod. If fails, throw OutputValidationError.
            - Return LLMInvocationResult { status: 'success', content: result.text or parsedObject, details }.
        - **Catch Block (for Cloud LLM Path):**
            - Analyze error from Vercel SDK.
            - If rate limit, server error, network error (retryable): if (i < maxRetries - 1) { await sleep(backoff_duration); continue; } else { return LLMInvocationResult { status: 'error_max_retries', error: ..., details: ... } }.
            - If API key error, bad request (non-retryable): return LLMInvocationResult { status: 'failure', error: ..., details: ... }.
            - If OutputValidationError: return LLMInvocationResult { status: 'failure', error: { message: 'Output validation failed', type: 'OutputValidationError', originalError: zodError }, details: ... }.
    4. If loop finishes due to max retries, return status: 'error_max_retries'.
- **streamModelResponse(taskCategory, messages, options) Logic:**
    1. Similar setup: getModelAndProvider, token counting (for initial prompt only, as completion is streamed).
    2. **Handle Chrome AI Path (if streaming supported, might just be text):**
        - If this.chromeAIService.streamText exists, use it. Adapt stream to UIMessage format (text parts).
    3. **Handle Cloud LLM Path:**
        - Get client.
        - Use Vercel SDK's streamText or experimental_streamObject.
            
            `// Conceptual for experimental_streamObject
            const streamResult = experimental_streamObject({ model: client, messages, schema: options.responseFormat.schema, ... });
            
            // Use Vercel AI SDK's utility to convert to UIMessage stream
            // and inject our custom metadata and data parts
            const uiStream = createUIMessageStream();
            const writer = uiStream.getWriter();
            const finalDetails: LLMOutputDetails = { /* init with provider/model */};
            
            (async () => {
              try {
                let fullTextContent = ""; // For token counting if needed
                let finalObject: any = null;
                let toolCalls: ToolCall[] | undefined = undefined;
            
                // Example of injecting a custom part at the beginning
                writer.write({ type: 'custom-data-part', data: { type: 'agent_invocation_start', data: { agentSlug: '...', taskId: '...' } } satisfies CustomUIMessageDataPart });
                
                for await (const part of streamResult) {
                  if (part.type === 'text-delta') {
                    writer.write({ type: 'text', value: part.textDelta });
                    fullTextContent += part.textDelta;
                  } else if (part.type === 'object-delta') {
                    // If streaming partial objects, can also send custom UI updates
                    writer.write({ type: 'custom-data-part', data: { type: 'partial_object_update', data: part.objectDelta }});
                  } else if (part.type === 'finish') {
                    finalDetails.finishReason = part.finishReason;
                    finalDetails.tokenUsage = part.usage; // API reported usage
                    finalObject = part.object; // For experimental_streamObject
                    toolCalls = part.toolCalls; // For tool usage
                  } else if (part.type === 'tool-calls') { // Standard tool calls part
                     writer.write({ type: 'tool-calls', toolCalls: part.toolCalls });
                     toolCalls = part.toolCalls;
                  }
                  // Handle other part types: error, tool-results etc.
                  // Potentially yield thoughts from metadata stream here if Vercel SDK supports it directly
                }
                // After loop, attach our final metadata
                 writer.write({ 
                    type: 'message-metadata', 
                    data: { 
                        llmDetails: finalDetails, 
                        cost: this.calculateCost(...finalDetails.tokenUsage),
                        // any other final metadata
                    }
                });
            
              } catch (e: any) {
                writer.write({ type: 'error', error: e });
              } finally {
                writer.close();
              }
            })();
            return uiStream.getReader().pipeThrough(new TextDecoderStream()); // Or whatever format UI expects`
            
            **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/********).TypeScript
            
        - The key is to correctly use toUIMessageStreamResponse or manually construct the UIMessage stream, injecting message.metadata with LLMOutputDetails and cost, and inserting custom CustomUIMessageDataParts for agent thoughts/steps by writing to the stream writer.
- **getClientForProvider(providerName, apiKey) Helper:**
    - If this.activeLLMClients.has(providerName_modelId_apiKeyHash), return cached client.
    - Else, switch (providerName):
        - case 'openai': client = createOpenAI({ apiKey }).
        - case 'anthropic': client = createAnthropic({ apiKey }).
        - Cache and return.
- **Error Handling:** Wrap calls in try/catch. Standardize errors into LLMInvocationResult.error.
- **AbortSignal:** Pass options.abortSignal to Vercel AI SDK calls.

**6. INTEGRATION SPECIFICATIONS**

- **Input Contracts:**
    - invokeModel / streamModelResponse expect taskCategory: LLMTaskCategory, messages: ModelMessage[], options?: InvokeModelOptions.
    - messages are assumed to be fully prepared by PromptBuilderService.
- **Output Contracts:**
    - invokeModel returns Promise<LLMInvocationResult>.
    - streamModelResponse returns ReadableStream<UIMessage> (or similar stream of Vercel AI SDK parts).
- **Service Injection:** LLMService constructor takes UserSettingsService, ChromeAIService. Instances are typically managed by a DI container or a central service locator and provided to agents via their AgentDependencies.
- **Configuration:** Relies on UserSettingsService for API keys and model preferences. Relies on bundled modelPricing.json.

**7. QUALITY ASSURANCE REQUIREMENTS**

- **Unit Testing Strategy:**
    - Mock UserSettingsService to return various configs (OpenAI only, Anthropic only, ChromeAI enabled/disabled, specific model prefs).
    - Mock ChromeAIService to simulate availability/unavailability and responses/errors.
    - Mock Vercel AI SDK provider functions (createOpenAI, etc.) to return mock LanguageModelV2 instances.
    - Mock the generateText, streamText, experimental_generateObject, experimental_streamObject methods of these mock models to return various responses:
        - Successful text/JSON.
        - Successful with tool calls.
        - API errors (rate limit, auth error, server error).
        - Malformed JSON (for structured output).
    - Mock tokenizer (countTokens).
    - **Test Cases:**
        - getModelAndProvider: Test all preference/fallback logic, including Chrome AI selection.
        - getClientForProvider: Test client creation and caching.
        - calculateCost: Test cost calculation for different models/token counts.
        - invokeModel:
            - Successful call for each provider path (OpenAI, Anthropic, ChromeAI).
            - Successful call with tool usage.
            - Successful structured JSON output (with Zod validation).
            - Retry logic: test it retries on specific errors and eventually succeeds or hits max retries.
            - Error handling: test it correctly wraps various API errors into LLMInvocationResult.
            - Test AbortSignal propagation.
            - Test token counting and cost estimation in output.
        - streamModelResponse / streamStructuredObject:
            - Mock the Vercel SDK stream methods.
            - Verify the output stream produces correctly formatted UIMessage parts (text, metadata, custom parts).
            - Test error handling during streaming.
            - Test AbortSignal.
- **Integration Testing (Conceptual, with other services):**
    - Test with a real (but mocked) PromptBuilderService providing messages.
    - Test with a mock Agent making calls to LLMService.
- **Performance Criteria:** Non-streaming calls should have reasonable overhead beyond the actual LLM API latency. Streaming should start quickly.
- **Logging Strategy:** Log key events: model selection, API call initiation, success, failure (with error details), retries, token/cost info. Use a structured logger.

**8. VALIDATION CRITERIA**

- All methods implement their described functionality.
- Model selection logic correctly uses LLMTaskCategory, user settings, Chrome AI preference, and fallbacks.
- Retry mechanism works for designated errors up to max retries.
- Token counts and cost estimations are accurate.
- Output structures (LLMInvocationResult, streamed UIMessages with metadata/custom parts) match specifications.
- Error conditions are handled gracefully and reported in the standardized output.
- AbortSignal correctly cancels requests.
- Works in both Node.js and Browser (with appropriate mocks for chrome.ai in Node).
- API keys are not exposed or logged.