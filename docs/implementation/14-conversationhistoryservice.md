1 What we need from Elasticsearch
A single ES query that:

keeps the current layer filters (search term, price, color … anything already chosen);

splits the result by category_ids so we know which categories belong in the facet;

returns the first hit of every bucket so we can grab its image.

Elasticsearch gives us all of that with a terms bucket + a top_hits sub-aggregation:

jsonc
Copier
Modifier
{
  "size": 0,
  "query": { ...  the exact bool-filter Magento already built ... },
  "aggs": {
    "by_category": {
      "terms": {
        "field": "category_ids",
        "size": 500        // > 200 to be safe – remember ES defaults to 10  
      },
      "aggs": {
        "sample": {
          "top_hits": {
            "size": 1,
            "_source": ["entity_id", "small_image"]
          }
        }
      }
    }
  }
}
top_hits is made for exactly this purpose – “give me the top document per bucket” .

2 Make Magento build that query
Magento’s stock XML request doesn’t know top_hits, so add it once and reuse it everywhere:

2.1 Extend the search XSD (once)
xml
Copier
Modifier
<!-- etc/search_request.xsd in your module -->
<xsd:complexType name="topHitsMetric" mixed="true">
    <xsd:attribute name="xsi:type" type="xsd:string" fixed="topHits"/>
    <xsd:attribute name="size" type="xsd:positiveInteger" use="optional"/>
    <xsd:attribute name="_source" type="xsd:string" use="optional"/>
</xsd:complexType>
2.2 Plugin the request builder
php
Copier
Modifier
class AddTopHitsToCategoryAggregation
{
    public function aroundBuild(
        \Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation $subject,
        \Closure $proceed,
        BucketInterface $bucket
    ) {
        $result = $proceed($bucket);

        if ($bucket->getType() === 'termBucket' && $bucket->getField() === 'category_ids') {
            $result['aggs']['sample'] = [
                'top_hits' => [
                    'size'   => 1,
                    '_source' => ['entity_id', 'small_image'] // keep light
                ]
            ];
        }
        return $result;
    }
}
That plugin fires only for the category bucket of the same layered-navigation request Magento already sends, so we do no extra call.

Tip – add the image field to the ES index
Indexers ignore media attributes by default. Either mark small_image “Searchable = Yes” in admin, or patch
\Magento\CatalogSearch\Model\Indexer\Fulltext\Action\DataProvider to add it to the document .

3 Read the images out of the aggregation
php
Copier
Modifier
class CategoryThumbProvider
{
    public function __construct(
        \Magento\Catalog\Model\Layer\Resolver $layerResolver,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {}

    public function getMap(): array
    {
        /** @var \Magento\Catalog\Model\Layer $layer */
        $layer       = $this->layerResolver->get();
        $collection  = $layer->getProductCollection();      // already executed once
        $aggs        = $collection->getSearchResult()->getAggregations();
        $buckets     = $aggs->getBucket('by_category')->getValues();

        $base        = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);

        $map = [];
        foreach ($buckets as $bucket) {
            $hit = $bucket->getSubAggregation('sample')->getDocuments()[0];
            $img = $hit->getSource()['small_image'] ?? null;
            $map[$bucket->getValue()] = $img ? $base . 'catalog/product' . $img : null;
        }
        return $map; //  category_id => image URL (or null)
    }
}
Because top_hits already gave us the ID and small_image string inside the ES response, we don’t touch MySQL at all.
If you prefer the full product model (for swatches, etc.) just collect the entity_ids and run one extra Product\Collection with addAttributeToSelect('small_image').

4 Wire the data into the sidebar
Inject CategoryThumbProvider into Magento\LayeredNavigation\Block\Navigation via a preference or a view-model.

In layer/filter.phtml (or Hyvä’s component) read:

php
Copier
Modifier
$thumbs = $block->getCategoryThumbs();  // returns the map above
foreach ($filter->getItems() as $item) {
    $catId = $item->getValue();
    $img   = $thumbs[$catId] ?? $placeholder;
    /* render <img src="<?= $escaper->escapeUrl($img) ?>" …> */
}
Cache the computed map in a private block using the same layer state key Magento already generates, so subsequent AJAX filter changes reuse it (no extra ES call).

5 Why this scales to 200+ categories
One ES query – the same query Magento already fires for layered nav; we merely ask for one extra metric.

No N + 1 – top_hits gives you 200 docs in a single response body.

Small payload – size = 1, _source = ["small_image", "entity_id"], so only two tiny strings per category.

No extra SQL if you indexed small_image; at worst, one SQL query for 200 IDs.

Facet limits – remember to raise terms.size above 200 or ES will truncate buckets .

Performance-wise this is indistinguishable from a regular layered-nav call; you just transferred a few extra kilobytes.

Recap
Index the image attribute.

Add a top_hits sub-aggregation to the existing category bucket.

Read the hit per bucket → build category → image map.

Render the thumbnail next to each category link.

You get category thumbnails with virtually zero overhead and no avalanche of database queries – exactly what you need when the facet list runs into the hundreds.






Vous avez dit :
Will it work with opensearch? Which is now the default search engine starting from Magento 2.4.8


ChatGPT a dit :

Thought for 1m 26s
Yes — the exact same strategy works when the store is on OpenSearch (the new default search engine from 2.4.8), you just need to point your plugin at the OpenSearch classes instead of the Elasticsearch ones.
1 Why nothing changes in the query itself
OpenSearch is a fork of Elasticsearch 7.10, so the terms bucket + top_hits sub-aggregation we rely on is still 100 % supported and syntactically identical. See the OpenSearch docs for top_hits
OpenSearch Documentation
.

Magento 2.4.8 simply swaps the backend adapter from Magento\Elasticsearch… to Magento\OpenSearch…, but the layered-navigation request that Magento builds (the Search API RequestInterface) and the JSON it sends over the wire are unchanged.

2 Namespace tweaks you must make
Part of the solution	Elasticsearch setup (2.4.7-)	OpenSearch setup (2.4.8+)
Aggregation-builder plugin	Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation	Magento\OpenSearch\SearchAdapter\Query\Builder\Aggregation
Indexer data-provider patch
(to put small_image into the index)	Magento\Elasticsearch\Model\Indexer\Fulltext\DataProvider	Magento\OpenSearch\Model\Indexer\Fulltext\DataProvider
Search-engine code in di.xml	<argument name="engine" xsi:type="string">elasticsearch7</argument>	<argument name="engine" xsi:type="string">opensearch</argument>

Tip – Keep two plugins in the same module (one for each class) so your code works on both ES and OpenSearch and survives further upgrades.

3 Concrete code changes
3.1 Plugin registration (etc/di.xml)
xml
Copier
Modifier
<!-- For OpenSearch -->
<type name="Magento\OpenSearch\SearchAdapter\Query\Builder\Aggregation">
    <plugin name="add_top_hits_to_category_bucket"
            type="Vendor\Module\Plugin\AddTopHitsToCategoryAggregation"/>
</type>

<!-- Keep ES fallback for < 2.4.8 -->
<type name="Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation">
    <plugin name="add_top_hits_to_category_bucket"
            type="Vendor\Module\Plugin\AddTopHitsToCategoryAggregation"/>
</type>
3.2 Indexing the image attribute
OpenSearch’s full-text index is built by
Magento\OpenSearch\Model\Indexer\Fulltext\DataProvider.
Create a preference or an after-plugin that adds the small_image attribute to the $documentData array before it is returned.

php
Copier
Modifier
public function afterGetProductDataArray(
    \Magento\OpenSearch\Model\Indexer\Fulltext\DataProvider $subject,
    array $result
) {
    $result['small_image'] = $subject->getAttributeValue($this->product, 'small_image');
    return $result;
}
Re-run bin/magento indexer:reindex catalogsearch_fulltext once so every product now carries its image path in OpenSearch.

4 No other adjustments needed
Query size / bucket size – OpenSearch defaults are the same; keep terms.size ≥ 200 as before.

Security plugin – the hard-coded Magento client talks to OpenSearch over plain HTTP. If you enable the OpenSearch security plugin you must proxy or disable auth exactly as for core search calls (not specific to our feature).

Performance – identical: one layered-nav request, same JSON body, plus the top_hits payload (a couple of kilobytes for 200 categories).

5 Proof that OpenSearch is now the default
Adobe’s 2.4.8 release notes explicitly list “OpenSearch 2” as the default catalog search engine. 
Experience League

Bottom line
Just retarget the class names in your plugin and in your indexer patch; the aggregation technique itself is fully supported in OpenSearch and performs the same as it did on Elasticsearch.









Sources
Vous avez dit :
Can we specify the sort order to use to fetch the first product using top hits? We have a custom sort field in products collections and we have to use this to be sure to get the first product based on that sort orser


ChatGPT a dit :

Thought for 1m 33s
Yes — top_hits lets you decide exactly which product becomes “first” by adding a sort clause, and Magento can inject that clause on the fly so you still keep the single-query, single-response strategy.
1 Make sure your custom field is in the OpenSearch index
Product attribute

If the field is an EAV attribute (for example custom_rank)

set Used for Sorting in Product Listing = Yes

set Searchable = Yes (or patch the DataProvider to add it manually).

Re-index: bin/magento indexer:reindex catalogsearch_fulltext.

OpenSearch will now store one numeric/keyword column called custom_rank for every product document, so it can be used in both query-level and top_hits sorting.

Mapping sanity-check

Numeric → type: long (doc_values = true by default).

Keyword → type: keyword (already sortable).

2 Add a sort stanza to the existing top_hits sub-aggregation
The JSON OpenSearch needs (unchanged boilerplate omitted):

jsonc
Copier
Modifier
"aggs": {
  "by_category": {
    "terms": {
      "field": "category_ids",
      "size": 500               // or whatever covers all facet buckets
    },
    "aggs": {
      "sample": {
        "top_hits": {
          "size": 1,
          "sort": [
            { "custom_rank": { "order": "asc", "missing": "_last" } }
          ],
          "_source": ["entity_id", "small_image", "custom_rank"]
        }
      }
    }
  }
}
sort is an officially-supported option of top_hits; by default the aggregation sorts by relevance score, but you can override it with one or more fields .

3 Wire it into Magento 2.4.8 (OpenSearch)
You already have the plugin that adds the top_hits. Change it so it also injects the sort block:

php
Copier
Modifier
class AddTopHitsToCategoryAggregation
{
    public function aroundBuild(
        \Magento\OpenSearch\SearchAdapter\Query\Builder\Aggregation $subject,
        \Closure $proceed,
        \Magento\Framework\Search\Request\BucketInterface $bucket
    ) {
        $result = $proceed($bucket);

        if ($bucket->getType() === 'termBucket' && $bucket->getField() === 'category_ids') {
            // pick current sort OR default to your custom field
            $field = $this->getCurrentSortField();          // see §4
            $dir   = $this->getCurrentDirection();          // 'asc' / 'desc'

            $result['aggs']['sample'] = [
                'top_hits' => [
                    'size' => 1,
                    'sort' => [
                        [ $field => ['order' => $dir, 'missing' => '_last'] ]
                    ],
                    '_source' => ['entity_id', 'small_image', $field]
                ]
            ];
        }
        return $result;
    }
}
The plugin is registered for both Magento\OpenSearch\…Aggregation (2.4.8+) and the old Magento\Elasticsearch\… class for backward compatibility.

4 Using the same sort the user chose
The layered-navigation request builder has no idea which order the toolbar is currently using, so the plugin needs to read it:

php
Copier
Modifier
private function getCurrentSortField(): string
{
    // easiest: read it straight from the request
    $param = $this->request->getParam('product_list_order');
    return $param ?: 'custom_rank';
}

private function getCurrentDirection(): string
{
    $dir = $this->request->getParam('product_list_dir');
    return ($dir === 'desc') ? 'desc' : 'asc';
}
If you only ever want the custom field, just hard-code 'custom_rank' / 'asc'.

5 Corner-cases
Case	How to handle
Field missing for some products	keep "missing": "_last" so empty values fall to the end.
Category default “position” sort	Category position isn’t indexed (it’s per-category). If you truly need “first product by position per category”, index a field like cat_{{ID}}_position during indexing or fall back to MySQL after getting the IDs.
Very large facet lists (≫ 200 categories)	the extra cost is trivial: OpenSearch sorts inside each bucket only until it finds one doc and returns one small source doc per bucket.
Multiple priority fields	pass an array in sort (OpenSearch will apply in order of precedence) .

6 Result consumption (unchanged)
The PHP that turns buckets into “category → image URL” now sees the chosen product already sorted by custom_rank, so no extra work:

php
Copier
Modifier
$hit = $bucket->getSubAggregation('sample')->getDocuments()[0];
Summary
Index the custom sort attribute.

Expand the same top_hits sub-agg Magento already fires: add a sort array pointing at that attribute (or whatever the current toolbar order is).

Use the returned hit per bucket exactly as before.

OpenSearch fully supports the sort option in top_hits, and Magento’s plugin architecture lets you slip that clause into the existing layered-navigation query without adding a single extra round-trip.






Vous avez dit :
Great! Can you now build the whole source code for this (no need for the front end part) and implementation plan?


ChatGPT a dit :
Below is a turn-key Magento 2.4.8 Open Source module that

adds the product image path (small_image) and your custom sort field (custom_rank) to the OpenSearch/Elasticsearch index;

injects a top_hits sub-aggregation with a configurable sort clause into the layered-navigation “category” bucket;

leaves the rest of Magento’s request flow untouched (still one search call per page).

The module contains no storefront code (templates, JS), because you said you will handle the rendering yourself.

0 Folder layout
pgsql
Copier
Modifier
app
└── code
    └── Vendor
        └── CategoryThumb
            ├── registration.php
            ├── etc
            │   ├── module.xml
            │   └── di.xml
            └── Plugin
                ├── AddImageAndRankToIndex.php
                └── AddTopHitsToCategoryAggregation.php
You can replace Vendor with your real vendor name.

1 Module bootstrap
registration.php
php
Copier
Modifier
<?php
use Magento\Framework\Component\ComponentRegistrar;

ComponentRegistrar::register(
    ComponentRegistrar::MODULE,
    'Vendor_CategoryThumb',
    __DIR__
);
etc/module.xml
xml
Copier
Modifier
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
    <module name="Vendor_CategoryThumb" setup_version="1.0.0"/>
</config>
2 Dependency-injection configuration
etc/di.xml
xml
Copier
Modifier
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- ────────────────────────────────────────────────────────────── -->
    <!-- 1.  Add 'small_image'       + 'custom_rank' to the search doc -->
    <!-- ────────────────────────────────────────────────────────────── -->

    <!--  OpenSearch (default from 2.4.8)  -->
    <type name="Magento\OpenSearch\Model\Indexer\Fulltext\DataProvider">
        <plugin name="vendor_catthumb_addimg"
                type="Vendor\CategoryThumb\Plugin\AddImageAndRankToIndex" />
    </type>

    <!--  Keep backward compatibility with Elasticsearch  -->
    <type name="Magento\Elasticsearch\Model\Indexer\Fulltext\DataProvider">
        <plugin name="vendor_catthumb_addimg"
                type="Vendor\CategoryThumb\Plugin\AddImageAndRankToIndex" />
    </type>

    <!-- ────────────────────────────────────────────────────────────── -->
    <!-- 2.  Inject top_hits into the category facet bucket            -->
    <!-- ────────────────────────────────────────────────────────────── -->

    <!--  OpenSearch query builder  -->
    <type name="Magento\OpenSearch\SearchAdapter\Query\Builder\Aggregation">
        <plugin name="vendor_catthumb_tophits"
                type="Vendor\CategoryThumb\Plugin\AddTopHitsToCategoryAggregation" />
    </type>

    <!--  Elasticsearch query builder (≤ 2.4.7)  -->
    <type name="Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation">
        <plugin name="vendor_catthumb_tophits"
                type="Vendor\CategoryThumb\Plugin\AddTopHitsToCategoryAggregation" />
    </type>

</config>
3 Plugin #1 – add the two fields to the search index
Plugin/AddImageAndRankToIndex.php
php
Copier
Modifier
<?php
declare(strict_types=1);

namespace Vendor\CategoryThumb\Plugin;

use Magento\Catalog\Api\Data\ProductInterface;

/**
 * Inject the image path and custom ranking field into every document
 * before it is sent to OpenSearch/Elasticsearch.
 */
class AddImageAndRankToIndex
{
    public function afterBuildDocument(
        object $subject,      // DataProvider from OS / ES
        array  $documentData,
        ProductInterface $product
    ): array {
        // ─── small_image ────────────────────────────────────────────
        $image = $product->getData('small_image');
        if ($image && $image !== 'no_selection') {
            $documentData['small_image'] = $image;
        }

        // ─── custom_rank  (replace with your real attribute code) ──
        $rank = $product->getData('custom_rank');
        if ($rank !== null) {
            $documentData['custom_rank'] = (int) $rank;
        }

        return $documentData;
    }
}
Notes

If custom_rank is not an EAV attribute but a custom table column, fetch it however you normally do.

Make sure custom_rank is declared integer (or keyword) and used in sorting in Stores → Attributes → Product.

After installing the module run:

bash
Copier
Modifier
bin/magento setup:upgrade
bin/magento indexer:reindex catalogsearch_fulltext
so OpenSearch receives the new fields.

4 Plugin #2 – insert top_hits with a sort clause
Plugin/AddTopHitsToCategoryAggregation.php
php
Copier
Modifier
<?php
declare(strict_types=1);

namespace Vendor\CategoryThumb\Plugin;

use Closure;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Search\Request\BucketInterface;

/**
 * Around-plugin on the JSON aggregation builder.
 * Adds a `top_hits` metric to the `category_ids` bucket and tells
 * OpenSearch which field/direction to use for choosing the "first" product.
 */
class AddTopHitsToCategoryAggregation
{
    /** default values if the toolbar isn't present */
    private const DEFAULT_SORT_FIELD = 'custom_rank';
    private const DEFAULT_SORT_DIR   = 'asc';

    public function __construct(private RequestInterface $request) {}

    /**
     * @param array<string, mixed> $result
     * @return array<string, mixed>
     */
    public function aroundBuild(
        object $subject,               // Aggregation builder (OS or ES)
        Closure $proceed,
        BucketInterface $bucket
    ): array {
        $result = $proceed($bucket);

        if ($bucket->getType() !== 'termBucket' ||
            $bucket->getField() !== 'category_ids') {
            return $result;            // not the category facet → skip
        }

        [$field, $dir] = $this->resolveSort();

        $result['aggs']['sample'] = [
            'top_hits' => [
                'size' => 1,
                'sort' => [
                    [ $field => ['order' => $dir, 'missing' => '_last'] ]
                ],
                '_source' => ['entity_id', 'small_image', $field]
            ]
        ];

        return $result;
    }

    /** @return string[] { field, dir } */
    private function resolveSort(): array
    {
        $field = $this->request->getParam('product_list_order')
              ?: self::DEFAULT_SORT_FIELD;

        $dir   = $this->request->getParam('product_list_dir') === 'desc'
              ? 'desc' : self::DEFAULT_SORT_DIR;

        return [$field, $dir];
    }
}
Works for both toolbar-selected sort orders or your hard-coded default.
If you only ever want custom_rank ASC, delete resolveSort() and return the constants directly.

5 Implementation / deployment plan
Step	Command / Action	Purpose
1. Copy code	Place the folder tree under app/code.	
2. Enable module	bin/magento setup:upgrade	Registers module & DI configuration.
3. Re-index search	bin/magento indexer:reindex catalogsearch_fulltext	Pushes small_image + custom_rank into the OpenSearch index.
4. Flush caches	bin/magento cache:flush	Loads new plugins without stale config.
5. Verify ES/OS mapping	curl -X GET 'http://<host>:9200/<index>/_mapping?pretty'	Ensure small_image = keyword, custom_rank = long.
6. Navigate to any anchor category / search page	DevTools → Network → the /_search call	The JSON should now contain:
"aggs" : { "by_category" : { … "aggs" : { "sample" : { "top_hits" : { "sort" : … }}}}}
7. Frontend (out of scope)	Use $bucket->getSubAggregation('sample') in PHP or GraphQL to pull
small_image and paint your thumbnails.	

Performance & safety checklist
Zero extra requests – only the body of the existing layered-nav search call is larger by a few KB.

Terms size – if you can have > 500 sub-categories, increase terms.size.

Custom field absence – products missing custom_rank fall to the end (missing:"_last").

Compatible with both OpenSearch 2+ and Elasticsearch 7.10 – dual DI wiring.

No core overrides / preferences – everything sits in plugins; Magento upgrades stay safe.

Drop these files into your repo, deploy, and you’re ready to show category thumbnails chosen by your own ranking logic without any extra SQL or extra search traffic.






Vous avez dit :
Ok, now let’s say we wanted to implement this BUT for a new “visual categories” filter that we want to add to tge layered navigation, where we want to regroup categories with the same name together, count the products matching all grouped categories + first image of those categories.
Our catgegories structure is like so:

-> Root store category
--> “Home” category
-—> “chairs” category
-—> “tables” category 
-—> “beds” category
--> “office” category
-—> “chairs” category
-—> “office chairs” category

In the example above, we want to display in our visual categories filter:
- “chairs” with first product image matching the office chairs and home chairs + count of products in those categories (if 10 products in home/chairs and 20 in office/chairs, but 5 products are in both categories, should count 25 products)
- “tables”
- “office chairs”
- “beds”

How could we implement this in the most efficient way possible? Knowing that we could have more than 200+ sub-categories


ChatGPT a dit :

Thought for 1m 24s
Below is a complete backend-only module that adds a brand-new “Visual categories” filter to layered navigation.

All categories with the same storefront name are merged into one facet bucket ( _home / chairs + _office / chairs ⇒ “chairs” ).

Product counts are deduplicated, so an SKU that sits in both “chairs” branches is still counted once.

Every bucket returns one thumbnail (the first product according to your custom ranking field custom_rank).

Everything runs in one OpenSearch request, thanks to a terms + top_hits aggregation ( OpenSearch supports top_hits.sort just like Elasticsearch).
Remember that terms.size defaults to 10; we raise it to 1000 so hundreds of buckets still come back in a single response.

0 Directory tree
pgsql
Copier
Modifier
app/code
└── Vendor
    └── VisualCategory
        ├── registration.php
        ├── etc
        │   ├── module.xml
        │   └── di.xml
        └── Plugin
            ├── AddVisualLabelToIndex.php
            └── AddVisualCategoryAggregation.php
        └── Model
            └── Layer
                └── Filter
                    └── VisualCategory.php
(Replace Vendor with your vendor name.)

1 Module skeleton
registration.php
php
Copier
Modifier
<?php
use Magento\Framework\Component\ComponentRegistrar;

ComponentRegistrar::register(
    ComponentRegistrar::MODULE,
    'Vendor_VisualCategory',
    __DIR__
);
etc/module.xml
xml
Copier
Modifier
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
    <module name="Vendor_VisualCategory" setup_version="1.0.0"/>
</config>
2 DI configuration
etc/di.xml
xml
Copier
Modifier
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- 1️⃣ Add the “visual_category” multi-value keyword to every document -->
    <type name="Magento\OpenSearch\Model\Indexer\Fulltext\DataProvider">
        <plugin name="vendor_visualcategory_index"
                type="Vendor\VisualCategory\Plugin\AddVisualLabelToIndex"/>
    </type>
    <!-- fallback for ES ≤ 2.4.7 -->
    <type name="Magento\Elasticsearch\Model\Indexer\Fulltext\DataProvider">
        <plugin name="vendor_visualcategory_index"
                type="Vendor\VisualCategory\Plugin\AddVisualLabelToIndex"/>
    </type>

    <!-- 2️⃣ Push our custom “visual_category” terms aggregation + top_hits -->
    <type name="Magento\OpenSearch\SearchAdapter\Query\Builder\Aggregation">
        <plugin name="vendor_visualcategory_tophits"
                type="Vendor\VisualCategory\Plugin\AddVisualCategoryAggregation"/>
    </type>
    <type name="Magento\Elasticsearch\SearchAdapter\Query\Builder\Aggregation">
        <plugin name="vendor_visualcategory_tophits"
                type="Vendor\VisualCategory\Plugin\AddVisualCategoryAggregation"/>
    </type>

    <!-- 3️⃣ Expose the filter to Layered Navigation -->
    <type name="Magento\Catalog\Model\Layer\FilterList">
        <arguments>
            <argument name="filters" xsi:type="array">
                <item name="visual_category"
                      xsi:type="string">Vendor\VisualCategory\Model\Layer\Filter\VisualCategory</item>
            </argument>
        </arguments>
    </type>
</config>
3 Indexer plugin – build the visual_category field
Plugin/AddVisualLabelToIndex.php
php
Copier
Modifier
<?php
declare(strict_types=1);

namespace Vendor\VisualCategory\Plugin;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;

class AddVisualLabelToIndex
{
    public function __construct(private CategoryRepositoryInterface $categoryRepo) {}

    public function afterBuildDocument(
        object $subject,                      // DataProvider (OS/ES)
        array  $data,
        ProductInterface $product
    ): array {
        $labels = [];

        foreach ($product->getCategoryIds() as $catId) {
            try {
                $cat = $this->categoryRepo->get($catId, $product->getStoreId());
            } catch (\Exception $e) {
                continue;                     // deleted category etc.
            }

            /* leaf only? uncomment if wanted
            if ((int)$cat->getChildrenCount() > 0) { continue; }
            */

            // Normalise: lower-case, trim – adjust to taste
            $labels[] = mb_strtolower(trim($cat->getName()));
        }

        if ($labels) {
            $data['visual_category'] = array_values(array_unique($labels));
        }

        // Also keep image & ranking for the later top_hits
        $image = $product->getData('small_image');
        if ($image && $image !== 'no_selection') {
            $data['small_image'] = $image;
        }
        $rank = $product->getData('custom_rank');
        if ($rank !== null) {
            $data['custom_rank'] = (int)$rank;
        }

        return $data;
    }
}
visual_category is stored as a multi-value keyword. Counts are therefore deduplicated automatically (one document ⇒ one vote).

Re-index once:

bash
Copier
Modifier
bin/magento setup:upgrade
bin/magento indexer:reindex catalogsearch_fulltext
4 Aggregation plugin – add terms + top_hits
Plugin/AddVisualCategoryAggregation.php
php
Copier
Modifier
<?php
declare(strict_types=1);

namespace Vendor\VisualCategory\Plugin;

use Closure;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Search\Request\BucketInterface;

/**
 * Adds a `terms` aggregation on visual_category and a `top_hits` metric
 * with custom sorting so the first product per bucket is deterministic.
 */
class AddVisualCategoryAggregation
{
    private const FIELD = 'visual_category';
    private const TERMS_SIZE = 1000;          // safe for 200+ buckets
    private const DEFAULT_SORT_FIELD = 'custom_rank';
    private const DEFAULT_SORT_DIR   = 'asc';

    public function __construct(private RequestInterface $request) {}

    public function aroundBuild(
        object $subject,
        Closure $proceed,
        BucketInterface $bucket
    ): array {
        $result = $proceed($bucket);

        // Inject ONLY ONCE – when Magento builds the root bool query bucket
        if ($bucket->getType() !== 'boolQuery') {
            return $result;
        }

        [$sortField, $sortDir] = $this->resolveSort();

        $result['aggs']['visual_categories'] = [
            'terms' => [
                'field' => self::FIELD,
                'size'  => self::TERMS_SIZE
            ],
            'aggs'  => [
                'sample' => [
                    'top_hits' => [
                        'size' => 1,
                        'sort' => [
                            [$sortField => ['order' => $sortDir, 'missing' => '_last']]
                        ],
                        '_source' => ['entity_id', 'small_image', $sortField]
                    ]
                ]
            ]
        ];

        return $result;
    }

    private function resolveSort(): array
    {
        return [
            $this->request->getParam('product_list_order') ?: self::DEFAULT_SORT_FIELD,
            $this->request->getParam('product_list_dir') === 'desc' ? 'desc' : self::DEFAULT_SORT_DIR
        ];
    }
}
5 Filter model – make the facet appear & work
Model/Layer/Filter/VisualCategory.php
php
Copier
Modifier
<?php
declare(strict_types=1);

namespace Vendor\VisualCategory\Model\Layer\Filter;

use Magento\Catalog\Model\Layer\Filter\AbstractFilter;
use Magento\Framework\App\RequestInterface;

class VisualCategory extends AbstractFilter
{
    public const REQUEST_PARAM = 'visual_category';   // ?visual_category=chairs

    public function __construct(
        \Magento\Catalog\Model\Layer\Filter\ItemFactory        $itemFactory,
        RequestInterface                                       $request,
        \Magento\Framework\UrlInterface                        $urlHelper,
        array $data = []
    ) {
        parent::__construct($itemFactory, $request, $urlHelper, $data);
        $this->_requestVar = self::REQUEST_PARAM;
    }

    /** Apply filter to product collection (field is already keyword) */
    public function apply(RequestInterface $request)
    {
        $value = $request->getParam(self::REQUEST_PARAM);
        if (!$value) {
            return $this;
        }
        $this->getLayer()->getProductCollection()
                         ->addFieldToFilter('visual_category', $value);
        $this->setValue($value);
        $this->getLayer()->getState()->addFilter(
            $this->_createItem(ucfirst($value), $value)
        );
        return $this;
    }

    /** Provide buckets → Filter items */
    protected function _getItemsData(): array
    {
        $faceted = $this->getLayer()->getProductCollection()
                                    ->getFacetedData('visual_category');

        $data = [];
        foreach ($faceted as $label => $stats) {
            $data[] = [
                'label' => ucfirst($label),
                'value' => $label,
                'count' => (int) $stats['count']
            ];
        }
        return $data;
    }

    public function getName(): string
    {
        return __('Visual Categories');
    }
}
This class is tiny because all heavy work (terms + counts + image) is already done by OpenSearch.

6 How you read the thumbnail list
Wherever you render the filter:

php
Copier
Modifier
/** @var \Magento\Catalog\Model\Layer $layer */
$layer   = $block->getLayer();                       // from Navigation block
$aggs    = $layer->getProductCollection()
                 ->getSearchResult()
                 ->getAggregations()
                 ->getBucket('visual_categories')
                 ->getValues();

$mediaBase = $block->getBaseUrl('media') . 'catalog/product';

$thumbMap = [];
foreach ($aggs as $bucket) {
    $name  = $bucket->getKey();                      // e.g. "chairs"
    $hit   = $bucket->getSubAggregation('sample')->getDocuments()[0];
    $img   = $hit->getSource()['small_image'] ?? null;
    $thumbMap[$name] = $img ? $mediaBase . $img : null;
}

/* $thumbMap = [ 'chairs' => 'https://…/a/b/abc.jpg', … ]  */
(Front-end output is outside this answer, by request.)

7 Installation / rollout steps
#	Action
1	Copy the folder under app/code.
2	bin/magento setup:upgrade – registers module.
3	bin/magento indexer:reindex catalogsearch_fulltext – pushes visual_category, small_image, custom_rank to OpenSearch.
4	bin/magento cache:flush
5	Open any anchor category ➜ DevTools ➜ observe one _search call now containing:
"aggs":{"visual_categories":{"terms":…,"aggs":{"sample":{"top_hits":…}}}}
6	Implement a small .phtml (or Hyvä component) that loops over $filter->getItems() and adds the thumbnail from $thumbMap next to each label.

8 Performance characteristics
Single OpenSearch request – we piggy-back on Magento’s existing layered-navigation call; only the body grows by top_hits results.

Counts are correct: each product document contributes once to its visual_category bucket, so duplicates across “chairs” branches don’t inflate the figure.

>200 categories OK – terms.size = 1000, well under the 65 536 bucket limit.

No extra SQL – all we added to the product index are three lightweight keyword/long fields.

References
OpenSearch top_hits.sort syntax

Default terms bucket size and raising it

Pattern for custom layered-navigation filters in Magento 2**SINGLE COMPONENT IMPLEMENTATION PLAN: ConversationHistoryService.ts**

**1\. COMPONENT OVERVIEW**

-   **Purpose:** The ConversationHistoryService is responsible for retrieving, managing, and preparing conversation history (including a persisted summary and recent messages) to be included in LLM prompts. Its key function is to ensure the history fits within a specified token budget, orchestrating summarization if the context becomes too large.

    -   **Scope:**

    -   Accepts context identifiers (e.g., chatId, taskId) and token budget parameters.

        -   Retrieves the persisted llmContextSummary and summaryLastMessageId for a given TaskSession (via TaskSessionRepository).

        -   Fetches recent ChatMessages from the ChatRepository that occurred after the summaryLastMessageId.

        -   Assembles a candidate history (summary + recent messages).

        -   Calculates the token count of this candidate history.

        -   **If the token count exceeds the available budget:**

        -   It orchestrates a summarization call:

            -   Uses GenericPromptBuilder to construct a specific prompt for the n8n-summarizer-agent (using predefined summarizer prompt parts/templates from src/engine/prompts/parts/).

                -   Uses LLMService to invoke the n8n-summarizer-agent with this prompt.

                -   Receives the new textual summary.

                -   Updates the TaskSession (via TaskSessionRepository) with the new llmContextSummary and the ID of the last message included in the summarization input (newSummaryLastMessageId).

            -   It then re-assembles a (now shorter) history using the new summary and a few very recent messages.

        -   Formats the final selected history messages (summary + recent turns that fit) into a ModelMessage[] array.

        -   Returns the history messages, the (potentially updated) TaskSession object, and a flag indicating if summarization occurred.

    -   **Dependencies:**

    -   TaskSessionRepository: To read/update TaskSession (specifically llmContextSummary and summaryLastMessageId).

        -   ChatRepository: To fetch ChatMessages for a given chat/task.

        -   LLMService: To invoke the n8n-summarizer-agent LLM call.

        -   GenericPromptBuilder: To construct the prompt for the summarizer agent.

        -   AgentConfigRegistry (or similar config source): To get details for n8n-summarizer-agent (its slug, LLMTaskCategory, core instruction if it has one beyond the summarizer prompt parts).

        -   src/engine/prompts/parts/summarizer.parts.ts (and potentially other parts): For the static prompt templates/fragments used to build the summarizer's prompt.

        -   Tokenizer functions (TokenCountFunction, ChatTokenCountFunction): For calculating token counts.

        -   TaskSession, ChatMessage, ModelMessage types.

        -   ContextWindowConfig (from promptBuilder.types.ts or a shared config): For thresholds like maxHistoryMessagesAfterSummarization.

    -   **Dependents:**

    -   AgentPromptBuilder.ts: Will be the primary consumer, calling getHistoryForPrompt() to get the managed conversation history segment.

    -   **Key Design Decisions:**

    -   Centralized ownership of conversation history retrieval and summarization orchestration.

        -   Summarization is triggered transparently when needed to fit token budgets.

        -   TaskSession is the source of truth for persisted summaries.

        -   ChatRepository is the source of truth for raw message history.

        -   Uses GenericPromptBuilder and LLMService as utilities for the summarization sub-task.

    -   **Evolution Notes:**

    -   This service's role became much clearer after we decided to decouple pure prompt assembly (GenericPromptBuilder) from stateful context management.

        -   It takes the burden of summarization orchestration away from TaskSessionManager (whose role is now more about general task lifecycle) and PromptBuilderService (which is now GenericPromptBuilder and stateless).

**2\. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

-   **System Architecture Overview:**\
    Our n8n AI Assistant processes user requests through AI-driven workflows (graphs). Effective LLM interaction requires providing relevant conversation history. TaskSession objects store task-specific state, including an llmContextSummary of past interactions. Raw messages are stored in a ChatMessage collection. The LLMService handles actual LLM calls. The GenericPromptBuilder assembles prompt pieces into ModelMessage arrays.

    -   **This Component's Role (**\
    The ConversationHistoryService is a specialized service responsible for providing a token-budget-aware segment of conversation history (a summary plus recent messages) ready for inclusion in an LLM prompt. When a component like AgentPromptBuilder needs to build a full prompt, it will ask this service for the historical context. ConversationHistoryService will retrieve the latest summary for the task and recent messages. If the combined token count of this history exceeds the allowed budget (passed as a parameter, considering tokens already used by other prompt parts), this service will autonomously:

    -   Construct a prompt to instruct a summarizer LLM (the n8n-summarizer-agent persona).

        -   Use the LLMService to get a new, more concise summary.

        -   Update the TaskSession's persisted summary via TaskSessionRepository.

        -   Then, provide a new history segment (the new summary + a few very recent messages) that fits the budget.\
        It effectively ensures that the conversational context given to other agents is always as rich as possible while respecting token limits.

    -   **Related Components:**

    -   **:** Used to read and update llmContextSummary and summaryLastMessageId in TaskSession documents.

        -   **:** Used to fetch the raw ChatMessages that need to be included as recent history or summarized.

        -   **:** Used by this service to build the specific prompt that will be sent to the n8n-summarizer-agent LLM call.

        -   **:** Used by this service to execute the LLM call for the n8n-summarizer-agent.

        -   **:** The primary consumer of this service.

        -   **Prompt Parts from ** Specifically, summarizer.parts.ts for building the summarizer's prompt.

    -   **Design Philosophy:**

    -   **Encapsulation:** All logic related to fetching, managing, and summarizing conversation history for prompts is contained here.

        -   **Transparency:** Summarization should be largely transparent to the ultimate caller (e.g., AgentPromptBuilder), which just receives a ready-to-use history block.

        -   **Efficiency:** Minimize redundant summarizations by tracking summaryLastMessageId.

        -   **State Ownership:** Directly interacts with repositories to update persisted summaries.

    -   **Technical Constraints:**

    -   Asynchronous operations due to DB and LLM calls.

        -   Must handle potential failures in the summarization LLM call gracefully.

        -   Relies on accurate token counting (via injected tokenizers).

**3\. EXTRACTED DESIGN DECISIONS**

-   **Decision:** Create a dedicated ConversationHistoryService for managing history and summarization.

    -   **Rationale:** Clear separation of concerns. Decouples AgentPromptBuilder and GenericPromptBuilder from the stateful and potentially LLM-intensive process of summarization.

        -   **Final Form:** export class ConversationHistoryService { ... }.

    -   **Decision:** Summarization is triggered when fetched history (summary + recent) exceeds a dynamically provided tokensAvailableForHistory budget.

    -   **Rationale:** Ensures history fits within the overall prompt budget for the main LLM call.

        -   **Final Form:** Logic within getHistoryForPrompt method.

    -   **Decision:** ConversationHistoryService orchestrates the summarization call itself using GenericPromptBuilder (for the summarizer's prompt) and LLMService (to call the n8n-summarizer-agent).

    -   **Rationale:** Keeps the summarization sub-process self-contained within this service.

        -   **Final Form:** Internal _performSummarization method.

    -   **Decision:** Persisted TaskSession stores llmContextSummary (string) and summaryLastMessageId.

    -   **Rationale:** Efficiently stores the summary and avoids re-summarizing already processed messages.

        -   **Final Form:** These fields are read from and updated in the TaskSession via TaskSessionRepository.

    -   **Decision:** After summarization, the returned history block for the main prompt consists of the new summary PLUS a few very recent (post-summary) messages.

    -   **Rationale:** Provides both condensed long-term context and immediate short-term context.

        -   **Final Form:** Logic in getHistoryForPrompt to select these messages.

**4\. TECHNICAL SPECIFICATIONS**

-   **TypeScript Interfaces/Types (**

    ```
    // src/services/types/conversationHistoryService.types.ts
    import { ModelMessage } from 'ai';
    import { TaskSession } from '@/types/tasks.types'; // Adjusted path
    import { LLMTaskCategory } from '@/engine/graph/types/graph.types';

    export interface GetHistoryOptions {
      targetModelContextWindow: number;
      // Max tokens specifically budgeted for the history part of the prompt
      // (already accounts for tokens used by system message, current agent task, etc.)
      tokensAvailableForHistory: number;
      // Max number of raw recent messages to keep AFTER a summarization event.
      maxMessagesToKeepAfterSummary?: number;
      // Max number of raw recent messages to consider for summarization input if no prior summary exists.
      maxRecentMessagesForInitialSummaryInput?: number;
      // Summarizer agent details
      summarizerAgentSlug?: string;
      summarizerTaskCategory?: LLMTaskCategory;
    }

    export interface HistoryForPromptResult {
      historyMessages: ModelMessage[]; // Ready for prompt (summary + recent turns)
      summaryWasPerformedThisCall: boolean;
      // The TaskSession object, potentially updated with a new summary and summaryLastMessageId
      updatedTaskSession: TaskSession;
      finalHistoryTokenCount: number;
    }

    // Internal type for the _performSummarization method result
    interface InternalSummarizationOutcome {
        success: boolean;
        newSummaryText?: string;
        summarizerTokenUsage?: LLMTokenUsage; // Assuming LLMTokenUsage type
        error?: string;
    }
    ```

    content_copydownload

    Use code [with caution](https://support.google.com/legal/answer/13505487).TypeScript

    -   ** Class:**

    ```
    // src/services/ConversationHistoryService.ts
    import { TaskSessionRepository } from '@/persistence/repositories/TaskSessionRepository';
    import { ChatRepository } from '@/persistence/repositories/ChatRepository'; // New Dependency
    import { LLMService } from '@/engine/services/LLMService';
    import { GenericPromptBuilder } from '@/engine/services/GenericPromptBuilder';
    import { AgentConfigRegistry, getAgentConfig } from '@/engine/agents/agentRegistry'; // For summarizer agent config
    import { PromptPartLoader } from '@/engine/prompts/promptPartLoader'; // New: Utility to load prompt parts
    import { TaskSession, ChatMessage } from '@/types'; // Combined import
    import { ModelMessage } from 'ai';
    import { GetHistoryOptions, HistoryForPromptResult, InternalSummarizationOutcome } from './types/conversationHistoryService.types';
    import { TokenCountFunction, ChatTokenCountFunction, ContextWindowConfig, DEFAULT_CONTEXT_WINDOW_CONFIG } from '@/engine/services/types/promptBuilder.types';

    export class ConversationHistoryService {
      private taskSessionRepo: TaskSessionRepository;
      private chatRepo: ChatRepository;
      private llmService: LLMService;
      private genericPromptBuilder: GenericPromptBuilder;
      private agentConfigs: AgentConfigRegistry;
      private promptPartLoader: PromptPartLoader; // For SUMMARIZER_SYSTEM_INSTRUCTION etc.
      private readonly countChatTokens: ChatTokenCountFunction;
      private readonly contextWindowConfig: ContextWindowConfig;

      constructor(
        taskSessionRepo: TaskSessionRepository,
        chatRepo: ChatRepository,
        llmService: LLMService,
        genericPromptBuilder: GenericPromptBuilder,
        agentConfigs: AgentConfigRegistry,
        promptPartLoader: PromptPartLoader,
        tokenizer: { countChatTokens: ChatTokenCountFunction },
        contextWindowConfig?: ContextWindowConfig
      ) { /* ... assign dependencies ... */ }

      public async getHistoryForPrompt(
        chatId: string,
        taskId: string,
        options: GetHistoryOptions
      ): Promise<HistoryForPromptResult> { /* ... */ }

      private async _fetchRelevantRawMessages(
        chatId: string,
        summaryLastMessageId?: string,
        limit?: number
      ): Promise<ChatMessage[]> { /* ... */ }

      private _selectMessagesToSummarize(
        rawMessages: ChatMessage[],
        currentSummary?: string
      ): { messagesForSummarizerInput: ModelMessage[], lastMessageIdInSummarizerInput?: string } { /* ... */ }

      private async _performSummarization(
        taskSession: TaskSession, // Pass full session for context and potential update
        messagesForSummarizerInput: ModelMessage[],
        currentSummaryToBuildOn?: string,
        summarizerSlug?: string,
        summarizerCategory?: LLMTaskCategory
      ): Promise<InternalSummarizationOutcome> { /* ... */ }
    }
    ```

    content_copydownload

    Use code [with caution](https://support.google.com/legal/answer/13505487).TypeScript

    -   ** (New Utility):**

    -   A simple utility to load static string constants from src/engine/prompts/parts/*.ts.

        -   loadPromptPart(key: 'SUMMARIZER_SYSTEM_INSTRUCTION' | 'SUMMARIZER_USER_TASK_TEMPLATE' | ...): string.

    -   **External Dependencies:** ai (for ModelMessage), Zod.

    -   **Internal Dependencies:** As listed in class constructor.

**5\. ULTRA-DETAILED IMPLEMENTATION GUIDE (**

-   **File Structure:** src/services/ConversationHistoryService.ts

    -   **Constructor:**

    -   Injects TaskSessionRepository, ChatRepository, LLMService, GenericPromptBuilder, AgentConfigRegistry, PromptPartLoader, tokenizer functions (countChatTokens), and ContextWindowConfig (defaulting to DEFAULT_CONTEXT_WINDOW_CONFIG).

    -   **:**

    -   Destructure options: targetModelContextWindow, tokensAvailableForHistory, maxMessagesToKeepAfterSummary (default from this.contextWindowConfig), maxRecentMessagesForInitialSummaryInput (default e.g., 20).

        -   Fetch currentTaskSession = await this.taskSessionRepo.getSessionById(taskId). If not found, throw TaskSessionNotFoundError.

        -   currentSummary = currentTaskSession.llmContextSummary;

        -   summaryLastMsgId = currentTaskSession.summaryLastMessageId;

        -   Fetch recent raw messages: recentRawMessages = await this._fetchRelevantRawMessages(chatId, summaryLastMsgId, maxRecentMessagesForInitialSummaryInput);

        -   **Attempt to build history without new summarization:**

        -   candidateHistoryMessages: ModelMessage[] = [];

            -   If currentSummary, add it: candidateHistoryMessages.push({ role: 'assistant', content: \## Previous Conversation Summary\n${currentSummary}` });`

            -   Iterate recentRawMessages (from oldest to newest within this recent batch), convert to ModelMessage, add to candidateHistoryMessages one by one, checking this.countChatTokens(candidateHistoryMessages) against tokensAvailableForHistory. Stop if budget exceeded. Let finalHistoryForPrompt be the messages that fit.

        -   currentHistoryTokenCount = this.countChatTokens(finalHistoryForPrompt).

        -   **Check if summarization is needed:**

        -   needsSummarization = options.forceSummarization || (currentHistoryTokenCount > tokensAvailableForHistory && recentRawMessages.length > 0);

            -   (Also consider if recentRawMessages itself is very long, indicating the current summary is too stale, even if the current combination fits due to few recent messages).

        -   If needsSummarization:

        -   const { messagesForSummarizerInput, lastMessageIdInSummarizerInput } = this._selectMessagesToSummarize(recentRawMessages, currentSummary); (This method decides which messages + old summary go into the summarizer call).

            -   summarizationOutcome = await this._performSummarization(currentTaskSession, messagesForSummarizerInput, currentSummary, options.summarizerAgentSlug, options.summarizerTaskCategory);

            -   If summarizationOutcome.success && summarizationOutcome.newSummaryText:

            -   newSummary = summarizationOutcome.newSummaryText;

                -   Update currentTaskSession:

                -   currentTaskSession.llmContextSummary = newSummary;

                    -   currentTaskSession.summaryLastMessageId = lastMessageIdInSummarizerInput || currentTaskSession.summaryLastMessageId; // Update high-water mark

                    -   currentTaskSession.updatedAt = new Date();

                -   savedSession = await this.taskSessionRepo.saveSession(currentTaskSession);

                -   **Re-assemble **

                -   Start with newSummary.

                    -   Add a few very recent messages from recentRawMessages that occurred after lastMessageIdInSummarizerInput and still fit within tokensAvailableForHistory (considering new summary size). Use maxMessagesToKeepAfterSummary.

                -   finalHistoryTokenCount = this.countChatTokens(finalHistoryForPrompt).

                -   Return { historyMessages: finalHistoryForPrompt, summaryWasPerformedThisCall: true, updatedTaskSession: savedSession, finalHistoryTokenCount }.

            -   Else (summarization failed):

            -   Log error. Return history assembled in step 6 (best effort, might be truncated), summaryWasPerformedThisCall: false, and original currentTaskSession.

        -   Else (no summarization needed):

        -   Return { historyMessages: finalHistoryForPrompt, summaryWasPerformedThisCall: false, updatedTaskSession: currentTaskSession, finalHistoryTokenCount: currentHistoryTokenCount }.

    -   ** (Private):**

    -   Uses this.chatRepo.getMessagesForChatSince(chatId, summaryLastMessageId, { limit, sort: 'asc' /* oldest first for processing */ }).

    -   ** (Private):**

    -   Converts rawMessages to ModelMessage[].

        -   Determines lastMessageIdInSummarizerInput from the last message in rawMessages.

        -   Returns { messagesForSummarizerInput: convertedMessages, lastMessageIdInSummarizerInput }. (The prompt for summarizer will prepend currentSummary).

    -   ** (Private):**

    -   summarizerSlug = effectiveSummarizerSlug || this.config.defaultSummarizerAgentSlug;

        -   summarizerCategory = effectiveTaskCategory || this.config.defaultSummarizerTaskCategory;

        -   summarizerAgentConfig = getAgentConfig(summarizerSlug); If not found, return failure.

        -   Load summarizer prompt parts:

        -   systemInstruction = this.promptPartLoader.loadPromptPart('SUMMARIZER_SYSTEM_INSTRUCTION_V1');

            -   userTaskTemplate = this.promptPartLoader.loadPromptPart('SUMMARIZER_USER_TASK_TEMPLATE_V1');

        -   Format messagesForSummarizerInput into a single string block.

        -   Fill userTaskTemplate with currentSummaryToBuildOn and the formatted history string.

        -   Use this.genericPromptBuilder.buildMessages(...) to create ModelMessage[] for the summarizer, passing the systemInstruction and the filled userTaskTemplate.

        -   llmResult = await this.llmService.invokeModel(summarizerCategory, summarizerMessages, { temperature: 0.2, maxOutputTokens: this.contextWindowConfig.maxSummaryTokens });

        -   If llmResult.status === 'success' && llmResult.text, return success with newSummaryText and token/cost details.

        -   Else, return failure with error details.

**6\. INTEGRATION SPECIFICATIONS**

-   Constructor injection for all dependencies.

    -   AgentPromptBuilder calls getHistoryForPrompt().

    -   Internally uses TaskSessionRepository, ChatRepository, LLMService, GenericPromptBuilder, PromptPartLoader, AgentConfigRegistry.

**7\. QUALITY ASSURANCE REQUIREMENTS**

-   **Unit Testing Strategy:**

    -   Mock all injected dependencies (TaskSessionRepository, ChatRepository, LLMService, GenericPromptBuilder, AgentConfigRegistry, PromptPartLoader, tokenizers).

        -   **Test **

        -   No summary, few recent messages (fits budget) -> returns recent messages, no summarization.

            -   Existing summary, few recent messages (fits budget) -> returns summary + recent, no summarization.

            -   No summary, many recent messages (exceeds budget) -> triggers summarization, updates session, returns new summary + few post-summary messages.

            -   Existing summary, many recent messages (summary + recent exceeds budget) -> triggers summarization (input includes old summary + recent), updates session, returns new summary + few post-summary messages.

            -   forceSummarization option works.

            -   Summarization LLM call fails -> returns original best-effort history, summaryWasPerformedThisCall: false.

            -   TaskSession not found -> throws TaskSessionNotFoundError.

        -   **Test ** Verify it calls ChatRepository with correct parameters.

        -   **Test ** Verify it correctly formats messages for summarizer input.

        -   **Test **

        -   Verify correct summarizer agent slug/category used.

            -   Verify PromptPartLoader and GenericPromptBuilder are called to build summarizer prompt.

            -   Verify LLMService.invokeModel is called with correct parameters.

            -   Verify success/failure outcomes are correctly processed.

    -   **Edge Cases:** Empty history, empty summary, summarizer returns empty string, very small tokensAvailableForHistory.

**8\. VALIDATION CRITERIA**

-   getHistoryForPrompt correctly retrieves and combines summary and recent messages.

    -   Token budget calculations are accurate in determining when to summarize.

    -   Summarization (when triggered) correctly calls GenericPromptBuilder for the summarizer's prompt and LLMService for the LLM call.

    -   TaskSession is correctly updated with the new summary and summaryLastMessageId via TaskSessionRepository after successful summarization.

    -   The final historyMessages returned fit within the allocated token budget.

    -   Handles failures in sub-services (LLM, Repos) gracefully.