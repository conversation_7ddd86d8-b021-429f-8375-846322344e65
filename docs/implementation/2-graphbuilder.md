**Implementation Plan: `GraphBuilder` API**

**1. Component Overview**

- **Purpose:** To provide a fluent, type-safe, and intuitive API for programmatically defining `GraphDefinition` objects in TypeScript.
- **Scope:** This component is responsible for the construction and initial validation of graph structures. It does not execute graphs.
- **Core Responsibilities:**
    - Allow definition of graph metadata (ID, description, version).
    - Manage the addition of various types of nodes (`start`, `end`, `agentCall`, `toolCall`, `conditionalBranch`, `dataTransform`).
    - Manage the addition of edges between nodes, with support for labels (e.g., `onSuccess`, `onFailure`, custom branch labels).
    - Provide helper methods for creating complex graph patterns, notably loops (`addLoop`).
    - Offer a fluent API for chaining node and edge definitions.
    - Facilitate the definition of how nodes receive their inputs (from graph initial inputs, other node outputs, constants, or static values).
    - Perform basic structural validation upon compiling the graph definition.
    - Output a standard `GraphDefinition` object consumable by the `GraphExecutor`.

**2. Dependencies**

- **Internal Types/Interfaces (from `src/common/types/`):**
    - `GraphDefinition`, `GraphNodeDefinition` (and its specific subtypes like `AgentCallNodeDef`, `ToolCallNodeDef`, etc.), `GraphEdgeDefinition`, `GraphInputValueSource`, `LLMTaskCategory`.
    - Zod schemas (e.g., for `expectedInitialInputs`, and used by helper types like `NodeOutputReference` for optional type hinting).
- **External Libraries:**
    - `zod` (for type hinting in the builder's API and internal validation, optional).
- **Utility Functions (from `src/common/utils/`):**
    - `generateUUID()` (if needed for internal node ID generation, though users will primarily provide IDs).

**3. File Structure**

- `src/common/graphs/GraphBuilder.ts`: Contains the `GraphBuilder` class and its related helper types (like `ResolvableInputValue`, `NodeOutputReference`, etc., or these can be in `graph.types.ts`).
- `src/common/types/graph.ts`: (Already defined) Contains core graph structure interfaces (`GraphDefinition`, `GraphNodeDefinition`, etc.).

**4. Core Class Structure (`GraphBuilder`)**

```tsx
// src/common/graphs/GraphBuilder.ts
import { z } from 'zod';
import {
  GraphDefinition, GraphNodeDefinition, GraphEdgeDefinition,
  GraphInputValueSource, AgentCallNodeDef, ToolCallNodeDef, /* ... other node types ... */
  LLMTaskCategory
} from '@/common/types/graph'; // Assuming path aliases

// Helper types for builder's fluent API
type NodeOutputReference<T = any> = { _type: 'NodeOutputReference'; nodeId: string; path?: string; outputVersion?: 'latest' | number | string; zodSchema?: z.ZodType<T>; defaultValue?: T };
type GraphInitialInputReference<T = any> = { _type: 'GraphInitialInputReference'; inputKey: string; zodSchema?: z.ZodType<T>; defaultValue?: T };
type GraphConstantReference<T = any> = { _type: 'GraphConstantReference'; constantKey: string; zodSchema?: z.ZodType<T>; };
type StaticValue<T = any> = { _type: 'StaticValue'; value: T; zodSchema?: z.ZodType<T>; };

type ResolvableInputValue<T = any> = NodeOutputReference<T> | GraphInitialInputReference<T> | GraphConstantReference<T> | StaticValue<T>;
type InputMappings = Record<string, ResolvableInputValue>;

interface LoopConstructPorts {
  loopId: string;
  entryNodeId: string; // Actual ID of the first internal node of the loop construct
  exitOnCompleteNodeId: string; // Actual ID of the node outputting final accumulator on normal completion
  // Optional: other specific exit ports like onMaxIterationsReachedNodeId

  // Fluent methods for connecting FROM the loop's exits
  onComplete(targetNodeId: string, description?: string): GraphBuilder;
  // onMaxIterationsReached?(targetNodeId: string, description?: string): GraphBuilder;
}

export class GraphBuilder {
  private id: string;
  private description: string;
  private version: string = '1.0.0';
  private entryNodeIdInternal: string = '__start__'; // Internal ID for the default start node
  private nodes: Map<string, GraphNodeDefinition> = new Map();
  private edges: GraphEdgeDefinition[] = [];

  private expectedInitialInputs: NonNullable<GraphDefinition['expectedInitialInputs']> = {};
  private graphConstants: NonNullable<GraphDefinition['graphConstants']> = {};

  private defaultMaxIterationsForAllLoops?: number;
  private defaultMaxLoopIterationsPerId: NonNullable<GraphDefinition['defaultMaxLoopIterations']> = {};

  private defaultErrorHandlerNodeId?: string;
  private defaultErrorInputMapping?: GraphNodeInputMapping; // How to map error data to the default handler

  private lastAddedNodeId: string | null = null;
  private definedBranchLabelsForLastConditional: Set<string> = new Set();

  // Mappings for logical loop IDs to actual internal node IDs
  private loopEntryPoints: Map<string /*logicalLoopId*/, string /*actualEntryNodeId*/> = new Map();
  private loopCompleteExits: Map<string /*logicalLoopId*/, string /*actualCompleteExitNodeId*/> = new Map();

  constructor(id: string, description: string) {
    this.id = id;
    this.description = description;
    // Automatically add a default start node. User can override entry point if they define their own start node.
    this.addNodeInternal(this.entryNodeIdInternal, { type: 'start', description: 'Default graph entry point' } as StartNodeDef);
  }

  // ... (Implementation of all public API methods will go here) ...

  private addNodeInternal(nodeId: string, nodeDef: Omit<GraphNodeDefinition, 'id'>): void {
    if (this.nodes.has(nodeId)) {
      throw new Error(`GraphBuilder: Node with ID "${nodeId}" already exists.`);
    }
    this.nodes.set(nodeId, { ...nodeDef, id: nodeId });
    this.lastAddedNodeId = nodeId; // Set context for fluent edge methods
  }

  private resolveInputMappings(mappings?: InputMappings): GraphNodeInputMapping | undefined {
    // ... (Implementation as discussed, converting builder's ResolvableInputValue to GraphInputValueSource)
    // This will also handle the 'fallbackSource' logic for fromNodeOutput.
    if (!mappings) return undefined;
    const resolved: GraphNodeInputMapping = {};
    for (const key in mappings) {
      const ref = mappings[key];
      let mainSource: GraphInputValueSource | null = null;
      let fallbackSourceInternal: GraphInputValueSource | null = null; // For fromNodeOutput fallback

      const createSource = (r: ResolvableInputValue): GraphInputValueSource | null => {
        switch (r._type) {
          case 'GraphInitialInputReference':
            return { type: 'fromGraphInitialInput', path: r.inputKey, zodSchema: r.zodSchema as any, defaultValue: r.defaultValue };
          case 'NodeOutputReference':
            // The fallback logic will be handled when this reference itself is created by builder.nodeOutput()
            return { type: 'fromNodeOutput', nodeId: r.nodeId, path: r.path, outputVersion: r.outputVersion || 'latest', zodSchema: r.zodSchema as any, defaultValue: r.defaultValue };
          case 'GraphConstantReference':
            return { type: 'fromGraphConstant', constantKey: r.constantKey, zodSchema: r.zodSchema as any };
          case 'StaticValue':
            return { type: 'staticValue', value: r.value, zodSchema: r.zodSchema as any };
          default: return null;
        }
      };

      mainSource = createSource(ref);
      // Check if this ResolvableInputValue is a NodeOutputReference with a fallback
      if (ref._type === 'NodeOutputReference' && (ref as any)._internalFallbackRef) {
          const fallbackRef = (ref as any)._internalFallbackRef as ResolvableInputValue;
          fallbackSourceInternal = createSource(fallbackRef);
          if (mainSource && mainSource.type === 'fromNodeOutput') {
              mainSource.fallbackSource = fallbackSourceInternal as any; // Cast as it's specific
          }
      }

      if (mainSource) {
        resolved[key] = mainSource;
      } else {
        throw new Error(`GraphBuilder: Could not resolve input mapping for key "${key}"`);
      }
    }
    return resolved;
  }

  private _validateGraphStructure(): void {
    // ... (Implementation as discussed: entry point, end nodes, edge validity, node references)
    // For V1, basic cycle detection for non-loop paths can be a placeholder or simple depth check.
  }

  public compile(): GraphDefinition {
    // ... (Implementation as discussed, calls _validateGraphStructure)
    if (!this.entryNodeIdInternal) {
        throw new Error("GraphBuilder: Entry point not set or default start node was removed.");
    }
    this._validateGraphStructure();
    return {
      id: this.id,
      description: this.description,
      version: this.version,
      entryNodeId: this.entryNodeIdInternal,
      nodes: Array.from(this.nodes.values()),
      edges: this.edges,
      expectedInitialInputs: this.expectedInitialInputs,
      graphConstants: this.graphConstants,
      defaultMaxIterationsForAllLoops: this.defaultMaxIterationsForAllLoops,
      defaultMaxLoopIterations: this.defaultMaxLoopIterationsPerId,
      defaultErrorHandlerNodeId: this.defaultErrorHandlerNodeId,
      // defaultErrorInputMapping: this.defaultErrorInputMapping, // Store this if needed by Executor
    };
  }
}

```

**5. Public API Methods (Step-by-Step Implementation Plan)**

- **A. Configuration Methods:**
    - **`setVersion(version: string): this`**: Sets `this.version`. Returns `this`.
    - **`setEntryPoint(nodeId: string): this`**: Validates `nodeId` exists and is of type `start`. Sets `this.entryNodeIdInternal`. Returns `this`.
    - **`addExpectedInitialInput(key: string, description: string, zodSchema?: z.ZodTypeAny, isOptional?: boolean): this`**: Adds to `this.expectedInitialInputs`. Returns `this`.
    - **`addGraphConstant(key: string, value: any): this`**: Adds to `this.graphConstants`. Returns `this`.
    - **`setGlobalDefaultMaxLoopIterations(iterations: number): this`**: Sets `this.defaultMaxIterationsForAllLoops`. Returns `this`.
    - **`setDefaultMaxLoopIterationsForLoopId(loopId: string, iterations: number): this`**: Adds to `this.defaultMaxLoopIterationsPerId`. Returns `this`.
    - **`registerDefaultErrorHandlerNode(handlerNodeId: string, errorInputMapping?: InputMappings): this`**: Sets `this.defaultErrorHandlerNodeId`. Resolves and stores `errorInputMapping` as `this.defaultErrorInputMapping` (converting `ResolvableInputValue` to `GraphInputValueSource`). Returns `this`.
        - The `GraphExecutor` will use `defaultErrorHandlerNodeId` and if `defaultErrorInputMapping` is present, it uses that to prepare inputs for the error handler. Otherwise, it uses a standard error object `{ error: ActualErrorObject, failedNodeId: string }`.
- **B. Input Source Factory Methods (Public on `GraphBuilder`):**
    - **`graphInput<T>(inputKey: string, defaultValue?: T, zodSchema?: z.ZodType<T>): GraphInitialInputReference<T>`**: Creates and returns a `GraphInitialInputReference` object. If `inputKey` not in `expectedInitialInputs`, it can optionally auto-add it with a generic description.
    - **`nodeOutput<T>(nodeId: string, path?: string, options?: { defaultValue?: T, zodSchema?: z.ZodType<T>, outputVersion?: 'latest' | number | string, fallback?: NodeOutputReference<T> /* For loop state logic */ }): NodeOutputReference<T>`**: Creates and returns a `NodeOutputReference` object. The `options.fallback` is key for loop state.
        - Internally, `NodeOutputReference` might store this `_internalFallbackRef`.
    - **`constant<T>(constantKey: string, zodSchema?: z.ZodType<T>): GraphConstantReference<T>`**: Creates and returns a `GraphConstantReference`.
    - **`staticValue<T>(value: T, zodSchema?: z.ZodType<T>): StaticValue<T>`**: Creates and returns a `StaticValue`.
- **C. Node Addition Methods (Public on `GraphBuilder`):**
    - Each `addXNode(id: string, ...)` method:
        1. Validates ID uniqueness.
        2. Constructs the specific `GraphNodeDefinition` subtype (e.g., `AgentCallNodeDef`).
        3. For `addAgentNode(id, agentClass, llmTaskCategoryOverride?, inputs?, description?)`:
            - Looks up `agentClass.agentSlug` and default category from `AGENT_REGISTRY_CONFIG` (as discussed).
            - Stores `agentSlug` and resolved `llmTaskCategory` in the node definition.
        4. Calls `this.resolveInputMappings(inputs)` to convert builder-friendly `InputMappings` (using `ResolvableInputValue`) to `GraphNodeInputMapping` (using `GraphInputValueSource`).
        5. Calls `this.addNodeInternal(id, nodeDefinitionWithoutId)`.
        6. Sets `this.lastAddedNodeId = id`.
        7. Clears `this.definedBranchLabelsForLastConditional` if the added node is not conditional. If it is conditional, it resets this set.
        8. Returns `this`.
    - Specific methods:
        - `addStartNode(id: string, description?: string): this`
        - `addEndNode(id: string, description?: string, isErrorEndNode?: boolean, outputSource?: ResolvableInputValue): this`
        - `addAgentNode(...) : this` (as detailed above)
        - `addToolNode(id: string, toolName: string, inputs?: InputMappings, description?: string): this`
        - `addConditionalNode(id: string, conditionFunctionKey: string /* Key to registered fn */, inputs?: InputMappings, description?: string): this` (Stores key, executor resolves function from a registry).
        - `addDataTransformNode(id: string, transformFunctionKey: string /* Key to registered fn */, inputs?: InputMappings, description?: string): this`
- **D. Edge Addition Methods (Public on `GraphBuilder`):**
    - **`onSuccess(toNodeId: string, description?: string): this`**: Uses `this.lastAddedNodeId` as `from`. Adds edge with label `onSuccess`. Validates `lastAddedNodeId` is suitable for `onSuccess`. Returns `this`.
    - **`onFailure(toNodeId: string, description?: string): this`**: Uses `this.lastAddedNodeId` as `from`. Adds edge with label `onFailure`. Validates. Returns `this`.
    - **`onBranch(branchLabel: string, toNodeId: string, description?: string): this`**: Uses `this.lastAddedNodeId` as `from`. Adds edge with `branchLabel`. Validates `lastAddedNodeId` is conditional and `branchLabel` isn't `__otherwise__` or duplicated. Adds `branchLabel` to `definedBranchLabelsForLastConditional`. Returns `this`.
    - **`otherwise(toNodeId: string, description?: string): this`**: Uses `this.lastAddedNodeId` as `from`. Adds edge with label `__otherwise__`. Validates `lastAddedNodeId` is conditional and `__otherwise__` isn't already defined. Adds `__otherwise__` to `definedBranchLabelsForLastConditional`. Returns `this`.
    - **`addEdge(fromNodeId: string, toNodeId: string, label?: string, description?: string): this`**:
        - Resolves `fromNodeId` and `toNodeId` (if they are logical loop IDs, map to actual internal node IDs via `this.loopCompleteExits` and `this.loopEntryPoints`).
        - Validates actual node existence.
        - Adds edge to `this.edges`.
        - Does *not* change `this.lastAddedNodeId`. Returns `this`.
    - **`setInitialNode(targetNodeId: string, description?: string): this`**: Calls `this.addEdge(this.entryNodeIdInternal, targetNodeId, \\u0027onSuccess\\u0027, description)`. Returns `this`.
- **E. Loop Construction Helper (`addLoop`):**
    - **`addLoop<TItem, TAcc>(loopId: string, itemsToIterateSource: ResolvableInputValue<TItem[]>, loopBodyBuilderFn: (...), options?: {...}): this`** (Returns `this` for general chaining after loop definition).
    - **Internal Implementation:**
        1. Validate `loopId` uniqueness.
        2. Generate unique internal node IDs: `initNodeId`, `conditionNodeId`, `iterDataProviderNodeId`, `updateStateNodeId`, `exitAggregatorNodeId`.
        3. Store `this.loopEntryPoints.set(loopId, initNodeId)`.
        4. Store `this.loopCompleteExits.set(loopId, exitAggregatorNodeId)`.
        5. Add `initNodeId` (`DataTransformNode`): Takes `itemsToIterateSource`, `options.accumulatorInitialValue`. Outputs initial `LoopStateObject: { items, currentIndex: 0, accumulator, isLoopActive: true }`.
        6. Add `conditionNodeId` (`ConditionalBranchNode`):
            - Input `currentLoopState`: `this.nodeOutput(updateStateNodeId, \\u0027/\\u0027, { fallback: this.nodeOutput(initNodeId) })` (using the new `fallback` feature of `nodeOutput` factory).
            - `conditionFunctionKey` checks `currentIndex` vs `items.length`, `isLoopActive`, and `graphContext.loopCounters.get(loopId)` vs `maxIterations`.
            - Outputs label `\\u0027loop_body_path\\u0027` or `\\u0027loop_exit_path\\u0027`.
        7. Add `iterationDataProviderNodeId` (`DataTransformNode`):
            - Input `loopStateFromCondition`: `this.nodeOutput(conditionNodeId, \\u0027_conditional_input_passthrough_.currentLoopState\\u0027)` (Executor needs to make conditional's input available if branch is taken). *This specific input passing from conditional needs robust design.*
            - Alternatively, if `conditionNodeId` outputs the `loopState` when branching to `loop_body_path`: `this.nodeOutput(conditionNodeId, \\u0027loopStateForBody\\u0027)`.
            - Outputs: `{ currentItem, currentIndex, currentAccumulatorSnapshot, _internalFullLoopState }`.
        8. Call `loopBodyBuilderFn(this, currentItemRef, currentIndexRef, currentAccumulatorRef, iterDataProviderNodeId)`. This function uses the builder to add its nodes, taking inputs from `iterDataProviderNodeId`. It returns `{ lastNodeInBodyId, accumulationLogic }`.
        9. Add `updateStateNodeId` (`DataTransformNode`):
            - Inputs: `itemProcessingResult` (from `bodyConfig.lastNodeInBodyId` based on `accumulationLogic`), `previousLoopState` (from `iterDataProviderNodeId._internalFullLoopState`).
            - Outputs new `LoopStateObject` with updated accumulator, index, `isLoopActive`.
        10. Add `exitAggregatorNodeId` (`DataTransformNode`):
            - Input `finalLoopState`: `this.nodeOutput(conditionNodeId, \\u0027_conditional_input_passthrough_.currentLoopState\\u0027)` when exiting.
            - Outputs `{ finalAccumulatedResults: finalLoopState.accumulator }`.
        11. Add internal edges: `init -> condition`, `condition --loop_body_path--> iterDataProvider`, `(body last node) -> updateState`, `updateState -> condition`, `condition --loop_exit_path--> exitAggregator`.
        12. Set `this.lastAddedNodeId = exitAggregatorNodeId` (to allow chaining `.onLoopComplete()` or `.onSuccess()` from the loop's successful exit).
        13. Returns `this`.
    - **Chaining from Loop Exits (e.g. `.onLoopComplete()`):**
        - These methods will use `this.lastAddedNodeId` (which `addLoop` set to its exit aggregator) as their `from` node.
- **F. Compilation Method (`compile`):**
    1. Set `this.entryNodeId = this.entryNodeIdInternal` (finalizing the entry point).
    2. Call `this._validateGraphStructure()`:
        - All node IDs in edges exist.
        - All `fromNodeOutput` references are valid.
        - Entry point is set and exists.
        - At least one `end` node exists and is reachable (basic reachability).
        - (V1 - Simple Cycle Check): Perform a basic DFS to detect obvious non-loop cycles. If `builder.addLoop` marks its internal cycle edges, these can be ignored by the detector. For V1, `MAX_TOTAL_GRAPH_STEPS` in executor is primary safeguard.
    3. Return the `GraphDefinition` object.

**6. Helper Types (builder-specific)**

- `NodeOutputReference`, `GraphInitialInputReference`, `GraphConstantReference`, `StaticValue`, `ResolvableInputValue`, `InputMappings`, `LoopConstructPorts`. These make the builder's method signatures type-safe and expressive.
- The `ResolvableInputValue` types will have a `_internalFallbackRef?: ResolvableInputValue` only for `NodeOutputReference` to support the loop condition state sourcing. The `nodeOutput()` factory method will be responsible for creating this nested structure if a fallback is provided.

**7. Testing Strategy**

- **Unit Tests for Builder Methods:**
    - Test each `addXNode` method creates the correct `GraphNodeDefinition`.
    - Test input resolution logic in `resolveInputMappings` for all `GraphInputValueSource` types, including fallbacks.
    - Test edge creation methods (`onSuccess`, `onFailure`, `onBranch`, `otherwise`, `addEdge`).
    - Test `addLoop` creates the correct internal node structure and edges.
- **Unit Tests for `compile()`:**
    - Test validation logic: throws errors for invalid node references, missing entry/end points.
    - Test that a valid sequence of builder calls produces the expected `GraphDefinition` object.
- **Integration Tests (Conceptual):** Define a few sample graphs using the `GraphBuilder`, compile them, and verify the structure of the output `GraphDefinition`. Compare against manually crafted expected objects.
- Test visualization output (`generateMermaidDiagram`) for simple graphs.

**8. Integration Points**

- The primary output is the `GraphDefinition` object.
- This `GraphDefinition` object is the direct input for the `GraphExecutor`.
- The `GraphBuilder` will be used in `src/common/graphs/definitions/` to define all our standard operational graphs (like `createNewWorkflowGraphV1`).

---

ADDITIONAL DETAILS:

---

**Implementation Plan: `GraphBuilder` API (Comprehensive)**

**1. Component Overview**

- **Purpose:** To provide a fluent, type-safe, and intuitive API for programmatically defining `GraphDefinition` objects in TypeScript. This builder is the primary way developers will construct executable AI workflows.
- **Scope:** This component is responsible for the construction, validation (structural and basic logical), and compilation of graph structures into a `GraphDefinition` object. It also manages the registration of user-provided functions for conditional and data transformation nodes.
- **Core Design Principles:**
    - **Fluency:** Method chaining for a natural graph definition experience.
    - **Type Safety:** Leverage TypeScript generics and strong typing to catch errors at "build time."
    - **Reduced Boilerplate:** Abstract common patterns and internal complexities.
    - **Explicitness where it matters:** While abstracting, ensure the core logic of the graph remains understandable.
    - **Developer Experience (DX):** Prioritize ease of use, clear error messages, and helpful abstractions like `addLoop`.
    - **Output:** A validated `GraphDefinition` object and a `FunctionRegistry` consumable by the `GraphExecutor`.

**2. Dependencies**

- **Internal Types/Interfaces (from `src/common/types/graph.types.ts` - defined in previous plan):**
    - `GraphDefinition`, `GraphNodeDefinition` (and its subtypes: `StartNodeDef`, `EndNodeDef`, `AgentCallNodeDef`, `ToolCallNodeDef`, `ConditionalBranchNodeDef`, `DataTransformNodeDef`), `GraphEdgeDefinition`, `GraphInputValueSource` (including `fallbackSource` capability), `GraphNodeInputMapping`.
    - `LLMTaskCategory` enum.
- **Internal Types/Interfaces (for Agent Configuration - to be defined, but concept needed):**
    - `BaseN8nAgent` (abstract class or interface defining `static readonly agentSlug` and `static readonly defaultLlmTaskCategory`).
    - `AgentDependencies` (interface defining services like `LLMService` to be injected into agents).
    - `AGENT_REGISTRY_CONFIG` (array/map storing agent class constructors, slugs, and default configs).
- **External Libraries:**
    - `zod` (used by `GraphInputValueSource` and other graph types for optional schema hinting, and by the builder itself for validating expected initial inputs if schemas are provided).
- **Utility Functions (from `src/common/utils/`):**
    - `generateUUID()`: For generating unique keys for registered functions if needed.

**3. File Structure**

- `src/common/graphs/GraphBuilder.ts`: Main `GraphBuilder` class and its public API helper types.
- `src/common/graphs/graph-builder.types.ts`: Specific helper types for the builder's fluent API if they become numerous (e.g., `ResolvableInputValue`, `InputMappings`, `LoopConstructPorts`). Alternatively, co-locate in `GraphBuilder.ts`.
- `src/common/types/graph.types.ts`: (Prerequisite) Core graph structure definitions.

**4. Helper Types for `GraphBuilder` API**

```tsx
// src/common/graphs/graph-builder.types.ts (or in GraphBuilder.ts)
import { z, ZodTypeAny } from 'zod';

// --- Input Value Representation for Builder API ---
// Base for type discrimination
interface BaseResolvableInputValue {
  _type: string;
  zodSchema?: ZodTypeAny;
  defaultValue?: any;
}

export interface NodeOutputReference<T = any> extends BaseResolvableInputValue {
  _type: 'NodeOutputReference';
  nodeId: string;
  path?: string; // e.g., 'payload.user.name'
  outputVersion?: 'latest' | number | string /* runId */;
  // For internal use by builder to construct GraphInputValueSource with fallback
  _internalFallbackRef?: NodeOutputReference<T>;
}

export interface GraphInitialInputReference<T = any> extends BaseResolvableInputValue {
  _type: 'GraphInitialInputReference';
  inputKey: string;
}

export interface GraphConstantReference<T = any> extends BaseResolvableInputValue {
  _type: 'GraphConstantReference';
  constantKey: string;
}

export interface StaticValue<T = any> extends BaseResolvableInputValue {
  _type: 'StaticValue';
  value: T;
}

export type ResolvableInputValue<T = any> =
  | NodeOutputReference<T>
  | GraphInitialInputReference<T>
  | GraphConstantReference<T>
  | StaticValue<T>;

export type InputMappings = Record<string, ResolvableInputValue>;

// --- For addLoop Return Type ---
export interface LoopConstructPorts {
  loopId: string;
  // Use with builder.addEdge(previousNode, loop.entryTargetId)
  // This is the ID of the actual first node *inside* the loop construct (e.g., init_state node)
  entryTargetId: string;

  // Use with builder.setLastAddedNodeId(loop.completeExitNodeId).onSuccess(...)
  // This is the ID of the node that outputs the final accumulator on normal completion
  completeExitNodeId: string;

  // Optional: If loop has a distinct max_iterations exit path defined by addLoop
  // maxIterationsExitNodeId?: string;
}

```

**5. `GraphBuilder` Class Structure and Private Members**

```tsx
// src/common/graphs/GraphBuilder.ts
// ... (imports: types from graph.types.ts, helper types from graph-builder.types.ts, AGENT_REGISTRY_CONFIG)

// Type for functions registered by the builder
export type RegisteredGraphFunction =
  | ((inputs: Record<string, any>, graphContext: /* GraphExecutionContext - to be defined */ any) => string | string[]) // Conditional
  | ((inputs: Record<string, any>, graphContext: /* GraphExecutionContext */ any) => Record<string, any>);     // DataTransform

export interface FunctionRegistry {
  [key: string]: RegisteredGraphFunction;
}

export class GraphBuilder {
  private readonly graphId: string;
  private readonly graphDescription: string;
  private graphVersion: string = '1.0.0';

  private nodesMap: Map<string, GraphNodeDefinition> = new Map();
  private edgesList: GraphEdgeDefinition[] = [];

  private graphEntryPointId: string; // Set by constructor, can be changed by setEntryPoint

  private readonly expectedInitialInputsConfig: NonNullable<GraphDefinition['expectedInitialInputs']> = {};
  private readonly graphConstantsMap: NonNullable<GraphDefinition['graphConstants']> = {};

  private readonly globalDefaultMaxIterations?: number;
  private readonly perLoopMaxIterations: NonNullable<GraphDefinition['defaultMaxLoopIterations']> = {};

  private registeredDefaultErrorHandlerNodeId?: string;
  // private registeredDefaultErrorInputMapping?: GraphNodeInputMapping; // Resolved

  private lastAddedNodeIdInternal: string | null = null;
  private definedBranchLabelsForLastConditionalInternal: Set<string> = new Set();

  // For managing logical loop IDs and their actual entry/exit node IDs
  private readonly loopIdToEntryNodeIdMap: Map<string /*logicalLoopId*/, string /*actualEntryNodeId*/> = new Map();
  private readonly loopIdToCompleteExitNodeIdMap: Map<string /*logicalLoopId*/, string /*actualCompleteExitNodeId*/> = new Map();
  // private readonly loopIdToMaxIterExitNodeIdMap: Map<string, string> = new Map(); // For V2

  // For registering functions passed to addConditionalNode/addDataTransformNode
  private readonly functionStore: Map<string /*functionKey*/, RegisteredGraphFunction> = new Map();
  private functionKeyCounter: number = 0;

  constructor(id: string, description: string) {
    this.graphId = id;
    this.graphDescription = description;
    const defaultStartNodeId = `__start_${id}__`;
    this.addNodeInternal(defaultStartNodeId, { type: 'start', description: 'Default graph entry point' } as StartNodeDef);
    this.graphEntryPointId = defaultStartNodeId;
    this.lastAddedNodeIdInternal = defaultStartNodeId; // Start node is initially the last added
  }

  // --- Private Helper Methods ---
  private generateFunctionKey(prefix: string): string {
    this.functionKeyCounter++;
    return `${prefix}_${this.graphId}_${this.functionKeyCounter}`;
  }

  private addNodeInternal(nodeId: string, nodeDefWithoutId: Omit<GraphNodeDefinition, 'id'>): void {
    if (this.nodesMap.has(nodeId)) {
      throw new Error(`GraphBuilder: Node with ID "${nodeId}" already exists in graph "${this.graphId}".`);
    }
    this.nodesMap.set(nodeId, { ...nodeDefWithoutId, id: nodeId });
    this.lastAddedNodeIdInternal = nodeId;
  }

  private resolveSingleInputValueSource(resolvable: ResolvableInputValue): GraphInputValueSource {
    // ... (Implementation as defined in previous plan: switch on resolvable._type)
    // ... (Handles nested fallback for NodeOutputReference by checking _internalFallbackRef and recursively calling itself)
    // Example for NodeOutputReference:
    if (resolvable._type === 'NodeOutputReference') {
      const mainSource: GraphInputValueSource = {
        type: 'fromNodeOutput',
        nodeId: resolvable.nodeId,
        path: resolvable.path,
        outputVersion: resolvable.outputVersion || 'latest',
        zodSchema: resolvable.zodSchema,
        defaultValue: resolvable.defaultValue
      };
      if (resolvable._internalFallbackRef) {
        const fallbackGraphValueSource = this.resolveSingleInputValueSource(resolvable._internalFallbackRef);
        if (fallbackGraphValueSource.type !== 'fromNodeOutput') { // Ensure fallback is also a node output
            throw new Error("Fallback source for a NodeOutputReference must also be a NodeOutputReference.");
        }
        (mainSource as any).fallbackSource = fallbackGraphValueSource as GraphInputValueSource_NodeOutput_Recursive;
      }
      return mainSource;
    }
    // ... other cases
    throw new Error(`Unsupported ResolvableInputValue type: ${(resolvable as any)._type}`);
  }

  private resolveInputMappings(mappings?: InputMappings): GraphNodeInputMapping | undefined {
    if (!mappings) return undefined;
    const resolved: GraphNodeInputMapping = {};
    for (const key in mappings) {
      resolved[key] = this.resolveSingleInputValueSource(mappings[key]);
    }
    return resolved;
  }

  // ... (_validateGraphStructure method as discussed previously) ...

  // --- Public API Methods ---
  // ... (Implement all methods step-by-step) ...
}

```

**6. Step-by-Step Implementation of Public API Methods**

- **A. Configuration Methods:**
    - **`setVersion(version: string): this`**
    - **`setEntryPoint(nodeId: string): this`**:
        - Validate `nodeId` exists in `this.nodesMap`.
        - Validate `this.nodesMap.get(nodeId)?.type === 'start'`.
        - Set `this.graphEntryPointId = nodeId`.
    - **`addExpectedInitialInput(key: string, description: string, zodSchema?: ZodTypeAny, isOptional?: boolean): this`**
    - **`addGraphConstant(key: string, value: any): this`**
    - **`setGlobalDefaultMaxLoopIterations(iterations: number): this`**
    - **`setDefaultMaxLoopIterationsForLoopId(loopId: string, iterations: number): this`**
    - **`registerDefaultErrorHandlerNode(handlerNodeId: string): this`**:
        - Validate `handlerNodeId` exists.
        - Set `this.registeredDefaultErrorHandlerNodeId = handlerNodeId`.
- **B. Input Source Factory Methods:**
    - **`graphInput<T>(inputKey: string, options?: { defaultValue?: T, zodSchema?: ZodTypeAny }): GraphInitialInputReference<T>`**:
        - If `!this.expectedInitialInputsConfig[inputKey]`, add it with a generic description and optionality based on `defaultValue`.
        - Return `{ _type: 'GraphInitialInputReference', inputKey, zodSchema: options?.zodSchema, defaultValue: options?.defaultValue }`.
    - **`nodeOutput<T>(nodeId: string, path?: string, options?: { defaultValue?: T, zodSchema?: ZodTypeAny, outputVersion?: 'latest' | number | string, fallback?: NodeOutputReference<T> }): NodeOutputReference<T>`**:
        - Return `{ _type: 'NodeOutputReference', nodeId, path, outputVersion: options?.outputVersion || 'latest', zodSchema: options?.zodSchema, defaultValue: options?.defaultValue, _internalFallbackRef: options?.fallback }`.
    - **`constant<T>(constantKey: string, zodSchema?: ZodTypeAny): GraphConstantReference<T>`**: Return `{ _type: 'GraphConstantReference', constantKey, zodSchema }`.
    - **`staticValue<T>(value: T, zodSchema?: ZodTypeAny): StaticValue<T>`**: Return `{ _type: 'StaticValue', value, zodSchema }`.
- **C. Node Addition Methods:**
    - **`addStartNode(id: string, description?: string): this`**: Call `this.addNodeInternal(id, { type: 'start', description } as StartNodeDef)`.
    - **`addEndNode(id: string, description?: string, isErrorEndNode?: boolean, outputSource?: ResolvableInputValue): this`**:
        - Resolve `outputSource` into `GraphInputValueSource` if present.
        - Call `this.addNodeInternal(id, { type: 'end', description, isErrorEndNode, inputs: outputSource ? { finalOutput: this.resolveSingleInputValueSource(outputSource) } : undefined } as EndNodeDef)`.
    - **`addAgentNode<TInput, TOutputPayload>(id: string, agentClass: new (deps: AgentDependencies) => BaseN8nAgent<TInput, TOutputPayload>, llmTaskCategoryOverride?: LLMTaskCategory, inputs?: InputMappings, description?: string): this`**:
        1. Find `agentConfig` in `AGENT_REGISTRY_CONFIG` using `agentClass` (or its static `agentSlug`). Throw if not found.
        2. Determine `categoryToUse = llmTaskCategoryOverride || agentConfig.defaultLlmTaskCategory || (agentClass as any).defaultLlmTaskCategory`. Throw if no category found.
        3. `resolvedInputs = this.resolveInputMappings(inputs)`.
        4. Call `this.addNodeInternal(id, { type: 'agentCall', agentSlug: agentConfig.slug, llmTaskCategory: categoryToUse, inputs: resolvedInputs, description } as AgentCallNodeDef)`.
    - **`addToolNode(id: string, toolName: string, inputs?: InputMappings, description?: string): this`**:
        1. `resolvedInputs = this.resolveInputMappings(inputs)`.
        2. Call `this.addNodeInternal(id, { type: 'toolCall', toolName, inputs: resolvedInputs, description } as ToolCallNodeDef)`.
    - **`addConditionalNode(id: string, conditionFunction: RegisteredGraphFunction, inputs?: InputMappings, description?: string): this`**:
        1. Validate `conditionFunction` is a function.
        2. `functionKey = this.generateFunctionKey('cond')`.
        3. `this.functionStore.set(functionKey, conditionFunction)`.
        4. `resolvedInputs = this.resolveInputMappings(inputs)`.
        5. Call `this.addNodeInternal(id, { type: 'conditionalBranch', conditionFunctionKey, inputs: resolvedInputs, description } as ConditionalBranchNodeDef)`.
        6. `this.definedBranchLabelsForLastConditionalInternal.clear()`.
    - **`addDataTransformNode(id: string, transformFunction: RegisteredGraphFunction, inputs?: InputMappings, description?: string): this`**: Similar to `addConditionalNode`, store function by key.
- **D. Edge Addition Methods:**
    - **`setInitialNode(targetNodeId: string, description?: string): this`**:
        - Call `this.addEdgeInternal(this.graphEntryPointId, targetNodeId, 'onSuccess', description, true)`. The `isEntryPointEdge` flag helps `addEdgeInternal` skip some checks.
    - **`onSuccess(toNodeId: string, description?: string): this`**:
        - Call `this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, 'onSuccess', description)`.
    - **`onFailure(toNodeId: string, description?: string): this`**:
        - Call `this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, 'onFailure', description)`.
    - **`onBranch(branchLabel: string, toNodeId: string, description?: string): this`**:
        - Validate `this.lastAddedNodeIdInternal` points to a conditional node.
        - Validate `branchLabel` is not `__otherwise__` and not already defined for this conditional.
        - Call `this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, branchLabel, description)`.
        - Add `branchLabel` to `this.definedBranchLabelsForLastConditionalInternal`.
    - **`otherwise(toNodeId: string, description?: string): this`**:
        - Similar validation. Call `this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, '__otherwise__', description)`.
        - Add `__otherwise__` to `this.definedBranchLabelsForLastConditionalInternal`.
    - **`addEdge(fromNodeId: string, toNodeId: string, label?: string, description?: string): this`**:
        - Call `this.addEdgeInternal(fromNodeId, toNodeId, label, description, false)`.
    - **Private `addEdgeInternal(fromNodeIdOrLogical: string | null, toNodeIdOrLogical: string, label?: string, description?: string, isEntryPointEdge: boolean = false): void`**:
        1. If `isEntryPointEdge` and `fromNodeIdOrLogical` is null, use `this.graphEntryPointId`.
        2. If `!fromNodeIdOrLogical`, throw "No source node for edge".
        3. `actualFrom = this.loopCompleteExits.get(fromNodeIdOrLogical) || this.loopEntryPoints.get(fromNodeIdOrLogical) || fromNodeIdOrLogical`.
        4. `actualTo = this.loopEntryPoints.get(toNodeIdOrLogical) || toNodeIdOrLogical`.
        5. Validate `actualFrom` and `actualTo` exist in `this.nodesMap`.
        6. `this.edgesList.push({ from: actualFrom, to: actualTo, label: label || 'onSuccess', description })`.
- **E. Loop Construction Helper (`addLoop`):**
    - **`addLoop<TItem, TAcc>(loopId: string, itemsToIterateSource: ResolvableInputValue<TItem[]>, loopBodyBuilderFn: (builder: GraphBuilder, currentItemRef: NodeOutputReference<TItem>, currentIndexRef: NodeOutputReference<number>, currentAccumulatorRef: NodeOutputReference<TAcc>, iterationProviderNodeId: string) => { lastNodeInBodyId: string; accumulationLogic: { type: 'appendItem'; itemToAppend: ResolvableInputValue<any>; } | { type: 'customTransform'; transformInput: ResolvableInputValue<any>; transformFunctionKey: string; }; }, options?: { accumulatorInitialValue?: ResolvableInputValue<TAcc>; maxIterations?: number; }): this`**
    - **Implementation Steps:**
        1. Validate `loopId` uniqueness (check `this.loopEntryPoints`).
        2. Generate internal node IDs: `initNodeId = ${loopId}_init_state`, `conditionNodeId = ${loopId}_condition`, `iterDataProviderNodeId = ${loopId}_iter_data_provider`, `updateStateNodeId = ${loopId}_update_state`, `exitAggregatorNodeId = ${loopId}_exit_aggregator`.
        3. Store mappings: `this.loopEntryPoints.set(loopId, initNodeId)`, `this.loopCompleteExits.set(loopId, exitAggregatorNodeId)`.
        4. Set per-loop max iterations: `this.setDefaultMaxLoopIterationsForLoopId(loopId, options?.maxIterations || this.globalDefaultMaxIterations || some_hardcoded_default)`.
        5. **Add `initNodeId` (`DataTransformNode`):**
            - Inputs: `rawItems: itemsToIterateSource`, `initialAcc: options?.accumulatorInitialValue || this.staticValue([])`.
            - `transformFunctionKey`: Register a function that returns `{ items: inputs.rawItems, currentIndex: 0, accumulator: inputs.initialAcc, isLoopActive: true }`.
        6. **Add `conditionNodeId` (`ConditionalBranchNode`):**
            - Inputs: `currentLoopState: this.nodeOutput(updateStateNodeId, '/', { fallback: this.nodeOutput(initNodeId) })`.
            - `conditionFunctionKey`: Register function that checks `currentLoopState.currentIndex < currentLoopState.items.length && currentLoopState.isLoopActive && loopCounter < maxIter`. Returns `_loop_body_path_` or `_loop_exit_path_`. (Executor manages `loopCounter` in `GraphExecutionContext`).
        7. **Add `iterDataProviderNodeId` (`DataTransformNode`):**
            - Inputs: `loopStateFromCondition: this.nodeOutput(conditionNodeId, '_conditional_input_passthrough_.currentLoopState')`. (This needs careful thought: the conditional node's output is a label. The executor might need to make the *input* that led to the condition being met available under a special key, or the conditional node, if true, also outputs the state it evaluated).
            - **Alternative for cleaner input to iterDataProvider:** The `conditionNode` (if taking `_loop_body_path_`) outputs the `currentLoopState` it evaluated. So, `iterDataProvider` input: `{ loopState: this.nodeOutput(conditionNodeId, 'payload.evaluatedLoopState') }`.
            - `transformFunctionKey`: Registers fn: `(inputs) => ({ currentItem: inputs.loopState.items[inputs.loopState.currentIndex], currentIndex: inputs.loopState.currentIndex, currentAccumulatorSnapshot: inputs.loopState.accumulator, _internalFullLoopState: inputs.loopState })`.
        8. **Call `loopBodyBuilderFn(this, ...)`:** Pass `NodeOutputReference`s pointing to outputs of `iterDataProviderNodeId`. It returns `bodyConfig = { lastNodeInBodyId, accumulationLogic }`.
        9. **Add `updateStateNodeId` (`DataTransformNode`):**
            - Inputs: `itemProcessingResult` (from `bodyConfig.lastNodeInBodyId` using `bodyConfig.accumulationLogic.itemToAppend` or `bodyConfig.accumulationLogic.transformInput`), `previousLoopState: this.nodeOutput(iterDataProviderNodeId, '_internalFullLoopState')`.
            - `transformFunctionKey`: Registers function that applies `accumulationLogic`, increments `currentIndex`, updates `isLoopActive`. Outputs new `LoopStateObject`.
        10. **Add `exitAggregatorNodeId` (`DataTransformNode`):**
            - Inputs: `finalLoopState: this.nodeOutput(conditionNodeId, '_conditional_input_passthrough_.currentLoopState')` (when taking `_loop_exit_path_`).
            - `transformFunctionKey`: Registers function that returns `{ finalAccumulatedResults: inputs.finalLoopState.accumulator }`.
        11. **Add internal Edges:** `initNodeId -> conditionNodeId`, `conditionNodeId --_loop_body_path_--> iterDataProviderNodeId`, `iterDataProviderNodeId -> (start of body defined by user)`, `(end of body) -> updateStateNodeId`, `updateStateNodeId -> conditionNodeId`, `conditionNodeId --_loop_exit_path_--> exitAggregatorNodeId`.
        12. Set `this.lastAddedNodeIdInternal = exitAggregatorNodeId`.
        13. Return `this`.
    - **`onLoopComplete(targetNodeId: string, description?: string): this`**:
        - Validates `this.lastAddedNodeIdInternal` is a known loop exit (e.g., check `this.loopCompleteExits.includesValue(this.lastAddedNodeIdInternal)`).
        - Calls `this.addEdgeInternal(this.lastAddedNodeIdInternal, targetNodeId, 'onLoopComplete', description)`.
- **F. Compilation Method (`compile`):**
    1. If `!this.nodesMap.has(this.graphEntryPointId)`, throw error or default to `__start_${this.graphId}__`.
    2. Call `this._validateGraphStructure()`.
    3. Return `{ definition: { id: this.graphId, ...nodes: Array.from(this.nodesMap.values()), edges: this.edgesList, ... }, functionRegistry: this.functionStore }`.

**7. `_validateGraphStructure()` Private Method**

- Implement checks:
    - Entry point exists and is of type `start`.
    - At least one `end` node exists.
    - All node IDs referenced in edges (`from`, `to`) exist in `nodesMap`.
    - All node IDs referenced in `GraphInputValueSource` (for `fromNodeOutput`) exist.
    - All `graphInitialInput` paths are declared in `expectedInitialInputsConfig`.
    - All `graphConstant` keys are declared in `graphConstantsMap`.
    - (V1 Basic Cycle Detection): Simple DFS from entry point, track visited nodes in current path. If a visited node is encountered again before path unwinds, it's a cycle. Ignore cycles formed by `addLoop`'s internal `updateStateNodeId -> conditionNodeId` edge. Report other cycles as errors or warnings.
    - (Optional V1) Check for orphaned nodes (nodes with no incoming/outgoing edges unless start/end).

**8. Testing Strategy**

- Extensive unit tests for each builder method, especially `addLoop` and input resolution with fallbacks.
- Test `compile()` validations thoroughly.
- Test output of `compile()` against known good `GraphDefinition` objects for simple and complex graphs.

Unit Tests for Builder Methods:
Test each addXNode method creates the correct GraphNodeDefinition.
Test input resolution logic in resolveInputMappings for all GraphInputValueSource types, including fallbacks.
Test edge creation methods (onSuccess, onFailure, onBranch, otherwise, addEdge).
Test addLoop creates the correct internal node structure and edges.
Unit Tests for compile():
Test validation logic: throws errors for invalid node references, missing entry/end points.
Test that a valid sequence of builder calls produces the expected GraphDefinition object.
Integration Tests (Conceptual): Define a few sample graphs using the GraphBuilder, compile them, and verify the structure of the output GraphDefinition. Compare against manually crafted expected objects.
Test visualization output (generateMermaidDiagram) for simple graphs.

**9. Integration Points**

- Used within `src/common/graphs/definitions/` to define all operational graphs.
- Output (`GraphDefinition` and `FunctionRegistry`) is consumed by `GraphExecutor`.

---