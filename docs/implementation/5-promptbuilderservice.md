**SINGLE COMPONENT IMPLEMENTATION PLAN: PromptBuilderService.ts**

**1. COMPONENT OVERVIEW**

- **Purpose:** The PromptBuilderService is responsible for dynamically constructing the complete ModelMessage[] array that AI agents will pass to the LLMService for their LLM interactions. It ensures prompts are well-structured, contextually relevant, include necessary instructions and schemas, and respect token limits.
- **Scope:**
    - Accepts agent-specific core task descriptions and dynamic data.
    - Loads and populates agent-specific base prompt templates.
    - Injects standardized sections into prompts:
        - "Team Awareness" context (current graph, other participating agents).
        - Clear definition of available input context fields for the agent.
        - Standardized instructions for output formatting, including the JSON schema (derived from Zod) for the agent's expected final output.
    - Integrates with RuleSelectorService to fetch and inject relevant N8nRule descriptions.
    - Integrates with KnowledgeRepository (via a RAG mechanism, potentially conceptual for V1 library) to fetch and inject relevant knowledge snippets (e.g., n8n documentation, best practices).
    - Manages conversation history for prompts:
        - Appends a summary of older conversation parts (TaskSession.llmContextSummary).
        - Appends recent relevant conversation turns (TaskSession.llmContextRelevantHistory).
        - **Crucially, if the assembled prompt context (history + new data) exceeds a threshold before the main LLM call, it will use LLMService to invoke the n8n-summarizer-agent to update TaskSession.llmContextSummary to make space.**
    - Formats all dynamic data using consistent tagging (e.g., \u003cuser_request\u003e, \u003clive_node_schema\u003e).
    - (Potentially) Performs a final token count on the assembled messages to provide a warning if it's close to the target model's limit, though LLMService also does pre-flight checks.
- **Dependencies (Conceptually, as these will be implemented later or mocked for unit testing PromptBuilderService):**
    - LLMService: To invoke n8n-summarizer-agent if needed for context compression.
    - RuleSelectorService: To get relevant rules for injection.
    - KnowledgeRepository (and EmbeddingService): For RAG (V2 for full docs, V1 might be simpler for core best practices).
    - TaskSession (or TaskSessionRepository access): To get conversation history, summaries, and current graph context.
    - GraphDefinition (via GraphExecutionContext or TaskSession): To derive "Team Awareness" info.
    - Agent Configuration (AGENT_REGISTRY_CONFIG or similar): To fetch base prompt templates and agent role descriptions.
    - Zod Schemas & zod-to-json-schema: To get JSON schema strings for output format instructions.
    - ModelMessage type (from Vercel AI SDK / ai package).
- **Dependents:**
    - All AI Agents (via BaseN8nAgent): Will use PromptBuilderService (likely injected into BaseN8nAgent or its AgentDependencies) to prepare their ModelMessage[] before calling LLMService.
- **Key Design Decisions (from our brainstorming):**
    - Centralized and standardized prompt assembly logic.
    - Dynamic injection of contextual information (schemas, rules, team awareness, history).
    - Proactive context window management via summarization if needed.
    - Separation of agent's core logic (what it wants to say) from prompt boilerplate.
    - Templates for different parts of the prompt (system message, standard sections).
- **Evolution Notes:**
    - Initially, prompt building was an implicit part of each agent.
    - We realized the need for a dedicated service to handle the increasing complexity of dynamic context injection, rule integration, history management, and standardized instructions (like output schemas and team awareness) to ensure consistency and maintainability.
    - The idea of this service proactively calling a summarizer agent to manage context window is a key advanced feature.

**2. COMPLETE CONTEXT PACKAGE (For Developer LLM with Zero Context)**

- **System Architecture Overview:**
    
    Our n8n AI Assistant uses a graph-based engine (GraphExecutor) to run AI workflows (GraphDefinition). These workflows are composed of AI Agents (specialized TypeScript classes performing LLM-driven tasks) and Tools. Agents need to send carefully constructed prompts to LLMs to get their tasks done. All LLM interactions are channeled through a central LLMService.
    
- **This Component's Role (PromptBuilderService):**
    
    The PromptBuilderService is a critical utility service that acts as an intelligent "prompt factory" for all AI Agents. Before an agent makes a call to an LLM (via LLMService), it uses the PromptBuilderService to assemble the complete set of messages (ModelMessage[]). This service ensures that every prompt is consistently formatted, includes all necessary operational context (like who the agent is, what other agents are in the current workflow, what data it has received), relevant guidance (best practices, custom rules), and crucial instructions (like the exact JSON schema its output must conform to). It also plays a key role in managing the overall context window by integrating conversation history and potentially triggering summarization if the prompt becomes too long.
    
- **Related Components:**
    - **AI Agents (BaseN8nAgent subclasses):** Primary consumers. They provide their specific task details and dynamic data to this service.
    - **LLMService:** PromptBuilderService might use LLMService to invoke the n8n-summarizer-agent if context compression is needed.
    - **RuleSelectorService:** Provides lists of relevant rule descriptions to be injected.
    - **KnowledgeRepository (RAG):** Provides relevant knowledge snippets (e.g., documentation) for injection.
    - **TaskSession / ConversationStore:** Source of conversation history and summaries.
    - **GraphDefinition / GraphExecutionContext:** Source for "Team Awareness" information (other agents in the graph).
    - **Agent Configuration / Prompt Templates:** Stores base prompt structures for different agents.
    - **Zod Schemas:** Used to generate JSON Schemas for output formatting instructions.
- **Design Philosophy:**
    - **DRY (Don't Repeat Yourself):** Centralize common prompt construction logic.
    - **Contextual Intelligence:** Dynamically tailor prompts with the most relevant information.
    - **Maintainability:** Changes to standard prompt sections or context injection logic are made in one place.
    - **LLM Guidance:** Provide LLMs with clear, structured, and comprehensive instructions to improve output quality and adherence to desired formats.
    - **Proactive Context Management:** Help prevent context window overflow errors.
- **Technical Constraints:**
    - Must produce ModelMessage[] compatible with Vercel AI SDK.
    - Needs to be efficient, as it's called before every significant LLM interaction.
    - Operates in TypeScript, multi-platform (Node.js/browser).

**3. EXTRACTED DESIGN DECISIONS**

1. **Decision:** Create a dedicated service for prompt assembly.
    - **Rationale:** Avoids repetitive and complex prompt logic in every agent. Ensures consistency.
    - **Final Form:** PromptBuilderService class.
2. **Decision:** Dynamically inject standardized prompt sections.
    - **Rationale:** "Team Awareness," input definitions, and output schema instructions should be consistent and auto-updated.
    - **Evolution:** Initially thought of these as static parts of agent prompts, then moved to dynamic injection for maintainability.
    - **Final Form:** PromptBuilderService will have logic to generate these sections.
3. **Decision:** Integrate RuleSelectorService and KnowledgeRepository (RAG).
    - **Rationale:** Provide agents with relevant guidance and knowledge directly in their prompts.
    - **Final Form:** PromptBuilderService calls these services and formats their output for prompt injection.
4. **Decision:** Manage conversation history (summary + recent turns) within the prompt.
    - **Rationale:** Give LLMs necessary conversational context.
    - **Final Form:** PromptBuilderService fetches history/summary from TaskSession and formats it.
5. **Decision:** Proactively trigger summarization via n8n-summarizer-agent (using LLMService) if assembled prompt context is too large *before* the main agent's LLM call.
    - **Rationale:** Avoid context window errors from the main agent's LLM call. Improves robustness.
    - **Final Form:** PromptBuilderService will check token count of assembled history + new data. If too high, it invokes summarizer, updates TaskSession summary, then re-assembles prompt with new shorter summary.
6. **Decision:** Inject JSON schema of agent's expected final output into its prompt.
    - **Rationale:** Force LLM to adhere to strict output formats. Auto-updates with Zod schema changes.
    - **Final Form:** Uses zod-to-json-schema and injects the stringified schema.
7. **Decision:** Use consistent tagging for different context parts in the prompt (e.g., \u003cuser_request\u003e).
    - **Rationale:** Improves LLM's ability to differentiate and use various pieces of context.
    - **Final Form:** PromptBuilderService will use predefined XML-like tags.

**4. TECHNICAL SPECIFICATIONS**

- **TypeScript Interfaces (src/background/services/promptBuilderService.types.ts or co-located):**
    
    `import { ModelMessage } from 'ai';
    import { TaskSession } from '@/common/types/tasks.types'; // Assumed type
    import { GraphDefinition } from '@/common/types/graph.types.ts';
    import { N8nRule } from '@/common/types/rules.types.ts'; // Assumed type
    import { KnowledgeChunk } from '@/common/types/knowledge.types.ts'; // Assumed type
    
    // Data specifically provided by an agent for *its current LLM call*
    export interface AgentCallSpecificData {
      // Key-value pairs, where value can be string, number, boolean, object, array.
      // Keys will be used for tagging, e.g., _USER_FULL_REQUEST, _STRUCTURED_REQUIREMENTS
      [placeholderKey: string]: any; 
    }
    
    // Options for building the prompt
    export interface PromptBuilderOptions {
      // If the agent's current call expects a specific Zod schema for *its internal processing*
      // different from its final output schema to the graph.
      overrideOutputJsonSchemaString?: string; 
      // For models that have separate system prompts vs. user/assistant messages
      systemPromptTemplateKey?: string; // Key to a specific system prompt template for this agent/sub-task
      userMessageTemplateKey?: string;  // Key to a specific user message template
      // Max tokens specifically for the history/context part of THIS prompt (excluding agent's new content)
      maxContextTokensForThisCall?: number; 
    }
    
    export interface AssembledPrompt {
      messages: ModelMessage[];
      promptTokenCount: number; // Estimated token count of these messages
      contextWasSummarized: boolean; // True if summarization was triggered for this build
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **External Dependencies:**
    - ai (Vercel AI SDK): For ModelMessage type.
    - zod-to-json-schema: To convert agent output Zod schemas to JSON schema strings.
    - Tokenizer (e.g., gpt-tokenizer): For token counting to manage context limits.
- **Internal Dependencies (Services/Repositories/Types):**
    - LLMService: To call n8n-summarizer-agent.
    - RuleSelectorService: To get rules.
    - KnowledgeRepository: To get knowledge snippets.
    - TaskSession (and its llmContextSummary, llmContextRelevantHistory).
    - GraphDefinition (for current graph details).
    - AGENT_REGISTRY_CONFIG or similar: To get agent core prompt templates, role descriptions, and final output Zod schemas (to convert to JSON schema).
    - N8nRule, KnowledgeChunk types.
- **Multi-Platform Requirements:**
    - Tokenizer needs to work in Node/Browser.
    - All logic is TypeScript, should be platform-agnostic.
- **Configuration Schema:**
    - Relies on agent configurations for base prompt templates.
    - Relies on LLMService (and thus UserSettingsService) for model context window sizes (to know when to summarize).
    - Thresholds for triggering summarization (e.g., "if context exceeds 70% of model window, summarize").
- **Error Handling Contract:**
    - buildPromptMessagesForAgentCall can throw errors if:
        - Agent slug not found in config (for templates/schemas).
        - Required dynamic data placeholder not provided in specificDataForThisLLMCall.
        - Summarization attempt fails critically (though LLMService should handle its own errors).
    - It aims to *prevent* ContextWindowExceededError in LLMService by managing context proactively.

**5. ULTRA-DETAILED IMPLEMENTATION GUIDE (PromptBuilderService.ts)**

- **File Structure:**
    - src/background/services/promptBuilderService.ts
    - src/background/services/promptBuilderService.types.ts
    - src/common/prompt_templates/ (Directory to store base prompt templates for agents/sections)
        - agents/n8n-architect-agent.system.md
        - agents/n8n-node-composer-agent.system.md
        - sections/team_awareness_block.md
        - sections/output_format_instruction_block.md
        - sections/input_context_definition_block.md
- **Import Strategy:** Standard TypeScript imports.
- **PromptBuilderService Class Definition:**
    
    `export class PromptBuilderService {
      private llmService: LLMService;
      private ruleSelectorService: RuleSelectorService;
      private knowledgeRepository: KnowledgeRepository; // Or a RAG service
      private taskSessionManager: TaskSessionManager; // To get/update TaskSession
      private agentConfigs: AgentConfigRegistry; // Access to AGENT_REGISTRY_CONFIG
      private tokenizer: (text: string, modelId?: string) => number; // e.g., countTokens
      private chatTokenizer: (messages: ModelMessage[], modelId?: string) => number; // e.g., countChatTokens
    
      constructor(
        llmService: LLMService,
        ruleSelectorService: RuleSelectorService,
        knowledgeRepository: KnowledgeRepository,
        taskSessionManager: TaskSessionManager,
        agentConfigs: AgentConfigRegistry // Map or object from AGENT_REGISTRY_CONFIG
        // Inject tokenizer functions
      ) { /* ... assign dependencies ... */ }
    
      // --- Public Method ---
      public async buildPromptMessagesForAgentCall(
        agentSlug: string,
        currentGraphDefinition: GraphDefinition | null, // Null if called outside a graph context
        taskSession: TaskSession, // Current TaskSession to read history/summary from, and potentially update summary
        specificDataForThisLLMCall: AgentCallSpecificData, // Data the agent wants to pass for THIS specific call
        options?: PromptBuilderOptions
      ): Promise<AssembledPrompt> { /* ... main logic ... */ }
    
      // --- Private Helper Methods ---
      private loadPromptTemplate(templateKey: string): string { /* Loads from src/common/prompt_templates/ */ }
      private getAgentConfig(agentSlug: string): AgentConfigEntry { /* ... */ }
      private generateTeamAwarenessBlock(currentAgentSlug: string, graphDef: GraphDefinition | null): string { /* ... */ }
      private generateInputContextDefinitionBlock(actualInputsProvidedToAgent: string[]): string { /* ... */ }
      private generateOutputFormatInstructionBlock(agentSlugForOutputSchema: string, overrideSchemaStr?: string): string { /* ... */ }
      private formatDynamicData(data: AgentCallSpecificData): string { /* Converts object to tagged string block */ }
      private assembleMessages(
          baseSystemPrompt: string, 
          standardSections: Record<string, string>, // e.g., team_awareness, input_def, output_instr
          formattedConversationHistory: ModelMessage[], // Summary + recent
          formattedRules: string, 
          formattedKnowledge: string,
          formattedSpecificCallData: string,
          agentTaskMessage: string // The core "user" message for this specific agent call
      ): ModelMessage[] { /* ... constructs final ModelMessage array ... */ }
      private async manageContextWindowAndSummarizeIfNeeded(
        taskSession: TaskSession, 
        currentMessagesWithoutHistory: ModelMessage[], // System prompt + new data
        targetModelId: string, // To get context window size
        maxContextTokensForThisCall?: number
      ): Promise<{ updatedTaskSession: TaskSession, historyMessagesForPrompt: ModelMessage[], summarized: boolean }> { /* ... */ }
    }`
    
    **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
    
- **Core Logic Flow for buildPromptMessagesForAgentCall:**
    1. **Get Agent Config:** agentConfig = this.getAgentConfig(agentSlug). This provides base system prompt template key, role description, and its final output Zod schema (to generate JSON schema).
    2. **Determine Target Model for Token Limits:** Based on agentSlug's default LLMTaskCategory (from agentConfig) and UserSettings (via LLMService or UserSettingsService), determine the likely model to be used for this call. Get its context window size from ModelPricingConfig.capabilities.
    3. **Prepare Standard Sections (Strings):**
        - teamAwareness = this.generateTeamAwarenessBlock(agentSlug, currentGraphDefinition).
        - inputContextDef = this.generateInputContextDefinitionBlock(Object.keys(specificDataForThisLLMCall)).
        - outputFormatInstr = this.generateOutputFormatInstructionBlock(agentSlug, options?.overrideOutputJsonSchemaString). (Gets Zod schema from agentConfig, converts to JSON schema string).
    4. **Fetch Contextual Data (Strings):**
        - rulesText = (await this.ruleSelectorService.getRulesForAgentPrompt(...)).map(r => r.descriptionForLLM).join('\\n').
        - knowledgeText = (await this.knowledgeRepository.retrieveSnippets(...)).map(k => k.chunkText).join('\\n'). (V1 might have simpler RAG).
    5. **Format specificDataForThisLLMCall:** formattedSpecificData = this.formatDynamicData(specificDataForThisLLMCall) (e.g., into an XML-like block).
    6. **Load Base System Prompt:** baseSystemPromptTemplate = this.loadPromptTemplate(agentConfig.systemPromptTemplateKey).
    7. **Initial System Message Assembly (without history yet):** Create a preliminary system message by replacing placeholders in baseSystemPromptTemplate with teamAwareness, inputContextDef, outputFormatInstr, rulesText, knowledgeText, formattedSpecificData.
    8. **Manage Context Window & Get History (manageContextWindowAndSummarizeIfNeeded):**
        - Create a temporary ModelMessage[] with the assembled system message parts and the agent's core task message (which comes from specificDataForThisLLMCall._CORE_AGENT_TASK_DESCRIPTION_).
        - Estimate token count for these non-history messages (tempTokenCount).
        - Retrieve taskSession.llmContextSummary and taskSession.llmContextRelevantHistory.
        - Iteratively add recent history messages, checking token count against (targetModelContextWindow * threshold_for_summarization) - tempTokenCount.
        - If total tokens still exceed limit even with just summary + minimal recent history:
            - Invoke n8n-summarizer-agent (via this.llmService) with taskSession.llmContextSummary + taskSession.llmContextRelevantHistory.
            - Receive new, more compressed summary.
            - **Update taskSession.llmContextSummary with the new summary** (via this.taskSessionManager.updateTaskSession(taskSession)). This is a side-effect.
            - Set summarized = true.
            - Re-fetch history based on the *new* summary and minimal recent turns.
        - Return { updatedTaskSession, historyMessagesForPrompt: ModelMessage[], summarized }.
    9. **Final Message Assembly (assembleMessages):**
        - Take the (potentially updated) taskSession from step 8.
        - Combine the final system prompt (with all injected sections), the historyMessagesForPrompt, and the agent's core task message from specificDataForThisLLMCall into the final ModelMessage[] array.
        - The core agent task message (e.g., "Design a plan for X") typically goes into the last HumanMessage.
    10. **Final Token Count:** Calculate token count for the final ModelMessage[] using this.chatTokenizer.
    11. Return AssembledPrompt { messages, promptTokenCount, contextWasSummarized }.
- **generateTeamAwarenessBlock Helper:**
    - Input: currentAgentSlug, graphDefinition.
    - Logic: Gets graphDefinition.description. Iterates graphDefinition.nodes to find other agentCall nodes, gets their slugs, looks up their role descriptions from this.agentConfigs. Formats the "You are agent X in graph Y. Other agents are Z, W..." string.
- **generateOutputFormatInstructionBlock Helper:**
    - Input: agentSlugForOutputSchema, overrideSchemaStr.
    - Logic: Gets the final output Zod schema for agentSlugForOutputSchema from this.agentConfigs. Converts to JSON schema string using zod-to-json-schema. Injects into a template string: "Your result.payload MUST conform to: {JSON_SCHEMA_STRING}". Handles override.
- **State Management:** PromptBuilderService is largely stateless itself but reads from TaskSession and can *trigger an update* to TaskSession.llmContextSummary via TaskSessionManager after summarization.
- **Async Patterns:** All methods involving service calls (RuleSelector, KnowledgeRepo, LLMService for summarizer) are async.
- **Error Handling:** Standard try/catch for service calls. Throws specific errors if configs/templates missing.
- **Platform Differences:** Tokenizer usage might need consideration for Web Workers in browser if main thread performance is an issue.

**6. INTEGRATION SPECIFICATIONS**

- **Input Contracts:**
    - buildPromptMessagesForAgentCall takes agentSlug, currentGraphDefinition, taskSession, specificDataForThisLLMCall, options?.
- **Output Contracts:**
    - Returns Promise<AssembledPrompt>, where AssembledPrompt.messages is ready for LLMService.
- **Event Patterns:** None directly emitted. If summarization occurs, TaskSession is updated, which might trigger events elsewhere if TaskSessionManager emits them.
- **Service Injection:** Constructor injection for its dependencies (LLMService, RuleSelectorService, etc.).
- **Lifecycle Management:** Instantiated once (singleton) or per orchestrator instance. No complex cleanup.

**7. QUALITY ASSURANCE REQUIREMENTS**

- **Unit Testing Strategy:**
    - Mock all external services (LLMService, RuleSelectorService, KnowledgeRepository, TaskSessionManager, agentConfigs).
    - **Test Case Groups:**
        - **Standard Section Injection:** Verify teamAwareness, inputContextDef, outputFormatInstr are correctly generated and injected based on mock graph/agent configs.
        - **Dynamic Data Formatting:** Verify specificDataForThisLLMCall is correctly tagged and injected.
        - **Rule/Knowledge Injection:** Mock RuleSelectorService/KnowledgeRepository to return data; verify it's formatted and injected.
        - **Conversation History Management (No Summarization):**
            - Test with empty history, short history, long history (but below summarization threshold). Verify correct summary + recent turns are included.
        - **Context Window Management & Summarization Trigger:**
            - Mock TaskSession with long history + provide large specificDataForThisLLMCall to exceed token threshold.
            - Verify LLMService.invokeModel is called with n8n-summarizer-agent slug.
            - Mock summarizer agent's response (new shorter summary).
            - Verify TaskSessionManager.updateTaskSession is called with the new summary.
            - Verify the final AssembledPrompt.messages uses the *new* summary and contextWasSummarized is true.
        - **Output Schema Injection:** Verify correct JSON schema string for the target agent's output is included. Test with overrideOutputJsonSchemaString.
        - **Token Counting:** Verify AssembledPrompt.promptTokenCount is reasonably accurate (based on mock tokenizer).
        - **Error Conditions:** Test missing agent configs, missing templates.
- **Edge Case Handling:** Empty specificDataForThisLLMCall, no rules/knowledge found, summarization still results in oversized context (should handle gracefully, maybe by truncating more aggressively or returning an error if still too big).
- **Performance Criteria:** Prompt building should be fast. Summarization call is the only LLM call, so its latency is subject to LLMService.

**8. VALIDATION CRITERIA**

- All public methods implemented as specified.
- Correctly loads and uses prompt templates.
- Dynamically generates and injects "Team Awareness," "Input Context Definition," and "Output Format Instruction (with JSON Schema)" sections.
- Correctly formats and injects data from specificDataForThisLLMCall, rules, and knowledge snippets.
- Accurately manages conversation history (summary + recent turns) in the prompt.
- Reliably triggers summarization via n8n-summarizer-agent (through LLMService) when calculated prompt tokens exceed model limits minus a buffer, and uses the new summary.
- Handles all specified error conditions gracefully.
- Produces ModelMessage[] and AssembledPrompt in the correct format.