---
description: Design/UI/UX instructions to follow when implementing customer-facing UI & interfaces
globs: 
alwaysApply: false
---
  <design_instructions>
    Overall Goal: Create visually stunning, unique, highly interactive, content-rich, and production-ready applications. Avoid generic templates.

    Visual Identity & Branding:
      - Establish a distinctive art direction (unique shapes, grids, illustrations).
      - Use premium typography with refined hierarchy and spacing.
      - Incorporate microbranding (custom icons, buttons, animations) aligned with the brand voice.
    
    Layout & Structure:
      - Implement a systemized spacing/sizing system (e.g., 8pt grid, design tokens).
      - Use fluid, responsive grids (CSS Grid, Flexbox) adapting gracefully to all screen sizes (mobile-first).
      - Employ atomic design principles for components (atoms, molecules, organisms).
      - Utilize whitespace effectively for focus and balance.

    User Experience (UX) & Interaction:
      - Design intuitive navigation and map user journeys.
      - Implement smooth, accessible microinteractions and animations (hover states, feedback, transitions) that enhance, not distract.
      - Use predictive patterns (pre-loads, skeleton loaders) and optimize for touch targets on mobile.
      - Ensure engaging copywriting and clear data visualization if applicable.

    Color & Typography:
      - Color system with a primary, secondary and accent, plus success, warning, and error states
      - Smooth animations for task interactions
      - Modern, readable fonts
      - Intuitive task cards, clean lists, and easy navigation
      - Responsive design with tailored layouts for mobile (<768px), tablet (768-1024px), and desktop (>1024px)
      - Subtle shadows and rounded corners for a polished look

    Animations:
      - Smooth transitions between screens and states (e.g., opening a product, selecting an option).
      - Immediate visual feedback for user interactions (taps, swipes).
      - Animations should be subtle, purposeful, and enhance the feeling of quality - not distracting or gimmicky. Think easing functions that provide a sense of natural movement.
    
    Loading States:
      - Elegant shimmer effects (skeleton loaders)
      - Avoid jarring spinners

    Technical Excellence:
      - Write clean, semantic HTML with ARIA attributes for accessibility (aim for WCAG AA/AAA).
      - Ensure consistency in design language and interactions throughout.
      - Pay meticulous attention to detail and polish.
      - Always prioritize user needs and iterate based on feedback.

    **Premium User Experience (UX) and User Interface (UI):**
    THIS IS REALLY IMPORTANT!! The design must reflect the luxury nature of the products and brand, providing a sophisticated and intuitive experience. Using the website should give a premium feel, with smooth, fluid and natural animations using UX best practices and latest trends. Place attention to detail in transitions between pages and elements, to maintain focus and make the shopping journey intuitive and uninterrupted. Incrorporate fluid animations, making browsing and using the website not just a task but an enjoyable, responsive, and efficient experience.
  </design_instructions>
