/**
 * n8n AI Assistant - Node.js Example
 * 
 * This file demonstrates using the n8n AI Assistant library in a Node.js environment.
 * 
 * Run with: node examples/node.js
 */

// Import the library
const {
  createAgent,
  createGraph,
  createTool,
  getEnvironment,
  createStorageProvider
} = require('../dist/node/index.js');

// Helpers for output formatting
function separator() {
  console.log('\n' + '-'.repeat(60) + '\n');
}

function displaySection(title, data) {
  separator();
  console.log(`\x1b[1m${title}:\x1b[0m`);
  console.log(JSON.stringify(data, null, 2));
}

// Check environment
const env = getEnvironment();
displaySection('Environment', env);

// Create an agent
const agent = createAgent({
  name: 'NodeAgent',
  capabilities: ['workflow-creation', 'data-processing']
});
displaySection('Created Agent', agent);

// Create a graph
const graph = createGraph({
  name: 'DataPipelineGraph',
  nodes: ['extract', 'transform', 'load'],
  edges: [
    { from: 'extract', to: 'transform' },
    { from: 'transform', to: 'load' }
  ]
});
displaySection('Created Graph', graph);

// Create and use a tool
const tool = createTool({
  name: 'JsonParser',
  description: 'Parses and validates JSON data',
  execute: async (input) => {
    try {
      const parsed = typeof input === 'string' ? JSON.parse(input) : input;
      return { valid: true, data: parsed };
    } catch (err) {
      return { valid: false, error: err.message };
    }
  }
});

// Execute the tool
(async () => {
  // Test with valid JSON
  const validResult = await tool.execute('{"name": "Test", "value": 123}');
  displaySection('Tool Execution - Valid JSON', {
    tool: tool,
    executionResult: validResult
  });

  // Test with invalid JSON
  const invalidResult = await tool.execute('{name: Test}');
  displaySection('Tool Execution - Invalid JSON', {
    tool: tool,
    executionResult: invalidResult
  });

  // Test storage
  const storage = createStorageProvider();
  await storage.set('test-key', { message: 'Stored from Node.js' });
  const value = await storage.get('test-key');
  
  displaySection('Storage Test', {
    storedValue: value,
    storageType: getEnvironment().name
  });
})(); 