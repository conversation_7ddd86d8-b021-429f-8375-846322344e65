<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>n8n AI Assistant - Browser Example</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    button {
      padding: 8px 16px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .output {
      margin-top: 20px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>n8n AI Assistant - Browser Example</h1>
  
  <p>This example demonstrates using the n8n AI Assistant library in a browser environment.</p>
  
  <div>
    <button id="createAgent">Create Agent</button>
    <button id="createGraph">Create Graph</button>
    <button id="createTool">Create Tool</button>
    <button id="checkEnv">Check Environment</button>
    <button id="testStorage">Test Storage</button>
  </div>
  
  <div class="output">
    <h3>Output</h3>
    <pre id="result">Click a button to test functionality...</pre>
  </div>

  <!-- Import the library -->
  <script src="../dist/browser/index.js"></script>
  
  <script>
    // Access the global n8nAiAssistant object
    const { 
      createAgent, 
      createGraph, 
      createTool, 
      getEnvironment,
      createStorageProvider
    } = window.n8nAiAssistant;
    
    // Output element
    const resultEl = document.getElementById('result');
    
    // Display function
    function displayResult(label, data) {
      resultEl.textContent = `${label}:\n${JSON.stringify(data, null, 2)}`;
    }
    
    // Button handlers
    document.getElementById('createAgent').addEventListener('click', () => {
      const agent = createAgent({ 
        name: 'BrowserAgent', 
        capabilities: ['workflow-creation', 'text-analysis']
      });
      displayResult('Created Agent', agent);
    });
    
    document.getElementById('createGraph').addEventListener('click', () => {
      const graph = createGraph({
        name: 'WorkflowGraph',
        nodes: ['input', 'process', 'output'],
        edges: [
          { from: 'input', to: 'process' },
          { from: 'process', to: 'output' }
        ]
      });
      displayResult('Created Graph', graph);
    });
    
    document.getElementById('createTool').addEventListener('click', () => {
      const tool = createTool({
        name: 'TextFormatter',
        description: 'Formats text with various styles',
        execute: async (input) => {
          return { formatted: input.toUpperCase() };
        }
      });
      
      // Execute the tool as demonstration
      tool.execute('Hello from the browser!').then(result => {
        displayResult('Tool Execution Result', {
          tool: tool,
          executionResult: result
        });
      });
    });
    
    document.getElementById('checkEnv').addEventListener('click', () => {
      const env = getEnvironment();
      displayResult('Environment Info', env);
    });
    
    document.getElementById('testStorage').addEventListener('click', async () => {
      const storage = createStorageProvider();
      await storage.set('test-key', { message: 'Stored from browser' });
      const value = await storage.get('test-key');
      
      displayResult('Storage Test', {
        storedValue: value,
        storageType: getEnvironment().name
      });
    });
  </script>
</body>
</html> 