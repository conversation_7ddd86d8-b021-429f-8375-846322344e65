{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(cp:*)", "Search(*)", "Search(pattern:*)", "Search(pattern:*, include:*)", "Read(*)", "Write(*)", "Edit(*)", "mcp__claudepoint__setup_claudepoint", "mcp__claudepoint__create_checkpoint", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "mcp__claudepoint__set_changelog", "<PERSON><PERSON>(mv:*)", "Bash(pnpm type-check:*)", "Bash(grep:*)", "Bash(pnpm test:*)", "Bash(npm test:*)"]}, "enableAllProjectMcpServers": true}