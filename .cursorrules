<project_summary>
**System Summary: n8n Agentic AI "Brain" Library (browser + node)**

**1. Core Philosophy and Architecture**

- **Objective:** To create an intelligent AI assistant capable of understanding user requests related to n8n, designing n8n workflows, generating their JSON, validating them against best practices, and assisting with documentation and explanation.
- **Architecture:** A custom, graph-based orchestration engine built in pure TypeScript. This engine executes `GraphDefinition`s, which are comprised of interconnected `GraphNodeDefinition`s (representing agent calls, tool calls, conditional logic, data transformations, etc.).
- **LLM Integration:** All Large Language Model (LLM) interactions are managed via the Vercel AI SDK Core (v5 alpha), abstracted through a dedicated `LLMService`.
- **Multi-Platform:** The core library is designed to be compatible with both Node.js (for development, testing, CLI) and browser environments (for eventual integration into a Chrome extension).
- **State Management:** Persistent state (task sessions, conversation history, user settings, knowledge) is managed by RxDB, providing multi-platform storage capabilities (IndexedDB/Dexie in browser, LokiJS/FileSystem in Node.js).
- **Modularity & Extensibility:** The system is designed with highly modular components (agents, tools, services) with clear responsibilities and interfaces, facilitating maintainability and future expansion.
- **Developer Experience:** A fluent `GraphBuilder` API is provided to simplify the definition of complex graphs. Agents extend a `BaseN8nAgent` class with helper methods for standardized responses and access to shared services.

**2. Component Inventory & Descriptions**

CRITICAL: Please note that the implementation details below may change, so ALWAYS check the actual files before implementing anything to be sure to have the latest inmplementation in mind. DON'T MAKE ASSUMPTIONS BASED ON THE CONTENT BELOW. this is just to give you a high-level understanding and view on the project and his vision.

- **A. Orchestration Engine:**
    - **`GraphBuilder` API:**
        - **Description:** A fluent API (TypeScript class) for programmatically defining `GraphDefinition` objects. It simplifies graph construction with methods like `addNode`, `addAgentNode` (takes agent class constructor), `addToolNode`, `addConditionalNode`, `addLoop`, `addEdge`, and fluent edge methods (`.onSuccess()`, `.onFailure()`, `.onBranch()`, `.otherwise()`, `.onLoopComplete()`).
        - **Responsibilities:** Abstracts the complexity of the raw `GraphDefinition` object, provides type safety during graph definition, handles loop construct creation internally, allows registration of a default error handler for the graph, and provides methods like `setInitialNode` for clear entry point definition. Its `compile()` method produces a validated `GraphDefinition`.
        - **Key Feature:** Manages `lastAddedNodeId` for intuitive fluent edge chaining.
    - **`GraphDefinition` (Interface/Type):**
        - **Description:** A TypeScript object representing the structure and logic of an executable graph. It's the "program" for the `GraphExecutor`.
        - **Structure:** Contains `id`, `description`, `version`, `entryNodeId`, an array of `nodes: GraphNodeDefinition[]`, an array of `edges: GraphEdgeDefinition[]`, `expectedInitialInputs`, `graphConstants`, `defaultMaxIterationsForAllLoops?`, `defaultMaxLoopIterations?: Record<loopId, number>`, and an optional `defaultErrorHandlerNodeId`.
    - **`GraphNodeDefinition` (Interface/Type):**
        - **Description:** Defines a single node within a `GraphDefinition`.
        - **Structure:** Contains `id`, `type` (e.g., `agentCall`, `toolCall`, `conditionalBranch`, `dataTransform`, `start`, `end`), `description?`, `inputs?: GraphNodeInputMapping`, and type-specific properties like `agentSlug`, `llmTaskCategory` (for `agentCall`), `toolName` (for `toolCall`), `conditionFunction` (reference/key for `conditionalBranch`), `transformFunction` (reference/key for `dataTransform`).
    - **`GraphInputValueSource` (Type):**
        - **Description:** Defines how a node's input field should be populated.
        - **Types:** `fromGraphInitialInput`, `fromNodeOutput` (with `nodeId`, `path?`, `outputVersion?`, `zodSchema?`, `defaultValue?`, and an optional `fallbackSource`), `fromGraphConstant`, `staticValue`.
    - **`GraphEdgeDefinition` (Interface/Type):**
        - **Description:** Defines a directed connection between two nodes in a graph.
        - **Structure:** `from` (source node ID), `to` (target node ID), `label?` (e.g., `onSuccess`, `onFailure`, `__otherwise__`, custom branch label from a conditional node), `description?`.
    - **`GraphExecutionContext` (Interface/Type):**
        - **Description:** Holds the runtime state for a single execution instance of a graph. It is created by the `OrchestratorService` and managed by the `GraphExecutor`.
        - **Key Fields:** `graphDefinitionId`, `graphDefinitionVersion`, `taskSessionId`, `currentGraphRunId`, `status` (running, paused, completed, etc.), `initialInputs`, `nodeOutputs: Map<string, NodeOutputVersion[]>` (versioned outputs of each node), `processingQueue: string[]` (nodes to run next), `loopCounters: Map<loopId, number>`, `maxLoopIterations: Map<loopId, number>`, `activeInterruptDetails?` (for HIL), and injected `services` (like `LLMService`, `NodeSpecService`, etc.).
    - **`NodeOutputVersion` (Interface/Type):**
        - **Description:** Represents a single, versioned output from a graph node execution.
        - **Key Fields:** `runId`, `timestamp`, `output` (the actual data), `status` (success, failure, interrupted_for_user_input), `error?`, `thought?` (from agents), `llmTokenUsage?`, `interruptPayload?` (for HIL).
    - **`GraphExecutor` (Class, part of `OrchestratorService`):**
        - **Description:** The core engine that interprets and runs a `GraphDefinition`. It is generic and stateless itself; all state is in `GraphExecutionContext`.
        - **Responsibilities:**
            - Manages the `GraphExecutionContext` for a graph run.
            - Traverses the graph based on `GraphDefinition` (nodes, edges).
            - Manages a `processingQueue` of nodes to execute.
            - Resolves inputs for each node using `GraphInputValueSource` logic (including `fallbackSource`).
            - Invokes agent execution, tool execution, data transforms, or conditional logic.
            - Stores versioned `NodeOutputVersion`s in `GraphExecutionContext.nodeOutputs`.
            - Determines the next node(s) based on edge labels and conditions.
            - Handles HIL pauses by recognizing specific agent output statuses/payloads and updating `GraphExecutionContext.status`.
            - Manages loop iteration counts against `maxLoopIterations` (defined in graph or per loop).
            - Implements a `MAX_TOTAL_GRAPH_STEPS` safeguard against infinite cycles.
            - Routes to explicit `onFailure` edges or the `graphDefinition.defaultErrorHandlerNodeId`.
- **B. Core Services:**
    - **`OrchestratorService.ts`:**
        - **Description:** High-level service that manages the overall user interaction flow and coordinates graph executions.
        - **Responsibilities:** Handles incoming user messages, uses `n8n-intent-router-agent` to determine action (start new graph, resume), manages `TaskSession` lifecycle, instantiates and runs `GraphExecutor`, handles HIL resume logic, and orchestrates post-graph "Apply & Review" sequences (conceptually for V1 library).
    - **`LLMService.ts`:**
        - **Description:** Centralized wrapper for all Vercel AI SDK v5 interactions.
        - **Responsibilities:** Selects LLM models based on `LLMTaskCategory` and `UserSettings`, attempts Chrome AI for suitable tasks, handles `UIMessage` <> `ModelMessage` conversion, manages API call retries, tracks token usage and cost per call, streams custom data parts (thoughts, steps) for UI, attaches metadata to messages.
    - **`PromptBuilderService.ts`:**
        - **Description:** Utility service used by agents to construct `ModelMessage[]` for LLM calls.
        - **Responsibilities:** Injects standardized sections into prompts (Team Awareness, Input Context Definition, Output Format Instructions with JSON Schema for agent's final output), manages conversation history context (summary + recent turns, possibly triggering `n8n-summarizer-agent`), formats dynamic data with standard tags, injects relevant rules/best practices (from `RuleSelectorService`), ensures token limits for specific calls.
    - **`NodeSpecService.ts`:**
        - **Description:** Loads, caches, and provides access to full n8n node specifications.
        - **Responsibilities:** Fetches `/types/nodes.json` (via `system_fetch_all_node_specs_tool` in browser, or loads from file in Node.js), caches this data, provides methods like `getNodeSpec(typeId, version?)`, `findNodes(query?)`, `getAllNodeDescriptors()`. Used primarily by `NodeSelectorAgent` and `NodeComposerAgent`.
    - **`ToolRegistry.ts` & `ToolExecutorService.ts`:**
        - **Description:** `ToolRegistry` holds definitions of all available tools. `ToolExecutorService` is called by `ToolCallNodeDef` in the graph to execute a specific tool.
        - **Responsibilities:** `ToolExecutorService` validates inputs against tool's Zod schema, calls tool's `execute` method, validates output (optional), handles tool-specific retries, returns standardized tool execution result or error.
    - **`RuleSelectorService.ts`:**
        - **Description:** Selects relevant `N8nRule`s to be injected into agent prompts.
        - **Responsibilities:** `getRulesForAgentPrompt()`: Fetches `ALWAYS_APPLY` rules for the current agent and contextually relevant `CONTEXTUAL_SUGGESTION` rules (V1: keyword matching; V2: Chrome AI).
    - **`EmbeddingService.ts` (used by `KnowledgeRepository`):**
        - **Description:** Handles generation of text embeddings.
        - **Responsibilities:** Uses `transformers.js` (e.g., `Xenova/all-MiniLM-L6-v2`) to convert text chunks into vector embeddings. Uses Web Workers in browser for performance.
- **C. AI Agents (`src/background/agents/`):**
    - **`BaseN8nAgent.ts` (Abstract Class):**
        - **Description:** Parent class for all specialized AI agents.
        - **Features:** Defines `static readonly agentSlug` and `static readonly defaultLlmTaskCategory`. Constructor takes `AgentDependencies` (injected services like `LLMService`, `PromptBuilderService`, `NodeSpecService`). Provides helper methods for standardized responses (`this.success()`, `this.failure()`, `this.requestUserClarification()`, `this.requestGraphTools()`), and `this.getJsonSchemaString()` utility.
    - **Specialized Agents (V1 List):**
        - `n8n-intent-router-agent`: Classifies user input to determine overall intent or route to a graph/action. (Uses `UTILITY_CLASSIFICATION_ROUTING`).
        - `n8n-requirements-gathering-agent`: Elicits detailed requirements from the user, with HIL clarification loops. (Uses `KNOWLEDGE_AUGMENTED_EXPLANATION`).
        - `n8n-architect-agent`: Designs high-level n8n workflow plans, including triggers, steps, node suggestions, data flow summaries, and "Focused Design Spikes" for complex components. (Uses `CORE_ORCHESTRATION_AND_PLANNING`).
        - `n8n-node-selector-agent`: Selects specific n8n node types based on the Architect's plan and `NodeSpecService` data. (Uses `ANALYTICAL_VALIDATION_CRITIQUE`).
        - `n8n-node-composer-agent`: Generates complete JSON for individual n8n nodes, using live schemas (from `NodeSpecService`) and detailed syntax rules. (Uses `N8N_CODE_GENERATION_COMPOSITION`).
        - `n8n-workflow-documenter-agent`: Creates and manages Sticky Note documentation for workflows. (Uses `KNOWLEDGE_AUGMENTED_EXPLANATION` for content, `N8N_CODE_GENERATION_COMPOSITION` for JSON).
        - `n8n-connection-composer-agent`: Generates the `connections` JSON for the workflow. (Uses `N8N_CODE_GENERATION_COMPOSITION`).
        - `n8n-best-practice-validator-agent`: Analyzes workflow JSON against n8n best practices and custom rules. (Uses `ANALYTICAL_VALIDATION_CRITIQUE`).
        - `n8n-reflection-agent`: Critiques and suggests refinements for the outputs of other key agents (Architect, Composer). (Uses `ANALYTICAL_VALIDATION_CRITIQUE`).
        - `n8n-summarizer-agent`: Summarizes conversation history for context window management. (Uses `UTILITY_CONVERSATION_SUMMARIZATION`).
    - Each agent has a defined Zod schema for its `result.payload` on successful completion.
- **D. Data Persistence Layer (RxDB & Repositories):**
    - **`rxdbSetup.ts`:** Initializes RxDB with appropriate storage adapters (Dexie for browser, LokiJS/FileSystem for Node.js) and collection schemas.
    - **RxDB Collections:**
        - `task_sessions`: Stores `TaskSession` objects.
        - `chat_threads`: Stores metadata for ongoing conversations/projects.
        - `conversation_messages`: Stores individual `ChatMessage` (aligning with Vercel AI SDK `UIMessage`) objects.
        - `custom_rules`: Stores user-defined `N8nRule`s.
        - `knowledge_chunks`: Stores pre-processed text chunks and their embeddings for RAG (n8n docs, workflow templates, best practices).
        - `user_settings`: Stores user preferences (API keys, model choices).
    - **Domain-Specific Repositories (e.g., `TaskSessionRepository`, `ChatRepository`, `CustomRulesRepository`, `KnowledgeRepository`, `UserSettingsRepository`):**
        - **Description:** Classes that abstract RxDB interactions for specific data domains. They take the RxDB database instance and provide typed CRUD and query methods.
        - **Example (`KnowledgeRepository`):** Handles adding chunks (with embedding generation via `EmbeddingService`), and performing vector search using custom indexing on RxDB.
- **E. State Management Objects:**
    - **`TaskSession`:** Manages state for a single high-level user task (e.g., "create a workflow"). Contains the active `GraphExecutionContext` (or history), overall task status, `llmContextSummary` for the task, accumulated cost, etc. Persisted by `TaskSessionRepository`.
    - **`ChatMessage` / `UIMessage`:** Represents a single message in the conversation, including content parts (text, tool calls, custom data for UI), sender, role, and Vercel AI SDK v5 `message.metadata` (our custom token usage, thoughts, etc.). Persisted by `ChatRepository`.
- **F. Knowledge & Rules Objects:**
    - **`N8nRule` (Interface/Type):** Structure for a single best practice or custom rule (`id`, `name`, `descriptionForLLM`, `type: \\u0027ALWAYS_APPLY\\u0027 | \\u0027CONTEXTUAL_SUGGESTION\\u0027 | \\u0027ANTI_PATTERN_CHECK\\u0027`, `appliesToAgents[]`, `triggerKeywordsOrContext?[]`).
    - **`KnowledgeChunk` (Interface/Type):** Structure for RAG documents (`id`, `sourceType`, `chunkText`, `embedding`, `metadata`).
- **G. Core Data Types (Zod Schemas in `src/common/zodSchemas.ts`):**
    - `WorkflowSchema`: Defines the structure of an entire n8n workflow (nodes, connections, settings).
    - `NodeJsonSchema`: Defines the structure of a single n8n node's JSON.
    - `WorkflowConnectionsSchema`: Defines the structure of n8n connections.
    - `NodeDescriptionSchema`: Zod schema for individual items in `/types/nodes.json`.
    - Output schemas for all agents (e.g., `ArchitectOutputSchemaV1`, `NodeComposerOutputSchema`).
    - Input/Output schemas for all tools.
    - `LLMTaskCategory` (Enum): `CORE_ORCHESTRATION_AND_PLANNING`, etc.
    - `AgentExecutionResult`, `ToolExecutionResult` (Standardized wrappers for agent/tool outputs).
- **H. Agent Registry (`src/background/agents/agentRegistry.ts`):**
    - Maps `agentSlug` strings to `AgentRegistryEntry` objects, which include the agent's class constructor and default `LLMTaskCategory`. Used by `GraphExecutor` to instantiate agents.

**3. Dependency Mapping (High-Level Conceptual):**

- **User Input** -\u003e `OrchestratorService`
- `OrchestratorService` uses `n8n-intent-router-agent` (via `LLMService`), `TaskSessionManager` (backed by `TaskSessionRepository`), `ChatManager` (backed by `ChatRepository`), and instantiates `GraphExecutor`.
- `GraphExecutor` uses `GraphDefinition`, manages `GraphExecutionContext`.
    - `GraphExecutionContext` holds `nodeOutputs` and injected `services`.
- **Nodes within a Graph:**
    - `AgentCallNode` (executed by `GraphExecutor`):
        - `GraphExecutor` uses `AgentRegistry` to get Agent instance (injecting `AgentDependencies`).
        - Agent `execute()` method uses:
            - `this.promptBuilder` (instance of `PromptBuilderService`).
            - `this.llmService` (instance of `LLMService`).
            - `this.nodeSpecService` (instance of `NodeSpecService`).
            - Response helpers (`this.success()`, etc.).
    - `ToolCallNode` (executed by `GraphExecutor`):
        - `GraphExecutor` uses `ToolExecutorService`.
        - `ToolExecutorService` uses `ToolRegistry` to get `ToolDefinition`.
        - Tool `execute()` method might use `NodeSpecService`, `KnowledgeRepository`, or message `main-world.ts` (for V1 library, main-world interactions are conceptual or file-based).
    - `DataTransformNode` / `ConditionalBranchNode`: Logic is part of `GraphDefinition`, executed by `GraphExecutor`.
- **`PromptBuilderService`** uses `RuleSelectorService`, `ConversationManager`/`TaskSession` (for history/summary), `n8n-summarizer-agent` (via `LLMService`), and Zod-to-JSON-Schema utilities.
- **`LLMService`** uses Vercel AI SDK, `UserSettings` (for model choice, API keys, via `UserSettingsRepository`).
- **RxDB Repositories** use the initialized RxDB instance.
- **`KnowledgeRepository`** uses `EmbeddingService`.
- **`EmbeddingService`** uses `transformers.js`.

**4. Interface Definitions (High-Level Conceptual APIs):**

- **`GraphBuilder.addAgentNode(id, AgentClass, llmTaskCategoryOverride?, inputs?, description?) : this`**
- **`GraphExecutor.run(): AsyncGenerator<GraphEvent, GraphExecutionResult, void>`**
- **`BaseN8nAgent.execute(inputs, graphContext, taskSession) : Promise<AgentExecutionResult>`**
- **`ToolDefinition.execute(args, graphContext, taskSessionId) : Promise<any>`**
- **`LLMService.invokeModel(taskCategory, messages, options) : Promise<LLMOutputWrapper>`**
- **`PromptBuilderService.buildPromptMessagesForAgentCall(agentSlug, subTaskDesc, taskSession, specificData, overrides?) : Promise<ModelMessage[]>`**
- **`NodeSpecService.getNodeSpec(typeId, version?) : Promise<NodeDescription | null>`**
- **`KnowledgeRepository.vectorSearch(queryEmbedding, queryIndexValues, topK, filters?) : Promise<KnowledgeChunkWithScore[]>`**
- **`TaskSessionRepository.save(taskSession) : Promise<void>`**

**5. Architecture Diagrams (Conceptual):**

- **Overall System Flow:**`User Input -> OrchestratorService -> (IntentRouterAgent) -> GraphExecutor -> (Graph Nodes: AgentCalls/ToolCalls) -> LLMService -> Vercel AI SDK -> LLMGraphExecutor -> (Agent/Tool Output) -> GraphExecutionContext -> OrchestratorService -> (UI Update / Next Graph Step)`
- **Agent Execution Flow:**`GraphExecutor provides inputs to Agent.execute() -> Agent.execute() uses PromptBuilderService to get ModelMessages -> Agent.execute() uses LLMService.invokeModel(ModelMessages) -> LLMService interacts with Vercel AI SDK -> LLM responds -> LLMService returns LLMOutputWrapper to Agent -> Agent processes response -> Agent returns AgentExecutionResult to GraphExecutor.`
- **Data/Service Access:**
    - RxDB (Core Data Store) \u003c-- Repositories (`TaskSessionRepo`, `ChatRepo`, etc.)
    - Repositories \u003c-- Managers/Services (`TaskSessionManager`, `KnowledgeService`, etc.)
    - `GraphExecutionContext.services` provides access to `LLMService`, `NodeSpecService`, `ToolExecutorService`, `RuleSelectorService` for nodes during execution.
- **Loop Structure (Conceptual, via `GraphBuilder.addLoop`):**`Input_to_Loop -> [LoopBuilder creates: InitStateNode -> ConditionNode --(body_path)--\\u003e IterationDataProviderNode -> (User-Defined Loop Body Nodes) -> UpdateStateNode --\\u003e ConditionNode ] --(exit_path)--\\u003e LoopExitAggregatorNode -> Output_from_Loop`
</project_summary>