**Product Requirements Document: n8n Agentic AI "Brain" Library (V1.0)**

## 1. EXECUTIVE SUMMARY

### 1.1 Product Vision

- **Core Mission**: To create a highly intelligent, multi-platform TypeScript library serving as the "AI Brain" for an n8n assistant. This brain will be capable of understanding user requests in natural language, designing complex n8n workflows, generating their precise JSON representations, validating workflows against best practices, and assisting with documentation, explanation, and optimization of n8n automations.
- **Target Users**:
    - **Primary**: n8n users (from beginner to advanced) seeking AI assistance to build, understand, and improve their workflows more efficiently.
    - **Secondary**: Developers integrating this library into tools or extensions for n8n users.
- **Value Proposition**:
    - Accelerate n8n workflow development significantly.
    - Lower the barrier to entry for complex n8n features and concepts.
    - Improve the quality and robustness of user-created workflows by embedding n8n best practices.
    - Provide a powerful, flexible, and extensible AI foundation for n8n assistance tools.
    - Offer a transparent and debuggable AI orchestration layer for advanced users and developers.
- **Success Metrics**:
    - Time-to-completion for common workflow creation tasks (reduction via AI).
    - Adherence of AI-generated workflows to defined n8n best practices (measured by internal validator agent).
    - User satisfaction scores (qualitative, future for UI integration).
    - Number of successful graph executions for core tasks (e.g., "create new workflow").
    - Modularity and test coverage of the library itself.

### 1.2 Strategic Objectives

- **Primary Goals**:
    1. Develop a robust, graph-based AI orchestration engine in pure TypeScript.
    2. Implement a suite of specialized AI agents for n8n workflow design, composition, and validation.
    3. Integrate seamlessly with LLMs via Vercel AI SDK v5 alpha.
    4. Ensure the library is multi-platform (Node.js for development/testing, browser for direct integration into a self-contained extension using our graphs/library directly in the browser to orchestrate and perform calls to LLM services).
    5. Establish a clear, maintainable, and highly decoupled architecture.
- **Business Impact**: Potential to significantly increase n8n adoption and user productivity, establish a leading AI-assisted experience for workflow automation.
- **Technical Innovation**: Custom graph orchestration without reliance on external agentic frameworks (like LangChain/LangGraph), advanced prompt engineering subsystem, and a highly modular service-oriented design.
- **Market Position**: Aims to be the most advanced and deeply integrated AI assistant for n8n users, offering expert-level guidance.

### 1.3 Project Scope

- **In Scope (V1 Library - "The Brain"):**
    - Core graph execution engine (GraphExecutor, GraphBuilder).
    - Foundational services (LLMService, NodeSpecService, PromptBuilderService hierarchy, TaskSessionManager, UserSettingsService, RuleSelectorService, ConversationHistoryService, ToolExecutorService).
    - Persistence layer (rxdbSetup, core repositories: UserSettingsRepository, TaskSessionRepository, CustomRulesRepository).
    - Agent infrastructure (BaseAgent, agentRegistry).
    - A defined set of V1 specialized AI agents (IntentRouter, RequirementsGatherer, Architect, NodeSelector, NodeComposer, WorkflowDocumenter, ConnectionComposer, Validator, Summarizer).
    - A defined set of V1 core tools (primarily for knowledge access and (mocked for library V1) n8n interaction placeholders).
    - A V1 graph definition for "Create New Workflow."
    - Implementation of "Update Existing Workflow" and "Analyze Workflow" graphs.
    - Bundled base n8n rules and LLM model configuration.
    - Comprehensive unit and integration testing for the library.
    - Advanced RAG on full n8n documentation / community workflows + MCP servers integration (context7 for n8n documentation, custom MCP server to query github for workflows examples, etc)
- **Out of Scope (V1 Library):**
    - User Interface (UI) / Chrome Extension frontend implementation (this library is the backend brain).
    - Direct, live n8n instance manipulation from the V1 library (tools for this will be mocked or conceptual, with real implementation deferred to extension integration). We should built something to then easily integrate this into our browser extension, and for now to have the developer manually peform the actions on the n8n instance. this is a requirement as the library should be compatible with node (for CLI/etc) and browser, so it should work WITH OR WITHOUT direct access o an n8n instance via browser extension
    - User accounts, authentication, or multi-user collaboration features.
    - Real-time collaborative editing features.
    - Advanced visual graph debugging UI for the GraphExecutor.
- **Future Roadmap (Post-V1 Library):**
    - Integration into a Chrome Browser Extension.
    - Development of UI/UX for the assistant.
    - Enhanced RAG capabilities.
    - Visual graph execution tracing.
    - User-configurable custom tool integration.
- **Dependencies:**
    - Node.js (for development, testing, and library's Node.js compatibility).
    - Modern browser environment (for browser compatibility of the library).
    - Vercel AI SDK Core v5 alpha and its dependencies.
    - RxDB and its storage adapter dependencies.
    - Zod for schema validation.
    - Tokenizer library.

### 1.4 High-Level Architecture

- **System Type**: Multi-platform TypeScript library acting as the AI "Brain."
- **Core Technology**: Custom-built, graph-based agentic orchestration engine using declarative GraphDefinitions executed by a GraphExecutor.
- **LLM Integration**: All LLM communication managed centrally via LLMService, which wraps the Vercel AI SDK Core v5 alpha.
- **Target Platforms**: Designed to be fully compatible with both Node.js environments (for testing, development, CLI usage) and modern browser environments (for future Chrome extension integration).
- **Integration Path:** The V1 library will be testable via command-line interfaces and unit/integration tests. Its design anticipates future integration into the full browser extension. Tools requiring direct n8n instance should be developed in a way that could be executed either by the developer (in CLI usage) OR, when the “brain” will be called directly in/by the browser extension, by the browser etxnesion itself

## 2. SYSTEM ARCHITECTURE & DESIGN PHILOSOPHY

### 2.1 Architectural Principles

- **Design Philosophy**:
    - **Maximum Reasonable Decoupling:** Components are designed with single, clear responsibilities and minimal, well-defined interfaces to promote modularity, testability, and independent evolution.
    - **Clarity & Explicitness:** System structure, data flow, and control flow are designed to be as explicit and understandable as possible, both in code and in graph definitions. "Magic" is avoided.
    - **Robustness & Resilience:** Emphasis on comprehensive error handling, retries for transient issues, and graceful degradation.
    - **Extensibility:** The architecture is designed to easily accommodate new agents, tools, graphs, and AI capabilities.
    - **Developer Experience (DX):** Prioritize clean APIs, clear naming conventions, and tools (like GraphBuilder) that make developing and maintaining the system efficient and intuitive.
    - **TypeScript First:** Leverage TypeScript's strong typing and OOP/functional paradigms to build a robust and maintainable codebase.
- **Quality Standards**:
    - **Code Quality:** Adherence to clean code principles, consistent formatting, comprehensive JSDoc/TSDoc comments.
    - **Maintainability:** Code should be easy to understand, modify, and debug. Small, focused modules are preferred.
    - **Performance:** While V1 focuses on correctness, the design considers performance implications, especially for LLM interactions, data processing, and context management.
    - **Testability:** All components must be designed for easy unit and integration testing, with clear mocking strategies for dependencies. Aim for high test coverage.
- **Modularity Strategy**:
    - **Component-Based Design:** The system is broken down into well-defined components (Engine Core, Services, Repositories, Agents, Tools, Configurations, Prompts, Types, Utilities).
    - **Interface-Driven Development:** Interactions between major components are defined by clear TypeScript interfaces and Zod schemas for data contracts.
    - **Layered Architecture:** Clear separation between application/orchestration logic (app/), higher-level application services (services/), core engine infrastructure (engine/), data persistence (persistence/), and concrete agent implementations (agents/).
- **Extensibility Vision**:
    - New AI agents can be added by extending BaseAgent and registering them in agentRegistry.
    - New tools can be added by implementing ToolDefinition and registering them in toolRegistry.
    - New graphs can be defined using GraphBuilder without modifying the core GraphExecutor.
    - The RAG system (KnowledgeRepository, EmbeddingService) is designed to incorporate new knowledge sources.
    - The LLMService can be extended to support new LLM providers compatible with Vercel AI SDK.
- **Multi-Platform Strategy**:
    - The core library is pure TypeScript, designed to run in Node.js (for CLI, testing, development) and modern browser environments.
    - Environment-specific functionalities (e.g., RxDB storage adapters, file system access for NodeSpecService in Node.js) are handled via conditional logic or abstracted interfaces.
    - The V1 library will focus on the AI logic. Direct n8n instance DOM manipulation or API calls requiring an active browser extension context will be designed as tool interfaces within the library, with the actual implementation being provided by the consuming environment (e.g., a CLI tool simulates it, the Chrome extension provides the live bridge).

### 2.2 Core Architecture Patterns

- **Graph-Based Orchestration**:
    - **Rationale:** Provides maximum flexibility for defining and executing complex, multi-step AI processes involving multiple agents and tools. Declarative graph definitions are easier to understand, modify, and visualize than hardcoded imperative logic. Allows for clear control flow (conditionals, loops) and data flow management.
    - **Implementation:** Custom GraphDefinition objects are interpreted and executed by a generic GraphExecutor. A fluent GraphBuilder API simplifies graph creation.
- **Agent-Centric Design**:
    - **Rationale:** Specialized AI agents, each with a single, well-defined responsibility (e.g., planning, node selection, code composition), promote modularity and allow for tailored prompting and LLM usage for each task. This aligns with building an "AI team."
    - **Implementation:** Agents extend a BaseAgent class, are managed by an agentRegistry, and are invoked as nodes within a graph. They use a sophisticated prompting subsystem (engine/prompts/) for LLM interaction.
- **Service-Oriented Architecture (Internal)**:
    - **Rationale:** Key functionalities (LLM interaction, prompt building, node specification access, data persistence, task state management) are encapsulated in dedicated services with clear APIs. This promotes SRP and testability.
    - **Implementation:** Services are typically classes with injected dependencies. Clear distinction between lower-level "engine services" (engine/llm/, engine/prompts/services/) and higher-level "application services" (services/).
- **Event-Driven Communication (Conceptual for HIL & Future UI):**
    - **Rationale:** For Human-in-the-Loop interactions and future UI updates, an event-driven pattern is suitable. The GraphExecutor pausing for user input is an example of an internal event.
    - **Implementation (V1):** HIL pauses are managed by the GraphExecutor changing GraphExecutionContext.status and storing activeInterruptDetails. The OrchestratorService observes this and manages communication with the (conceptual) UI. Full event bus is V2.
- **State Management Strategy**:
    - **Rationale:** Persistent state is essential for UserSettings, TaskSession (including graph execution snapshots for pause/resume), conversation history, custom rules, and cached knowledge (RAG embeddings). RxDB was chosen for its multi-platform capabilities, local-first approach, querying, and schema support.
    - **Implementation:** rxdbSetup.ts initializes the database. Domain-specific Repositories (UserSettingsRepository, TaskSessionRepository, etc.) abstract RxDB interactions.

### 2.3 Technology Stack Rationale

- **TypeScript Selection**:
    - **Rationale:** Strong typing for improved code quality, maintainability, refactorability, and early error detection in a complex system. Excellent tooling and community support. Aligns with modern JavaScript development.
    - **Alternatives:** Plain JavaScript (less type safety).
- **Vercel AI SDK Core v5 alpha Integration**:
    - **Rationale:** Provides a modern, unified API for interacting with various LLMs, supporting streaming, tool usage, and structured outputs. Content-first design with ModelMessage and UIMessage (with metadata and custom parts) is ideal for rich interactions.
    - **Alternatives:** Direct provider SDKs (OpenAI, Anthropic SDKs) would require more abstraction work. Other generic LLM libraries might not be as cutting-edge or well-suited for the Vercel ecosystem.
- **RxDB for Persistence**:
    - **Rationale:** True multi-platform (Node.js/Browser) local-first database. Supports JSON document storage, schemas, Mango queries, and has good performance characteristics for client-side operations. Crucially, it allows for custom indexing suitable for implementing client-side vector search for RAG, as demonstrated in examples.
    - **Alternatives:** chrome.storage.local (too limited in size and querying, browser-only), IndexedDB directly (more boilerplate), SQLite via WASM (larger bundle, more complex setup for V1).
- **Custom vs. Framework (for Agent Orchestration)**:
    - **Rationale for Custom:** Building our graph engine (GraphExecutor, GraphBuilder) from scratch provides maximum flexibility to tailor the orchestration precisely to our n8n-specific needs and agent interaction patterns. It avoids imposing the opinions or limitations of external agentic frameworks like LangChain/LangGraph.js (though we draw inspiration from their concepts like stateful graphs and HIL). Allows for a leaner, more optimized core. We also take inspiration from Openai Agents SDK and Google ADK framework.
    - **Alternatives:** LangChain.js/LangGraph.js (mature but can be opinionated and add overhead; our goal was a pure TS, from-scratch "brain").
- **Build and Distribution (Conceptual for V1 Library):**
    - The library will be built using a standard TypeScript build process (e.g., tsc or a bundler like Rollup/esbuild for different module formats if needed for distribution).
    - Packaging as an npm module is the likely distribution method.

### **Final Agreed Target Project Structure (V3.3.1):**

`src/
├── 📄 index.ts
├── 📁 __tests__/
│   └── 📄 setup.ts
│
├── 📁 agents/                     // CONCRETE AI AGENT IMPLEMENTATIONS
│   ├── 📁 architect/
│   │   ├── 📄 N8nArchitectAgent.ts
│   │   └── 📄 architect.prompts.ts
│   ├── ...
│   └── 📄 index.ts
│
├── 📁 app/                        // APPLICATION-LEVEL ORCHESTRATION
│   ├── 📄 OrchestratorService.ts
│   ├── 📄 index.ts
│   └── 📁 __tests__/
│
├── 📁 config/                     // GLOBAL STATIC CONFIGURATIONS
│   ├── 📄 baseN8nRules.config.ts
│   ├── 📄 llmModels.config.ts
│   ├── 📄 nodes.snapshot.json
│   └── 📄 index.ts
│
├── 📁 engine/                     // CORE AI ENGINE INFRASTRUCTURE
│   ├── 📁 agents/                   // Agent *infrastructure*
│   │   ├── 📄 BaseAgent.ts
│   │   ├── 📄 agentRegistry.ts
│   │   ├── 📄 initializeAgents.ts
│   │   └── 📁 types/
│   │       ├── 📄 agentRegistry.types.ts
│   │       └── 📄 agents.types.ts
│   ├── 📁 graph/                    // Graph execution engine
│   │   ├── 📄 GraphBuilder.ts
│   │   ├── 📄 GraphExecutor.ts
│   │   └── 📁 types/
│   │       ├── 📄 builder.types.ts
│   │       ├── 📄 execution.types.ts
│   │       └── 📄 graph.types.ts
│   ├── 📁 llm/                      // LLM Interaction Subsystem
│   │   ├── 📄 LLMService.ts
│   │   └── 📁 types/
│   │       └── 📄 llmService.types.ts
│   ├── 📁 prompts/                  // PROMPT ENGINEERING SUBSYSTEM (Utilities & Services)
│   │   ├── 📁 formatters/           // (SystemInstructionFormatter.ts, etc.)
│   │   ├── 📁 normalizers/            // (MessageNormalizer.ts, etc.)
│   │   ├── 📁 mergers/                // (MessageMerger.ts)
│   │   ├── 📁 parts/                  // (system.parts.ts, etc. - Universal prompt snippets)
│   │   ├── 📁 services/               // Prompt *orchestration* services WITHIN THE ENGINE
│   │   │   ├── 📄 GenericPromptBuilder.ts
│   │   │   ├── 📄 AgentPromptBuilder.ts
│   │   │   ├── 📄 ConversationHistoryService.ts
│   │   │   ├── 📄 RuleSelectorService.ts
│   │   │   └── 📁 types/              // Types for these prompting services
│   │   ├── 📄 index.ts
│   │   └── 📁 types/
│   │       └── 📄 prompting.types.ts    // Common types for this subsystem (PromptComponent, etc.)
│   └── 📄 index.ts                   // Exports key engine infrastructure
│
├── 📁 graphs/                     // User-defined GRAPH DEFINITIONS
│   ├── 📄 createNewWorkflow.graph.ts
│   └── 📄 index.ts
│
├── 📁 knowledge/                  // RAG System
│   ├── 📄 EmbeddingService.ts
│   └── ...
│
├── 📁 persistence/                // DATA PERSISTENCE LAYER (RxDB)
│   ├── 📄 rxdbSetup.ts
│   ├── 📁 repositories/
│   └── ...
│
├── 📁 services/                   // HIGHER-LEVEL APPLICATION SERVICES
│   ├── 📄 UserSettingsService.ts
│   ├── 📄 TaskSessionManager.ts
│   ├── 📄 NodeSpecService.ts        // Domain data service
│   ├── 📄 KnowledgeService.ts (Future)
│   └── 📁 types/                   // Types specific to these application services (e.g. nodeSpecService.types.ts)
│
├── 📄 index.ts                    // Main library barrel export
│
├── 📁 tools/                      // TOOL DEFINITIONS, REGISTRY & EXECUTOR
│   ├── 📄 toolRegistry.ts
│   ├── 📄 ToolExecutorService.ts
│   └── ...
│
├── 📁 types/                      // GLOBAL SHARED DOMAIN ZOD SCHEMAS & TYPES
│   ├── 📄 nodes.types.ts           // NodeDescriptionSchema, NodeSelectorDescriptor (used by NodeSpecService)
│   └── ...
│
└── 📁 utils/
    └── ...`

*(Note: __tests__ subdirectories are implied for most modules and omitted here for brevity but should be present as per the original structure you provided).*

### 2.4 System Architecture Diagrams

**Mermaid Diagrams for n8n Agentic AI "Brain" Library**

**Diagram 1: High-Level System Architecture & Key Layers**

This diagram shows the main layers and how they generally interact.

`graph LR
    subgraph User/External
        A1[User Input/Request]
    end

    subgraph ApplicationLayer [src/app/ & src/services/]
        B1(OrchestratorService)
        B2(TaskSessionManager)
        B3(UserSettingsService)
        B4(NodeSpecService)
        B5(KnowledgeService - V2)
    end

    subgraph ConcreteAgents [src/agents/]
        C1[N8nArchitectAgent]
        C2[N8nNodeComposerAgent]
        C3[...]
    end

    subgraph EngineLayer [src/engine/]
        D1(GraphExecutor)
        D2(GraphBuilder)
        D3(BaseAgent Infrastructure)
        D4(LLMService)
        D5(Prompting Subsystem)
    end
    
    subgraph EngineSubsystem_Prompting [engine/prompts/services/]
        P1(AgentPromptBuilder)
        P2(GenericPromptBuilder)
        P3(ConversationHistoryService)
        P4(RuleSelectorService)
    end

    subgraph EngineSubsystem_LLM [engine/llm/]
        L1(LLMService)
    end

    subgraph EngineSubsystem_Graph [engine/graph/]
        G1(GraphExecutor)
        G2(GraphBuilder)
    end
    
    subgraph EngineSubsystem_AgentInfra [engine/agents/]
        AG1(BaseAgent)
        AG2(agentRegistry)
    end

    subgraph PersistenceLayer [src/persistence/]
        E1(RxDB Setup)
        E2(Repositories e.g., TaskSessionRepo)
    end

    subgraph ConfigLayer [src/config/]
        F1[Static Configurations e.g., baseN8nRules]
    end

    subgraph ToolsLayer [src/tools/]
        T1(ToolExecutorService)
        T2(ToolRegistry & Definitions)
    end
    
    subgraph ExternalIntegrations
        X1[Vercel AI SDK]
        X2[LLM Providers]
        X3[n8n Instance APIs - via Extension Bridge]
        X4[Tokenizer Lib]
    end

    A1 --> B1;
    B1 --> D1;
    B1 --> B2;
    B1 --> C1;
    B1 --> C2;
    
    D1 --> D3;
    D1 --> T1;
    D3 --> P1;
    D3 --> L1; 
    
    P1 --> P2;
    P1 --> P3;
    P1 --> P4;
    P3 --> E2; % CHS uses TaskSessionRepo (via TSM) & ChatRepo
    P3 --> L1; % CHS uses LLMService for summarizer
    P3 --> P2; % CHS uses GenericPromptBuilder for summarizer prompt
    P4 --> E2; % RuleSelectorService uses CustomRulesRepo
    P4 --> F1; % RuleSelectorService uses baseN8nRules

    C1 --> D3; % Concrete agents extend BaseAgent
    C2 --> D3;

    D1 -.-> B2; % GraphExecutor signals TaskSessionManager to save state
    
    L1 --> X1;
    X1 --> X2;
    L1 --> B3; % LLMService uses UserSettingsService for API keys/prefs
    L1 --> X4; % For token counting

    B2 --> E2; % Manager uses Repository
    B3 --> E2;
    B4 --> F1; % NodeSpecService uses nodes.snapshot.json
    B4 -.-> X3; % NodeSpecService can be primed from live n8n instance

    T1 --> T2;
    
    E1 --> E2;
    
    classDef app fill:#f9d,stroke:#333,stroke-width:2px;
    classDef engine fill:#dfd,stroke:#333,stroke-width:2px;
    classDef persist fill:#ddf,stroke:#333,stroke-width:2px;
    classDef config fill:#fef,stroke:#333,stroke-width:2px;
    classDef tools fill:#ffd,stroke:#333,stroke-width:2px;
    classDef external fill:#eee,stroke:#333,stroke-width:2px;

    class A1,B1,B2,B3,B4,B5,C1,C2,C3 app;
    class D1,D2,D3,P1,P2,P3,P4,L1,G1,G2,AG1,AG2 engine;
    class E1,E2 persist;
    class F1 config;
    class T1,T2 tools;
    class X1,X2,X3,X4 external;`

**Diagram 2: GraphExecutor - Core Execution Loop (Conceptual)**

`graph TD
    Start((Start Graph Execution)) --> InitContext{Initialize GraphExecutionContext};
    InitContext --> QueueNotEmpty{Processing Queue NOT Empty?};
    QueueNotEmpty -- Yes --> CheckMaxSteps{Total Steps < MAX_GRAPH_STEPS?};
    CheckMaxSteps -- No (Limit Reached) --> FailGraphMaxSteps[Fail Graph: Max Steps];
    QueueNotEmpty -- No --> Finalize[Finalize Execution (Result/Error)];
    
    CheckMaxSteps -- Yes --> GetNode[Dequeue Next NodeID];
    GetNode --> FindNodeDef{Find NodeDefinition};
    FindNodeDef -- Found --> ResolveInputs{Resolve Node Inputs};
    FindNodeDef -- Not Found --> FailGraphNodeNotFound[Fail Graph: Node Not Found];
    
    ResolveInputs -- Success --> ExecuteNode{Execute Node Logic};
    ResolveInputs -- Error --> HandleNodeProcessingError[Process Node Error];
    
    ExecuteNode --> StoreOutput[Store NodeOutputVersion];
    StoreOutput --> CheckStatus{Check Output Status};
    
    CheckStatus -- interrupted_for_user_input --> PauseGraph[Set Graph Status: PAUSED_FOR_USER_INPUT];
    PauseGraph --> Finalize;
    
    CheckStatus -- failure --> HandleNodeFailure{Handle Node Failure};
    HandleNodeFailure -- Explicit onFailure Edge --> DetermineNext[Determine Next Node(s)];
    HandleNodeFailure -- Default Error Handler --> DetermineNext;
    HandleNodeFailure -- No Handler/Critical --> FailGraph[Set Graph Status: FAILED];
    FailGraph --> Finalize;
    
    CheckStatus -- success --> DetermineNext;
    DetermineNext --> EnqueueNext[Enqueue Next NodeID(s)];
    EnqueueNext --> QueueNotEmpty;
    
    HandleNodeProcessingError --> StoreErrorOutput[Store Error NodeOutputVersion];
    StoreErrorOutput --> HandleNodeFailure;

    FailGraphMaxSteps --> Finalize;
    FailGraphNodeNotFound --> Finalize;

    Finalize --> End((End Graph Execution));`

**Diagram 3: Agent LLM Call & Prompt Assembly Flow (Simplified)**

`graph TD
    subgraph AgentExecute [Agent's execute() method]
        A1[1. Agent Logic Decides to Call LLM] --> A2{Call this.prepareLlmMessages()};
    end

    subgraph BaseAgentHelpers [BaseAgent.prepareLlmMessages()]
        B1(prepareLlmMessages) --> B2[Calls AgentPromptBuilder.prepareAgentLlmMessages()];
    end
    
    subgraph AgentPromptBuilderService [src/engine/prompts/services/AgentPromptBuilder.ts]
        APB1(prepareAgentLlmMessages) --> APB2{Uses ConversationHistoryService for history block};
        APB2 --> APB3{Uses RuleSelectorService for rules block};
        APB3 --> APB4{Uses GenericPromptBuilder for final assembly};
    end

    subgraph GenericPromptBuilderService [src/engine/prompts/services/GenericPromptBuilder.ts]
        GPB1(buildMessages) --> GPB2{Uses Formatters (engine/prompts/formatters/)};
        GPB2 --> GPB3{Uses Normalizers (engine/prompts/normalizers/)};
        GPB3 --> GPB4{Uses Mergers (engine/prompts/mergers/)};
        GPB4 --> GPB5[Returns AssembledPrompt to APB];
    end

    subgraph ConversationHistoryService [src/engine/prompts/services/ConversationHistoryService.ts]
        CHS1(getManagedHistoryBlock) --> CHS2{Access TaskSession Summary/History (via Repos)};
        CHS2 -- Check Token Budget --> CHS3{Summarization Needed?};
        CHS3 -- Yes --> CHS4{Build Summarizer Prompt (using GenericPromptBuilder)};
        CHS4 --> CHS5{Call LLMService for Summarizer Agent};
        CHS5 --> CHS6{Update TaskSession Summary (via Repo)};
        CHS6 --> CHS7[Return Final History Block];
        CHS3 -- No --> CHS7;
    end
    
    A2 --> B1;
    B2 --> APB1;
    APB4 --> GPB1;
    
    APB1 -.-> CHS1; % APB uses CHS
    
    subgraph AgentExecuteCont [Agent's execute() method cont.]
        A3{Receives ModelMessage[] from prepareLlmMessages} --> A4{Calls this.deps.llmService.invokeModel()};
    end
    
    GPB5 -.-> APB1; % APB gets result from GPB
    APB1 -.-> A3; % APB returns messages to Agent
    
    A4 --> LLMS[LLMService.invokeModel()];
    LLMS --> VAI[Vercel AI SDK];
    VAI --> LLM((LLM Provider));
    LLM --> VAI;
    VAI --> LLMS;
    LLMS --> A5[Agent Receives LLMInvocationResult];
    A5 --> A6[Agent Logic Processes Result];`

**Diagram 4: GraphBuilder.addLoop() - Conceptual Internal Structure Created**

This shows the pattern of nodes the builder creates for a loop.

`graph TD
    LoopEntry(Input to Loop) --> Init[initLoopNode_loopId (DataTransform: Init State)];
    Init --> Cond[conditionNode_loopId (ConditionalBranch)];
    
    Cond -- loop_body_path --> IterData[iterationDataProviderNode_loopId (DataTransform: Get CurrentItem)];
    IterData --> BodyStart(User-Defined Loop Body Starts Here);
    BodyStart --> BodyLogic1[...];
    BodyLogic1 --> BodyLogicN[...];
    BodyLogicN --> UpdateState[updateLoopStateNode_loopId (DataTransform: Accumulate & Inc Index)];
    UpdateState --> Cond; % Loop back to condition
    
    Cond -- loop_exit_path --> Exit[loopExitAggregatorNode_loopId (DataTransform: Output Accumulator)];
    Exit --> LoopOutput(Output from Loop);

    style Init fill:#e6e6fa,stroke:#333;
    style Cond fill:#ffd700,stroke:#333;
    style IterData fill:#e6e6fa,stroke:#333;
    style UpdateState fill:#e6e6fa,stroke:#333;
    style Exit fill:#e6e6fa,stroke:#333;`

**Diagram 5: HIL (Human-in-the-Loop) Pause & Resume Flow**

`graph TD
    A[AgentX (e.g., RequirementsGatherer)] -- Decides HIL needed --> B(AgentX returns AgentExecutionResult <br> {status: 'needs_clarification', clarificationOrToolRequestDetails: HILPayload});
    B --> C{GraphExecutor receives result};
    C --> D[GraphExecutor sets GraphContext.status = 'PAUSED_FOR_USER_INPUT'];
    D --> E[GraphExecutor stores HILPayload in GraphContext.activeInterruptDetails];
    E --> F[GraphExecutor notifies OrchestratorService: Graph Paused];
    
    F --> G((UI Presents Question to User));
    G --> H[User Submits Response];
    
    H --> I{OrchestratorService receives User Response};
    I --> J[OrchestratorService calls GraphExecutor.resumeWithUserInput(userResponse)];
    
    subgraph GraphExecutorResume
        K[GraphExecutor.resumeWithUserInput()] --> L{Validates state};
        L --> M[Creates new NodeOutputVersion for AgentX <br> (output = userResponse, status = 'success')];
        M --> N[Stores this new output for AgentX];
        N --> O[Clears interrupt details, sets GraphContext.status = 'RUNNING'];
        O --> P[Calls determineNextNodes(AgentX_NodeDef, resumedOutputVersion)];
        P --> Q[Continues GraphExecutor.run() loop];
    end
    J --> K;`

**Diagram 6: Default Error Handling Flow**

`graph TD
    NodeX[NodeX Execution] -- Fails (Exception or returns Failure status) --> CheckExplicitHandler{GraphExecutor: Explicit onFailure edge for NodeX?};
    CheckExplicitHandler -- Yes --> CustomErrorPath[Route to NodeX.onFailure target];
    CheckExplicitHandler -- No --> CheckDefaultHandler{GraphExecutor: Graph defaultErrorHandlerNodeId defined?};
    CheckDefaultHandler -- Yes --> DefaultErrorHandler[Route to graph.defaultErrorHandlerNodeId <br> (Inputs: error, failedNodeId)];
    CheckDefaultHandler -- No --> CriticalFail[Graph Execution Fails Critically. <br> TaskSession status = 'FAILED'];
    
    CustomErrorPath --> EndOrContinue1(...)
    DefaultErrorHandler --> EndOrContinue2(...)`

---

These diagrams aim to cover the major architectural aspects, key service interactions, and complex flow patterns like loops, HIL, and error handling. They visually reinforce the V3.3.1 structure and our design decisions.

### **Conceptual Layers & Key Interactions:**

1. **Application Layer (app/, services/, agents/):**
    - OrchestratorService (app/): Entry point for user requests. Manages TaskSession lifecycle via TaskSessionManager. Initiates GraphExecutor runs.
    - Application Services (services/): TaskSessionManager, UserSettingsService, NodeSpecService. These consume Repositories and Engine Services.
    - Concrete Agents (agents/): Implement specific AI logic, using BaseAgent and consuming Engine Services (via AgentDependencies).
2. **Engine Layer (engine/):**
    - GraphExecutor & GraphBuilder (engine/graph/): Core orchestration.
    - BaseAgent & AgentRegistry (engine/agents/): Agent infrastructure.
    - LLMService (engine/llm/): LLM communication gateway.
    - Prompting Subsystem (engine/prompts/): Includes GenericPromptBuilder, AgentPromptBuilder, ConversationHistoryService, RuleSelectorService (all in engine/prompts/services/) and micro-utilities (formatters, normalizers, mergers, parts).
3. **Persistence Layer (persistence/):**
    - rxdbSetup: DB initialization.
    - Repositories: Abstract RxDB collection interactions.
4. **Configuration Layer (config/):**
    - Static configurations (rules, model info, node snapshots).
5. **Tools Layer (tools/):**
    - ToolExecutorService, ToolRegistry, and specific ToolDefinitions.
6. **Shared Types (types/) & Utilities (utils/):** Cross-cutting concerns.

**Data Flow Example (Create New Workflow):**

User Input -> OrchestratorService -> (new TaskSession via TaskSessionManager) -> GraphExecutor starts createNewWorkflowGraph -> IntentRouterAgent (uses AgentPromptBuilder -> GenericPromptBuilder -> LLMService) -> ... (other agents in graph similarly) ... -> Graph outputs ProposedWorkflowJson -> OrchestratorService manages "Apply & Review" (conceptual for V1 library, involving user confirmation before any actual n8n state change).

### 2.5 Component Interaction Patterns

- **Service Communication:** Primarily via constructor-based Dependency Injection. Services expose clear asynchronous method interfaces.
- **Data Flow Patterns:**
    - Data between graph nodes is passed explicitly via GraphNodeDefinition.inputs sourcing from previous NodeOutputVersion.output.
    - Agents receive structured inputs, process them (often involving LLM calls), and return structured outputs (AgentExecutionResult).
    - Zod schemas define data contracts at all major boundaries.
- **Error Propagation:**
    - Services and repositories throw specific errors for critical issues.
    - LLMService wraps LLM API errors into its LLMInvocationResult.
    - Agents return status: 'failure' with errorDetails in AgentExecutionResult.
    - GraphExecutor routes to onFailure edges or the graph's default error handler. Unhandled errors fail the graph and update TaskSession.status and TaskSession.lastError.
- **Event Broadcasting (V1 - Minimal/Internal):**
    - GraphExecutor transitions GraphExecutionContext.status (e.g., to PAUSED_FOR_USER_INPUT), which OrchestratorService observes to manage HIL.
    - No global event bus for V1 of the library.
- **Resource Management:**
    - LLMService tracks token usage/cost per call. TaskSession aggregates this.
    - PromptBuilderService (via ConversationHistoryService) manages prompt context window by triggering summarization.
    - GraphExecutor has a MAX_TOTAL_GRAPH_STEPS limit. Loops defined by GraphBuilder.addLoop() have configurable maxIterations.
    - RxDB manages its own resource usage (IndexedDB storage, memory).

## 3. DETAILED COMPONENT SPECIFICATIONS

This section details the architecture, purpose, key features, and design rationale for each major component and subsystem within the n8n Agentic AI "Brain" Library. It draws upon the detailed implementation plans created for each, providing a consolidated architectural overview. The file structure referenced is the agreed-upon V3.3.1.

### 3.1 Orchestration Engine

The Orchestration Engine is the heart of the AI Brain, responsible for defining, executing, and managing the complex, multi-step AI workflows (graphs) that drive the assistant's intelligence. It is located primarily within src/engine/graph/.

### **3.1.1 GraphBuilder API**

- **Purpose**: To provide a developer-friendly, fluent, and type-safe TypeScript API for programmatically defining GraphDefinition objects. It abstracts the complexities of the raw graph data structure, reduces boilerplate, encapsulates common patterns (especially for loops and conditional edge definitions), and allows for validation during the graph construction phase.
- **Design Decisions**:
    - **Fluent API Pattern:** Chosen for its readability, intuitive chained method calls (.addNode().onSuccess()...), and ability to guide the developer through valid graph construction sequences.
    - **Abstraction of Internal IDs:** For complex constructs like loops, the builder manages internal node IDs, exposing only logical entry and exit points to the graph designer.
    - **Type Safety:** Leverages TypeScript generics and specific input/output reference types (NodeOutputReference, GraphInitialInputReference) to enhance type safety when defining node inputs and connections.
    - **Centralized Graph Validation:** The compile() method performs structural and semantic validation of the defined graph before producing the final GraphDefinition object.
- **Key Features**:
    - Methods for adding all standard node types: addStartNode, addEndNode, addAgentNode (takes agent class constructor & optional LLMTaskCategory override), addToolNode, addConditionalNode, addDataTransformNode.
    - Fluent edge definition methods: .onSuccess(targetNodeId), .onFailure(targetNodeId), .onBranch(label, targetNodeId), .otherwise(targetNodeId). These operate on the lastAddedNodeId.
    - A sophisticated addLoop(loopId, itemsSource, loopBodyBuilderFn, options) method that encapsulates the creation of all necessary internal nodes for loop initialization, condition checking, iteration data provision, and state update/accumulation. It returns an object with methods like .onComplete(targetNodeId) for fluent exit chaining.
    - Helper methods for defining node inputs from various sources: builder.graphInput(), builder.nodeOutput() (supports path, defaultValue, and fallbackSource), builder.staticValue(), builder.constant().
    - Configuration methods: setVersion(), setEntryPoint() (for custom start nodes), addExpectedInitialInput(), addGraphConstant(), setGlobalDefaultMaxLoopIterations(), registerDefaultErrorHandlerNode().
    - compile(): Finalizes the graph, performs validations (e.g., existence of referenced nodes, basic cycle detection for non-loop paths [V2+], presence of end nodes), and returns the GraphDefinition object and a FunctionRegistry (mapping function keys to actual JS functions for conditionals/transforms).
- **Interface Specification (Conceptual Public API of GraphBuilder instance):**
    - constructor(graphId: string, description: string)
    - Node Adders: addAgentNode(...), addToolNode(...), etc. (all return this)
    - Edge Chaining: onSuccess(...), onFailure(...), onBranch(...), otherwise(...) (all return this)
    - Loop Constructor: addLoop(...): this (which internally uses LoopControl for its chained exits like .onComplete())
    - Input Helpers: graphInput(...), nodeOutput(...), etc. (return ResolvableInputValue types)
    - Configuration: setGlobalDefaultMaxLoopIterations(...), registerDefaultErrorHandlerNode(...), addExpectedInitialInput(...)
    - Finalization: compile(): CompiledGraph (where CompiledGraph is { definition: GraphDefinition, functionRegistry: FunctionRegistry })
- **Implementation Strategy**: The GraphBuilder class maintains internal Maps for nodes and arrays for edges. Node addition methods set a lastAddedNodeId to enable fluent edge definition. The addLoop method is a higher-level abstraction that internally calls basic node and edge addition methods to construct the loop's control flow. resolveInputMappings method translates ResolvableInputValue builder types into GraphInputValueSource objects for the GraphDefinition.
- **Error Handling**: Throws errors during graph definition for invalid operations (e.g., adding duplicate node ID, referencing non-existent node in an edge *during compile time*, defining branch on non-conditional node). compile() performs final validation.
- **Extension Points**: New node types or complex patterns can be added as new methods to the GraphBuilder.

### **3.1.2 GraphExecutor**

- **Purpose**: To serve as the runtime engine that interprets and executes a GraphDefinition (obtained from GraphBuilder.compile().definition). It manages the GraphExecutionContext, orchestrates node operations, handles data and control flow, and manages HIL pauses and error propagation.
- **Execution Model**:
    - Asynchronous, step-by-step execution of nodes based on a processingQueue.
    - Stateless itself; all runtime state is held in the GraphExecutionContext instance specific to a single graph run.
    - Generic: It does not have special logic for specific node types beyond what's defined by their type and GraphNodeDefinition. Loop execution is managed by the interaction of standard ConditionalBranchNode and DataTransformNodes as defined by GraphBuilder.addLoop().
- **State Management (GraphExecutionContext):**
    - Creates and manages a GraphExecutionContext per graph run.
    - nodeOutputs: Stores a versioned array (NodeOutputVersion[]) for each executed node ID, capturing output, status, thoughts, errors, token usage, etc.
    - status: Tracks current graph status (RUNNING, PAUSED_FOR_USER_INPUT, COMPLETED, FAILED).
    - Manages activeInterruptDetails for HIL pauses.
- **Concurrency Handling**: V1 focuses on sequential processing of nodes in the processingQueue. True parallel execution of independent graph branches is a V2+ consideration. Asynchronous operations within a single node (e.g., an agent's LLM call) are handled via async/await.
- **Loop and Conditional Logic**:
    - Executes registered JavaScript functions (from FunctionRegistry) for ConditionalBranchNodeDef and DataTransformNodeDef.
    - Follows edge labels determined by conditional node outputs or node success/failure status.
    - Enforces MAX_TOTAL_GRAPH_STEPS to prevent runaway executions. Loop-specific max iterations are handled by the loop's conditional function logic, which can access graph-defined limits.
- **Input Resolution**: Implements robust logic to resolve inputs for each node based on its GraphInputValueSource definitions, including path traversal on previous node outputs, outputVersion selection ('latest', index, runId), and fallbackSource handling (critical for loops created by GraphBuilder).
- **Node Execution**:
    - For agentCall: Uses AgentRegistry to instantiate the agent (injecting AgentDependencies from graphContext.services), then calls its execute() method.
    - For toolCall: Uses ToolExecutorService (from graphContext._internalExecutorServices) to execute the tool.
    - For dataTransform / conditionalBranch: Looks up and executes the corresponding JavaScript function from FunctionRegistry, passing resolved inputs and the GraphExecutionContext.
- **Human-in-the-Loop (HIL) & Error Handling**:
    - If an agent's AgentExecutionResult signals needs_clarification or a tool/node indicates a need for user interaction, the executor sets GraphExecutionContext.status = 'PAUSED_FOR_USER_INPUT', stores interruptPayload, and yields control.
    - Resumes execution when OrchestratorService provides user input, by treating the user input as the successful output of the paused node.
    - If a node execution fails, it checks for an explicit onFailure edge. If none, it routes to the graphDefinition.defaultErrorHandlerNodeId (if registered). If neither, the graph execution fails.
- **Performance Optimizations**: V1 focuses on correctness. Future optimizations could include memoization for pure DataTransformNodes or more advanced scheduling.
- **Debugging and Monitoring**: Versioned nodeOutputs in GraphExecutionContextSnapshot provide a rich trace. Logging within the executor details step execution, input resolution, and errors.

### **3.1.3 Graph Definition System (graph.types.ts, execution.types.ts, builder.types.ts)**

- **Data Structures**:
    - GraphDefinition: The primary JSON-compatible structure containing id, description, version, entryNodeId, nodes: GraphNodeDefinition[], edges: GraphEdgeDefinition[], expectedInitialInputs, graphConstants, defaultMaxIterationsForAllLoops?, defaultErrorHandlerNodeId?.
    - GraphNodeDefinition: Polymorphic type for agentCall, toolCall, conditionalBranch, dataTransform, start, end. Includes id, type, inputs: GraphNodeInputMapping, and type-specific fields.
    - GraphInputValueSource: Defines how inputs are sourced (e.g., fromNodeOutput with nodeId, path, outputVersion, fallbackSource).
    - GraphEdgeDefinition: from, to, label.
    - FunctionRegistry: Record<string, Function> mapping keys (stored in GraphNodeDefinition) to actual JS functions for conditionals/transforms. (Managed by GraphBuilder.compile()).
    - GraphExecutionContext and NodeOutputVersion: As detailed for the executor.
- **Validation Logic**:
    - GraphBuilder.compile() performs initial structural validation (e.g., referenced nodes/inputs exist).
    - RxDB schema validation if graph definitions are ever persisted (V2+).
    - GraphExecutor performs runtime validation (e.g., function keys exist in registry).
- **Serialization**: GraphDefinition is designed to be JSON serializable if functions in ConditionalBranchNodeDef and DataTransformNodeDef are stored as keys/references to the FunctionRegistry. GraphExecutionContextSnapshot is designed for JSON serialization.
- **Versioning Strategy**: GraphDefinition.version field allows for managing changes to graph structures over time. Migration logic for graph state (GraphExecutionContext) would be a V2+ concern.
- **Compatibility**: V1 focuses on the current definition.

### 3.2 Core Services Architecture

This section outlines the key services that provide foundational capabilities to the Orchestration Engine and AI Agents. These services encapsulate specific functionalities like LLM interaction, prompt construction, n8n node specification access, and rule selection.

### **3.2.1 LLMService (Located in src/engine/llm/)**

- **Purpose**: To act as the sole, centralized interface for all interactions with Large Language Models (LLMs), abstracting the Vercel AI SDK Core v5 alpha and managing provider-specific details, API keys, model selection, token counting, cost estimation, retries, and standardized response/streaming.
- **Integration Strategy**:
    - Wraps Vercel AI SDK v5 (@ai-sdk/core and provider packages like @ai-sdk/openai, @ai-sdk/anthropic).
    - Utilizes UserSettingsService to fetch user-provided API keys and model preferences.
    - Integrates with a conceptual ChromeAIService to attempt on-device model execution for suitable tasks, with fallback to cloud LLMs.
- **Model Selection Logic**:
    - Selects LLM provider and model ID based on the LLMTaskCategory provided for the call and the user's preferences stored in UserSettings.
    - Implements a fallback strategy if no specific user preference is set for a category (e.g., category default, then core default like CORE_ORCHESTRATION_AND_PLANNING model).
    - Prioritizes chrome.ai (Gemini Nano) for designated LLMTaskCategorys (e.g., UTILITY_CLASSIFICATION_ROUTING, UTILITY_CONVERSATION_SUMMARIZATION) if enabled by the user and available in the environment.
- **Token Management & Cost Optimization**:
    - Performs client-side token counting (using gpt-tokenizer or similar) for prompts before sending to the LLM (primarily as a pre-check or for models not returning prompt tokens).
    - Relies on usage data returned by Vercel AI SDK (result.usage) for accurate prompt and completion token counts.
    - Calculates estimated cost per call using bundled llmModels.config.ts pricing data and actual token usage.
    - Accumulated costs are tracked within the TaskSession.metadata.
- **Error Handling & Retry Strategy**:
    - Implements automatic retries with exponential backoff for transient LLM API errors (e.g., rate limits, server errors) up to a configurable maximum (defaultLlmServiceMaxRetries from user settings or a system default).
    - Distinguishes between retryable and non-retryable errors.
    - Returns standardized LLMInvocationResult objects, including structured error details.
- **Streaming Support (Vercel AI SDK v5)**:
    - Provides methods (streamText, streamObject) that return ReadableStream<UIMessage>.
    - Utilizes Vercel AI SDK utilities (createUIMessageStream, formatStreamPart) to construct the stream.
    - Injects custom metadata into the stream (via message.metadata or custom stream parts like data-completion-metadata) including LLMOutputDetails (model used, tokens, cost, finish reason) and our specific CustomUIMessageDataParts (e.g., agent_invocation_start, agent_thought_chunk, partial_object_update, agent_error) for rich UI feedback.
- **Key API Methods (Conceptual from LLMService.ts plan):**
    - invokeModel(taskCategory, messages, options): Promise<LLMInvocationResult> (for non-streaming, generic responses)
    - generateStructuredObject<OutputSchema>(taskCategory, messages, outputZodSchema, options): Promise<LLMInvocationResult<OutputSchema>> (for non-streaming, Zod-validated JSON output using experimental_generateObject).
    - streamText(taskCategory, messages, options): ReadableStream<UIMessage> (for streaming text and tool calls).
    - streamStructuredObject<OutputSchema>(taskCategory, messages, outputZodSchema, options): ReadableStream<UIMessage> (for streaming structured JSON objects using experimental_streamObject).
- **Security**: Manages API keys securely, passing them directly to Vercel AI SDK provider functions, and ensures they are not logged or exposed in error messages.

### **3.2.2 Prompt Engineering Subsystem (Located in src/engine/prompts/)**

This subsystem is a cohesive set of micro-utilities and orchestrating services responsible for constructing all LLM prompts used by AI agents.

- **parts/ (Static Prompt Parts):**
    - Contains reusable, static Markdown/text snippets (e.g., SYSTEM_PROMPT_BASE_INTRO, TEAM_AWARENESS_TEMPLATE_STRING, OUTPUT_FORMAT_INSTRUCTION_TEMPLATE_STRING).
    - These are imported and used by GenericPromptBuilder and agent-specific prompt files.
- **formatters/ (Micro-Formatters):**
    - Small, focused TypeScript modules/functions each responsible for formatting a specific PromptComponent type (e.g., SystemInstructionComponent, UserMessageComponent, JsonSchemaInstructionComponent) into an intermediate { role: string, contentString: string } representation.
    - Examples: JsonSchemaInstructionFormatter (uses zod-to-json-schema), TaggedDataFormatter.
- **normalizers/ (Message Normalizers):**
    - MessageNormalizer.ts: Ensures ModelMessage[] (especially from history) conform to Vercel AI SDK v5 standards (e.g., role conversion, handling legacy tool_calls properties).
    - ToolContentNormalizer.ts: Specifically ensures content for role: 'tool' messages is ToolResultPart[] and for role: 'assistant' (for tool calls) is ToolCallPart[].
- **mergers/ (Message Mergers):**
    - MessageMerger.ts: Handles the complex logic of merging adjacent, role-compatible ModelMessages, including merging different content types (string + string, string + array, array + array) into a single ModelMessage where appropriate.
- **services/GenericPromptBuilder.ts:**
    - **Purpose**: A lean orchestrator that uses the formatters, normalizers, and mergers to assemble a preliminary ModelMessage[] array from a list of abstract PromptComponents.
    - **Responsibilities**: Iterates PromptComponent[], dispatches to appropriate formatters, applies normalization, then uses MessageMerger to combine messages. It does *not* handle agent-specific logic, conversation history, or rule injection directly.
- **services/RuleSelectorService.ts:**
    - **Purpose**: Selects relevant N8nRules (base and custom) to be injected into agent prompts.
    - **Responsibilities**: Loads base rules. Fetches active user-defined rules from CustomRulesRepository. Filters rules by agentSlug and rule.type (ALWAYS_APPLY, CONTEXTUAL_SUGGESTION). For contextual rules, V1 uses keyword matching against task description and rule triggerKeywordsOrContext to select the most relevant.
- **services/ConversationHistoryService.ts:**
    - **Purpose**: Manages the retrieval and summarization of conversation history specifically for inclusion in LLM prompts.
    - **Responsibilities**:
        - Fetches relevant history: TaskSession.llmContextSummary (from TaskSessionRepository) and recent ChatMessages (from ChatRepository, filtered by TaskSession.summaryLastMessageId or similar watermark).
        - **Proactive Summarization:** If the combined size of current summary + recent history + new data (estimated by GenericPromptBuilder for a target model) exceeds a token threshold, it orchestrates a summarization call:
            1. Uses GenericPromptBuilder to create a prompt for the n8n-summarizer-agent (using prompt parts from engine/prompts/parts/summarizer.parts.ts).
            2. Uses LLMService to invoke the n8n-summarizer-agent (with UTILITY_CONVERSATION_SUMMARIZATION category).
            3. Updates TaskSession.llmContextSummary and TaskSession.summaryLastMessageId via TaskSessionRepository.
        - Provides the final, managed (potentially newly summarized) history block (ModelMessage[]) for prompt assembly.
- **services/AgentPromptBuilder.ts:**
    - **Purpose**: The primary service used by BaseAgent to construct the complete, context-aware ModelMessage[] for a specific agent's LLM call.
    - **Responsibilities**:
        1. Takes agent's CORE_SYSTEM_INSTRUCTION, subTaskUserInstruction, current TaskSession, GraphDefinition (for team awareness), AgentCallSpecificData, and options.
        2. Calls ConversationHistoryService.getManagedHistoryForPrompt(...) to get the history block (this call might trigger summarization within CHS).
        3. Calls RuleSelectorService.getRulesForAgentPrompt(...) to get relevant rules.
        4. Assembles a list of PromptComponents including:
            - Agent's CORE_SYSTEM_INSTRUCTION.
            - Standardized "Team Awareness" block (using graph definition).
            - Standardized "Input Context Definition" block (listing actual inputs).
            - Formatted rules from RuleSelectorService.
            - Formatted knowledge snippets (from RAG - V1 advanced scope).
            - The managed conversation history block from ConversationHistoryService.
            - The agent's subTaskUserInstruction and AgentCallSpecificData (formatted with tags).
            - Standardized "Output Format Instruction" block, including the JSON schema for the agent's *final expected output to the graph*.
        5. Uses GenericPromptBuilder.buildMessages(promptComponents) to generate the final ModelMessage[].
        6. Performs a final token count and returns the AssembledPrompt (messages, token count, flag if summarization occurred).

### **3.2.3 NodeSpecService (Located in src/services/)**

- **Purpose**: To load, cache, and provide centralized, structured access to the specifications of all available n8n nodes for the operational n8n environment. This is a crucial domain data service for n8n-specific agents.
- **Data Loading & Caching**:
    - **Node.js Mode:** Loads node specifications from a bundled nodes.snapshot.json file (a snapshot of an n8n instance's /api/v1/node-types or /types/nodes.json).
    - **Browser Mode (Extension):** Receives full node specifications (as a JSON string or object) fetched from the active n8n instance by a dedicated tool (system_fetch_all_node_specs_tool). The NodeSpecService then parses and caches this data.
    - Maintains an in-memory cache (Map<string, NodeDescription>) of parsed NodeDescription objects, keyed by node type ID (name).
- **Schema & Validation**: Uses NodeDescriptionSchema (Zod) to validate the structure of loaded node specifications.
- **Key API Methods**:
    - loadSpecsFromFile(filePath: string): Promise<void> (Node.js only).
    - loadSpecsFromJsonString(jsonString: string, sourceName?: string): Promise<void>.
    - loadSpecsFromArray(specsArray: NodeDescription[], sourceName?: string): void.
    - getNodeSpec(typeId: string, version?: number): Promise<NodeDescription | null>: Retrieves full spec for a node type. V1 primarily returns latest/default version.
    - getAllNodeDescriptors(): Promise<NodeSelectorDescriptor[]>: Returns a lightweight list (typeId, displayName, description, categories) for use by NodeSelectorAgent.
    - findNodes(query: string, options?): Promise<NodeSelectorDescriptor[]>: V1 basic filtering on descriptors. V2 advanced search.
- **Integration**: Injected into AgentDependencies for use by agents like NodeSelectorAgent (uses getAllNodeDescriptors, findNodes) and NodeComposerAgent (uses getNodeSpec).

### 3.3 AI Agent Ecosystem

The AI Agent Ecosystem comprises the foundational infrastructure for creating and managing AI agents, as well as the specifications for each specialized agent that performs distinct cognitive tasks within the AI Brain. Concrete agent implementations reside in src/agents/, while the agent infrastructure lives in src/engine/agents/.

### **3.3.1 Agent Infrastructure**

This infrastructure provides the common framework and utilities for all AI agents.

- **BaseAgent.ts (Abstract Class - in src/engine/agents/base/)**
    - **Purpose**: Defines the common contract and provides shared utilities for all specialized AI agents. It ensures consistency in how agents are structured, receive dependencies, execute tasks, and report results back to the GraphExecutor.
    - **Key Features & Design**:
        - **Abstract Nature**: Concrete agents *must* extend BaseAgent.
        - **Static Metadata**: Subclasses must define:
            - static readonly agentSlug: string; (Unique identifier, e.g., n8n-architect-agent).
            - static readonly CORE_SYSTEM_INSTRUCTION: string; (The primary system instruction string for this agent, typically loaded from its co-located *.prompts.ts file and used by AgentPromptBuilder).
            - static readonly defaultLlmTaskCategory: LLMTaskCategory; (The default LLM category this agent uses for its primary LLM calls).
            - static readonly description?: string; (Brief description for "Team Awareness" context in prompts).
            - static readonly roleDescription?: string; (More detailed role description for "Team Awareness").
        - **Dependency Injection**: Constructor takes an AgentDependencies object (containing instances of llmService, agentPromptBuilder, nodeSpecService, etc.) and stores it in protected readonly deps.
        - **Core Abstract Method execute()**:
            - Signature: abstract execute(inputs: TInputParams, graphContext: GraphExecutionContext, taskSession: TaskSession): Promise<AgentExecutionResult<z.infer<TFinalOutputSchemaType>>>;
            - This is the main entry point called by the GraphExecutor. TInputParams represents the specific inputs resolved for this agent node by the graph. TFinalOutputSchemaType is the Zod schema for its *final, successful result.payload returned to the graph executor*.
        - **Abstract Method getFinalOutputSchema()**:
            - Signature: abstract getFinalOutputSchema(): TFinalOutputSchemaType;
            - Returns the Zod schema for this agent's *final successful result.payload*. This is used by AgentPromptBuilder (if no override is provided for an internal call) and by the this.success() helper for output validation.
        - **Protected Helper Method prepareLlmMessages()**:
            - Signature:
                
                `protected async prepareLlmMessages(
                  taskSession: TaskSession,
                  graphContext: GraphExecutionContext,
                  subTaskUserInstruction: string, // The specific "user-side" instruction for THIS LLM call
                  callSpecificData: Record<string, any>, // Data to be tagged and injected for THIS LLM call
                  options?: {
                    systemInstruction?: string; // Optional: Override agent's CORE_SYSTEM_INSTRUCTION
                    outputSchema?: z.ZodTypeAny; // Optional: Zod schema for *expected output of THIS LLM call*
                  } & AgentSpecificPromptOptions // Other options for AgentPromptBuilder
                ): Promise<ModelMessage[]>`
                
                **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
                
            - Calls this.deps.agentPromptBuilder.prepareAgentLlmMessages(...), passing the agent's static CORE_SYSTEM_INSTRUCTION (or the options.systemInstruction override), the subTaskUserInstruction, taskSession, graphContext (for graph def for Team Awareness), callSpecificData, and the options (including outputSchema if this internal LLM call expects structured output different from the agent's final output).
        - **Protected Helper Method invokeLlm()**:
            - Signature:
                
                `protected async invokeLlm(
                  messages: ModelMessage[],
                  options?: InvokeModelOptions & { 
                    llmTaskCategory?: LLMTaskCategory; // Optional: Override agent's defaultLlmTaskCategory
                  }
                ): Promise<LLMInvocationResult>`
                
                **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
                
            - Determines task category using options?.llmTaskCategory or the agent's static defaultLlmTaskCategory.
            - Calls this.deps.llmService.invokeModel(resolvedTaskCategory, messages, options).
        - **Protected Helper Method invokeAndStreamLlm()**:
            - Signature:
                
                `protected invokeAndStreamLlm(
                  messages: ModelMessage[],
                  options?: InvokeModelOptions & {
                    llmTaskCategory?: LLMTaskCategory; // Optional: Override
                    streamType: 'text' | 'object';    // To call streamText or streamStructuredObject
                    outputSchema?: z.ZodTypeAny;     // For streamStructuredObject
                  }
                ): ReadableStream<UIMessage>`
                
                **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
                
            - Similar to invokeLlm but calls the appropriate streaming method on this.deps.llmService.
        - **Protected Response Helper Methods**:
            - success(data: SuccessPayload<z.infer<TFinalOutputSchemaType>>): AgentExecutionResult<z.infer<TFinalOutputSchemaType>>: Validates data.payload against this.getFinalOutputSchema() and returns a standard success result.
            - failure(details: { reason: string; errorType?: string; originalError?: any }): Returns standard failure result.
            - requestUserClarification(details: ClarificationRequestDetails): Returns result indicating status: 'needs_clarification'.
            - requestGraphTools(details: ToolCallRequestDetails): Returns result indicating status: 'needs_tool_calls'.
        - **Utility getJsonSchemaString()**: For agents to convert arbitrary Zod schemas to JSON schema strings (e.g., for the outputSchema option in prepareLlmMessages if an internal LLM call needs to be prompted for a specific structure).
- **agentRegistry.ts (in src/engine/agents/)**
    - **Purpose**: Provides a central registry for all available AI agent classes. It maps string slugs (used in GraphDefinition) to agent constructors and their static metadata.
    - **Structure**:
        - AgentRegistryEntry interface:
            
            `interface AgentRegistryEntry<TInput = any, TOutputPayloadZodType extends z.ZodTypeAny = z.ZodTypeAny> {
              slug: string;
              description: string; // Static from agent class
              roleDescription: string; // Static from agent class
              constructor: AgentConstructor<TInput, z.infer<TOutputPayloadZodType>>; // Constructor type
              defaultLlmTaskCategory: LLMTaskCategory; // Static from agent class
              coreSystemInstruction: string; // Static from agent class
              finalOutputSchema: TOutputPayloadZodType; // From agent's getFinalOutputSchema()
              factory?: (dependencies: AgentDependencies) => BaseAgent<TInput, z.infer<TOutputPayloadZodType>>;
            }`
            
            **content_copydownload**Use code [**with caution**](https://support.google.com/legal/answer/13505487).TypeScript
            
        - AGENT_REGISTRY_CONFIG: AgentRegistryEntry[]: A static array, constructed by importing all concrete agent classes from src/agents/* and extracting their static properties (agentSlug, defaultLlmTaskCategory, CORE_SYSTEM_INSTRUCTION, description, roleDescription) and an instance of their finalOutputSchema (by temporarily instantiating or making getFinalOutputSchema accessible statically if feasible and safe, though instance method is preferred for consistency).
        - agentRegistryMap: Map<string, AgentRegistryEntry>: Derived from AGENT_REGISTRY_CONFIG for efficient runtime lookup by agentSlug.
    - **Usage**:
        - **GraphBuilder (addAgentNode):** When addAgentNode(id, AgentClass, llmTaskCategoryOverride?, ...) is called:
            - It finds the corresponding entry in AGENT_REGISTRY_CONFIG using AgentClass.
            - It stores the agentConfig.slug and the resolved llmTaskCategory (override or default from entry) in the GraphNodeDefinition.
        - **GraphExecutor:** Uses agentSlug from GraphNodeDefinition to look up the AgentRegistryEntry in agentRegistryMap. It then uses entry.constructor (or entry.factory) to instantiate the agent, injecting AgentDependencies (which includes all necessary services from graphContext.services).
        - **AgentPromptBuilder:** Uses agentRegistryMap.get(agentSlug) to fetch the agent's coreSystemInstruction (for the main system prompt) and its finalOutputSchema (for default output schema injection instructions within the prompt).
    - **initializeAgents.ts File:** **This file is no longer necessary.** The AGENT_REGISTRY_CONFIG array directly imports agent classes and their static metadata. There's no separate template registration step with a central TemplateRegistry as that component was removed in favor of agents defining their CORE_SYSTEM_INSTRUCTION statically and AgentPromptBuilder using that.

### **3.3.2 Specialized Agent Specifications (V1)**

The following agents are planned for V1. Each extends BaseAgent, resides in src/agents/{agentName}/, and has a co-located *.prompts.ts file exporting its CORE_SYSTEM_INSTRUCTION string and any other unique prompt pieces.

1. **N8nIntentRouterAgent** (Slug: n8n-intent-router-agent)
    - **Purpose**: Analyzes user input and system state to determine the primary intent and route to the appropriate graph or action.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./intentRouter.prompts.ts. Describes its role, available graph operations (dynamically injected by AgentPromptBuilder), and core internal intents.
    - **getFinalOutputSchema()**: Returns IntentRouterOutputSchema.
    - **execute(inputs, graphContext, taskSession)**:
        - Uses this.prepareLlmMessages() with its specific task instruction (e.g., "Classify user input based on context and available operations.") and relevant inputs (userInputText, conversationSummary, details of paused graph if any).
        - Calls this.invokeLlm().
        - Processes LLM response to fit IntentRouterOutputSchema.
        - Returns this.success() or this.failure().
2. **N8nRequirementsGatheringAgent** (Slug: n8n-requirements-gathering-agent)
    - **Purpose**: Elicits detailed requirements for an n8n task, with HIL clarification loops.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./requirementsGathering.prompts.ts. Details its iterative questioning process.
    - **getFinalOutputSchema()**: Returns RequirementsOutputSchema.
    - **execute(inputs, graphContext, taskSession)**:
        - Uses this.prepareLlmMessages() with task (e.g., "Elicit requirements or ask next clarifying question based on history and user response.") and inputs (user_full_request, previous_agent_state, user_response_to_clarification).
        - Calls this.invokeLlm().
        - If LLM provides structured requirements, returns this.success().
        - If LLM provides a question for the user, returns this.requestUserClarification().
3. **N8nArchitectAgent** (Slug: n8n-architect-agent)
    - **Purpose**: Designs high-level n8n workflow plans including trigger, steps, node suggestions, data flow, and "Focused Design Spikes" for complex parts.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./architect.prompts.ts (our detailed V1 Architect prompt with FDS instructions).
    - **getFinalOutputSchema()**: Returns ArchitectOutputSchemaV1.
    - **execute(inputs, graphContext, taskSession)**:
        - May involve multiple internal LLM calls (e.g., draft plan, then refine after potential tool calls or internal reflection). Each uses this.prepareLlmMessages() with specific sub-task instructions, data, and potentially overridden outputSchema or systemInstruction for that internal call.
        - If needs external docs/examples, returns this.requestGraphTools() (for search_documentation, etc.). intermediate_agent_state_for_resume will store its current draft. Graph re-invokes with tool results.
        - If needs user clarification, returns this.requestUserClarification().
        - Finally, returns this.success() with the ArchitectOutputSchemaV1 payload.
4. **N8nNodeSelectorAgent** (Slug: n8n-node-selector-agent)
    - **Purpose**: Selects specific n8n node type IDs based on Architect's plan, using NodeSpecService.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./nodeSelector.prompts.ts.
    - **getFinalOutputSchema()**: Returns NodeSelectorOutputSchema.
    - **execute(inputs, graphContext, taskSession)**:
        - Internally calls this.deps.nodeSpecService.getAllNodeDescriptors() to get available nodes.
        - Uses this.prepareLlmMessages() with task (e.g., "Select nodes for the provided plan steps from the available node list.") and inputs (architectural_plan, formatted list of node descriptors).
        - Calls this.invokeLlm().
        - Returns this.success() with NodeSelectorOutputSchema payload.
5. **N8nNodeComposerAgent** (Slug: n8n-node-composer-agent)
    - **Purpose**: Generates JSON for a single n8n node type, including associated Sticky Notes.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./composer.prompts.ts (emphasizing use of live schema and n8n syntax rules).
    - **getFinalOutputSchema()**: Returns NodeComposerOutputSchema.
    - **execute(inputs, graphContext, taskSession)**:
        - First internal step: const liveNodeSchema = await this.deps.nodeSpecService.getNodeSpec(inputs.node_to_compose_details.node_type_id);
        - If !liveNodeSchema, returns this.failure().
        - Uses this.prepareLlmMessages() with task (e.g., "Compose node X.") and inputs (node_to_compose_details, architect_plan_context, upstream_data_context) and crucial callSpecificData including _LIVE_NODE_SCHEMA_JSON: liveNodeSchema.
        - Calls this.invokeLlm().
        - Returns this.success() with NodeComposerOutputSchema payload.
6. **N8nWorkflowDocumenterAgent** (Slug: n8n-workflow-documenter-agent)
    - **Purpose**: Generates Sticky Note JSON for documenting groups of functional nodes.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./workflowDocumenter.prompts.ts (with sticky note guidelines).
    - **getFinalOutputSchema()**: Returns WorkflowDocumenterOutputSchema.
    - **execute(inputs, graphContext, taskSession)**:
        - Internally gets Sticky Note schema via this.deps.nodeSpecService.getNodeSpec('n8n-nodes-base.stickyNote').
        - Processes inputs.composed_functional_nodes_json and inputs.architectural_plan_steps.
        - May make multiple LLM calls (via prepareLlmMessages and invokeLlm) if generating content for several sticky notes.
        - Returns this.success() with payload containing array of sticky note NodeJsonSchema.
7. **N8nConnectionComposerAgent** (Slug: n8n-connection-composer-agent)
    - **Purpose**: Generates connections JSON for the workflow.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./connectionComposer.prompts.ts.
    - **getFinalOutputSchema()**: Returns ConnectionComposerOutputSchema.
    - **execute(inputs, graphContext, taskSession)**: Takes composed_nodes_array (all nodes), architect_plan_steps. Uses this.prepareLlmMessages() & this.invokeLlm(). Returns this.success().
8. **N8nBestPracticeValidatorAgent** (Slug: n8n-best-practice-validator-agent)
    - **Purpose**: Analyzes workflow JSON against rules.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./validator.prompts.ts.
    - **getFinalOutputSchema()**: Returns ValidationOutputSchema.
    - **execute(inputs, graphContext, taskSession)**: Takes workflow_json_to_validate, relevant_rules_text. Uses this.prepareLlmMessages() & this.invokeLlm(). Returns this.success().
9. **N8nReflectionAgent** (Slug: n8n-reflection-agent)
    - **Purpose**: Critiques output of other agents.
    - **Static defaultLlmTaskCategory**: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE.
    - **Static CORE_SYSTEM_INSTRUCTION**: Loaded from ./reflection.prompts.ts.
    - **getFinalOutputSchema()**: Returns ReflectionOutputSchema.
    - **execute(inputs, graphContext, taskSession)**: Takes output_to_review, original_goal, rules_text. Uses this.prepareLlmMessages() & this.invokeLlm(). Returns this.success().
10. **(Conceptual) n8n-summarizer-agent Role for ConversationHistoryService**
    - **Slug**: n8n-summarizer-agent.
    - **CORE_SYSTEM_INSTRUCTION**: Resides in src/engine/prompts/parts/summarizer.parts.ts.
    - **defaultLlmTaskCategory**: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION.
    - **finalOutputSchema**: z.string() (the summary text).
    - The ConversationHistoryService uses AgentPromptBuilder with these details when it needs to invoke LLMService for summarization. No separate agent class is needed.

### 3.4 Data Persistence Layer

The Data Persistence Layer is responsible for storing and retrieving all persistent state required by the n8n Agentic AI "Brain" Library. It ensures data durability, consistency, and provides multi-platform support (Node.js and Browser). This layer is primarily located within src/persistence/.

### **3.4.1 RxDB Integration (rxdbSetup.ts)**

- **Purpose**: To initialize and configure the RxDB database instance, define all necessary data collections (tables) with their schemas, and provide a singleton-like access pattern to the initialized database instance for the rest of the application.
- **Design Decisions**:
    - **RxDB as Core Database**: Chosen for its local-first capabilities, multi-platform support via pluggable storage adapters, reactive data streams (future use), robust querying (Mango queries), and JSON schema enforcement for collections.
    - **Environment-Specific Storage Adapters**:
        - **Browser**: Uses getRxStorageDexie() which leverages IndexedDB for persistent client-side storage.
        - **Node.js**: Uses getRxStorageLoki({ adapter: new LokiFsStructuredAdapter() }) for filesystem-based persistence, primarily for development, testing, and potential CLI tools.
    - **Schema Derivation**: RxDB collection schemas (JSON Schema) are derived at runtime from primary Zod schemas (defined in src/types/) using the zod-to-json-schema utility. This ensures Zod remains the single source of truth for data shapes.
    - **Centralized Initialization**: A single initializeDb() function in rxdbSetup.ts handles database creation, plugin loading, and collection definition. It returns a Promise<N8nAidDatabase> which resolves to the singleton DB instance.
    - **Required RxDB Plugins (V1)**: RxDBDevModePlugin (for development builds), RxDBQueryBuilderPlugin (for Mango query syntax), RxDBSchemaCheckPlugin (for schema validation). For browser environments, RxDBLeaderElectionPlugin is added to manage multi-tab access.
- **Key Features & V1 Collections**:
    - **Database Initialization**: initializeDb(dbName?: string) function.
    - **Singleton Access**: getDb(): Promise<N8nAidDatabase> function.
    - **V1 Collections Defined**:
        1. **user_settings**: Stores UserSettings (API keys, model preferences, feature flags). Singleton document pattern with a fixed ID (e.g., 'singleton_user_settings'). Schema derived from UserSettingsSchema.
        2. **task_sessions**: Stores TaskSession objects (state for individual AI operations, including GraphExecutionContextSnapshot, llmContextSummary). Schema derived from TaskSessionSchema. Key indexes: id (primaryKey), chatId, status, updatedAt.
        3. **chat_threads**: Stores ChatThread metadata (e.g., id, title, createdAt, lastUpdatedAt, globalSummary). Schema derived from ChatThreadSchema. Key indexes: id (primaryKey), lastUpdatedAt.
        4. **conversation_messages**: Stores individual ChatMessage objects (aligning with Vercel AI SDK UIMessage). Schema derived from ChatMessageSchema. Key indexes: id (primaryKey), chatId, timestamp, composite ['chatId', 'timestamp'].
        5. **custom_rules**: Stores user-defined N8nRule objects. Schema derived from N8nRuleSchema. Key indexes: id (primaryKey), isActive, type, appliesToAgents (array index).
        6. **knowledge_chunks**: Stores KnowledgeChunk objects for RAG (text chunks, embeddings, source metadata). Schema derived from KnowledgeChunkSchema. Key indexes: id (primaryKey), sourceType, and custom fields for vector search (e.g., idx0-idx4).
    - **RxDB Schema Requirements**: Each collection schema includes title, version: 0 (for initial schema), primaryKey (mapped from the Zod schema's id field), and appropriate indexes.
- **Error Handling**: initializeDb() handles errors during database creation and collection setup, resetting its promise to allow for retries.
- **Multi-Platform**: isNodeEnvironment() utility used to select the correct storage adapter.

### **3.4.2 Repository Pattern Implementation (src/persistence/repositories/)**

- **Purpose**: To abstract the direct RxDB database interactions for each data domain (collection). Repositories provide a clean, domain-specific, asynchronous API for Create, Read, Update, Delete (CRUD) operations and common queries. They decouple service-layer logic from RxDB-specific implementation details.
- **Design Decisions**:
    - **One Repository per Collection**: Each RxDB collection (e.g., user_settings, task_sessions) has its own dedicated repository class.
    - **Dependency Injection**: Repositories receive the Promise<N8nAidDatabase> (from getDb()) via their constructor, allowing for easy testing with mock DB instances.
    - **Plain Data Objects**: Repository methods accept and return plain JavaScript objects (POJOs) that conform to the Zod-inferred TypeScript types (e.g., TaskSession), not raw RxDocument objects. They handle doc.toJSON() internally.
    - **Zod Validation on Write**: Before inserting or updating data, repositories validate the input data (or the resulting merged data for updates) against the relevant Zod schema to ensure application-level data integrity *before* it reaches RxDB's JSON schema validation.
    - **Atomic/Incremental Updates**: Prefer RxDB's doc.atomicPatch() or doc.incrementalPatch() for updates to existing documents to ensure data consistency and efficient patching. collection.atomicUpsert() is used for "save" operations that might create or update.
    - **Comprehensive Error Handling**: Repository methods wrap RxDB calls in try...catch blocks, log errors, and re-throw them as specific application errors or return null/false as appropriate for the method contract.
- **Key V1 Repositories & Their Core Responsibilities:**
    - **UserSettingsRepository.ts**:
        - Manages the singleton user_settings document.
        - getSettings(): Promise<UserSettings>: Fetches settings; creates and returns defaults (derived from UserSettingsSchema.parse({id: ...})) if document doesn't exist.
        - saveSettings(settingsDelta: Partial<UserSettings>): Promise<UserSettings>: Updates the settings document using atomicPatch or incrementalPatch.
        - resetSettingsToDefaults(): Promise<UserSettings>: Overwrites settings with defaults.
    - **TaskSessionRepository.ts**:
        - Manages TaskSession documents.
        - createSession(data: Pick<TaskSession, 'chatId' | 'originalUserInput'> & Partial<...>): Promise<TaskSession>: Creates a new session with generated UUID, default title, timestamps, and validates with TaskSessionSchema.
        - getSessionById(id: string): Promise<TaskSession | null>.
        - updateSession(id: string, updates: Partial<Omit<TaskSession, 'id'|'createdAt'>>): Promise<TaskSession | null>: Validates merged state and uses incrementalPatch.
        - saveSession(session: TaskSession): Promise<TaskSession>: Upserts a session using atomicUpsert (validates first).
        - deleteSession(id: string): Promise<boolean>.
        - findSessions(options?: TaskSessionQueryOptions): Promise<TaskSession[]>: Implements querying with filters (status, chatId, priority, tags), sorting, and pagination.
        - countSessions(options?): Promise<number>: Counts sessions based on filters.
        - getLatestSessionForChat(chatId: string): Promise<TaskSession | null>.
        - getActiveSessions(chatId?: string): Promise<TaskSession[]>.
    - **CustomRulesRepository.ts**:
        - Manages user-defined N8nRule documents.
        - addRule(ruleData: Omit<N8nRule, 'id'|'createdAt'|'updatedAt'>): Promise<N8nRule>: Validates, adds UUID/timestamps, inserts.
        - getRuleById(id: string): Promise<N8nRule | null>.
        - updateRule(id: string, updates: Partial<...>): Promise<N8nRule | null>: Validates merged state, uses incrementalPatch.
        - deleteRule(id: string): Promise<boolean>.
        - getAllRules(options?: RuleQueryOptions): Promise<N8nRule[]>: Querying with filters (isActive, type, appliesToAgent, source, category), pagination.
        - getActiveRulesForAgent(agentSlug: string): Promise<N8nRule[]>.
        - getRulesByType(type: RuleType, activeOnly?: boolean): Promise<N8nRule[]>.
        - countRules(options?): Promise<number>.
    - **ChatRepository.ts (Plan will be similar for this future component):**
        - Manages chat_threads and conversation_messages collections.
        - Methods for creating threads, adding messages, fetching messages for a thread (with pagination, before/after timestamp), fetching messages associated with a TaskSessionID.
    - **KnowledgeRepository.ts (Plan will be similar for this future component):**
        - Manages knowledge_chunks collection.
        - addChunk(chunkData): Includes calling EmbeddingService to generate embedding, calculating custom vector search indexes (idx0-idx4).
        - vectorSearch(queryEmbedding, queryIndexValues, topK, filters?): Implements the two-stage vector search (RxDB query on idx fields, then in-memory similarity).
- **Error Handling**: Repositories catch RxDB errors and Zod validation errors, log them, and typically re-throw as a more generic RepositoryError or return null/false to indicate failure to the consuming service.

## 4. FUNCTIONAL REQUIREMENTS

This section outlines the functional capabilities of the n8n Agentic AI "Brain" Library (V1). These requirements describe the specific tasks and operations the system must be able to perform.

### 4.1 Core Workflow Lifecycle Capabilities (V1 Focus)

The V1 library will support the full lifecycle for three primary types of n8n workflow-related user requests: creating new workflows, updating existing workflows, and analyzing existing workflows.

### **4.1.1 New Workflow Creation ("Create New Workflow" Graph)**

- **FR-CR-001: Intent Recognition for Creation:** The system must be able to recognize a user's intent to create a new n8n workflow from a natural language request.
- **FR-CR-002: Requirements Elicitation:** If the initial request lacks sufficient detail, the system must be able to ask targeted, clarifying questions to gather necessary requirements for workflow design (e.g., trigger type, key data sources/actions, desired outcome). This includes supporting multi-turn clarification dialogues.
- **FR-CR-003: Architectural Planning:** Based on gathered requirements, the system (via N8nArchitectAgent) must design a high-level plan for the new workflow. This plan must include:
    - A suggested trigger node and its configuration rationale.
    - A sequence of logical steps detailing the workflow's process.
    - For each step: a description, suggested key n8n node types, summaries of expected data inputs and outputs, and basic error handling considerations.
    - For complex steps, "Focused Design Spike" notes detailing design rationale and primary implementation approaches.
    - A suggested workflow name and overall error handling strategy.
- **FR-CR-004: Node Selection:** Based on the architectural plan, the system (via N8nNodeSelectorAgent) must select the most appropriate specific n8n node type IDs for each planned step, utilizing cached n8n node specifications (NodeSpecService).
- **FR-CR-005: Node Composition:** For each selected node type, the system (via N8nNodeComposerAgent) must:
    - Fetch the live/cached detailed specification (schema, parameters, defaults) for that node type (via NodeSpecService).
    - Generate the complete, syntactically valid n8n JSON for that node, populating parameters based on the architectural plan, upstream data context, and n8n best practices/syntax rules.
    - Assign a descriptive display name to the node.
    - Include typeVersion from the node specification.
    - Describe its key output data structure.
- **FR-CR-006: Workflow Documentation (Sticky Notes):** The system (via N8nWorkflowDocumenterAgent) must analyze the composed functional nodes and the architectural plan to generate and place relevant Sticky Note JSON objects for documenting complex sections or key logic.
- **FR-CR-007: Connection Composition:** The system (via N8nConnectionComposerAgent) must generate the connections JSON array to correctly link all composed nodes (functional and sticky notes) based on the architectural plan and n8n connection rules (e.g., IF/Switch output indexing).
- **FR-CR-008: Best Practice Validation:** The system (via N8nBestPracticeValidatorAgent) must validate the complete proposed workflow JSON (nodes and connections) against a predefined set of n8n best practices and any active user-defined custom rules. This includes checks for common pitfalls, anti-patterns, and adherence to design guidelines.
- **FR-CR-009: Self-Reflection and Refinement (V1 Basic):** For critical outputs (e.g., Architect's plan, Composer's node JSON), the system must support a basic self-reflection loop (using N8nReflectionAgent) to critique and suggest improvements based on the initial goal and best practices. The original agent will then attempt to refine its output.
- **FR-CR-010: Output Proposed Workflow:** The graph execution must culminate in outputting a complete ProposedWorkflowJson (containing nodes and connections) and a user-friendly summary of the proposed solution. (Actual application to an n8n canvas is outside V1 library scope, managed by the consuming application).

### **4.1.2 Existing Workflow Update ("Update Existing Workflow" Graph - V1 High-Level)**

- **FR-UP-001: Intent Recognition for Update:** The system must recognize a user's intent to modify an existing n8n workflow (which would be provided as input JSON to the graph).
- **FR-UP-002: Change Requirements Elicitation:** Similar to FR-CR-002, gather details about *what specific changes* are desired in the existing workflow.
- **FR-UP-003: Contextual Architectural Planning:** The N8nArchitectAgent must analyze the existing workflow JSON and the change requirements to produce a plan for modifications. This plan should identify nodes to add, remove, or update, and detail changes to connections.
- **FR-UP-004: Contextual Node Selection/Composition:** NodeSelectorAgent and NodeComposerAgent will operate based on the modification plan, potentially re-composing existing nodes with changes or adding new ones. Composer must be able to take existing node JSON as context for modifications.
- **FR-UP-005: Contextual Documentation & Connection Update:** WorkflowDocumenterAgent and ConnectionComposerAgent must update sticky notes and connections to reflect the changes.
- **FR-UP-006: Validation of Modified Workflow:** N8nBestPracticeValidatorAgent validates the *entire resulting workflow* after modifications.
- **FR-UP-007: Output Modified Workflow Proposal:** Output a ProposedWorkflowJson representing the fully modified workflow.

### **4.1.3 Workflow Analysis ("Analyze Workflow" Graph - V1 High-Level)**

- **FR-AN-001: Intent Recognition for Analysis:** The system must recognize a user's intent to analyze an existing n8n workflow (provided as input JSON). The request might be general ("review this workflow") or specific ("check this for performance issues").
- **FR-AN-002: Analysis Execution:** The graph will primarily utilize agents like:
    - N8nBestPracticeValidatorAgent: To check against best practices and custom rules.
    - (Future V1.x/V2) N8nOptimizerAgent: To identify performance bottlenecks or suggest optimizations (V1 might use Validator for basic performance rules).
    - N8nExplainerAgent: To generate a natural language summary or explanation of the workflow's functionality or specific parts.
- **FR-AN-003: Output Analysis Report:** The graph execution must output a structured report containing findings, explanations, and suggestions.

### 4.2 User Interaction & Context Management (via TaskSessionManager & HIL)

- **FR-HIL-001: Request User Clarification:** Agents must be able to signal (via AgentExecutionResult.status = 'needs_clarification') when they require additional information from the user to proceed. This signal must include the question and optionally suggested replies.
- **FR-HIL-002: Graph Pause for User Input:** The GraphExecutor must pause execution when an agent requests user clarification, making the interruptPayload (question details) available.
- **FR-HIL-003: Resume Graph with User Input:** The system (OrchestratorService via GraphExecutor.resumeWithUserInput()) must be able to resume a paused graph, providing the user's response as input to the waiting agent or graph path.
- **FR-CTX-001: Task-Specific Conversation Context:** Each TaskSession must maintain its own LLM context, including a summary (llmContextSummary) and a window of recent relevant messages.
- **FR-CTX-002: Proactive Context Summarization:** The system (via ConversationHistoryService used by AgentPromptBuilder, orchestrated by TaskSessionManager if direct call) must monitor the token size of the context being prepared for an LLM call. If it exceeds a threshold relative to the target model's context window, it must automatically invoke the n8n-summarizer-agent to condense the history and update the TaskSession.llmContextSummary before proceeding with the original agent's LLM call.
- **FR-CTX-003: "Team Awareness" in Prompts:** Agent prompts (constructed by AgentPromptBuilder) must include context about the agent's role within the current graph and a brief overview of other key agents participating in that graph.
- **FR-CTX-004: Rule Injection in Prompts:** Agent prompts must include relevant N8nRule descriptions (base and custom) selected by RuleSelectorService.
- **FR-CTX-005: Knowledge Snippet Injection (V1 Advanced RAG):** Agent prompts must be capable of including relevant knowledge snippets retrieved from indexed n8n documentation or example workflows (via KnowledgeRepository and a RAG mechanism, orchestrated by AgentPromptBuilder).
- **FR-CTX-006: Output Schema Injection:** Agent prompts must include a JSON schema representation (derived from Zod) of their expected final structured output to guide the LLM.

### 4.3 n8n Domain Knowledge & Integration

- **FR-N8N-001: Node Specification Access:** The system (NodeSpecService) must load and cache n8n node specifications (from a snapshot file in Node.js, or from live instance data pushed to it in browser mode). Agents like NodeSelector and NodeComposer must use this service for accurate node information.
- **FR-N8N-002: Adherence to n8n Syntax:** N8nNodeComposerAgent must generate JSON that strictly adheres to n8n node parameter syntax, expression language ({{ ... }}, $json, $('Node Name')), and typeVersion requirements based on the live/cached node specifications.
- **FR-N8N-003: Base n8n Best Practices:** A core set of n8n best practices (as defined in baseN8nRules.config.ts) must be available to relevant agents (Architect, Validator, Reflection) via RuleSelectorService.
- **FR-N8N-004: User-Defined Custom Rules:** The system must allow users to define their own custom N8nRules (persisted via CustomRulesRepository), which will be considered by RuleSelectorService and relevant agents.

### 4.4 LLM Interaction & Configuration

- **FR-LLM-001: Multi-Provider Support (Conceptual):** LLMService must use Vercel AI SDK, which supports multiple LLM providers (OpenAI, Anthropic, Google, etc.).
- **FR-LLM-002: User-Configurable API Keys:** Users must be able to provide their own API keys for different LLM providers (managed by UserSettingsService).
- **FR-LLM-003: Model Selection per Task Category:** Users must be able to configure preferred models for different LLMTaskCategorys. LLMService must use these preferences with defined fallbacks.
- **FR-LLM-004: Chrome AI Integration:** LLMService must attempt to use chrome.ai (Gemini Nano) for suitable utility tasks if enabled by the user and available, falling back to cloud models otherwise.
- **FR-LLM-005: Token Counting & Cost Estimation:** LLMService must track token usage (prompt and completion) for each call and calculate estimated costs based on model pricing. This data should be available per TaskSession.
- **FR-LLM-006: Retry Logic:** LLMService must implement automatic retries with exponential backoff for transient LLM API errors.
- **FR-LLM-007: Streaming Responses:** LLMService must support streaming LLM responses, including text deltas and custom UI data parts (thoughts, steps) for a responsive experience.
- **FR-LLM-008: Structured Output Support:** LLMService must support instructing LLMs (via Vercel AI SDK options) to produce structured JSON output conforming to a provided schema (Zod-derived JSON schema).

### 4.5 System Operations & Extensibility

- **FR-SYS-001: Multi-Platform Library:** The core AI Brain must function in both Node.js and modern browser environments.
- **FR-SYS-002: Graph Execution Cancellation:** GraphExecutor (via OrchestratorService) must support cancellation of ongoing graph executions (e.g., via AbortSignal).
- **FR-SYS-003: Agent Extensibility:** The system must allow new specialized AI agents to be developed by extending BaseAgent and registering them in agentRegistry.
- **FR-SYS-004: Tool Extensibility:** The system must allow new tools to be defined (ToolDefinition) and registered in toolRegistry for use in graphs.
- **FR-SYS-005: MCP Server Integration (Context7, Custom GitHub Querier - V1 Advanced RAG):** The system must define tool interfaces that can interact with MCP-compliant servers (like Context7 for n8n documentation, and a custom one for querying GitHub for workflow examples) as part of its advanced RAG capabilities. The actual MCP client logic might be part of the tool's implementation or a shared utility.

## 5. NON-FUNCTIONAL REQUIREMENTS

This section outlines the non-functional requirements (NFRs) for the n8n Agentic AI "Brain" Library (V1). These NFRs define the quality attributes, performance characteristics, and operational constraints of the system.

### 5.1 Performance Requirements

- **NFR-PER-001: Agent Response Time (Individual LLM Call):** For typical LLM interactions initiated by an agent (e.g., planning step, node composition), the LLMService should aim to return a response (or start streaming) within an acceptable timeframe, acknowledging that actual LLM provider latency is a major factor. Target: P95 latency for the LLMService overhead (excluding actual LLM processing time) to be < 500ms.
- **NFR-PER-002: Prompt Building Time:** The AgentPromptBuilder (including its use of GenericPromptBuilder, ConversationHistoryService, and RuleSelectorService) should assemble prompts efficiently. Target: P95 time for prepareLlmMessages() to be < 300ms (excluding time for any summarization LLM calls).
- **NFR-PER-003: Context Summarization Time:** If context summarization is triggered, the sub-process (summarizer prompt build + summarizer LLM call via LLMService + TaskSession update) should complete within a reasonable timeframe so as not to unduly delay the primary agent's task. Target: P95 for entire summarization cycle < 10-15 seconds (highly dependent on summarizer LLM speed and text length).
- **NFR-PER-004: Graph Executor Step Time:** The overhead of the GraphExecutor transitioning between nodes, resolving inputs, and calling agent/tool execute methods (excluding the agent/tool execution time itself) should be minimal. Target: P95 step transition overhead < 50ms.
- **NFR-PER-005: Resource Usage (Library in Browser):** When integrated into a browser extension, the AI Brain library's idle CPU usage should be negligible. During active processing, CPU and memory usage should be optimized to prevent noticeable degradation of browser performance or the n8n editor experience.
    - Specific target: In-memory caches (e.g., NodeSpecService) should be managed efficiently. RxDB queries should be optimized.
- **NFR-PER-006: Resource Usage (Library in Node.js):** For Node.js usage (CLI, testing), CPU and memory footprint should be reasonable for typical development machines.
- **NFR-PER-007: Scalability (Conceptual for V1 Library):** While V1 library doesn't manage concurrent users, its internal design (e.g., RxDB, stateless services where possible) should not inherently prevent future scalability if used in a multi-user backend or more intensive browser scenarios. The primary bottleneck will be LLM provider capacity and API key limits.
- **NFR-PER-008: RAG Retrieval Speed (V1 Advanced RAG):** Queries to the KnowledgeRepository (for rules, n8n docs, workflow examples via local vector search) should return results quickly to not delay prompt building. Target: P95 for simple RAG retrieval < 500ms for a moderately sized local knowledge base (e.g., core rules + key n8n docs).

### 5.2 Reliability and Availability

- **NFR-REL-001: Error Handling Robustness:** The system must gracefully handle errors from LLM APIs, internal services, tool executions, and data persistence. Specific error types should be used.
- **NFR-REL-002: Retry Mechanisms:** LLMService must implement retries for transient LLM API errors. ToolExecutorService should support retries for tools that define themselves as retryable.
- **NFR-REL-003: Data Integrity (Persistence):** Data stored via RxDB (TaskSessions, UserSettings, Rules) must maintain integrity. Schema validation (Zod at app layer, JSON schema at DB layer) is crucial. Atomic operations should be used for updates where appropriate.
- **NFR-REL-004: State Preservation on Pause:** When a graph execution is paused (e.g., for HIL), its complete GraphExecutionContextSnapshot must be reliably persisted via TaskSessionRepository to allow for accurate resumption.
- **NFR-REL-005: Graceful Degradation (e.g., Chrome AI Unavailable):** If an optional component like Chrome AI is unavailable, the system must gracefully fall back to alternative mechanisms (e.g., cloud LLM for utility tasks) without crashing.
- **NFR-REL-006: Input Validation:** All external inputs to services, agents, and tools (especially those originating from user text or LLM outputs) must be validated against their expected schemas (Zod).

### 5.3 Security Requirements

- **NFR-SEC-001: API Key Management:**
    - User-provided LLM API keys (UserSettings) must be stored securely (RxDB can support encryption at rest if an encryption plugin is used and a user-provided master password, though this is complex for V1 browser. For V1, they are stored as-is in local RxDB, access controlled by browser/OS user profile).
    - API keys must *only* be sent to the intended LLM provider's domain by LLMService.
    - API keys must *never* be logged or exposed in non-error UI outputs or retrievable by non-privileged parts of the system.
- **NFR-SEC-002: Prompt Injection Mitigation (General Awareness):**
    - While users provide their own LLM API keys, and the "attacker" is often the user themselves trying to explore, system prompts should be designed to give clear instructions and roles to agents.
    - User inputs that are directly embedded into prompts (especially those for N8nNodeComposerAgent or CodeNode generation if ever implemented) should be clearly delimited and the LLM instructed to treat them as data, not instructions. (This is a general LLM security best effort).
- **NFR-SEC-003: Data Exposure Limitation:**
    - Sensitive information from UserSettings (like API keys) should not be exposed in general logs or non-essential contexts.
    - Agents should be prompted to avoid including unnecessary sensitive data from user inputs or workflow context in their outputs or logs (as per rule BR_SEC_004).
- **NFR-SEC-004: Secure Tool Execution (Conceptual for V1 Library):**
    - Tools that interact with the local system (e.g., main-world.ts bridge in browser, file system in Node.js) must be designed with security in mind, validating inputs and limiting scope of action. The ToolExecutorService should not allow arbitrary code execution based on LLM-generated tool arguments without strict validation.
- **NFR-SEC-005: External Data Validation (Covered by FR-N8N-002, BR_SEC_002):** All data fetched from external n8n instances (node specs) or from RAG sources must be treated as potentially untrusted and validated against expected schemas.

### 5.4 Usability Requirements (for the Library Developer and Future UI)

- **NFR-USA-001: API Clarity (Library):** The public APIs of core services (OrchestratorService, TaskSessionManager, UserSettingsService, GraphBuilder) should be well-documented, intuitive, and type-safe.
- **NFR-USA-002: Debuggability:**
    - The system should produce detailed logs (configurable levels) to aid in debugging graph executions, agent behavior, and service interactions.
    - GraphExecutionContextSnapshot and versioned NodeOutputVersions provide a rich trace.
    - Error messages should be clear and actionable.
- **NFR-USA-003: Extensibility & Maintainability (Developer DX):**
    - The highly decoupled architecture (V3.3.1 file structure) with clear SRP for components should make the codebase easy to understand, maintain, and extend.
    - Adding new agents, tools, or graph definitions should follow clear, documented patterns.
- **NFR-USA-004: Configuration Management:** User settings (API keys, model preferences) should be manageable (future UI will use UserSettingsService). Default configurations should allow the system to function "out-of-the-box" for core features once API keys are provided.
- **NFR-USA-005: Transparency of AI Actions (for future UI):**
    - The streaming of agent thoughts and custom UI data parts (LLMService, Vercel AI SDK v5) is designed to allow a future UI to provide users with clear insight into the AI's reasoning process.
    - The "Team Awareness" context in prompts helps agents generate outputs that are explainable in the context of a collaborative AI system.

### 5.5 Compatibility Requirements

- **NFR-COM-001: Node.js Version:** The library must be compatible with actively supported LTS versions of Node.js (e.g., Node.js 18.x, 20.x at time of development).
- **NFR-COM-002: Browser Environment:** The library must be compatible with modern evergreen browser environments where Chrome Extensions can run (primarily targeting latest Chrome). Polyfills should be avoided if possible, or used judiciously for widely missing features.
- **NFR-COM-003: TypeScript Version:** Developed using a recent, stable version of TypeScript (e.g., 5.x).
- **NFR-COM-004: Vercel AI SDK Version:** Must use Vercel AI SDK Core v5 alpha (and its associated provider packages) as specified. Compatibility with future stable releases should be monitored.
- **NFR-COM-005: RxDB Version:** Must use a stable, current version of RxDB and compatible storage adapters.
- **NFR-COM-006: n8n Node Specification Compatibility:** NodeSpecService must be ableto parse and understand the structure of n8n's /api/v1/node-types or /types/nodes.json endpoint from recent n8n versions. The nodes.snapshot.json should be from a defined target n8n version.

### 5.6 Maintainability Requirements

- **NFR-MNT-001: Code Readability:** Code must follow established TypeScript and clean code best practices, with clear naming and logical organization.
- **NFR-MNT-002: Modularity:** High cohesion within modules and loose coupling between modules, as per V3.3.1 architecture.
- **NFR-MNT-003: Test Coverage:** Aim for high unit and integration test coverage for all critical components and logic paths. Target: >80-90% for core logic.
- **NFR-MNT-004: Documentation (Code):** JSDoc/TSDoc for all public APIs, classes, methods, and complex internal logic.
- **NFR-MNT-005: Dependency Management:** Minimize external dependencies. Keep dependencies up-to-date.
- **NFR-MNT-006: Configuration Simplicity:** Configuration (base rules, model pricing) should be managed in easily updatable files (JSON or TS constants).

## 6. DESIGN DECISIONS AND RATIONALE

This section documents the key architectural and technological design decisions made during the planning of the n8n Agentic AI "Brain" Library, along with their rationale, alternatives considered, and impacts. This provides a clear understanding of why the system is designed the way it is.

### 6.1 Architectural Decisions

1. **Decision: Custom Graph-Based Orchestration Engine (from Scratch)**
    - **Context**: Need for a flexible and highly controllable way to define and execute complex, multi-step AI agent workflows.
    - **Alternatives Considered**:
        - LangChain/LangGraph.js: Mature frameworks for agentic systems.
        - Hardcoded agent sequences: Simpler initially but less flexible.
    - **Decision Criteria**: Maximum flexibility, control over execution logic, avoidance of external framework overhead/opinions, TypeScript-native solution, learning and deep understanding of agentic principles.
    - **Rationale**: Building a custom graph engine (GraphExecutor, GraphBuilder, GraphDefinition) provides unparalleled flexibility to tailor orchestration precisely for n8n-specific AI tasks. It allows fine-grained control over state management, data flow, HIL integration, error handling, and prompt engineering specific to our needs. It also avoids tying our core "Brain" to the specific abstractions or potential limitations of an external framework, ensuring long-term adaptability and maintainability.
    - **Trade-offs**: Higher initial development effort compared to using an existing framework. Requires meticulous design of the graph execution model and primitives.
    - **Impact**: Core of the system. All agent interactions and task flows are managed by this engine. Requires robust implementation of GraphExecutor and an intuitive GraphBuilder API.
    - **Validation**: Successful execution of complex V1 graph definitions (e.g., "Create New Workflow") with correct data flow, conditional logic, looping, and HIL.
2. **Decision: Specialized, Modular AI Agents with Centralized Infrastructure**
    - **Context**: Need for different AI "skills" (planning, composing, validating, etc.) to collaborate on n8n workflow tasks.
    - **Alternatives Considered**:
        - Single monolithic AI agent: Would be overly complex and hard to prompt effectively.
        - Purely functional agents without shared infrastructure: Leads to code duplication.
    - **Decision Criteria**: Single Responsibility Principle (SRP), maintainability, testability, clear separation of concerns, ability to tailor prompts and models per specific cognitive task.
    - **Rationale**: Breaking down the AI's work into specialized agents (e.g., N8nArchitectAgent, N8nNodeComposerAgent) allows for:
        - Highly focused prompting for each agent, improving LLM performance.
        - Independent development, testing, and refinement of each agent's logic.
        - Easier assignment of appropriate LLMTaskCategory (and thus models) per agent.
        - Clearer overall system logic.
            
            The BaseN8nAgent class and agentRegistry provide common infrastructure and management.
            
    - **Trade-offs**: Requires careful definition of interfaces and data contracts between agents (managed by graph I/O). More components to manage initially.
    - **Impact**: Defines the "actors" in our system. GraphDefinition orchestrates these agents. BaseN8nAgent standardizes their structure.
    - **Validation**: Each agent successfully performs its specific task and integrates correctly within graph executions.
3. **Decision: Highly Decoupled Prompt Engineering Subsystem (engine/prompts/)**
    - **Context**: Recognizing that constructing effective, context-rich, and maintainable LLM prompts is a complex and critical task itself.
    - **Alternatives Considered**:
        - Each agent builds its entire prompt from scratch: Leads to massive duplication and inconsistency.
        - A single, large PromptBuilderService doing everything: Becomes a monolith.
    - **Decision Criteria**: SRP, maintainability, testability, flexibility to adapt to evolving prompt engineering techniques.
    - **Rationale**: The chosen subsystem (V3.3.1 structure) with:
        - Static prompt parts/
        - Micro-utility formatters/, normalizers/, mergers/
        - Orchestrating engine services (GenericPromptBuilder, AgentPromptBuilder)
        - Context-providing engine services (ConversationHistoryService, RuleSelectorService)
            
            ...achieves maximum reasonable decoupling. Each piece is small, focused, and testable. It allows for consistent application of "Team Awareness," rule injection, history management, and output schema instructions across all agents.
            
    - **Trade-offs**: Larger number of small files/modules. Requires understanding the pipeline of how these utilities compose a final prompt.
    - **Impact**: Central to how all agents prepare for LLM calls. Ensures high-quality, context-aware prompts.
    - **Validation**: Agents receive correctly formatted and contextually rich prompts. Summarization triggers correctly. Rules and schemas are injected as expected.
4. **Decision: RxDB for Multi-Platform Persistent Storage**
    - **Context**: Need for a persistent storage solution that works in both Node.js (dev/test/CLI) and browser (Chrome extension) for user settings, task session state, custom rules, and RAG knowledge.
    - **Alternatives Considered**:
        - chrome.storage.local: Browser-only, limited size and querying.
        - IndexedDB directly: More boilerplate than RxDB.
        - SQLite via WASM: Larger bundle size, potentially more complex setup for V1.
        - Separate solutions for Node.js (e.g., file system) and browser: Defeats "single library" goal and adds complexity.
    - **Decision Criteria**: Multi-platform compatibility, local-first operation, querying capabilities, schema support, ability to store JSON-like documents, potential for vector search via custom indexing.
    - **Rationale**: RxDB with pluggable storage adapters (Dexie for browser, LokiJS/FS for Node.js V1) perfectly meets the multi-platform requirement. Its Mango query support, schema enforcement (via JSON schema derived from Zod), and the demonstrated ability to implement client-side vector search (from provided examples) make it a powerful and flexible choice.
    - **Trade-offs**: Adds RxDB as a dependency. Slightly higher initial setup complexity compared to simpler storage.
    - **Impact**: Forms the foundation of the Data Persistence Layer. All stateful services and repositories depend on it.
    - **Validation**: Data is correctly persisted and retrieved across Node.js and (mocked/eventual) browser environments. Queries work as expected.
5. **Decision: Fluent GraphBuilder API with Abstracted Loop Constructs**
    - **Context**: The raw GraphDefinition TypeScript object can be verbose and error-prone to create manually for complex graphs.
    - **Alternatives Considered**:
        - Manual JSON/TS object creation for graphs: More verbose, less type-safe during definition.
        - Visual graph editor (Out of scope for V1 library).
    - **Decision Criteria**: Developer Experience (DX), readability of graph definitions, type safety, ability to encapsulate common patterns.
    - **Rationale**: A fluent builder API (builder.addAgentNode(...).onSuccess(...)) makes graph definition more intuitive, readable, and less error-prone than manipulating raw objects. The builder.addLoop() method, by encapsulating the internal nodes required for iteration, significantly simplifies the definition of loops while ensuring the GraphExecutor remains generic. GraphBuilder.compile() also provides a central place for graph validation.
    - **Trade-offs**: The builder class itself is a complex component to implement correctly.
    - **Impact**: Primary way graphs will be defined. Simplifies the creation of the V1 createNewWorkflowGraph and future graphs.
    - **Validation**: Graphs defined with the builder compile to valid GraphDefinitions. Loop constructs work as expected. API is intuitive.
6. **Decision: "Apply-Then-Review-on-Canvas" for Workflow Changes (UX for Consuming Application)**
    - **Context**: How should users review and confirm AI-generated workflow changes?
    - **Alternatives Considered**:
        - Abstract JSON diff view before applying: Can be hard to understand for n8n workflows.
        - AI applies changes silently: Too risky.
    - **Decision Criteria**: User experience, safety, leveraging n8n's visual interface.
    - **Rationale**: The V1 library will output ProposedWorkflowJson. The consuming application (e.g., Chrome Extension) will then:
        1. Use a checkpoint_workflow_tool to save the current n8n state.
        2. Use apply_changes_to_canvas_tool to apply the AI's proposed JSON to the live n8n canvas.
        3. Allow the user to review the changes *directly on the n8n canvas* (with potential UI highlights for changed nodes/parameters).
        4. User confirms (keep) or reverts (using rollback_workflow_tool which restores the checkpoint).
            
            This provides the most intuitive review experience.
            
    - **Trade-offs**: Requires robust checkpointing and rollback tools. The apply_changes_to_canvas_tool needs to be atomic or handle partial failures gracefully.
    - **Impact**: Defines how the V1 library's output is intended to be consumed by a UI-based host. The library itself doesn't perform the canvas application for V1.
    - **Validation**: (Conceptual for V1 library) Tools for checkpoint, apply, and rollback are defined with clear contracts.

### 6.3 Component Design Decisions (Summary of Key Patterns)

- **Service Layer Abstraction**: Most core functionalities are encapsulated in services (LLMService, PromptBuilderService hierarchy, NodeSpecService, TaskSessionManager, UserSettingsService, RuleSelectorService, ToolExecutorService). Services have injected dependencies.
- **Repository Pattern**: Used for all data persistence (UserSettingsRepository, TaskSessionRepository, etc.) to abstract RxDB interactions.
- **Agent Infrastructure (BaseAgent, AgentRegistry):** Provides a common structure and utilities for all specialized AI agents, promoting consistency and reusability. Agents are instantiated by GraphExecutor via the registry.
- **Fluent Builder API (GraphBuilder):** For defining graphs declaratively and readably.
- **Explicit State Management for Graphs (GraphExecutionContext):** All runtime state for a graph execution is contained within this object, managed by the GraphExecutor. Node outputs are versioned.
- **Proactive Context Management (ConversationHistoryService):** Conversation history for prompts is managed centrally, including automated summarization if context windows are at risk of overflowing.
- **Specialized Micro-Utilities for Prompting (engine/prompts/formatters, normalizers, mergers):** Maximizes decoupling and SRP for the complex task of prompt assembly, orchestrated by GenericPromptBuilder.

### 6.4 Integration Pattern Decisions

- **Service Communication**: Primarily through constructor-based Dependency Injection. Asynchronous method calls (Promise-based).
- **Event Management (V1 - Internal/Implicit):**
    - HIL pauses are signaled by agents returning specific AgentExecutionResult.status values, which GraphExecutor interprets to change GraphExecutionContext.status. The OrchestratorService then handles UI notification.
    - No global event bus in V1 library.
- **State Management (TaskSession & RxDB):** TaskSession objects, persisted via TaskSessionRepository, are the central record for an AI operation's state, including GraphExecutionContextSnapshot and llmContextSummary.
- **Configuration Management**:
    - Static app configurations (base rules, model capabilities) in src/config/.
    - User-specific configurations (API keys, model preferences) managed by UserSettingsService via UserSettingsRepository (RxDB).
- **Dependency Injection:** Core services and repositories are designed to have their dependencies injected, facilitating testability and decoupling. GraphExecutor injects AgentDependencies into agent instances.