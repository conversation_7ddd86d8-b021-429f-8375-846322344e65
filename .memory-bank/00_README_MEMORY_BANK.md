# 00. Memory Bank README

This document outlines the structure, purpose, and maintenance guidelines for the n8n AI Assistant Project Memory Bank.

## Purpose

The Memory Bank serves as the **single source of truth** for all project-related knowledge, decisions, and context. It is designed to:
- Facilitate context continuity between AI development sessions.
- Enable new AI agents (or human developers) to quickly understand the project.
- Provide a stable foundation for the AI-to-AI development workflow.
- Document architectural decisions, component designs, and project history.

## Structure (V4.0 "The Rosetta Stone")

The Memory Bank is organized into the following top-level directories:

- **`01_PROJECT_FOUNDATION/`**: Core mission, product vision, user context, glossary.
- **`02_ARCHITECTURE_AND_DESIGN/`**: System overview, tech stack, data architecture, design patterns.
- **`03_ARCHITECTURE_DECISION_RECORDS/`**: Formal records of key architectural decisions.
- **`04_COMPONENTS/`**: Detailed documentation for all system components, mirroring `src/`.
- **`05_PATTERNS_AND_CONVENTIONS/`**: Coding standards, testing strategies, error handling, prompt guidelines.
- **`06_PROJECT_STATE_AND_ROADMAP/`**: Current phase, roadmap, progress, known issues, release notes.
- **`07_AI_COLLABORATION_WORKFLOW/`**: AI-to-AI workflow, task templates, review processes.
- **`08_TECHNICAL_CONTEXT_AND_SETUP/`**: Dev environment, project structure, dependencies.
- **`09_KNOWLEDGE_BASE/`**: n8n rules, model info, RAG sources, critical domain knowledge.
- **`10_ARCHIVE_AND_HISTORY/`**: Decision evolution, implementation archives, brainstorming logs.

## Maintenance

- **Prioritize Latest Information**: Always update with the most current decisions and designs. Superseded information should be archived or clearly marked.
- **Adhere to Templates**: Use the provided templates (e.g., `ADR_TEMPLATE.md`, `COMPONENT_TEMPLATE.md`) for new entries.
- **Cross-Reference**: Link related documents, decisions, and components extensively.
- **AI-to-AI Workflow**: Follow the procedures outlined in `07_AI_COLLABORATION_WORKFLOW/04_MemoryBankMaintenance.md`.

## Using the Memory Bank

Before starting any new development task, AI agents should consult the relevant sections of the Memory Bank to gain full context. After completing a task that results in new decisions, designs, or knowledge, the AI agent is responsible for updating the Memory Bank accordingly.

# n8n AI Assistant Project Memory Bank (V4.0 "The Rosetta Stone")

Welcome to the Memory Bank for the n8n AI Assistant project. This repository serves as the single source of truth for all project knowledge, including architecture decisions, component specifications, implementation details, project state, and historical context. Its primary purpose is to provide a comprehensive, structured, and easily navigable knowledge base for both human collaborators and AI agents working on the project.

The Memory Bank is designed to be a living document, constantly updated and refined as the project evolves. By centralizing all critical information, we aim to improve communication, reduce knowledge silos, accelerate onboarding, and ensure consistency across all aspects of development.

## Core Principles of the Memory Bank

The Memory Bank operates under several core principles to ensure its effectiveness and reliability:

1.  **Single Source of Truth (SSoT):** All authoritative project knowledge resides here. Information found elsewhere should be considered secondary or potentially outdated.
2.  **Structured and Organized:** Content is organized into logical directories and files following a defined structure (V4.0 "The Rosetta Stone") to facilitate easy navigation and retrieval.
3.  **Version Controlled:** The Memory Bank is part of the project's Git repository, allowing for tracking changes, reviewing history, and collaborating effectively.
4.  **Actionable and Practical:** Information should be presented clearly and concisely, providing practical guidance for development, decision-making, and project management.
5.  **AI-Accessible and AI-Friendly:** The structure and content are designed to be easily parsed and utilized by AI agents, enabling them to understand the project context and contribute effectively.
6.  **Collaborative:** Updates and contributions are encouraged from all team members, human and AI, following a defined process.
7.  **Evolvable:** The structure and content can evolve over time, with changes documented and agreed upon.

## V4.0 "The Rosetta Stone" Structure Overview

The Memory Bank is organized into the following top-level directories, each serving a specific purpose:

-   [`00_README_MEMORY_BANK.md`](.memory-bank/00_README_MEMORY_BANK.md): This file, providing an introduction, principles, structure overview, and usage/maintenance guidelines.
-   [`01_PROJECT_FOUNDATION/`](.memory-bank/01_PROJECT_FOUNDATION/): High-level "what and why."
-   [`02_ARCHITECTURE_AND_DESIGN/`](.memory-bank/02_ARCHITECTURE_AND_DESIGN/): The "how" at a system level.
-   [`03_ARCHITECTURE_DECISION_RECORDS/`](.memory-bank/03_ARCHITECTURE_DECISION_RECORDS/): Immutable log of key decisions. Standard ADR format.
-   [`04_COMPONENTS/`](.memory-bank/04_COMPONENTS/): Granular documentation per component, following a template. This is where the details extracted from our implementation plans will primarily reside, summarized for the PRD context. It's organized mirroring the `src` structure for easy correlation.
-   [`05_PATTERNS_AND_CONVENTIONS/`](.memory-bank/05_PATTERNS_AND_CONVENTIONS/): Cross-cutting concerns for quality and consistency.
-   [`06_PROJECT_STATE_AND_ROADMAP/`](.memory-bank/06_PROJECT_STATE_AND_ROADMAP/): Dynamic project management documents. `ProgressStatus.md` will be the main "current work" tracker, absorbing `activeContext.md`.
-   [`07_AI_COLLABORATION_WORKFLOW/`](.memory-bank/07_AI_COLLABORATION_WORKFLOW/): Defines how we (AI Studio and Claude Code) work.
-   [`08_TECHNICAL_CONTEXT_AND_SETUP/`](.memory-bank/08_TECHNICAL_CONTEXT_AND_SETUP/): Practical info for developers.
-   [`09_KNOWLEDGE_BASE/`](.memory-bank/09_KNOWLEDGE_BASE/): Specific data/configs used by the AI, with explanations.
-   [`10_ARCHIVE_AND_HISTORY/`](.memory-bank/10_ARCHIVE_AND_HISTORY/): Crucial for context continuity. `DecisionEvolution.md` explicitly tracks how we arrived at current decisions, fulfilling your critical requirement for prioritizing latest versions while retaining history.

## Navigating and Using the Memory Bank

-   **Browsing:** Explore the directories and files directly in your file explorer or within your IDE. The numerical prefixes help maintain a logical order.
-   **Searching:** Utilize your IDE's search functionality to find specific keywords, phrases, or code snippets within the Markdown files.
-   **Linking:** Use relative Markdown links (e.g., `[Project Brief](../01_PROJECT_FOUNDATION/01_ProjectBrief.md)`) to reference other documents within the Memory Bank.
-   **Templates:** When creating new ADRs or Component documentation, always start from the provided templates in [`03_ARCHITECTURE_DECISION_RECORDS/`](.memory-bank/03_ARCHITECTURE_DECISION_RECORDS/) and [`04_COMPONENTS/`](.memory-bank/04_COMPONENTS/) respectively to ensure consistency.

## Maintaining and Updating the Memory Bank

Maintaining the Memory Bank is a collaborative effort involving human team members and AI agents. The goal is to keep the information accurate, complete, and up-to-date.

**General Guidelines:**

-   **Keep it Current:** Whenever a decision is made, a component is designed or modified, or the project state changes, update the relevant documentation in the Memory Bank promptly.
-   **Be Clear and Concise:** Write clearly and avoid jargon where possible. Assume the reader (human or AI) may not have full context.
-   **Use Markdown:** All documents should be in Markdown format.
-   **Follow Structure:** Place documents in the appropriate directories according to the V4.0 structure.
-   **Pull Requests:** All significant changes should go through a review process via Git pull requests.

**AI-to-AI Collaborative Update Workflow:**

The Memory Bank is central to the collaboration between AI agents like the Architect (Roo) and the Developer (Claude Code). The general workflow for AI-driven updates is as follows:

```mermaid
graph TD
    A[Developer AI (e.g., Claude Code)] --> B{Identifies need for Memory Bank Update};
    B --> C{Proposes Update (e.g., new component doc, ADR)};
    C --> D[Creates Draft Markdown File];
    D --> E[Submits Draft to Architect AI (Roo)];
    E --> F{Architect AI Reviews Draft};
    F -- Validated --> G[Architect AI Approves Update];
    F -- Requires Revision --> H[Architect AI Provides Feedback];
    H --> A;
    G --> I[Architect AI Initiates File Write/Diff];
    I --> J[Updates Memory Bank File(s)];
    J --> K{Human Orchestrator Reviews Changes (via PR)};
    K -- Approved --> L[Changes Merged];
    K -- Needs Human Input --> M[Human Provides Guidance];
    M --> A;
    L --> N[Memory Bank Updated];
    N --> A;
    N --> E; %% Memory Bank update informs future Architect reviews
```

**Explanation of the AI Workflow:**

1.  **Developer AI (e.g., Claude Code):** While working on implementation or design tasks, identifies a need to update the Memory Bank (e.g., completing a component design, making a significant implementation decision).
2.  **Proposes Update:** The Developer AI formulates the information to be added or changed.
3.  **Creates Draft:** The Developer AI generates the content for the update in Markdown format, adhering to the relevant templates and structure.
4.  **Submits Draft to Architect AI (Roo):** The Developer AI presents the proposed Markdown content to the Architect AI for review and validation.
5.  **Architect AI (Roo) Reviews Draft:** The Architect AI examines the proposed content for accuracy, completeness, adherence to structure and principles, and overall alignment with the project's architecture and goals.
6.  **Validation/Feedback:**
    *   If the draft is validated, the Architect AI approves the update.
    *   If the draft requires revision, the Architect AI provides specific feedback to the Developer AI on what needs to be changed.
7.  **Initiates File Write/Diff:** Upon approval, the Architect AI uses the available tools (like `write_to_file` or `apply_diff`) to apply the changes to the actual files in the Memory Bank.
8.  **Updates Memory Bank File(s):** The changes are written to the repository.
9.  **Human Orchestrator Reviews Changes:** A human team member reviews the changes via a standard Git pull request process. This provides a final layer of oversight.
10. **Approval/Human Input:**
    *   If the changes are approved, they are merged into the main branch.
    *   If human input is needed (e.g., for complex decisions or conflicts), the human provides guidance.
11. **Memory Bank Updated:** The Memory Bank now contains the new validated information, which informs future work for both human and AI collaborators.

This workflow ensures that AI contributions to the Memory Bank are reviewed and validated by the Architect AI before being committed, maintaining the quality and integrity of the knowledge base.

## Contribution Guidelines

-   All contributions, whether from humans or AI agents, should follow the structure and principles outlined in this README.
-   Use clear and descriptive commit messages.
-   Submit changes via pull requests for review.
-   Ensure any new documents adhere to the relevant templates.

## Further Reading

-   [`docs/memorybank_structure_and_rationale.md`](docs/memorybank_structure_and_rationale.md): The detailed definition and rationale for the V4.0 "The Rosetta Stone" structure.
-   [`docs/aistudio_export/2_N8n Agentic AI Brain Library Design.txt`](docs/aistudio_export/2_N8n Agentic AI Brain Library Design.txt): Provides context on the design process and the vision for the AI-accessible knowledge base.
-   [`docs/aistudio_export/1_N8N Agent System Brainstorm.txt`](docs/aistudio_export/1_N8N Agent System Brainstorm.txt): Historical context on the initial brainstorming for the agent system.