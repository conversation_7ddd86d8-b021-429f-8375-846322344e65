# Technical Context

## Technology Stack

### Core Technologies
- **TypeScript 5.x**: Primary language with strict typing
- **Node.js 18+/20+**: Runtime for development and CLI
- **Modern Browsers**: Target for extension integration

### Key Dependencies

#### LLM Integration
- **Vercel AI SDK Core v5 alpha**: LLM provider abstraction
- **@ai-sdk/openai, @ai-sdk/anthropic**: Provider packages
- **gpt-tokenizer**: Client-side token counting

#### Data Persistence
- **RxDB**: Multi-platform document database
- **Dexie**: IndexedDB adapter for browser
- **LokiJS**: File system adapter for Node.js

#### Validation & Schemas
- **Zod**: Runtime type validation and schema definition
- **zod-to-json-schema**: Schema conversion for LLM instructions

#### Testing
- **Vitest**: Test runner and framework
- **TypeScript Test Utils**: Type-safe testing utilities

#### Build & Development
- **TSC**: TypeScript compiler
- **ESLint**: Code linting
- **Prettier**: Code formatting

## Development Environment

### Required Setup
- Node.js 18+ with pnpm package manager
- TypeScript globally installed
- Modern code editor with TS support

### Build Commands
```bash
pnpm build          # Build for all environments
pnpm dev            # Development mode with watch
pnpm test           # Run all tests
pnpm test:coverage  # Run tests with coverage
pnpm type-check     # TypeScript type checking
pnpm lint           # ESLint code quality
```

### Project Configuration
- **tsconfig.json**: TypeScript configuration with path aliases
- **vitest.config.ts**: Test configuration
- **package.json**: Dependencies and scripts
- **.gitignore**: Version control exclusions

## Architecture Constraints

### Multi-Platform Requirements
- Code must work in both Node.js and browser environments
- Storage adapters switch based on environment detection
- Network calls abstracted through tool interfaces
- File system access only in Node.js context

### Performance Considerations
- Client-side vector search for RAG
- Streaming LLM responses for UI responsiveness
- Efficient prompt caching and summarization
- Optimized RxDB queries and indexing

### Security Requirements
- API keys stored locally in RxDB
- No sensitive data in logs or outputs
- Input validation at all boundaries
- Secure tool execution boundaries

## Path Aliases (tsconfig.json)
```typescript
{
  "@/engine/*": ["src/engine/*"],
  "@/agents/*": ["src/agents/*"],
  "@/services/*": ["src/services/*"],
  "@/persistence/*": ["src/persistence/*"],
  "@/types/*": ["src/types/*"],
  "@/utils/*": ["src/utils/*"],
  "@/config/*": ["src/config/*"]
}
```

## Environment Detection
```typescript
// src/utils/environment.ts
export const isNodeEnvironment = () => typeof window === 'undefined'
export const isBrowserEnvironment = () => typeof window !== 'undefined'
```

## Database Configuration

### RxDB Collections (V1)
- **user_settings**: Singleton user configuration
- **task_sessions**: AI operation state and history
- **chat_threads**: Conversation metadata
- **conversation_messages**: Individual chat messages
- **custom_rules**: User-defined n8n rules
- **knowledge_chunks**: RAG content with embeddings

### Storage Adapters
- **Browser**: Dexie (IndexedDB) for persistence
- **Node.js**: LokiJS with file system adapter

## LLM Configuration

### Model Categories
```typescript
enum LLMTaskCategory {
  CORE_ORCHESTRATION_AND_PLANNING = 'core_orchestration_and_planning',
  N8N_CODE_GENERATION_COMPOSITION = 'n8n_code_generation_composition',
  ANALYTICAL_VALIDATION_CRITIQUE = 'analytical_validation_critique',
  KNOWLEDGE_AUGMENTED_EXPLANATION = 'knowledge_augmented_explanation',
  UTILITY_CLASSIFICATION_ROUTING = 'utility_classification_routing',
  UTILITY_CONVERSATION_SUMMARIZATION = 'utility_conversation_summarization'
}
```

### Provider Support
- OpenAI (GPT-4, GPT-3.5)
- Anthropic (Claude)
- Chrome AI (Gemini Nano) for utility tasks
- User-configurable preferences per category

## Integration Patterns

### Tool Architecture
- ToolDefinition interface with Zod schemas
- ToolExecutorService for runtime execution
- Environment-specific implementations
- Mocked tools for library-only usage

### Agent Architecture
- BaseAgent abstract class with standard interface
- Static metadata (slug, instruction, category)
- Dependency injection via AgentDependencies
- Standardized response helpers

### Graph Architecture
- GraphDefinition JSON-serializable structure
- GraphBuilder fluent API for construction
- GraphExecutor generic runtime engine
- FunctionRegistry for conditional/transform logic

## Testing Strategy

### Unit Testing
- Individual component isolation
- Mocked dependencies
- Property-based testing where appropriate
- High coverage targets (>80%)

### Integration Testing
- Service interaction testing
- Graph execution testing with mocked LLM
- Multi-platform compatibility testing
- Performance benchmarking

### Test Utilities
- Shared mocking helpers
- Graph execution test harnesses
- LLM response simulation
- Database test utilities

## Deployment & Distribution

### Library Packaging
- CommonJS and ES Module outputs
- Type definitions included
- Multi-platform compatibility
- npm distribution ready

### Future Integration
- Chrome extension bridge interfaces
- CLI tool wrappers
- Server-side usage patterns
- API endpoint wrappers