# Project Scope (V1 Library - "The Brain")

## In Scope

*   **Core AI Engine:**
    *   Graph-based orchestration engine (`GraphExecutor`, `GraphBuilder`).
    *   Foundational services (`LLMService`, `NodeSpecService`, Prompt Engineering Subsystem, `TaskSessionManager`, `UserSettingsService`, `RuleSelectorService`, `ConversationHistoryService`, `ToolExecutorService`).
    *   Persistence layer (`rxdbSetup`, core repositories: `UserSettingsRepository`, `TaskSessionRepository`, `CustomRulesRepository`, `KnowledgeRepository`, `ChatRepository`).
    *   Agent infrastructure (`BaseAgent`, `agentRegistry`).
*   **V1 Specialized AI Agents:**
    *   `N8nIntentRouterAgent`
    *   `N8nRequirementsGatheringAgent`
    *   `N8nArchitectAgent`
    *   `N8nNodeSelectorAgent`
    *   `N8nNodeComposerAgent`
    *   `N8nWorkflowDocumenterAgent`
    *   `N8nConnectionComposerAgent`
    *   `N8nBestPracticeValidatorAgent`
    *   `N8nReflectionAgent`
    *   `N8nSummarizerAgent` (as a role/prompt for `ConversationHistoryService`)
*   **V1 Core Tools:**
    *   Tools for knowledge access (RAG: `search_documentation_tool`, `search_workflow_templates_tool`, `get_workflow_template_json_tool`).
    *   Tools for MCP server integration (`query_context7_n8n_docs_tool`, `query_github_workflows_mcp_tool`).
    *   Conceptual/mocked n8n interaction tools for library V1 (`fetch_current_workflow_json_tool`, `apply_changes_to_canvas_tool`, `checkpoint_workflow_tool`, `rollback_workflow_tool`). The library will define interfaces and V1 mock implementations; actual browser interaction is for the consuming extension.
    *   Internal HIL tool mechanism (`internal_await_user_input_tool`).
*   **V1 Graph Definitions:**
    *   `createNewWorkflowGraph_v1`
    *   `updateExistingWorkflowGraph_v1`
    *   `analyzeWorkflowGraph_v1`
*   **Knowledge & Configuration:**
    *   Bundled base n8n rules (`baseN8nRules.config.ts`).
    *   LLM model configuration and pricing data (`llmModels.config.ts`).
    *   Snapshot of n8n node specifications (`nodes.snapshot.json`) for Node.js environment.
    *   Default user settings (`userSettings.defaults.ts`).
*   **RAG System (V1 Advanced):**
    *   Client-side vector search capability using RxDB and `EmbeddingService`.
    *   Ingestion pipeline for n8n documentation and community workflow examples (from pre-processed sources or via MCP for V1 library).
*   **Testing:** Comprehensive unit and integration tests for the library.
*   **Multi-Platform Support:** Node.js (for CLI, testing, development) and browser-compatible library code.
*   **Documentation:** Internal TSDoc/JSDoc for library APIs. Initial Memory Bank population.

## Out of Scope (V1 Library)

*   **User Interface (UI) / Chrome Extension Frontend:** The V1 "Brain" library is backend logic only. UI development is a separate, subsequent project that will consume this library.
*   **Direct, Live n8n Instance Manipulation from Library:** While tools for n8n interaction are defined, their *live execution* (DOM manipulation, direct n8n API calls from browser context) is deferred to the consuming Chrome extension. The V1 library will provide interfaces and mock/conceptual implementations for these tools, allowing the AI logic to be fully developed and tested. The design will ensure easy integration for the extension to provide the "live" tool implementations.
*   **User Accounts & Authentication:** No user account system or authentication mechanisms within the V1 library.
*   **Multi-User Collaboration Features:** The library is designed for a single-user context (per instance).
*   **Real-Time Collaborative Editing:** No features for multiple users editing the same workflow via the AI simultaneously.
*   **Advanced Visual Graph Debugging UI:** While the `GraphExecutor` will have detailed logging, a dedicated visual UI for debugging graph executions is not in V1 scope for the library.
*   **Backend Server for AI Processing:** All AI logic and LLM calls are orchestrated client-side (in Node.js or browser via the library). No dedicated backend server for the AI Brain itself. (MCP servers are external).
*   **Automatic n8n Instance Version Detection for NodeSpecService in Browser:** While `NodeSpecService` can be updated with new specs, V1 won't automatically detect n8n version changes in the browser to trigger this; it will rely on an explicit refresh action (e.g., via a tool call).