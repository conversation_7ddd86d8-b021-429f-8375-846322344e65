# Project Brief: n8n Agentic AI "Brain" Library (V1.0)

## Core Mission

To create a highly intelligent, multi-platform TypeScript library serving as the "AI Brain" for an n8n assistant. This brain will be capable of understanding user requests in natural language, designing complex n8n workflows, generating their precise JSON representations, validating workflows against best practices, and assisting with documentation, explanation, and optimization of n8n automations.

## Vision Statement

Build the world's most sophisticated n8n workflow AI assistant that understands n8n like a specialist engineer, embedding deep knowledge of best practices, anti-patterns, and the nuances of n8n's execution model. The system will democratize n8n workflow creation while maintaining professional-grade quality and reliability.

## Primary Goals

### 1. Develop a Robust Graph-Based AI Orchestration Engine
- Implement a custom, graph-based AI orchestration engine in pure TypeScript (`GraphExecutor`, `GraphBuilder`)
- Capable of executing complex, multi-step agentic workflows
- Built from scratch with maximum flexibility (NO LangChain/LangGraph)
- Support for conditional logic, loops, error handling, and human-in-the-loop interactions

### 2. Implement Specialized AI Agents
Create a suite of V1 AI agents with clearly defined responsibilities:
- **IntentRouter**: Classifies user requests and determines appropriate graph execution
- **RequirementsGatherer**: Elicits detailed requirements and asks clarifying questions
- **Architect**: Creates high-level workflow plans and design strategies
- **NodeSelector**: Selects appropriate n8n node types for implementation
- **NodeComposer**: Generates precise n8n node JSON with live schema validation
- **WorkflowDocumenter**: Manages sticky notes and workflow documentation
- **ConnectionComposer**: Creates proper node connections and data flow
- **Validator**: Validates workflows against n8n best practices
- **Optimizer**: Suggests performance and design improvements
- **Explainer**: Provides detailed explanations of workflows and concepts

### 3. Seamless LLM Integration
- Integrate with LLMs via Vercel AI SDK v5 alpha
- Managed by a central `LLMService` with model selection, token/cost tracking, and retries
- Support for multiple LLM providers and models
- Intelligent task categorization for optimal model selection

### 4. Multi-Platform Compatibility
- Browser and Node.js compatible library architecture
- Enables testing from command line and simple HTML pages
- Foundation for browser extension integration
- Consistent API across all platforms

### 5. Establish Decoupled, Service-Oriented Architecture
- Clear separation of concerns between components
- Robust dependency injection and service management
- Highly maintainable and testable codebase
- Modular design enabling independent component development

## High-Level Architecture Principles

### Graph-Driven Agentic System
- **Main Orchestrator Service**: TypeScript class that runs graphs and manages execution state
- **Graph Definitions**: JSON/TS objects representing high-level tasks (create, update, analyze workflows)
- **Specialized Agents**: TypeScript functions using Vercel AI SDK for LLM interactions
- **Node Types**: Agent calls, tool calls, conditional logic, data transformations, loops

### Agent Specialization Strategy
- Each agent has a focused, single responsibility
- Strict input/output contracts using Zod schemas
- Contextual data injection with minimal necessary context
- Tools as data providers (agents request, orchestrator fulfills)

### Knowledge Integration
- RAG (Retrieval Augmented Generation) for n8n best practices
- Comprehensive knowledge base of n8n patterns and anti-patterns
- Live schema integration via `get_node_template` tool
- Context-aware prompt engineering

## V1 Scope Definition

### Core AI Engine Components
- **Graph Orchestration**: `GraphExecutor`, `GraphBuilder`
- **Foundational Services**: `LLMService`, `NodeSpecService`, Prompt Engineering Subsystem
- **Persistence Layer**: RxDB setup with core repositories
- **Agent Infrastructure**: `BaseAgent`, `agentRegistry`

### V1 Specialized AI Agents (Complete List)
1. `N8nIntentRouterAgent`
2. `N8nRequirementsGatheringAgent`
3. `N8nArchitectAgent`
4. `N8nNodeSelectorAgent`
5. `N8nNodeComposerAgent`
6. `N8nWorkflowDocumenterAgent`
7. `N8nConnectionComposerAgent`
8. `N8nBestPracticeValidatorAgent`
9. `N8nOptimizerAgent`
10. `N8nExplainerAgent`
11. `N8nReflectionAgent`
12. `N8nSummarizerAgent`

### Core Graph Definitions (V1)
1. **CreateNewWorkflow**: Complete workflow generation from user requirements
2. **UpdateExistingWorkflow**: Modify existing workflows with change management
3. **AnalyzeAndOptimizeWorkflow**: Performance and best practice analysis
4. **ExplainWorkflowOrNode**: Educational and documentation support
5. **GeneralChatOrKnowledgeLookup**: Conversational AI support

### Technical Foundation
- **Multi-platform TypeScript library** (browser + Node.js)
- **Vercel AI SDK v5 alpha** for LLM integration
- **RxDB** for cross-platform persistence
- **Zod** for schema validation and type safety
- **Custom graph orchestration** (no external agent frameworks)

## Success Criteria

### Functional Requirements
- All V1 graph definitions working end-to-end
- Complete agent ecosystem functional with proper error handling
- Robust prompt engineering subsystem with context management
- Multi-platform persistence layer with data consistency
- Comprehensive test coverage (>80%)

### Quality Standards
- n8n specialist-level understanding and output quality
- Proper handling of n8n execution nuances (items, expressions, loops, merge nodes)
- Best practice validation and optimization suggestions
- Clear, maintainable, and well-documented codebase
- Scalable architecture supporting future enhancements

### User Experience Goals
- Natural language workflow creation and modification
- Intelligent clarification and requirement gathering
- Educational explanations and best practice guidance
- Seamless integration with existing n8n workflows
- Professional-grade output quality and reliability

## Current State Analysis

The project is in active development with:
- Basic engine infrastructure partially implemented
- Core type definitions and interfaces established
- Initial service architecture designed
- Agent framework foundation in place
- Memory bank structure (V4.0 "The Rosetta Stone") established

## Immediate Priorities

1. Complete architectural alignment with V3.3.1 structure
2. Implement missing core components in dependency order
3. Establish comprehensive testing framework
4. Validate end-to-end graph execution
5. Integrate knowledge base and RAG system

## Future Roadmap (Post-V1)

- Browser extension integration with full UI/UX
- Advanced reflection and self-improvement capabilities
- Extended agent ecosystem for specialized use cases
- Performance optimization and caching strategies
- Enterprise features and deployment options