# Project Overview: n8n Agentic AI "Brain" Library (V1.0)

## Mission

To create a highly intelligent, multi-platform TypeScript library serving as the "AI Brain" for an n8n assistant. This brain will be capable of understanding user requests in natural language, designing complex n8n workflows, generating their precise JSON representations, validating workflows against best practices, and assisting with documentation, explanation, and optimization of n8n automations.

## Vision

To empower n8n users, from beginners to advanced, by providing expert-level AI assistance that significantly accelerates workflow development, lowers the barrier to entry for complex features, improves workflow quality, and offers a transparent and extensible AI foundation for n8n automation. The long-term vision is to integrate this library into a self-contained browser extension with a rich UI/UX.

## High-Level Purpose

Develop a robust, graph-based AI orchestration engine in pure TypeScript, creating a multi-platform library (browser + Node.js compatible) that serves as the "brain" containing all AI/LLM/Agent functionalities. This system will use Vercel AI SDK Core (v5 alpha) for LLM integration and implement custom graph-based orchestration without external agent frameworks.