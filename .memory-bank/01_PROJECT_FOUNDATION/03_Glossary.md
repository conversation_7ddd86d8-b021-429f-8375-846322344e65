# Glossary: n8n AI Assistant Project

## Core System Concepts

### AI and Agent Terms

**Agent**: A specialized AI component with a focused responsibility (e.g., <PERSON>deCom<PERSON><PERSON>, Architect). Each agent has specific input/output contracts and uses LLMs to perform its designated task.

**Agent Registry**: A centralized mapping system that associates agent slugs (string identifiers) with their corresponding TypeScript class constructors and metadata.

**Agentic System**: An AI architecture where multiple specialized agents collaborate to accomplish complex tasks, each with distinct capabilities and responsibilities.

**Graph-Based Orchestration**: A system where workflows are defined as directed graphs with nodes (actions) and edges (flow control), enabling complex multi-step AI processes.

**GraphBuilder**: A fluent API for constructing graph definitions using TypeScript. Provides methods like `addAgentNode()`, `addToolNode()`, and `onSuccess()` for building workflows.

**GraphExecutor**: The runtime engine that interprets and executes graph definitions, managing state, data flow, and control flow between nodes.

**GraphDefinition**: A compiled data structure representing a complete workflow graph, including nodes, edges, and execution metadata.

**LLM Task Category**: Classification system for different types of AI tasks (e.g., CORE_ORCHESTRATION_AND_PLANNING, N8N_CODE_GENERATION_COMPOSITION) used for optimal model selection.

### n8n-Specific Terms

**Item-Based Processing**: n8n's fundamental execution model where data flows through workflows as discrete "items" (JSON objects), with most nodes processing each item individually.

**Node**: A single functional unit in an n8n workflow that performs a specific action (e.g., HTTP Request, Code, IF). Each node has inputs, outputs, and configuration parameters.

**Node Schema**: The JSON schema definition that describes a node's structure, parameters, and validation rules. Retrieved via `get_node_template` tool.

**Node Composer**: Specialized agent responsible for generating precise n8n node JSON configurations based on requirements and live schemas.

**Sticky Note**: Documentation nodes in n8n workflows that provide explanations and context for groups of functional nodes.

**Workflow JSON**: The complete JSON representation of an n8n workflow, including all nodes, connections, and metadata.

**Expression**: n8n's templating system for dynamic data access using syntax like `{{ $json.fieldName }}` or `{{ $('Node Name').item.json.data }}`.

**Merge Node**: n8n node that combines data from multiple branches. Critical consideration: in 'Wait' mode, if one branch produces no items, the merge may halt the workflow.

### Architecture and Design Patterns

**Decoupled Architecture**: Design principle where components have minimal dependencies and communicate through well-defined interfaces, enabling independent development and testing.

**Service-Oriented Architecture (SOA)**: System design where functionality is organized into discrete services (LLMService, NodeSpecService, etc.) that can be independently deployed and maintained.

**Dependency Injection**: Pattern where components receive their dependencies from external sources rather than creating them internally, improving testability and flexibility.

**Repository Pattern**: Data access pattern that encapsulates database operations behind a consistent interface, used with RxDB for persistence.

**RAG (Retrieval Augmented Generation)**: AI pattern that enhances LLM responses by retrieving relevant information from knowledge bases before generation.

### Technical Infrastructure

**RxDB**: Cross-platform reactive database used for persistence, supporting both browser and Node.js environments with real-time synchronization.

**Vercel AI SDK**: Library for integrating with various LLM providers, providing unified interfaces for text generation, streaming, and structured output.

**Zod**: TypeScript-first schema validation library used for runtime type checking and data validation throughout the system.

**Multi-Platform Library**: Software designed to work consistently across different environments (browser, Node.js) without modification.

**HIL (Human-in-the-Loop)**: System capability to pause execution and request user input when clarification or decisions are needed.

## Workflow and Process Terms

### Graph Execution

**Graph Context**: Runtime state object containing execution metadata, node outputs, loop counters, and service references during graph execution.

**Node Output Version**: Versioned storage of node execution results, including output data, status, timestamps, and error information.

**Processing Queue**: Internal queue in GraphExecutor that manages the order of node execution based on graph flow and dependencies.

**Edge Traversal**: The process of determining which nodes to execute next based on current node results and graph edge definitions.

**Loop Counter**: Mechanism for tracking iteration counts in graph loops to prevent infinite execution and enforce limits.

### Data Flow

**Input Resolution**: Process of resolving node inputs from various sources (previous outputs, constants, initial inputs) before execution.

**Fallback Source**: Alternative input source used when primary input resolution fails, enabling robust data flow handling.

**Path Traversal**: Accessing nested properties in node outputs using dot notation or JSONPath-like syntax.

**Data Transformation**: Converting data between different formats or structures, often using dedicated transformation nodes.

### Error Handling

**Default Error Handler**: Graph-level fallback mechanism for handling node failures when no explicit error path is defined.

**Error Propagation**: System for passing error information through the graph execution flow to appropriate handling mechanisms.

**Graceful Degradation**: System behavior that maintains partial functionality when components fail or encounter errors.

## Development and Quality Terms

### Code Organization

**Component Template**: Standardized documentation format for describing system components, including purpose, dependencies, and interfaces.

**ADR (Architecture Decision Record)**: Formal documentation of architectural decisions, including context, options considered, and rationale.

**Memory Bank**: Structured knowledge repository containing all project decisions, patterns, and documentation for AI-to-AI collaboration.

**Implementation Plan**: Detailed, step-by-step guide for implementing specific components, designed for zero-context developers.

### Testing and Validation

**Unit Testing**: Testing individual components in isolation with mocked dependencies to verify specific functionality.

**Integration Testing**: Testing component interactions and data flow between multiple system parts.

**End-to-End Testing**: Complete workflow testing from user input to final output, validating entire system behavior.

**Schema Validation**: Runtime verification that data structures conform to their defined Zod schemas.

### Quality Assurance

**Best Practice Validation**: Automated checking of workflows against established n8n patterns and recommendations.

**Code Quality Standards**: Established guidelines for TypeScript code including naming conventions, structure, and documentation.

**Cross-Platform Compatibility**: Ensuring consistent behavior across different runtime environments (browser, Node.js).

## Business and User Terms

### User Categories

**n8n Beginner**: User new to automation with basic technical skills, requiring guided learning and best practice enforcement.

**Intermediate User**: User comfortable with basic workflows who wants to tackle complex scenarios and learn advanced patterns.

**n8n Expert**: Experienced user creating complex enterprise workflows, focused on efficiency and consistency.

**Business User**: Non-technical user who understands business processes but needs assistance translating requirements to technical implementation.

**Integration Specialist**: Technical user focused on connecting systems and APIs, requiring specialized authentication and error handling support.

### Value Propositions

**Democratized Automation**: Making advanced automation capabilities accessible to users regardless of technical expertise level.

**Educational First**: Design principle prioritizing user learning and understanding over pure task completion.

**Contextually Intelligent**: System behavior that adapts to user expertise level and current workflow context.

**Transparent and Explainable**: AI behavior that provides clear reasoning for decisions and suggestions.

## Acronyms and Abbreviations

**AI**: Artificial Intelligence
**API**: Application Programming Interface
**ADR**: Architecture Decision Record
**CLI**: Command Line Interface
**DI**: Dependency Injection
**HIL**: Human-in-the-Loop
**JSON**: JavaScript Object Notation
**LLM**: Large Language Model
**MVP**: Minimum Viable Product
**RAG**: Retrieval Augmented Generation
**SDK**: Software Development Kit
**SOA**: Service-Oriented Architecture
**TS**: TypeScript
**UI**: User Interface
**UX**: User Experience
**V1**: Version 1 (initial release scope)
**V2**: Version 2 (future enhancements)