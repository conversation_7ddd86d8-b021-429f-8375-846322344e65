# System Patterns & Architecture

## Core Architectural Patterns

### 1. Graph-Based Orchestration
- **Pattern**: Custom GraphDefinition objects executed by generic GraphExecutor
- **Benefits**: Maximum flexibility, declarative definitions, clear control flow
- **Implementation**: GraphBuilder fluent API for creation, GraphExecutor for runtime

### 2. Agent-Centric Design
- **Pattern**: Specialized AI agents with single responsibilities
- **Benefits**: Focused prompting, independent development, clear separation
- **Implementation**: BaseAgent class, agentRegistry, standardized interfaces

### 3. Maximum Reasonable Decoupling
- **Pattern**: Small, focused modules with clear interfaces
- **Benefits**: Testability, maintainability, independent evolution
- **Implementation**: Micro-utilities in prompt engineering, service-oriented architecture

### 4. Prompt Engineering Subsystem
- **Pattern**: Formatters → Normalizers → Mergers → Assembled Prompt
- **Benefits**: Reusable components, consistent formatting, testable pieces
- **Implementation**: 
  - Formatters: Convert PromptComponent → { role, contentString }
  - Normalizers: Ensure SDK compatibility
  - Mergers: Combine compatible messages
  - Orchestrators: GenericPromptBuilder, AgentPromptBuilder

### 5. Multi-Platform Strategy
- **Pattern**: Environment detection with conditional implementations
- **Benefits**: Single codebase for Node.js and browser
- **Implementation**: RxDB storage adapters, environment-specific utilities

## Key Design Decisions

### Service Layer Organization
```
src/app/ → Application orchestration (OrchestratorService)
src/services/ → High-level domain services (UserSettings, TaskSession, NodeSpec)
src/engine/ → Core infrastructure (graph, llm, prompts, agents)
src/persistence/ → Data layer (RxDB, repositories)
src/agents/ → Concrete agent implementations
```

### Dependency Injection Pattern
- Services receive dependencies via constructor
- Agents receive AgentDependencies bundle
- GraphExecutor injects services into execution context
- Clear mocking boundaries for testing

### State Management Strategy
- **TaskSession**: Central operational state
- **GraphExecutionContext**: In-flight execution state
- **UserSettings**: User preferences and configuration
- **RxDB Collections**: Persistent storage with versioning

### Error Handling Approach
- Agents return structured results (success/failure/clarification/tools)
- GraphExecutor handles agent errors with explicit onFailure edges
- Default error handlers for unhandled failures
- Graceful degradation where possible

## Critical Integration Points

### LLM Integration
- Centralized through LLMService
- Model selection by task category
- Token counting and cost tracking
- Streaming support with custom UI parts

### Human-in-the-Loop (HIL)
- Agents signal clarification needs
- GraphExecutor pauses execution
- OrchestratorService manages user interaction
- Resume with user input

### Knowledge Integration (RAG)
- EmbeddingService for vector generation
- KnowledgeRepository for storage and search
- Context integration via prompt builders
- MCP server integration for external sources

## Component Relationships

### Prompt Flow
```
Agent → BaseAgent.prepareLlmMessages() 
      → AgentPromptBuilder.prepareAgentLlmMessages()
      → ConversationHistoryService (history + summarization)
      → RuleSelectorService (rules selection)
      → GenericPromptBuilder (assembly)
      → Formatters/Normalizers/Mergers
      → Final ModelMessage[]
```

### Graph Execution Flow
```
OrchestratorService → GraphExecutor.run()
                   → Node Resolution
                   → Agent/Tool Execution
                   → Output Storage
                   → Next Node Determination
                   → Loop until completion
```

### Data Flow
```
User Input → TaskSession Creation
          → Graph Initialization
          → Agent Executions (with LLM calls)
          → Output Accumulation
          → Final Result
          → TaskSession Update
```

## Extension Points

### Adding New Agents
1. Extend BaseAgent
2. Define static properties (slug, instruction, category)
3. Implement execute() and getFinalOutputSchema()
4. Register in agentRegistry
5. Create prompt files

### Adding New Tools
1. Define ToolDefinition
2. Implement execute method
3. Register in toolRegistry
4. Create tests

### Adding New Graphs
1. Use GraphBuilder fluent API
2. Define nodes and edges
3. Set up inputs and outputs
4. Test with compile()
5. Register in graphs module

## Quality Patterns

### Testing Strategy
- Unit tests for individual components
- Integration tests for service interactions
- Graph execution tests with mocked LLM
- Property-based testing where applicable

### Code Organization
- Single responsibility per file
- Clear naming conventions
- Comprehensive TypeScript types
- JSDoc documentation
- Barrel exports (index.ts files)

### Error Prevention
- Zod schema validation at boundaries
- TypeScript strict mode
- Runtime type checking
- Graceful error handling
- Comprehensive logging