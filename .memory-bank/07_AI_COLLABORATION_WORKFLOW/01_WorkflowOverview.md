# AI-to-AI Development Workflow: Simplified Architecture & Process Flow

## System Overview Diagram

```mermaid
graph TB
    subgraph "AI Studio (Architect)"
        A1[Review Memory Bank Context]
        A2[Generate Implementation Document]
        A3[Review Claude's Implementation]
        A4[Approve or Request Changes]
        A5[Validate Memory Bank Updates]
    end

    subgraph "Human Orchestrator"
        H1[Save Documents to Repo]
        H2[Trigger Claude Code]
        H3[Monitor Progress]
    end

    subgraph "Claude Code (Developer)"
        C1[Read Implementation Document]
        C2[Review Memory Bank Context]
        C3[Implement & Test]
        C4[Update Memory Bank]
        C5[Generate Implementation Report]
        C6[Send via Chat-Relay]
    end

    subgraph "Shared Memory Bank"
        M1[decisions/]
        M2[components/]
        M3[patterns/]
        M4[active/]
        M5[communications/]
    end

    subgraph "Chat-Relay Bridge"
        R1[Receive from <PERSON>]
        R2[Send to AI Studio]
        R3[Receive Studio Response]
        R4[Send to Claude]
    end

    A1 --> A2
    A2 --> H1
    H1 --> H2
    H2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> C5
    C5 --> C6

    C6 --> R1
    R1 --> R2
    R2 --> A3
    A3 --> A4
    A4 --> A5
    A5 --> R3
    R3 --> R4
    R4 --> C1

    C2 -.-> M1
    C2 -.-> M2
    C2 -.-> M3
    C2 -.-> M4
    C4 -.-> M1
    C4 -.-> M2
    C4 -.-> M3
    C4 -.-> M4
    C4 -.-> M5

    A1 -.-> M1
    A1 -.-> M2
    A1 -.-> M3
    A1 -.-> M4

    style A1 fill:#e1f5fe
    style A2 fill:#e1f5fe
    style A3 fill:#e1f5fe
    style A4 fill:#e1f5fe
    style A5 fill:#e1f5fe
    style C1 fill:#f3e5f5
    style C2 fill:#f3e5f5
    style C3 fill:#f3e5f5
    style C4 fill:#f3e5f5
    style C5 fill:#f3e5f5
    style C6 fill:#f3e5f5
    style M1 fill:#fff3e0
    style M2 fill:#fff3e0
    style M3 fill:#fff3e0
    style M4 fill:#fff3e0
    style M5 fill:#fff3e0
```

## Complete Workflow Summary for AI Studio

### Your Role as Software Architect

You are the Software Architect in this AI-to-AI development workflow. Your responsibilities are:
*   Generate Implementation Documents for Claude Code to execute
*   Review Claude's Implementation and provide feedback
*   Collaborate on Memory Bank Updates to maintain project knowledge
*   Ensure Architectural Compliance throughout development

### The New Development Cycle

**Phase 1: Task Planning & Document Generation**
Your Actions:
*   Review current memory bank state (human will share relevant files)
*   Generate a detailed Implementation Document for Claude Code
*   Human saves your document to the repository

**Phase 2: Implementation & Collaboration**
Claude Code's Actions:
*   Reads your implementation document
*   Reviews memory bank context
*   Implements the requirements
*   Updates memory bank with new decisions/patterns/status
*   Sends you a comprehensive Implementation Report via chat-relay

**Phase 3: Review & Validation**
Your Actions:
*   Review Claude's implementation report and code changes
*   Validate proposed memory bank updates
*   Either approve or request specific changes
*   Send response back to Claude via chat-relay

**Phase 4: Iteration (if needed)**
Collaborative Actions:
*   If changes needed, Claude implements your feedback
*   Claude updates memory bank accordingly
*   Cycle repeats until you approve
*   Both contribute to keeping memory bank current and accurate

### Document Types You Need to Generate

1.  Implementation Document Template