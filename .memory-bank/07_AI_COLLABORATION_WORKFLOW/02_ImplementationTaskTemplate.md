# Implementation Task: [ComponentName]

**Priority**: High/Medium/Low
**Phase**: [Current project phase]
**Estimated Effort**: Small/Medium/Large

## Objective
[Clear statement of what needs to be built/changed]

## Context & Rationale
**Current State**: [Where we are now]
**Desired State**: [Where we want to be]
**Why This Matters**: [Business/technical justification]

## Functional Requirements

### Must Have (Critical)
- [ ] REQ-001: [Specific, testable requirement]
- [ ] REQ-002: [Specific, testable requirement]
- [ ] REQ-003: [Specific, testable requirement]

### Should Have (Important)
- [ ] REQ-004: [Nice-to-have with clear value]

### Could Have (Optional)
- [ ] REQ-005: [Future enhancement possibility]

## Technical Specifications

### Architecture Constraints
- **Design Patterns**: [Which patterns must be followed]
- **Existing Interfaces**: [Interfaces that must be preserved]
- **Performance Requirements**: [Specific metrics if applicable]
- **Testing Requirements**: [Coverage expectations]

### Implementation Guidance
- **Preferred Approach**: [Recommended implementation strategy]
- **Integration Points**: [How this connects to existing code]
- **Error Handling**: [Specific error handling expectations]
- **Configuration**: [Any configuration requirements]

### Definition of Done
- [ ] All must-have requirements implemented
- [ ] Test coverage >80% with passing tests
- [ ] Documentation updated
- [ ] Memory bank updated with decisions made
- [ ] No breaking changes (or properly documented)
- [ ] Integration tests passing

## Memory Bank Impact

### Expected Updates
- **Components**: [Which component docs need updating]
- **Patterns**: [New patterns that might emerge]
- **Decisions**: [Decisions that need ADR documentation]
- **Status**: [Project status changes]

### Collaboration Points
- **Decision Validation**: [Decisions you want to review]
- **Pattern Approval**: [Patterns that need architectural approval]
- **Documentation Quality**: [Sections that need your review]

## Review Expectations

### What I'll Validate
- [ ] Requirements correctly implemented
- [ ] Architecture patterns followed
- [ ] Code quality meets standards
- [ ] Memory bank updates are accurate
- [ ] Integration doesn't break existing functionality

### What I Need in Your Report
- [ ] Complete requirements compliance check
- [ ] All implementation decisions explained
- [ ] Memory bank updates with rationale
- [ ] Git diff of all changes
- [ ] Test results and coverage
- [ ] Any questions or concerns

## Related Context
- **Memory Bank Files**: [Specific files Claude should review]
- **Previous Decisions**: [Relevant ADRs to consider]
- **Dependencies**: [Other components this affects]