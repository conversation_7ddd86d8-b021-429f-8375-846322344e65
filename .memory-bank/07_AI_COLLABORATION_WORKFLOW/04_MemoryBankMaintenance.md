# Memory Bank Maintenance Protocols

## Role of the Memory Bank

The Memory Bank serves as the single source of truth for the n8n AI Assistant project. It contains critical information regarding architectural decisions, component designs, established patterns, current project state, and communication logs. Maintaining its accuracy and completeness is paramount for effective AI-to-AI collaboration and overall project success.

## Collaborative Update Process

Updates to the Memory Bank are a collaborative effort primarily between Claude Code (Developer) and AI Studio (Architect).

1.  **Claude Code Proposes Updates:** During the implementation phase of a task, <PERSON> is responsible for identifying and proposing necessary updates to the Memory Bank. This includes documenting new decisions made, changes to component designs, emerging patterns, and updating the project status. These proposed updates are typically included in the Implementation Report sent to AI Studio.
2.  **AI Studio (Architect) Reviews and Validates:** AI Studio reviews Claude Code's Implementation Report, which includes the proposed Memory Bank updates. The Architect validates that the proposed changes accurately reflect the implementation, align with the overall architecture, and maintain the integrity of the Memory Bank as the single source of truth.
3.  **Iteration and Approval:** If the Architect identifies discrepancies, inaccuracies, or architectural concerns in the proposed updates, they will request specific changes via the Review Response. Claude Code will then revise the updates and resubmit for review. This cycle continues until the Architect approves the Memory Bank updates.
4.  **Finalization:** Upon approval, the validated updates are considered official and integrated into the Memory Bank.

## Importance of Keeping Sections Current

It is crucial that all sections of the Memory Bank remain current and accurate. This includes, but is not limited to:

*   **Architectural Decision Records (ADRs):** Documenting the rationale and context for significant technical decisions.
*   **Component Documentation:** Reflecting the current design, implementation details, and interfaces of all project components.
*   **Pattern Documentation:** Detailing established design and coding patterns used within the project.
*   **Project State:** Keeping track of the current progress, completed tasks, and upcoming work.

Outdated or inaccurate information in any section can lead to misunderstandings, incorrect implementations, and architectural drift.

## Handling Conflicting Information

In a collaborative environment, conflicting information may occasionally arise. The protocol for handling such conflicts is as follows:

1.  **Prioritize Latest Validated Information:** Information that has been most recently reviewed and validated by the Architect takes precedence.
2.  **Document Evolution:** Significant changes or reversals of previous decisions should be documented in a dedicated section, such as `10_ARCHIVE_AND_HISTORY/DecisionEvolution.md`, to provide historical context and explain the rationale for the change.
3.  **Seek Clarification:** If conflicting information cannot be resolved based on the above, the Architect should seek clarification, potentially involving the Human Orchestrator if necessary.

## Frequency of Updates

Memory Bank updates should occur frequently to ensure currency. A general guideline is to propose and validate updates:

*   After the completion of each significant task or feature implementation.
*   Whenever a new architectural decision is made or an existing one is modified.
*   When a new design or coding pattern is introduced or significantly altered.
*   Upon any change that affects the overall project state or direction.

## Responsibilities

*   **Claude Code:** Responsible for proposing initial Memory Bank updates based on implementation work.
*   **AI Studio (Architect):** Responsible for reviewing, validating, and approving all Memory Bank updates, ensuring accuracy, completeness, and architectural compliance.
*   **Human Orchestrator:** Responsible for facilitating the process, ensuring documents are saved to the repository, and triggering the AI agents as needed.

By adhering to these protocols, the team can ensure the Memory Bank remains a reliable and valuable resource throughout the project lifecycle.