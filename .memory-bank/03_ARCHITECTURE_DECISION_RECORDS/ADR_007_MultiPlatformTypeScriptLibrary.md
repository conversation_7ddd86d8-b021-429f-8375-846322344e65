# Architecture Decision Record: Multi-Platform TypeScript Library Architecture

## Status

Accepted

## Context

The n8n AI Assistant needs to operate across multiple environments to support different use cases and deployment scenarios:

- **Browser Environment**: For future Chrome extension integration and web-based usage
- **Node.js Environment**: For CLI tools, testing, development, and server-side operations
- **Consistent API**: Same functionality and behavior across all platforms
- **Performance**: Optimal performance in each environment
- **Bundle Size**: Minimal bundle size for browser deployment
- **Development Experience**: Single codebase with platform-specific optimizations

The challenge was creating a library that works identically across platforms while leveraging platform-specific capabilities and maintaining optimal performance.

## Decision

We will implement a **multi-platform TypeScript library** with the following approach:

1. **Pure TypeScript Core**: Platform-agnostic core logic in TypeScript
2. **Environment Detection**: Runtime detection of execution environment
3. **Adaptive Storage**: Platform-specific storage adapters (IndexedDB vs SQLite)
4. **Conditional Imports**: Platform-specific modules loaded conditionally
5. **Unified API**: Single API surface that works across all platforms
6. **Build Targets**: Multiple build outputs for different environments

### Architecture Approach:

- **Core Library**: Platform-agnostic TypeScript code
- **Environment Adapters**: Platform-specific implementations
- **Runtime Detection**: Automatic environment detection and adaptation
- **Build System**: Multiple build targets for different platforms

## Rationale

### Why Multi-Platform Library

**Unified Development Experience**:
- Single codebase for all platforms reduces maintenance overhead
- Consistent API and behavior across environments
- Shared business logic and AI orchestration
- Common testing strategies and quality assurance

**Future-Proof Architecture**:
- Ready for Chrome extension integration without major refactoring
- Supports CLI tools and server-side usage
- Enables testing and development in Node.js environment
- Flexible deployment options for different use cases

**Performance Optimization**:
- Platform-specific optimizations where beneficial
- Optimal storage solutions for each environment
- Efficient resource utilization per platform
- Minimal bundle size for browser deployment

**Developer Experience**:
- Single API to learn and maintain
- Consistent debugging and development tools
- Shared documentation and examples
- Unified testing and quality assurance

### Why Not Alternatives

**Separate Platform-Specific Libraries**:
- Significant code duplication and maintenance overhead
- Inconsistent behavior across platforms
- Multiple APIs to maintain and document
- Difficult to ensure feature parity

**Browser-Only Library**:
- No support for CLI tools and server-side usage
- Difficult testing and development workflow
- Limited deployment options
- Poor developer experience for backend integration

**Node.js-Only Library**:
- Cannot be used in browser environments
- No support for Chrome extension integration
- Limited to server-side use cases
- Misses key deployment scenarios

**Electron-Based Approach**:
- Heavy runtime overhead
- Complex deployment and distribution
- Not suitable for web-based integration
- Poor performance compared to native solutions

## Consequences

### Positive

**Unified Codebase**: Single codebase reduces maintenance and ensures consistency
**Platform Flexibility**: Supports multiple deployment scenarios and use cases
**Future-Ready**: Ready for Chrome extension and web-based integration
**Optimal Performance**: Platform-specific optimizations where beneficial
**Developer Experience**: Consistent API and development patterns
**Testing Strategy**: Comprehensive testing across all target platforms
**Deployment Options**: Flexible deployment for different environments

### Negative

**Complexity**: More complex than single-platform solutions
**Build Process**: Multiple build targets and configurations
**Testing Overhead**: Need to test across multiple environments
**Bundle Management**: Need to manage different bundle sizes and optimizations
**Platform Differences**: Need to handle platform-specific quirks and limitations

### Mitigation Strategies

**Environment Abstraction**: Clear abstraction layers for platform differences
**Automated Testing**: Comprehensive testing across all target platforms
**Build Automation**: Automated build process for all targets
**Documentation**: Clear documentation of platform-specific considerations
**Performance Monitoring**: Monitor performance across all platforms

## Implementation Details

### Environment Detection

```typescript
// src/utils/environment.ts
export function isNodeEnvironment(): boolean {
  return typeof process !== 'undefined' && 
         process.versions != null && 
         process.versions.node != null;
}

export function isBrowserEnvironment(): boolean {
  return typeof window !== 'undefined' && 
         typeof window.document !== 'undefined';
}

export function isWebWorkerEnvironment(): boolean {
  return typeof importScripts === 'function' && 
         typeof WorkerGlobalScope !== 'undefined';
}

export function getEnvironmentInfo(): EnvironmentInfo {
  return {
    platform: isNodeEnvironment() ? 'node' : 'browser',
    version: isNodeEnvironment() ? process.version : navigator.userAgent,
    capabilities: {
      localStorage: typeof localStorage !== 'undefined',
      indexedDB: typeof indexedDB !== 'undefined',
      fileSystem: isNodeEnvironment(),
      webWorkers: typeof Worker !== 'undefined'
    }
  };
}
```

### Platform-Specific Storage Adapters

```typescript
// src/persistence/adapters/storageAdapter.ts
export interface StorageAdapter {
  initialize(): Promise<void>;
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Browser implementation
export class IndexedDBStorageAdapter implements StorageAdapter {
  private db: IDBDatabase;
  
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('n8n-ai-assistant', 1);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      request.onerror = () => reject(request.error);
    });
  }
  
  // ... implementation
}

// Node.js implementation
export class SQLiteStorageAdapter implements StorageAdapter {
  private db: Database;
  
  async initialize(): Promise<void> {
    const sqlite3 = await import('sqlite3');
    this.db = new sqlite3.Database(':memory:');
    // ... setup
  }
  
  // ... implementation
}

// Factory function
export function createStorageAdapter(): StorageAdapter {
  if (isNodeEnvironment()) {
    return new SQLiteStorageAdapter();
  } else {
    return new IndexedDBStorageAdapter();
  }
}
```

### RxDB Platform Configuration

```typescript
// src/persistence/rxdbSetup.ts
import { createRxDatabase, addRxPlugin } from 'rxdb';
import { isNodeEnvironment } from '../utils/environment';

async function getStorageAdapter() {
  if (isNodeEnvironment()) {
    // Node.js storage
    const { getRxStorageSQLite } = await import('rxdb/plugins/storage-sqlite');
    return getRxStorageSQLite();
  } else {
    // Browser storage
    const { getRxStorageIndexedDB } = await import('rxdb/plugins/storage-indexeddb');
    return getRxStorageIndexedDB();
  }
}

export async function initializeDatabase(): Promise<RxDatabase> {
  const storage = await getStorageAdapter();
  
  return await createRxDatabase({
    name: 'n8n-ai-assistant',
    storage,
    multiInstance: !isNodeEnvironment(), // Enable for browser, disable for Node.js
    eventReduce: true,
    cleanupPolicy: {
      minimumDeletedTime: 1000 * 60 * 60 * 24 * 7, // 7 days
      runEach: 1000 * 60 * 60 * 24, // Daily cleanup
    }
  });
}
```

### Conditional Module Loading

```typescript
// src/services/NodeSpecService.ts
export class NodeSpecService {
  private nodeSpecs: Map<string, NodeTemplate> = new Map();
  
  async initialize(): Promise<void> {
    if (isNodeEnvironment()) {
      // Load from file system in Node.js
      await this.loadFromFileSystem();
    } else {
      // Load from bundled data in browser
      await this.loadFromBundledData();
    }
  }
  
  private async loadFromFileSystem(): Promise<void> {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const configPath = path.join(__dirname, '../config/nodes.snapshot.json');
    const data = await fs.readFile(configPath, 'utf-8');
    const nodeSpecs = JSON.parse(data);
    
    this.processNodeSpecs(nodeSpecs);
  }
  
  private async loadFromBundledData(): Promise<void> {
    // Import bundled data (will be included in browser bundle)
    const { nodeSpecs } = await import('../config/nodes.snapshot.json');
    this.processNodeSpecs(nodeSpecs);
  }
}
```

### Build Configuration

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: {
        // Main entry points
        index: resolve(__dirname, 'src/index.ts'),
        node: resolve(__dirname, 'src/node.ts'),
        browser: resolve(__dirname, 'src/browser.ts')
      },
      formats: ['es', 'cjs', 'umd']
    },
    rollupOptions: {
      external: [
        // Node.js specific modules (excluded from browser bundle)
        'fs',
        'path',
        'sqlite3',
        'crypto'
      ],
      output: {
        globals: {
          // Global variables for UMD build
          'rxdb': 'RxDB'
        }
      }
    },
    target: ['es2020', 'node16']
  },
  define: {
    // Environment-specific constants
    __PLATFORM__: JSON.stringify(process.env.PLATFORM || 'universal')
  }
});
```

### Package.json Configuration

```json
{
  "name": "n8n-ai-assistant",
  "version": "1.0.0",
  "type": "module",
  "main": "./dist/node/index.cjs",
  "module": "./dist/browser/index.js",
  "browser": "./dist/browser/index.js",
  "types": "./dist/types/index.d.ts",
  "exports": {
    ".": {
      "node": {
        "import": "./dist/node/index.js",
        "require": "./dist/node/index.cjs"
      },
      "browser": {
        "import": "./dist/browser/index.js"
      },
      "types": "./dist/types/index.d.ts"
    }
  },
  "files": [
    "dist/"
  ],
  "scripts": {
    "build": "npm run build:node && npm run build:browser",
    "build:node": "vite build --mode node",
    "build:browser": "vite build --mode browser",
    "test:node": "vitest run --environment node",
    "test:browser": "vitest run --environment jsdom"
  }
}
```

### Platform-Specific Entry Points

```typescript
// src/node.ts - Node.js specific entry point
export * from './index';
export { NodeSpecService } from './services/NodeSpecService.node';
export { FileSystemLogger } from './utils/logger.node';

// src/browser.ts - Browser specific entry point
export * from './index';
export { BrowserLogger } from './utils/logger.browser';
export { IndexedDBCache } from './utils/cache.browser';

// src/index.ts - Universal entry point
export { OrchestratorService } from './app/OrchestratorService';
export { GraphBuilder, GraphExecutor } from './engine/graph';
export { LLMService } from './engine/llm';
export * from './types';
```

### Testing Strategy

```typescript
// tests/platform.test.ts
import { describe, it, expect } from 'vitest';
import { getEnvironmentInfo, isNodeEnvironment } from '../src/utils/environment';

describe('Platform Detection', () => {
  it('should detect Node.js environment correctly', () => {
    if (typeof process !== 'undefined') {
      expect(isNodeEnvironment()).toBe(true);
    } else {
      expect(isNodeEnvironment()).toBe(false);
    }
  });
  
  it('should provide environment info', () => {
    const info = getEnvironmentInfo();
    expect(info).toHaveProperty('platform');
    expect(info).toHaveProperty('capabilities');
    expect(['node', 'browser']).toContain(info.platform);
  });
});

// Platform-specific test suites
describe.runIf(isNodeEnvironment())('Node.js specific tests', () => {
  // Node.js only tests
});

describe.runIf(!isNodeEnvironment())('Browser specific tests', () => {
  // Browser only tests
});
```

## Future Considerations

### Planned Enhancements

**WebAssembly Integration**: Use WebAssembly for performance-critical operations
**Service Workers**: Browser service worker support for offline functionality
**Electron Support**: Support for Electron-based desktop applications
**React Native**: Potential support for mobile applications
**Edge Runtime**: Support for edge computing environments

### Platform Expansion

**Deno Support**: Add support for Deno runtime
**Bun Support**: Add support for Bun runtime
**Web Workers**: Enhanced web worker support for background processing
**Progressive Web Apps**: PWA-specific optimizations and features

## Notes

This decision establishes the multi-platform approach as fundamental to the n8n AI Assistant architecture. The unified codebase with platform-specific optimizations provides the flexibility needed for diverse deployment scenarios while maintaining consistency and performance.

The environment detection and adapter patterns ensure that the library can leverage platform-specific capabilities while presenting a unified API to consumers. This approach supports both current requirements and future expansion into new platforms and use cases.

The build system and package configuration ensure optimal bundle sizes and performance for each target platform while maintaining a single development and maintenance workflow.
