# Architecture Decision Record: RxDB for Cross-Platform Persistence

## Status

Accepted

## Context

The n8n AI Assistant requires a robust persistence layer that works consistently across multiple environments:

- **Multi-Platform Support**: Must work identically in browser (IndexedDB) and Node.js (filesystem)
- **Real-Time Synchronization**: Support for real-time data updates and collaboration
- **Offline Capability**: Local-first architecture with offline functionality
- **Type Safety**: Strong TypeScript integration with runtime validation
- **Query Performance**: Efficient querying and indexing for complex data operations
- **Schema Evolution**: Support for schema versioning and data migration
- **Reactive Programming**: Observable data streams for reactive UI updates

The system needs to persist various types of data including workflows, conversations, execution history, and knowledge base content. The solution must be lightweight enough for browser environments while powerful enough for server-side operations.

## Decision

We will use **RxDB (Reactive Database)** as our primary persistence layer, configured with:

1. **Cross-Platform Storage Adapters**: IndexedDB for browser, SQLite for Node.js
2. **Zod Schema Integration**: Runtime type validation with automatic TypeScript inference
3. **Reactive Queries**: Observable data streams using RxJS
4. **Repository Pattern**: Abstracted data access layer for clean separation of concerns
5. **Schema Versioning**: Built-in migration support for schema evolution

## Rationale

### Why RxDB

**Cross-Platform Consistency**:
- Identical API and behavior across browser and Node.js environments
- Automatic storage adapter selection based on environment
- Consistent query syntax and performance characteristics
- Single codebase for all persistence operations

**Reactive Programming Model**:
- Built on RxJS observables for reactive data flow
- Automatic UI updates when data changes
- Real-time collaboration capabilities
- Event-driven architecture support

**TypeScript Integration**:
- Full TypeScript support with automatic type inference
- Runtime type validation through schema definitions
- Compile-time safety for database operations
- Excellent IDE support and autocomplete

**Performance and Scalability**:
- Efficient indexing and query optimization
- Support for complex queries with multiple conditions
- Lazy loading and pagination support
- Memory-efficient data handling

**Schema Management**:
- Built-in schema versioning and migration support
- Automatic validation of data against schemas
- Easy schema evolution without data loss
- Backup and restore capabilities

### Why Not Alternatives

**SQLite (Direct)**:
- Browser compatibility issues (requires WASM)
- Complex setup for cross-platform usage
- No built-in reactive capabilities
- Manual schema management and migrations

**MongoDB/Mongoose**:
- Requires external server infrastructure
- Not suitable for browser-based applications
- Complex setup and operational overhead
- No built-in cross-platform support

**LocalStorage/IndexedDB (Direct)**:
- Browser-only solution
- No structured querying capabilities
- Size limitations and performance issues
- No schema validation or type safety

**Dexie.js**:
- Browser-only, no Node.js support
- Limited reactive capabilities
- Manual TypeScript integration
- No built-in schema versioning

**PouchDB**:
- Document-oriented, not suitable for our relational needs
- Complex replication setup
- Limited query capabilities
- Inconsistent performance across platforms

## Consequences

### Positive

**Unified Development Experience**: Single API for all persistence operations across platforms
**Type Safety**: End-to-end type safety from schema definition to data access
**Real-Time Updates**: Automatic UI updates through reactive queries
**Offline Support**: Local-first architecture with sync capabilities
**Performance**: Efficient querying and indexing for complex operations
**Developer Experience**: Excellent TypeScript support and debugging capabilities
**Future-Proof**: Built-in migration support for schema evolution

### Negative

**Learning Curve**: Team needs to learn RxDB concepts and reactive patterns
**Bundle Size**: Additional JavaScript bundle size for browser applications
**Complexity**: More complex than simple storage solutions
**Debugging**: Reactive queries can be harder to debug than simple operations
**Documentation**: Less extensive documentation compared to mainstream databases

### Mitigation Strategies

**Training**: Comprehensive team training on RxDB and reactive patterns
**Documentation**: Create internal documentation and best practices
**Testing**: Extensive testing of all database operations and migrations
**Performance Monitoring**: Monitor bundle size and runtime performance
**Fallback Strategies**: Implement fallback storage for critical operations

## Alternatives

### 1. SQLite with WASM
**Pros**: SQL familiarity, excellent performance, mature ecosystem
**Cons**: Complex browser setup, large bundle size, no reactive capabilities
**Rejected**: Browser compatibility and complexity issues

### 2. Firebase/Firestore
**Pros**: Real-time sync, managed infrastructure, good documentation
**Cons**: Vendor lock-in, requires internet connectivity, cost implications
**Rejected**: Not suitable for offline-first architecture

### 3. IndexedDB with Dexie.js
**Pros**: Lightweight, good performance, familiar API
**Cons**: Browser-only, no Node.js support, limited reactive capabilities
**Rejected**: Doesn't meet cross-platform requirements

### 4. In-Memory with Periodic Sync
**Pros**: Simple implementation, fast access, full control
**Cons**: Data loss risk, complex sync logic, no persistence guarantees
**Rejected**: Too risky for production use

## Implementation Details

### Database Configuration

```typescript
// Environment-specific storage adapters
const getStorageAdapter = () => {
  if (isNodeEnvironment()) {
    return getRxStorageSQLite(); // SQLite for Node.js
  } else {
    return getRxStorageIndexedDB(); // IndexedDB for browser
  }
};

// Database initialization
const database = await createRxDatabase({
  name: 'n8n-ai-assistant',
  storage: getStorageAdapter(),
  multiInstance: true,
  eventReduce: true,
  cleanupPolicy: {
    minimumDeletedTime: 1000 * 60 * 60 * 24 * 7, // 7 days
    runEach: 1000 * 60 * 60 * 24, // Daily cleanup
  }
});
```

### Schema Definition with Zod

```typescript
// Zod schema for type safety
const WorkflowSchema = z.object({
  id: z.string().max(100),
  name: z.string().max(200),
  description: z.string().max(1000),
  workflowJson: z.object({}),
  tags: z.array(z.string()),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  status: z.enum(['draft', 'active', 'inactive', 'archived']),
  metadata: z.object({
    nodeCount: z.number(),
    complexity: z.enum(['simple', 'medium', 'complex']),
    lastValidated: z.string().datetime().optional(),
  })
});

// Convert to RxDB schema
const workflowRxSchema = zodToRxSchema(WorkflowSchema);
```

### Repository Pattern

```typescript
class WorkflowRepository extends BaseRepository<WorkflowDocument> {
  async findByTags(tags: string[]): Promise<WorkflowDocument[]> {
    return await this.collection.find({
      selector: { tags: { $in: tags } }
    }).exec();
  }
  
  // Reactive queries
  watchActiveWorkflows$(): Observable<WorkflowDocument[]> {
    return this.collection.find({
      selector: { status: 'active' }
    }).$;
  }
}
```

### Data Migration

```typescript
const migrationStrategies = {
  1: {
    workflows: (oldDoc: any) => ({
      ...oldDoc,
      metadata: {
        nodeCount: oldDoc.workflowJson?.nodes?.length || 0,
        complexity: 'medium',
        lastValidated: new Date().toISOString(),
      }
    })
  }
};
```

## Future Considerations

### Planned Enhancements

**Multi-Instance Sync**: Real-time synchronization between multiple application instances
**Cloud Backup**: Optional cloud backup and restore functionality
**Performance Optimization**: Query optimization and caching strategies
**Advanced Indexing**: Custom indexing strategies for complex queries
**Data Compression**: Compression for large workflow definitions

### Monitoring and Analytics

**Performance Metrics**: Query execution time and database operation metrics
**Storage Usage**: Monitor storage usage and cleanup effectiveness
**Error Tracking**: Comprehensive error tracking for database operations
**Migration Monitoring**: Track schema migration success and performance

## Notes

This decision establishes RxDB as the foundation for all data persistence in the n8n AI Assistant. The choice prioritizes cross-platform consistency and reactive programming patterns over traditional database approaches.

The decision was influenced by the requirement for identical behavior across browser and Node.js environments, which eliminated many traditional database solutions. RxDB's reactive nature aligns well with our agent-based architecture and real-time collaboration requirements.

Regular performance monitoring will be essential to ensure the database layer scales effectively as the application grows in complexity and data volume.
