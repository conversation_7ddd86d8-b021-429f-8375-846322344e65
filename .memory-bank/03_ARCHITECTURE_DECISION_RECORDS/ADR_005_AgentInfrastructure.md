# Architecture Decision Record: Agent Infrastructure and Registry Pattern

## Status

Accepted

## Context

The n8n AI Assistant requires a sophisticated agent system to coordinate 12+ specialized AI agents, each with distinct responsibilities and capabilities. The system needs:

- **Agent Specialization**: Each agent focuses on a specific aspect of n8n workflow management
- **Standardized Interface**: Consistent interface across all agents for orchestration
- **Dependency Injection**: Clean dependency management for services and resources
- **Registry Management**: Dynamic agent discovery and instantiation
- **Type Safety**: Strong TypeScript integration for agent contracts
- **Extensibility**: Easy addition of new agents without modifying core infrastructure
- **Error Handling**: Consistent error handling and reporting across agents
- **Context Management**: Proper context passing and state management

The challenge was designing an agent infrastructure that could support diverse agent types while maintaining consistency and enabling seamless orchestration through the graph execution engine.

## Decision

We will implement a **standardized agent infrastructure** with the following components:

1. **BaseAgent Abstract Class**: Foundation for all specialized agents
2. **AgentRegistry**: Centralized registry for agent discovery and instantiation
3. **AgentDependencies Interface**: Standardized dependency injection pattern
4. **Agent Execution Contracts**: Consistent input/output schemas using Zod
5. **Agent Metadata System**: Rich metadata for agent capabilities and requirements

### Core Architecture:

- **BaseAgent**: Abstract base class in `src/engine/agents/BaseAgent.ts`
- **AgentRegistry**: Registry service in `src/engine/agents/agentRegistry.ts`
- **Concrete Agents**: Specialized implementations in `src/agents/`
- **Type System**: Comprehensive types in `src/engine/agents/types/`

## Rationale

### Why Agent Infrastructure Pattern

**Standardization and Consistency**:
- All agents follow the same interface and execution pattern
- Consistent error handling and logging across all agents
- Standardized dependency injection and resource management
- Uniform integration with the graph execution engine

**Modularity and Extensibility**:
- New agents can be added without modifying existing infrastructure
- Clear separation between agent business logic and infrastructure concerns
- Easy to test agents in isolation with mocked dependencies
- Support for different agent types and execution patterns

**Type Safety and Contracts**:
- Strong TypeScript contracts for agent inputs and outputs
- Runtime validation using Zod schemas
- Compile-time checking of agent integration
- Clear documentation through type definitions

**Orchestration Integration**:
- Seamless integration with GraphExecutor through standardized interface
- Support for different execution modes (sync, async, streaming)
- Consistent handling of Human-in-the-Loop scenarios
- Proper error propagation and recovery mechanisms

### Why Not Alternatives

**Direct Function Calls**:
- No standardization across different agent types
- Difficult to manage dependencies and context
- Poor error handling and logging consistency
- Hard to extend and maintain

**External Agent Frameworks**:
- Additional dependencies and complexity
- May not integrate well with our graph orchestration
- Limited customization for n8n-specific needs
- Potential vendor lock-in and learning curve

**Microservices Architecture**:
- Too heavy for in-process coordination
- Network overhead and complexity
- Difficult to manage state and context
- Not suitable for browser-based execution

## Consequences

### Positive

**Consistent Agent Development**: Standardized patterns for all agent implementations
**Easy Extension**: Simple process for adding new specialized agents
**Type Safety**: Strong contracts and runtime validation for all agent interactions
**Testability**: Clear testing patterns with dependency injection
**Maintainability**: Centralized infrastructure reduces duplication
**Integration**: Seamless integration with graph orchestration engine
**Performance**: Efficient in-process execution with minimal overhead

### Negative

**Initial Complexity**: More complex than simple function-based approach
**Learning Curve**: Developers need to understand the agent infrastructure
**Abstraction Overhead**: Additional layers between business logic and execution
**Registry Management**: Need to maintain agent registry and metadata

### Mitigation Strategies

**Clear Documentation**: Comprehensive documentation of agent development patterns
**Code Templates**: Templates and examples for creating new agents
**Development Tools**: Tools for testing and debugging agent implementations
**Training**: Team training on agent architecture and best practices

## Implementation Details

### BaseAgent Abstract Class

```typescript
export abstract class BaseN8nAgent<TInputParams, TOutputPayload> {
  // Static metadata for registry
  static readonly agentSlug: string;
  static readonly agentMetadata: AgentMetadata;
  
  constructor(protected deps: AgentDependencies) {}
  
  // Main execution method - must be implemented by concrete agents
  abstract execute(
    inputs: TInputParams,
    context: AgentExecutionContext
  ): Promise<AgentExecutionResult<TOutputPayload>>;
  
  // Helper methods for common operations
  protected async prepareLlmMessages(
    promptComponents: PromptComponent[]
  ): Promise<ModelMessage[]> {
    return await this.deps.agentPromptBuilder.prepareAgentLlmMessages(
      this.constructor.agentSlug,
      promptComponents,
      this.currentContext
    );
  }
  
  protected async invokeLlm<T>(
    messages: ModelMessage[],
    outputSchema?: ZodSchema<T>,
    taskCategory?: LLMTaskCategory
  ): Promise<LLMInvocationResult<T>> {
    return await this.deps.llmService.generateStructuredObject(
      taskCategory || this.getDefaultTaskCategory(),
      messages,
      outputSchema
    );
  }
  
  // Abstract methods for agent-specific configuration
  protected abstract getDefaultTaskCategory(): LLMTaskCategory;
  protected abstract getInputSchema(): ZodSchema<TInputParams>;
  protected abstract getOutputSchema(): ZodSchema<TOutputPayload>;
}
```

### Agent Registry System

```typescript
export interface AgentRegistryEntry {
  agentSlug: string;
  agentClass: AgentConstructor;
  metadata: AgentMetadata;
}

export interface AgentMetadata {
  displayName: string;
  description: string;
  category: AgentCategory;
  capabilities: AgentCapability[];
  requiredServices: string[];
  defaultLLMTaskCategory: LLMTaskCategory;
  estimatedTokenUsage: {
    typical: number;
    maximum: number;
  };
}

export class AgentRegistry {
  private agents = new Map<string, AgentRegistryEntry>();
  
  register(entry: AgentRegistryEntry): void {
    if (this.agents.has(entry.agentSlug)) {
      throw new Error(`Agent ${entry.agentSlug} is already registered`);
    }
    
    this.validateAgentClass(entry.agentClass);
    this.agents.set(entry.agentSlug, entry);
  }
  
  createAgent<T extends BaseN8nAgent<any, any>>(
    agentSlug: string,
    dependencies: AgentDependencies
  ): T {
    const entry = this.agents.get(agentSlug);
    if (!entry) {
      throw new Error(`Agent ${agentSlug} not found in registry`);
    }
    
    return new entry.agentClass(dependencies) as T;
  }
  
  getAgentMetadata(agentSlug: string): AgentMetadata | undefined {
    return this.agents.get(agentSlug)?.metadata;
  }
  
  listAgents(): AgentRegistryEntry[] {
    return Array.from(this.agents.values());
  }
  
  private validateAgentClass(agentClass: AgentConstructor): void {
    if (!agentClass.agentSlug) {
      throw new Error('Agent class must define static agentSlug property');
    }
    
    if (!agentClass.agentMetadata) {
      throw new Error('Agent class must define static agentMetadata property');
    }
  }
}
```

### Dependency Injection Pattern

```typescript
export interface AgentDependencies {
  // Core services
  llmService: LLMService;
  agentPromptBuilder: AgentPromptBuilder;
  
  // Application services
  nodeSpecService: NodeSpecService;
  userSettingsService: UserSettingsService;
  
  // Repositories
  taskSessionRepository: TaskSessionRepository;
  conversationRepository: ConversationRepository;
  
  // Utilities
  logger: Logger;
  tokenCounter: TokenCounter;
}

export interface AgentExecutionContext {
  taskSessionId: string;
  userId: string;
  graphExecutionId: string;
  nodeId: string;
  tokenBudget: number;
  maxRetries: number;
  metadata: Record<string, any>;
}
```

### Concrete Agent Implementation Example

```typescript
export class N8nArchitectAgent extends BaseN8nAgent<
  ArchitectInputParams,
  ArchitectOutputPayload
> {
  static readonly agentSlug = 'n8n-architect';
  static readonly agentMetadata: AgentMetadata = {
    displayName: 'n8n Workflow Architect',
    description: 'Creates high-level workflow designs and strategies',
    category: 'CORE_WORKFLOW',
    capabilities: ['WORKFLOW_PLANNING', 'STRATEGY_DESIGN'],
    requiredServices: ['nodeSpecService', 'llmService'],
    defaultLLMTaskCategory: 'CORE_ORCHESTRATION_AND_PLANNING',
    estimatedTokenUsage: {
      typical: 2000,
      maximum: 5000
    }
  };
  
  async execute(
    inputs: ArchitectInputParams,
    context: AgentExecutionContext
  ): Promise<AgentExecutionResult<ArchitectOutputPayload>> {
    try {
      // 1. Prepare prompt components
      const promptComponents: PromptComponent[] = [
        {
          type: 'system_instruction',
          instruction: ARCHITECT_SYSTEM_PROMPT
        },
        {
          type: 'tagged_data',
          tag: 'User Requirements',
          data: inputs.requirements
        },
        {
          type: 'json_schema_instruction',
          zodSchema: this.getOutputSchema()
        }
      ];
      
      // 2. Prepare LLM messages
      const messages = await this.prepareLlmMessages(promptComponents);
      
      // 3. Invoke LLM
      const result = await this.invokeLlm(
        messages,
        this.getOutputSchema(),
        'CORE_ORCHESTRATION_AND_PLANNING'
      );
      
      if (result.success) {
        return {
          status: 'success',
          output: result.data,
          metadata: {
            tokenUsage: result.tokenUsage,
            cost: result.estimatedCost
          }
        };
      } else {
        return {
          status: 'failure',
          errorDetails: {
            type: 'LLM_INVOCATION_FAILED',
            message: result.error?.message || 'Unknown LLM error'
          }
        };
      }
    } catch (error) {
      return {
        status: 'failure',
        errorDetails: {
          type: 'AGENT_EXECUTION_ERROR',
          message: error.message
        }
      };
    }
  }
  
  protected getDefaultTaskCategory(): LLMTaskCategory {
    return 'CORE_ORCHESTRATION_AND_PLANNING';
  }
  
  protected getInputSchema(): ZodSchema<ArchitectInputParams> {
    return ArchitectInputParamsSchema;
  }
  
  protected getOutputSchema(): ZodSchema<ArchitectOutputPayload> {
    return ArchitectOutputPayloadSchema;
  }
}
```

### Agent Registration and Initialization

```typescript
// initializeAgents.ts
export function initializeAgentRegistry(): AgentRegistry {
  const registry = new AgentRegistry();
  
  // Register all V1 agents
  registry.register({
    agentSlug: N8nArchitectAgent.agentSlug,
    agentClass: N8nArchitectAgent,
    metadata: N8nArchitectAgent.agentMetadata
  });
  
  registry.register({
    agentSlug: N8nNodeComposerAgent.agentSlug,
    agentClass: N8nNodeComposerAgent,
    metadata: N8nNodeComposerAgent.agentMetadata
  });
  
  // ... register all other agents
  
  return registry;
}
```

### Integration with Graph Execution

```typescript
// In GraphExecutor
async executeAgentNode(
  nodeDefinition: AgentCallNodeDef,
  context: GraphExecutionContext
): Promise<NodeOutputVersion> {
  const agent = context.services.agentRegistry.createAgent(
    nodeDefinition.agentSlug,
    context.services.agentDependencies
  );
  
  const agentContext: AgentExecutionContext = {
    taskSessionId: context.taskSessionId,
    userId: context.userId,
    graphExecutionId: context.executionId,
    nodeId: nodeDefinition.id,
    tokenBudget: this.calculateTokenBudget(nodeDefinition),
    maxRetries: nodeDefinition.maxRetries || 3,
    metadata: nodeDefinition.metadata || {}
  };
  
  const result = await agent.execute(resolvedInputs, agentContext);
  
  return {
    nodeId: nodeDefinition.id,
    outputVersion: 'latest',
    status: result.status,
    output: result.output,
    error: result.errorDetails,
    metadata: result.metadata,
    timestamp: new Date().toISOString()
  };
}
```

## Future Considerations

### Planned Enhancements

**Agent Composition**: Support for composite agents that coordinate multiple sub-agents
**Dynamic Loading**: Runtime loading of agents from external modules
**Agent Versioning**: Version management for agent implementations
**Performance Monitoring**: Detailed performance analytics for agent execution
**Agent Marketplace**: Framework for third-party agent development and distribution

### Extension Points

**Custom Agent Types**: Framework for different agent execution patterns
**Agent Middleware**: Middleware system for cross-cutting concerns
**Agent Communication**: Direct agent-to-agent communication patterns
**Agent State Management**: Persistent state management for stateful agents

## Notes

This decision establishes the agent infrastructure as the foundation for all AI agent coordination in the n8n AI Assistant. The standardized approach enables consistent development patterns while supporting the diverse requirements of different agent types.

The registry pattern provides flexibility for dynamic agent management while maintaining type safety and clear contracts. The dependency injection approach ensures clean separation of concerns and enables comprehensive testing strategies.

The infrastructure is designed to support the current set of 12 V1 agents while providing a clear path for future expansion and enhancement of agent capabilities.
