# Architecture Decision Record: Custom Graph Orchestration Engine

## Status

Accepted

## Context

The n8n AI Assistant requires a sophisticated orchestration system to coordinate multiple specialized AI agents in complex, multi-step workflows. The system needs to handle:

- **Complex Control Flow**: Conditional logic, loops, error handling, and human-in-the-loop interactions
- **Agent Coordination**: Orchestrating 12+ specialized agents with different responsibilities
- **Data Flow Management**: Passing data between agents while maintaining type safety
- **State Management**: Tracking execution state across long-running workflows
- **Flexibility**: Supporting dynamic workflow modification and extension
- **Performance**: Efficient execution with minimal overhead
- **Debugging**: Clear visibility into execution flow and state

During the AI Studio discussions, we evaluated several approaches for implementing this orchestration system, ranging from existing frameworks to custom solutions.

## Decision

We will implement a **custom graph-based orchestration engine** built from scratch in TypeScript, consisting of:

1. **GraphExecutor**: Runtime engine that executes graph definitions
2. **GraphBuilder**: Fluent API for constructing graph definitions  
3. **Graph Definitions**: JSON-serializable workflow specifications
4. **Node Types**: Agent calls, tool calls, conditionals, loops, data transformations

The system will be built entirely in TypeScript without external agent frameworks, providing maximum control and flexibility.

## Rationale

### Why Custom Implementation

**Maximum Flexibility**: 
- Complete control over execution semantics and behavior
- Ability to implement n8n-specific patterns and optimizations
- No constraints from external framework assumptions
- Easy to extend with new node types and capabilities

**Performance Optimization**:
- Minimal overhead compared to heavy frameworks
- Optimized for our specific use cases and data patterns
- Direct control over memory usage and garbage collection
- Efficient state management without framework abstractions

**TypeScript Integration**:
- Full type safety throughout the execution pipeline
- Compile-time validation of graph structures
- Excellent IDE support and developer experience
- Seamless integration with our TypeScript codebase

**Debugging and Observability**:
- Complete visibility into execution flow and state
- Custom debugging tools and execution tracing
- Detailed error reporting and recovery mechanisms
- Performance profiling and optimization insights

### Why Not Existing Solutions

**LangChain/LangGraph**:
- Heavy framework with significant overhead
- Complex abstractions that don't match our needs
- Limited TypeScript support and type safety
- Difficult to customize for n8n-specific requirements
- Over-engineered for our focused use case

**Microsoft Bot Framework**:
- Designed for conversational bots, not workflow orchestration
- Heavy dependencies and complex setup
- Not optimized for our agent coordination patterns
- Limited flexibility for custom node types

**Apache Airflow**:
- Server-based solution, not suitable for client-side execution
- Python-based, doesn't integrate with our TypeScript stack
- Overkill for our in-memory orchestration needs
- Complex deployment and operational requirements

**Temporal**:
- Requires external infrastructure and services
- Designed for distributed systems, not single-process orchestration
- Complex learning curve and operational overhead
- Not suitable for browser-based execution

## Consequences

### Positive

**Complete Control**: Full control over execution semantics, performance, and behavior
**Type Safety**: End-to-end type safety from graph definition to execution
**Performance**: Optimized performance for our specific use cases
**Flexibility**: Easy to extend and modify for new requirements
**Debugging**: Excellent debugging and observability capabilities
**Integration**: Seamless integration with our TypeScript ecosystem
**Portability**: Works identically in browser and Node.js environments

### Negative

**Development Time**: Requires significant upfront development effort
**Maintenance**: Ongoing maintenance and feature development responsibility
**Testing**: Comprehensive testing required for all execution scenarios
**Documentation**: Need to document custom APIs and patterns
**Learning Curve**: Team needs to learn custom system instead of standard framework

### Mitigation Strategies

**Incremental Development**: Build core functionality first, add advanced features iteratively
**Comprehensive Testing**: Extensive unit and integration tests for all execution paths
**Clear Documentation**: Detailed documentation and examples for all APIs
**Performance Monitoring**: Built-in performance monitoring and optimization tools
**Community Patterns**: Document common patterns and best practices

## Alternatives

### 1. LangChain/LangGraph
**Pros**: Established framework, community support, pre-built components
**Cons**: Heavy overhead, limited TypeScript support, complex abstractions, hard to customize
**Rejected**: Too heavy and inflexible for our needs

### 2. State Machines (XState)
**Pros**: Well-defined state management, good TypeScript support, visual tools
**Cons**: Not designed for agent orchestration, limited data flow capabilities, complex for our use case
**Rejected**: Doesn't match our agent coordination patterns

### 3. Simple Function Composition
**Pros**: Lightweight, easy to understand, full control
**Cons**: No built-in control flow, difficult to handle complex scenarios, limited reusability
**Rejected**: Too simplistic for our complex orchestration needs

### 4. Workflow Engines (Zeebe, Camunda)
**Pros**: Enterprise-grade, battle-tested, rich feature sets
**Cons**: Heavy infrastructure requirements, not suitable for client-side execution, complex setup
**Rejected**: Too heavy and infrastructure-dependent

## Implementation Details

### Core Components

```typescript
// Graph definition structure
interface GraphDefinition {
  id: string;
  description: string;
  version: string;
  entryNodeId: string;
  nodes: GraphNodeDefinition[];
  edges: GraphEdgeDefinition[];
  expectedInitialInputs?: Record<string, any>;
  graphConstants?: Record<string, any>;
}

// Fluent builder API
class GraphBuilder {
  addAgentNode(id: string, agentClass: AgentClass, inputs?: InputMappings): this;
  addToolNode(id: string, toolName: string, inputs?: InputMappings): this;
  addConditionalNode(id: string, condition: ConditionFunction): this;
  onSuccess(targetNodeId: string): this;
  onError(targetNodeId: string): this;
  compile(): CompiledGraph;
}

// Runtime executor
class GraphExecutor {
  async execute(graph: GraphDefinition, initialInputs: any): Promise<ExecutionResult>;
  async executeNode(nodeId: string, context: GraphContext): Promise<NodeResult>;
  private async resolveInputs(node: GraphNodeDefinition, context: GraphContext): Promise<any>;
}
```

### Key Features

**Type-Safe Graph Construction**: GraphBuilder provides compile-time type checking for graph structure
**Flexible Input Resolution**: Support for static values, graph inputs, node outputs, and computed values
**Error Handling**: Comprehensive error handling with custom error paths and recovery mechanisms
**Loop Support**: Built-in support for loops with configurable limits and break conditions
**Human-in-the-Loop**: Native support for pausing execution and requesting user input
**Performance Monitoring**: Built-in execution timing and performance metrics

## Future Considerations

### Planned Enhancements

**Visual Graph Editor**: Browser-based visual editor for graph construction
**Graph Optimization**: Automatic graph optimization and performance improvements
**Distributed Execution**: Support for distributed graph execution across multiple processes
**Graph Versioning**: Version management and migration support for graph definitions
**Advanced Debugging**: Step-through debugging and execution replay capabilities

### Extension Points

**Custom Node Types**: Framework for adding new node types and execution behaviors
**Plugin System**: Plugin architecture for extending core functionality
**External Integrations**: Integration points for external systems and services
**Monitoring Hooks**: Extensible monitoring and observability framework

## Notes

This decision represents a significant architectural commitment that will shape the entire system. The custom graph orchestration engine will be the foundation for all AI agent coordination and workflow execution.

The decision was made after extensive discussion in the AI Studio sessions, where we evaluated the trade-offs between existing solutions and custom implementation. The consensus was that the benefits of complete control and optimization for our specific use case outweigh the additional development effort required.

Regular review of this decision is recommended as the system evolves and new orchestration frameworks emerge in the market.
