# Architecture Decision Record: Decoupled Prompt Engineering Subsystem

## Status

Accepted

## Context

The n8n AI Assistant requires sophisticated prompt engineering capabilities to coordinate 12+ specialized AI agents, each with unique prompting requirements. The system needs:

- **Modular Prompt Construction**: Reusable components for building complex prompts
- **Context Management**: Intelligent conversation history and context window management
- **Rule Integration**: Dynamic injection of relevant n8n best practices and rules
- **Agent Specialization**: Tailored prompting for different agent types and tasks
- **Maintainability**: Clear separation of prompt logic from agent business logic
- **Consistency**: Standardized prompt formatting and structure across all agents
- **Performance**: Efficient prompt assembly and token optimization

The challenge was designing a prompt engineering system that could handle the complexity of multi-agent coordination while remaining maintainable and extensible.

## Decision

We will implement a **decoupled prompt engineering subsystem** located in `src/engine/prompts/` with the following architecture:

1. **Micro-Component Architecture**: Small, focused modules for specific prompt formatting tasks
2. **Service Orchestration**: Higher-level services that coordinate prompt assembly
3. **Static Prompt Parts**: Reusable prompt snippets and templates
4. **Context Management**: Intelligent conversation history and rule selection
5. **Agent-Specific Integration**: Specialized prompt builders for different agent types

### Core Components:

- **parts/**: Static prompt snippets and templates
- **formatters/**: Micro-formatters for specific prompt component types
- **normalizers/**: Message normalization for Vercel AI SDK compatibility
- **mergers/**: Logic for merging adjacent compatible messages
- **services/**: Orchestrating services (GenericPromptBuilder, AgentPromptBuilder, etc.)

## Rationale

### Why Decoupled Architecture

**Modularity and Reusability**:
- Small, focused components can be tested and maintained independently
- Prompt parts can be reused across different agents and contexts
- Easy to modify specific formatting without affecting other components
- Clear separation of concerns between different prompt aspects

**Maintainability**:
- Prompt logic is separated from agent business logic
- Changes to prompt structure don't require agent code modifications
- Easy to track and version prompt changes
- Clear debugging and testing of prompt assembly

**Flexibility**:
- Different agents can use different combinations of prompt components
- Easy to add new prompt components without modifying existing ones
- Support for different prompt styles and formats
- Adaptable to new LLM requirements and capabilities

**Performance Optimization**:
- Intelligent context window management through conversation summarization
- Efficient rule selection based on relevance
- Token counting and optimization at the prompt level
- Caching of expensive prompt assembly operations

### Why Not Alternatives

**Monolithic Prompt Templates**:
- Hard to maintain and modify
- Difficult to reuse components across agents
- No flexibility for dynamic content injection
- Poor separation of concerns

**Agent-Embedded Prompting**:
- Duplicated prompt logic across agents
- Difficult to maintain consistency
- Hard to optimize context management
- Poor testability of prompt logic

**External Prompt Management Tools**:
- Additional dependency and complexity
- May not integrate well with our TypeScript ecosystem
- Limited customization for our specific needs
- Potential vendor lock-in

## Consequences

### Positive

**Maintainable Prompt Engineering**: Clear separation and organization of prompt logic
**Consistent Agent Behavior**: Standardized prompt formatting across all agents
**Efficient Context Management**: Intelligent conversation history and rule selection
**Easy Testing**: Individual components can be unit tested independently
**Performance Optimization**: Token counting and context window management
**Extensibility**: Easy to add new prompt components and formatting logic
**Developer Experience**: Clear APIs and well-organized prompt construction

### Negative

**Initial Complexity**: More complex than simple string templates
**Learning Curve**: Team needs to understand the component architecture
**Abstraction Overhead**: Additional layers between agents and final prompts
**Debugging Complexity**: More components involved in prompt assembly

### Mitigation Strategies

**Comprehensive Documentation**: Clear documentation of all prompt components and services
**Testing Strategy**: Extensive unit and integration tests for prompt assembly
**Development Tools**: Tools for visualizing and debugging prompt construction
**Training**: Team training on prompt engineering patterns and best practices

## Implementation Details

### Micro-Component Architecture

```typescript
// Static prompt parts
export const SYSTEM_PROMPT_BASE_INTRO = `
You are a specialized AI agent in the n8n AI Assistant system.
Your role is to work collaboratively with other agents to help users
create, analyze, and optimize n8n workflows.
`;

export const OUTPUT_FORMAT_INSTRUCTION_TEMPLATE = `
## Output Format
Respond with a valid JSON object matching this schema:
{schema}

Do not include any text outside the JSON object.
`;
```

### Formatters for Specific Components

```typescript
// JsonSchemaInstructionFormatter
export class JsonSchemaInstructionFormatter {
  static format(component: JsonSchemaInstructionComponent): FormattedMessage {
    const jsonSchema = zodToJsonSchema(component.zodSchema);
    const content = OUTPUT_FORMAT_INSTRUCTION_TEMPLATE
      .replace('{schema}', JSON.stringify(jsonSchema, null, 2));
    
    return {
      role: 'system',
      contentString: content
    };
  }
}

// TaggedDataFormatter
export class TaggedDataFormatter {
  static format(component: TaggedDataComponent): FormattedMessage {
    const content = `## ${component.tag}\n${component.data}`;
    return {
      role: 'user',
      contentString: content
    };
  }
}
```

### Service Orchestration

```typescript
// GenericPromptBuilder - Core prompt assembly
export class GenericPromptBuilder {
  async buildMessages(components: PromptComponent[]): Promise<ModelMessage[]> {
    // 1. Format each component using appropriate formatter
    const formattedMessages = components.map(component => 
      this.formatComponent(component)
    );
    
    // 2. Normalize messages for Vercel AI SDK compatibility
    const normalizedMessages = this.messageNormalizer.normalize(formattedMessages);
    
    // 3. Merge adjacent compatible messages
    const mergedMessages = this.messageMerger.merge(normalizedMessages);
    
    return mergedMessages;
  }
  
  private formatComponent(component: PromptComponent): FormattedMessage {
    switch (component.type) {
      case 'system_instruction':
        return SystemInstructionFormatter.format(component);
      case 'json_schema_instruction':
        return JsonSchemaInstructionFormatter.format(component);
      case 'tagged_data':
        return TaggedDataFormatter.format(component);
      default:
        throw new Error(`Unknown component type: ${component.type}`);
    }
  }
}

// AgentPromptBuilder - Agent-specific orchestration
export class AgentPromptBuilder {
  constructor(
    private genericBuilder: GenericPromptBuilder,
    private conversationHistoryService: ConversationHistoryService,
    private ruleSelectorService: RuleSelectorService
  ) {}
  
  async prepareAgentLlmMessages(
    agentSlug: string,
    agentPromptComponents: PromptComponent[],
    context: AgentExecutionContext
  ): Promise<ModelMessage[]> {
    const components: PromptComponent[] = [];
    
    // 1. Add conversation history
    const historyBlock = await this.conversationHistoryService
      .getManagedHistoryBlock(context.taskSessionId, context.tokenBudget);
    if (historyBlock) {
      components.push(historyBlock);
    }
    
    // 2. Add relevant rules
    const rulesBlock = await this.ruleSelectorService
      .selectRelevantRules(agentSlug, context);
    if (rulesBlock) {
      components.push(rulesBlock);
    }
    
    // 3. Add agent-specific components
    components.push(...agentPromptComponents);
    
    // 4. Use generic builder for final assembly
    return await this.genericBuilder.buildMessages(components);
  }
}
```

### Context Management Services

```typescript
// ConversationHistoryService - Intelligent history management
export class ConversationHistoryService {
  async getManagedHistoryBlock(
    taskSessionId: string,
    tokenBudget: number
  ): Promise<PromptComponent | null> {
    const session = await this.taskSessionRepo.findById(taskSessionId);
    if (!session) return null;
    
    // Check if summarization is needed
    const historyTokens = this.estimateTokens(session.conversationHistory);
    
    if (historyTokens > tokenBudget) {
      // Trigger summarization
      const summary = await this.summarizeHistory(session);
      await this.updateSessionSummary(taskSessionId, summary);
      
      return {
        type: 'tagged_data',
        tag: 'Conversation Summary',
        data: summary
      };
    } else {
      return {
        type: 'conversation_history',
        messages: session.conversationHistory
      };
    }
  }
  
  private async summarizeHistory(session: TaskSession): Promise<string> {
    const summarizerPrompt = await this.genericBuilder.buildMessages([
      {
        type: 'system_instruction',
        instruction: 'Summarize the conversation history concisely...'
      },
      {
        type: 'tagged_data',
        tag: 'Conversation History',
        data: JSON.stringify(session.conversationHistory)
      }
    ]);
    
    const result = await this.llmService.invokeModel(
      'UTILITY_CONVERSATION_SUMMARIZATION',
      summarizerPrompt
    );
    
    return result.content;
  }
}

// RuleSelectorService - Intelligent rule selection
export class RuleSelectorService {
  async selectRelevantRules(
    agentSlug: string,
    context: AgentExecutionContext
  ): Promise<PromptComponent | null> {
    // 1. Get base rules for agent type
    const baseRules = this.getBaseRulesForAgent(agentSlug);
    
    // 2. Get custom rules from user settings
    const customRules = await this.getCustomRules(context.userId);
    
    // 3. Filter rules based on context relevance
    const relevantRules = this.filterRelevantRules(
      [...baseRules, ...customRules],
      context
    );
    
    if (relevantRules.length === 0) return null;
    
    return {
      type: 'tagged_data',
      tag: 'Relevant n8n Rules and Best Practices',
      data: relevantRules.map(rule => `- ${rule.description}`).join('\n')
    };
  }
}
```

### Message Normalization and Merging

```typescript
// MessageNormalizer - Ensure Vercel AI SDK compatibility
export class MessageNormalizer {
  normalize(messages: FormattedMessage[]): ModelMessage[] {
    return messages.map(msg => ({
      role: this.normalizeRole(msg.role),
      content: this.normalizeContent(msg.contentString)
    }));
  }
  
  private normalizeRole(role: string): 'system' | 'user' | 'assistant' | 'tool' {
    // Handle role normalization logic
    return role as any;
  }
}

// MessageMerger - Merge adjacent compatible messages
export class MessageMerger {
  merge(messages: ModelMessage[]): ModelMessage[] {
    const merged: ModelMessage[] = [];
    
    for (const message of messages) {
      const lastMessage = merged[merged.length - 1];
      
      if (this.canMerge(lastMessage, message)) {
        lastMessage.content = this.mergeContent(lastMessage.content, message.content);
      } else {
        merged.push(message);
      }
    }
    
    return merged;
  }
  
  private canMerge(msg1: ModelMessage, msg2: ModelMessage): boolean {
    return msg1 && msg1.role === msg2.role && 
           typeof msg1.content === 'string' && 
           typeof msg2.content === 'string';
  }
}
```

## Future Considerations

### Planned Enhancements

**Advanced Context Management**: More sophisticated context window optimization strategies
**Prompt Versioning**: Version management for prompt templates and components
**A/B Testing**: Framework for testing different prompt variations
**Performance Optimization**: Caching and memoization for expensive prompt operations
**Visual Prompt Builder**: UI tools for constructing and testing prompts

### Extension Points

**Custom Formatters**: Framework for adding new prompt component formatters
**Rule Engines**: More sophisticated rule selection and application logic
**Context Providers**: Pluggable context providers for different data sources
**Prompt Analytics**: Analytics and optimization insights for prompt performance

## Notes

This decision establishes the prompt engineering subsystem as a critical foundation for all AI agent interactions. The decoupled architecture enables sophisticated prompt construction while maintaining clarity and maintainability.

The design was heavily influenced by the need to support 12+ specialized agents with different prompting requirements while ensuring consistency and performance across the system. The micro-component approach allows for fine-grained control over prompt assembly while maintaining reusability.

The context management services (ConversationHistoryService and RuleSelectorService) are particularly important for managing the complexity of multi-turn conversations and ensuring relevant information is included in prompts without exceeding token limits.
