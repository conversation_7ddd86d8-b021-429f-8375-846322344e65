# Architecture Decision Record: Service-Oriented Architecture Pattern

## Status

Accepted

## Context

The n8n AI Assistant requires a complex system architecture that coordinates multiple specialized components including AI agents, LLM services, persistence layers, and external integrations. The system needs:

- **Clear Separation of Concerns**: Distinct responsibilities for different system components
- **Dependency Management**: Clean dependency injection and service composition
- **Testability**: Easy unit testing with mocked dependencies
- **Maintainability**: Modular design that supports independent development and updates
- **Scalability**: Architecture that can grow with additional features and complexity
- **Reusability**: Services that can be shared across different components
- **Configuration Management**: Centralized configuration and environment handling

The challenge was designing an architecture that could handle the complexity of AI orchestration while maintaining clarity and maintainability.

## Decision

We will implement a **Service-Oriented Architecture (SOA)** with clear layering and dependency injection:

1. **Layered Architecture**: Clear separation between application, engine, persistence, and configuration layers
2. **Service Abstraction**: Core functionalities encapsulated in dedicated services
3. **Dependency Injection**: Constructor-based dependency injection throughout the system
4. **Interface-Driven Design**: Clear contracts between services and components
5. **Service Composition**: Higher-level services compose lower-level services

### Architecture Layers:

- **Application Layer** (`src/app/`, `src/services/`): High-level orchestration and business logic
- **Engine Layer** (`src/engine/`): Core AI infrastructure and execution engine
- **Persistence Layer** (`src/persistence/`): Data storage and retrieval
- **Configuration Layer** (`src/config/`): Static configurations and settings
- **Tools Layer** (`src/tools/`): External tool integrations

## Rationale

### Why Service-Oriented Architecture

**Modularity and Maintainability**:
- Each service has a single, well-defined responsibility
- Services can be developed, tested, and maintained independently
- Clear boundaries between different system concerns
- Easy to understand and modify individual components

**Dependency Management**:
- Explicit dependency declaration through constructor injection
- Easy to mock dependencies for testing
- Clear understanding of service relationships
- Prevents circular dependencies and tight coupling

**Testability**:
- Services can be unit tested in isolation
- Dependencies can be easily mocked or stubbed
- Clear test boundaries and responsibilities
- Comprehensive testing strategies for each layer

**Reusability**:
- Services can be shared across different components
- Common functionality centralized in reusable services
- Consistent patterns across the application
- Reduced code duplication

**Scalability**:
- New services can be added without modifying existing ones
- Services can be optimized independently
- Clear scaling strategies for different concerns
- Support for future architectural evolution

### Why Not Alternatives

**Monolithic Architecture**:
- Poor separation of concerns
- Difficult to test and maintain
- Hard to scale individual components
- Tight coupling between different functionalities

**Microservices Architecture**:
- Too heavy for in-process coordination
- Network overhead and complexity
- Difficult state management
- Not suitable for browser-based execution

**Functional Programming Approach**:
- Difficult to manage complex state
- Poor encapsulation of related functionality
- Hard to implement dependency injection
- Limited object-oriented benefits

## Consequences

### Positive

**Clear Architecture**: Well-defined layers and service boundaries
**Easy Testing**: Comprehensive testing with dependency injection
**Maintainable Code**: Modular design with single responsibilities
**Flexible Configuration**: Centralized configuration management
**Reusable Components**: Services shared across different parts of the system
**Scalable Design**: Architecture supports growth and evolution
**Developer Experience**: Clear patterns and consistent structure

### Negative

**Initial Complexity**: More complex than simple procedural approach
**Abstraction Overhead**: Additional layers between business logic and implementation
**Dependency Management**: Need to carefully manage service dependencies
**Learning Curve**: Team needs to understand service patterns

### Mitigation Strategies

**Clear Documentation**: Comprehensive documentation of service architecture
**Dependency Injection Container**: Tools to manage service composition
**Service Templates**: Templates and patterns for creating new services
**Architecture Guidelines**: Clear guidelines for service design and implementation

## Implementation Details

### Service Layer Structure

```typescript
// Application Services (src/services/)
export interface UserSettingsService {
  getUserSettings(userId: string): Promise<UserSettings>;
  updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<void>;
  getApiKeys(userId: string): Promise<ApiKeyConfiguration>;
}

export interface TaskSessionManager {
  createSession(userId: string, initialContext: any): Promise<TaskSession>;
  getSession(sessionId: string): Promise<TaskSession | null>;
  updateSession(sessionId: string, updates: Partial<TaskSession>): Promise<void>;
  saveExecutionSnapshot(sessionId: string, snapshot: GraphExecutionContextSnapshot): Promise<void>;
}

export interface NodeSpecService {
  getNodeTemplate(nodeType: string): Promise<NodeTemplate>;
  getAllNodeTypes(): Promise<string[]>;
  validateNodeConfiguration(nodeType: string, config: any): Promise<ValidationResult>;
}
```

### Engine Services (src/engine/)

```typescript
// LLM Service
export interface LLMService {
  invokeModel(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    options?: LLMInvocationOptions
  ): Promise<LLMInvocationResult>;
  
  generateStructuredObject<T>(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    schema: ZodSchema<T>,
    options?: LLMInvocationOptions
  ): Promise<LLMInvocationResult<T>>;
  
  streamText(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    options?: StreamingOptions
  ): ReadableStream<UIMessage>;
}

// Prompt Services
export interface AgentPromptBuilder {
  prepareAgentLlmMessages(
    agentSlug: string,
    promptComponents: PromptComponent[],
    context: AgentExecutionContext
  ): Promise<ModelMessage[]>;
}

export interface ConversationHistoryService {
  getManagedHistoryBlock(
    taskSessionId: string,
    tokenBudget: number
  ): Promise<PromptComponent | null>;
}
```

### Dependency Injection Pattern

```typescript
// Service Dependencies Interface
export interface ServiceDependencies {
  // Repositories
  userSettingsRepository: UserSettingsRepository;
  taskSessionRepository: TaskSessionRepository;
  conversationRepository: ConversationRepository;
  customRulesRepository: CustomRulesRepository;
  
  // Engine Services
  llmService: LLMService;
  agentPromptBuilder: AgentPromptBuilder;
  conversationHistoryService: ConversationHistoryService;
  ruleSelectorService: RuleSelectorService;
  
  // Configuration
  baseN8nRules: N8nRule[];
  llmModelsConfig: LLMModelsConfiguration;
  
  // Utilities
  logger: Logger;
  tokenCounter: TokenCounter;
}

// Service Implementation with Dependency Injection
export class TaskSessionManagerImpl implements TaskSessionManager {
  constructor(
    private taskSessionRepo: TaskSessionRepository,
    private conversationRepo: ConversationRepository,
    private logger: Logger
  ) {}
  
  async createSession(userId: string, initialContext: any): Promise<TaskSession> {
    const session: TaskSession = {
      id: generateId(),
      userId,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      context: initialContext,
      conversationHistory: [],
      metadata: {
        totalTokensUsed: 0,
        totalCost: 0,
        agentInvocations: 0
      }
    };
    
    await this.taskSessionRepo.create(session);
    this.logger.info(`Created task session ${session.id} for user ${userId}`);
    
    return session;
  }
  
  // ... other methods
}
```

### Service Composition and Orchestration

```typescript
// OrchestratorService - High-level service composition
export class OrchestratorService {
  constructor(
    private taskSessionManager: TaskSessionManager,
    private graphExecutor: GraphExecutor,
    private agentRegistry: AgentRegistry,
    private userSettingsService: UserSettingsService,
    private logger: Logger
  ) {}
  
  async executeUserRequest(
    userId: string,
    request: UserRequest
  ): Promise<ExecutionResult> {
    // 1. Create or retrieve task session
    const session = await this.taskSessionManager.createSession(userId, {
      originalRequest: request,
      timestamp: new Date().toISOString()
    });
    
    // 2. Select appropriate graph based on request
    const graphDefinition = await this.selectGraph(request);
    
    // 3. Prepare execution context
    const executionContext = await this.prepareExecutionContext(session, userId);
    
    // 4. Execute graph
    const result = await this.graphExecutor.execute(
      graphDefinition,
      request,
      executionContext
    );
    
    // 5. Update session with results
    await this.taskSessionManager.updateSession(session.id, {
      status: result.status === 'success' ? 'COMPLETED' : 'FAILED',
      updatedAt: new Date().toISOString(),
      lastError: result.error
    });
    
    return result;
  }
  
  private async prepareExecutionContext(
    session: TaskSession,
    userId: string
  ): Promise<GraphExecutionContext> {
    const userSettings = await this.userSettingsService.getUserSettings(userId);
    
    return {
      taskSessionId: session.id,
      userId,
      executionId: generateId(),
      services: {
        agentRegistry: this.agentRegistry,
        agentDependencies: await this.createAgentDependencies(userSettings),
        // ... other services
      },
      // ... other context
    };
  }
}
```

### Configuration Service Pattern

```typescript
// Configuration Management
export interface ConfigurationService {
  getBaseN8nRules(): N8nRule[];
  getLLMModelsConfig(): LLMModelsConfiguration;
  getEnvironmentConfig(): EnvironmentConfiguration;
}

export class ConfigurationServiceImpl implements ConfigurationService {
  private static instance: ConfigurationServiceImpl;
  private baseRules: N8nRule[];
  private llmConfig: LLMModelsConfiguration;
  private envConfig: EnvironmentConfiguration;
  
  private constructor() {
    this.loadConfigurations();
  }
  
  static getInstance(): ConfigurationServiceImpl {
    if (!ConfigurationServiceImpl.instance) {
      ConfigurationServiceImpl.instance = new ConfigurationServiceImpl();
    }
    return ConfigurationServiceImpl.instance;
  }
  
  private loadConfigurations(): void {
    this.baseRules = require('../config/baseN8nRules.config').baseN8nRules;
    this.llmConfig = require('../config/llmModels.config').llmModelsConfig;
    this.envConfig = this.detectEnvironmentConfig();
  }
  
  getBaseN8nRules(): N8nRule[] {
    return this.baseRules;
  }
  
  // ... other methods
}
```

### Service Factory Pattern

```typescript
// Service Factory for Dependency Injection
export class ServiceFactory {
  private static services: Map<string, any> = new Map();
  
  static async createServices(): Promise<ServiceDependencies> {
    // Initialize repositories
    const db = await getDb();
    const userSettingsRepo = new UserSettingsRepository(db.user_settings);
    const taskSessionRepo = new TaskSessionRepository(db.task_sessions);
    
    // Initialize engine services
    const llmService = new LLMServiceImpl(userSettingsRepo);
    const agentPromptBuilder = new AgentPromptBuilderImpl(/* dependencies */);
    
    // Initialize application services
    const taskSessionManager = new TaskSessionManagerImpl(
      taskSessionRepo,
      /* other dependencies */
    );
    
    return {
      userSettingsRepository: userSettingsRepo,
      taskSessionRepository: taskSessionRepo,
      llmService,
      agentPromptBuilder,
      taskSessionManager,
      // ... other services
    };
  }
}
```

## Future Considerations

### Planned Enhancements

**Service Discovery**: Dynamic service discovery and registration
**Service Monitoring**: Performance monitoring and health checks for services
**Service Versioning**: Version management for service interfaces
**Service Mesh**: Advanced service communication patterns
**Microservice Evolution**: Potential evolution to microservices for specific components

### Extension Points

**Plugin Architecture**: Framework for third-party service plugins
**Service Middleware**: Middleware system for cross-cutting concerns
**Service Composition**: Advanced service composition patterns
**Service Caching**: Intelligent caching strategies for service calls

## Notes

This decision establishes the service-oriented architecture as the foundation for system organization and component interaction. The layered approach with clear service boundaries enables maintainable, testable, and scalable code.

The dependency injection pattern ensures loose coupling while maintaining clear relationships between components. The service abstraction allows for easy testing and future evolution of individual components without affecting the overall system.

The architecture supports both the current V1 requirements and provides a clear path for future enhancements and scaling as the system grows in complexity.
