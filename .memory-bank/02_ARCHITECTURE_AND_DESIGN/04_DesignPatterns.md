# Design Patterns: n8n AI Assistant

## Overview

This document catalogs the key software design patterns used throughout the n8n AI Assistant architecture. These patterns provide proven solutions to common design problems and ensure consistency, maintainability, and scalability across the system.

## Core Architectural Patterns

### 1. Service-Oriented Architecture (SOA)

**Intent**: Organize functionality into discrete services with well-defined interfaces

**Implementation**:
```typescript
// Service interface definition
export interface LLMService {
  generateStructuredObject<T>(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    schema: ZodSchema<T>
  ): Promise<LLMInvocationResult<T>>;
}

// Service implementation
export class LLMServiceImpl implements LLMService {
  constructor(
    private userSettingsRepo: UserSettingsRepository,
    private logger: Logger
  ) {}
  
  async generateStructuredObject<T>(...): Promise<LLMInvocationResult<T>> {
    // Implementation
  }
}
```

**Benefits**:
- Clear separation of concerns
- Easy testing with dependency injection
- Reusable services across components
- Independent service evolution

**Usage**: Core services (LLMService, NodeSpecService, etc.)

### 2. Repository Pattern

**Intent**: Encapsulate data access logic and provide a uniform interface to data

**Implementation**:
```typescript
// Base repository with common operations
export abstract class BaseRepository<T> {
  protected collection: RxCollection<T>;
  
  async create(data: T): Promise<RxDocument<T>> {
    return await this.collection.insert(data);
  }
  
  async findById(id: string): Promise<RxDocument<T> | null> {
    return await this.collection.findOne(id).exec();
  }
  
  // Reactive queries
  find$(query: MangoQuery<T>): Observable<RxDocument<T>[]> {
    return this.collection.find(query).$;
  }
}

// Specialized repository
export class WorkflowRepository extends BaseRepository<WorkflowDocument> {
  async findByTags(tags: string[]): Promise<RxDocument<WorkflowDocument>[]> {
    return await this.collection.find({
      selector: { tags: { $in: tags } }
    }).exec();
  }
}
```

**Benefits**:
- Abstracted data access
- Consistent query patterns
- Easy testing with mock repositories
- Database-agnostic business logic

**Usage**: All data persistence operations

### 3. Factory Pattern

**Intent**: Create objects without specifying their exact classes

**Implementation**:
```typescript
// Agent factory
export class AgentRegistry {
  private agents = new Map<string, AgentRegistryEntry>();
  
  createAgent<T extends BaseN8nAgent<any, any>>(
    agentSlug: string,
    dependencies: AgentDependencies
  ): T {
    const entry = this.agents.get(agentSlug);
    if (!entry) {
      throw new Error(`Agent ${agentSlug} not found`);
    }
    
    return new entry.agentClass(dependencies) as T;
  }
}

// Storage adapter factory
export function createStorageAdapter(): StorageAdapter {
  if (isNodeEnvironment()) {
    return new SQLiteStorageAdapter();
  } else {
    return new IndexedDBStorageAdapter();
  }
}
```

**Benefits**:
- Flexible object creation
- Platform-specific implementations
- Centralized creation logic
- Easy to extend with new types

**Usage**: Agent creation, storage adapters, service factories

### 4. Strategy Pattern

**Intent**: Define a family of algorithms and make them interchangeable

**Implementation**:
```typescript
// LLM provider strategy
export interface LLMProvider {
  generateText(prompt: string, options: GenerationOptions): Promise<string>;
  generateStructured<T>(prompt: string, schema: ZodSchema<T>): Promise<T>;
}

export class OpenAIProvider implements LLMProvider {
  async generateText(prompt: string, options: GenerationOptions): Promise<string> {
    // OpenAI implementation
  }
}

export class AnthropicProvider implements LLMProvider {
  async generateText(prompt: string, options: GenerationOptions): Promise<string> {
    // Anthropic implementation
  }
}

// Context for strategy selection
export class LLMService {
  private providers: Map<string, LLMProvider> = new Map();
  
  private selectProvider(taskCategory: LLMTaskCategory): LLMProvider {
    // Strategy selection logic
    return this.providers.get(this.getPreferredProvider(taskCategory));
  }
}
```

**Benefits**:
- Interchangeable algorithms
- Easy to add new strategies
- Runtime strategy selection
- Clean separation of concerns

**Usage**: LLM provider selection, storage adapters, prompt formatting

### 5. Observer Pattern (Reactive)

**Intent**: Define a one-to-many dependency between objects

**Implementation**:
```typescript
// RxDB reactive queries
export class WorkflowService {
  watchActiveWorkflows(): Observable<WorkflowDocument[]> {
    return this.workflowRepo.find$({
      selector: { status: 'active' }
    });
  }
}

// Component using reactive data
export class WorkflowListComponent {
  private workflows$ = this.workflowService.watchActiveWorkflows();
  
  ngOnInit() {
    this.workflows$.subscribe(workflows => {
      this.updateUI(workflows);
    });
  }
}
```

**Benefits**:
- Automatic UI updates
- Decoupled components
- Real-time data synchronization
- Event-driven architecture

**Usage**: RxDB reactive queries, UI updates, real-time collaboration

## Agent-Specific Patterns

### 6. Template Method Pattern

**Intent**: Define the skeleton of an algorithm, letting subclasses override specific steps

**Implementation**:
```typescript
// Base agent with template method
export abstract class BaseN8nAgent<TInput, TOutput> {
  // Template method
  async execute(
    inputs: TInput,
    context: AgentExecutionContext
  ): Promise<AgentExecutionResult<TOutput>> {
    try {
      // 1. Validate inputs
      const validatedInputs = await this.validateInputs(inputs);
      
      // 2. Prepare prompt
      const promptComponents = await this.preparePromptComponents(validatedInputs, context);
      
      // 3. Generate response
      const response = await this.generateResponse(promptComponents);
      
      // 4. Validate output
      const validatedOutput = await this.validateOutput(response);
      
      return {
        status: 'success',
        output: validatedOutput
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
  
  // Abstract methods for subclasses to implement
  protected abstract preparePromptComponents(
    inputs: TInput,
    context: AgentExecutionContext
  ): Promise<PromptComponent[]>;
  
  protected abstract validateOutput(response: any): Promise<TOutput>;
}

// Concrete agent implementation
export class N8nArchitectAgent extends BaseN8nAgent<ArchitectInput, ArchitectOutput> {
  protected async preparePromptComponents(
    inputs: ArchitectInput,
    context: AgentExecutionContext
  ): Promise<PromptComponent[]> {
    return [
      {
        type: 'system_instruction',
        instruction: ARCHITECT_SYSTEM_PROMPT
      },
      {
        type: 'tagged_data',
        tag: 'Requirements',
        data: inputs.requirements
      }
    ];
  }
  
  protected async validateOutput(response: any): Promise<ArchitectOutput> {
    return this.getOutputSchema().parse(response);
  }
}
```

**Benefits**:
- Consistent agent execution flow
- Customizable steps for each agent
- Shared error handling and validation
- Easy to add new agents

**Usage**: All AI agent implementations

### 7. Command Pattern

**Intent**: Encapsulate a request as an object

**Implementation**:
```typescript
// Graph node as command
export interface GraphNodeDefinition {
  id: string;
  type: 'agent_call' | 'tool_call' | 'conditional' | 'loop';
  execute(context: GraphExecutionContext): Promise<NodeOutputVersion>;
}

export class AgentCallNodeDef implements GraphNodeDefinition {
  constructor(
    public id: string,
    public agentSlug: string,
    public inputs: InputMappings
  ) {}
  
  async execute(context: GraphExecutionContext): Promise<NodeOutputVersion> {
    const agent = context.services.agentRegistry.createAgent(
      this.agentSlug,
      context.services.agentDependencies
    );
    
    const resolvedInputs = await this.resolveInputs(context);
    const result = await agent.execute(resolvedInputs, context);
    
    return {
      nodeId: this.id,
      status: result.status,
      output: result.output,
      timestamp: new Date().toISOString()
    };
  }
}
```

**Benefits**:
- Encapsulated operations
- Undo/redo capability
- Queuing and scheduling
- Macro recording

**Usage**: Graph node execution, tool calls, user actions

## Data Flow Patterns

### 8. Pipeline Pattern

**Intent**: Process data through a series of transformations

**Implementation**:
```typescript
// Prompt building pipeline
export class PromptPipeline {
  private stages: PromptStage[] = [];
  
  addStage(stage: PromptStage): this {
    this.stages.push(stage);
    return this;
  }
  
  async process(input: PromptInput): Promise<ModelMessage[]> {
    let current = input;
    
    for (const stage of this.stages) {
      current = await stage.process(current);
    }
    
    return current.messages;
  }
}

// Usage
const pipeline = new PromptPipeline()
  .addStage(new ContextInjectionStage())
  .addStage(new RuleSelectionStage())
  .addStage(new MessageNormalizationStage())
  .addStage(new MessageMergingStage());

const messages = await pipeline.process(initialInput);
```

**Benefits**:
- Modular processing steps
- Easy to add/remove stages
- Clear data flow
- Reusable components

**Usage**: Prompt building, data transformation, validation chains

### 9. Builder Pattern

**Intent**: Construct complex objects step by step

**Implementation**:
```typescript
// Graph builder with fluent API
export class GraphBuilder {
  private nodes: GraphNodeDefinition[] = [];
  private edges: GraphEdgeDefinition[] = [];
  private currentNodeId: string;
  
  addAgentNode(id: string, agentSlug: string, inputs?: InputMappings): this {
    this.nodes.push(new AgentCallNodeDef(id, agentSlug, inputs));
    this.currentNodeId = id;
    return this;
  }
  
  addToolNode(id: string, toolName: string, inputs?: InputMappings): this {
    this.nodes.push(new ToolCallNodeDef(id, toolName, inputs));
    this.currentNodeId = id;
    return this;
  }
  
  onSuccess(targetNodeId: string): this {
    this.edges.push({
      from: this.currentNodeId,
      to: targetNodeId,
      condition: 'success'
    });
    return this;
  }
  
  onError(targetNodeId: string): this {
    this.edges.push({
      from: this.currentNodeId,
      to: targetNodeId,
      condition: 'error'
    });
    return this;
  }
  
  compile(): CompiledGraph {
    return new CompiledGraph(this.nodes, this.edges);
  }
}

// Usage
const graph = new GraphBuilder()
  .addAgentNode('intent-router', 'n8n-intent-router')
  .onSuccess('requirements-gatherer')
  .addAgentNode('requirements-gatherer', 'n8n-requirements-gatherer')
  .onSuccess('architect')
  .addAgentNode('architect', 'n8n-architect')
  .compile();
```

**Benefits**:
- Fluent, readable API
- Step-by-step construction
- Validation during building
- Immutable final object

**Usage**: Graph construction, complex configuration objects

### 10. Adapter Pattern

**Intent**: Allow incompatible interfaces to work together

**Implementation**:
```typescript
// Storage adapter for cross-platform compatibility
export interface StorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  delete(key: string): Promise<void>;
}

// Browser implementation
export class IndexedDBAdapter implements StorageAdapter {
  async get(key: string): Promise<any> {
    // IndexedDB implementation
  }
  
  async set(key: string, value: any): Promise<void> {
    // IndexedDB implementation
  }
}

// Node.js implementation
export class FileSystemAdapter implements StorageAdapter {
  async get(key: string): Promise<any> {
    // File system implementation
  }
  
  async set(key: string, value: any): Promise<void> {
    // File system implementation
  }
}

// Client code uses unified interface
export class ConfigurationService {
  constructor(private storage: StorageAdapter) {}
  
  async loadConfig(): Promise<Configuration> {
    return await this.storage.get('configuration');
  }
}
```

**Benefits**:
- Platform compatibility
- Legacy system integration
- Unified interfaces
- Easy testing with adapters

**Usage**: Storage adapters, API integrations, platform-specific implementations

## Error Handling Patterns

### 11. Chain of Responsibility

**Intent**: Pass requests along a chain of handlers

**Implementation**:
```typescript
// Error handler chain
export abstract class ErrorHandler {
  protected nextHandler?: ErrorHandler;
  
  setNext(handler: ErrorHandler): ErrorHandler {
    this.nextHandler = handler;
    return handler;
  }
  
  async handle(error: Error, context: any): Promise<boolean> {
    if (await this.canHandle(error, context)) {
      await this.handleError(error, context);
      return true;
    }
    
    if (this.nextHandler) {
      return await this.nextHandler.handle(error, context);
    }
    
    return false;
  }
  
  protected abstract canHandle(error: Error, context: any): Promise<boolean>;
  protected abstract handleError(error: Error, context: any): Promise<void>;
}

export class LLMErrorHandler extends ErrorHandler {
  protected async canHandle(error: Error): Promise<boolean> {
    return error instanceof LLMError;
  }
  
  protected async handleError(error: LLMError, context: any): Promise<void> {
    // Handle LLM-specific errors
    await this.retryWithBackoff(error, context);
  }
}

export class ValidationErrorHandler extends ErrorHandler {
  protected async canHandle(error: Error): Promise<boolean> {
    return error instanceof ZodError;
  }
  
  protected async handleError(error: ZodError, context: any): Promise<void> {
    // Handle validation errors
    await this.logValidationError(error, context);
  }
}

// Setup error handling chain
const errorHandler = new LLMErrorHandler()
  .setNext(new ValidationErrorHandler())
  .setNext(new GenericErrorHandler());
```

**Benefits**:
- Flexible error handling
- Easy to add new handlers
- Separation of error concerns
- Configurable handling chains

**Usage**: Error handling, request processing, validation chains

## Performance Patterns

### 12. Lazy Loading

**Intent**: Defer object creation until needed

**Implementation**:
```typescript
// Lazy service initialization
export class ServiceContainer {
  private services = new Map<string, any>();
  private factories = new Map<string, () => any>();
  
  register<T>(name: string, factory: () => T): void {
    this.factories.set(name, factory);
  }
  
  get<T>(name: string): T {
    if (!this.services.has(name)) {
      const factory = this.factories.get(name);
      if (!factory) {
        throw new Error(`Service ${name} not registered`);
      }
      this.services.set(name, factory());
    }
    
    return this.services.get(name);
  }
}

// Lazy component loading
export class ComponentLoader {
  private components = new Map<string, Promise<any>>();
  
  async loadComponent(name: string): Promise<any> {
    if (!this.components.has(name)) {
      this.components.set(name, this.doLoadComponent(name));
    }
    
    return await this.components.get(name);
  }
  
  private async doLoadComponent(name: string): Promise<any> {
    const module = await import(`./components/${name}`);
    return module.default;
  }
}
```

**Benefits**:
- Reduced initial load time
- Memory efficiency
- On-demand resource allocation
- Better performance

**Usage**: Service initialization, component loading, resource management

These design patterns form the foundation of the n8n AI Assistant architecture, providing proven solutions for common design challenges while ensuring consistency, maintainability, and scalability throughout the system.
