# Data Architecture: n8n AI Assistant

## Overview

The n8n AI Assistant employs a sophisticated data architecture built on RxDB for cross-platform persistence, with carefully designed schemas and data flow patterns that support real-time collaboration, offline functionality, and efficient querying across browser and Node.js environments.

## Core Data Architecture Principles

### 1. Cross-Platform Consistency
- **Unified Schema**: Same data structures work identically in browser and Node.js
- **Storage Abstraction**: RxDB abstracts underlying storage mechanisms
- **Sync Capability**: Built-in synchronization between different instances
- **Offline First**: Local-first architecture with eventual consistency

### 2. Reactive Data Flow
- **Real-Time Updates**: Automatic UI updates when data changes
- **Event-Driven**: Data changes trigger appropriate system responses
- **Observable Patterns**: RxJS observables for reactive programming
- **Change Streams**: Live change streams for real-time collaboration

### 3. Schema-Driven Design
- **Type Safety**: Zod schemas ensure runtime type safety
- **Validation**: Automatic validation of all data operations
- **Evolution**: Schema versioning and migration support
- **Documentation**: Self-documenting schemas with clear field descriptions

## RxDB Configuration

### Database Setup

```typescript
// Core database configuration
const dbConfig = {
  name: 'n8n_ai_assistant',
  storage: getRxStorageAdapter(), // Platform-specific adapter
  multiInstance: true,
  eventReduce: true,
  cleanupPolicy: {
    minimumDeletedTime: 1000 * 60 * 60 * 24 * 7, // 7 days
    minimumCollectionAge: 1000 * 60 * 60 * 24 * 30, // 30 days
    runEach: 1000 * 60 * 60 * 24, // Daily cleanup
  }
};
```

### Storage Adapters

**Browser Environment**:
- **Primary**: IndexedDB via `rxdb/plugins/storage-indexeddb`
- **Fallback**: Memory storage for testing
- **Encryption**: Optional encryption for sensitive data

**Node.js Environment**:
- **Primary**: SQLite via `rxdb/plugins/storage-sqlite`
- **Alternative**: LevelDB for high-performance scenarios
- **File System**: JSON file storage for development

### Collection Architecture

## Core Collections

### 1. Workflows Collection

**Purpose**: Store n8n workflow definitions and metadata

```typescript
const workflowSchema = {
  version: 0,
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: { type: 'string', maxLength: 100 },
    name: { type: 'string', maxLength: 200 },
    description: { type: 'string', maxLength: 1000 },
    workflowJson: { type: 'object' }, // Complete n8n workflow JSON
    tags: { type: 'array', items: { type: 'string' } },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    createdBy: { type: 'string', maxLength: 100 },
    version: { type: 'number', minimum: 1 },
    status: { 
      type: 'string', 
      enum: ['draft', 'active', 'inactive', 'archived'] 
    },
    metadata: {
      type: 'object',
      properties: {
        nodeCount: { type: 'number' },
        complexity: { type: 'string', enum: ['simple', 'medium', 'complex'] },
        estimatedExecutionTime: { type: 'number' },
        lastValidated: { type: 'string', format: 'date-time' },
        validationStatus: { type: 'string', enum: ['valid', 'warning', 'error'] }
      }
    }
  },
  required: ['id', 'name', 'workflowJson', 'createdAt', 'status'],
  indexes: ['name', 'status', 'createdAt', 'tags']
};
```

### 2. Conversations Collection

**Purpose**: Store user interaction history and context

```typescript
const conversationSchema = {
  version: 0,
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: { type: 'string', maxLength: 100 },
    sessionId: { type: 'string', maxLength: 100 },
    userId: { type: 'string', maxLength: 100 },
    title: { type: 'string', maxLength: 200 },
    messages: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          role: { type: 'string', enum: ['user', 'assistant', 'system'] },
          content: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' },
          metadata: { type: 'object' }
        }
      }
    },
    context: {
      type: 'object',
      properties: {
        currentWorkflowId: { type: 'string' },
        userExpertiseLevel: { type: 'string', enum: ['beginner', 'intermediate', 'expert'] },
        preferences: { type: 'object' },
        activeGraph: { type: 'string' }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    status: { type: 'string', enum: ['active', 'completed', 'archived'] }
  },
  required: ['id', 'sessionId', 'messages', 'createdAt', 'status'],
  indexes: ['sessionId', 'userId', 'createdAt', 'status']
};
```

### 3. Graph Executions Collection

**Purpose**: Track graph execution history and performance

```typescript
const graphExecutionSchema = {
  version: 0,
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: { type: 'string', maxLength: 100 },
    graphId: { type: 'string', maxLength: 100 },
    conversationId: { type: 'string', maxLength: 100 },
    startTime: { type: 'string', format: 'date-time' },
    endTime: { type: 'string', format: 'date-time' },
    status: { type: 'string', enum: ['running', 'completed', 'failed', 'cancelled'] },
    initialInputs: { type: 'object' },
    finalOutputs: { type: 'object' },
    nodeExecutions: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          nodeId: { type: 'string' },
          startTime: { type: 'string', format: 'date-time' },
          endTime: { type: 'string', format: 'date-time' },
          status: { type: 'string' },
          inputs: { type: 'object' },
          outputs: { type: 'object' },
          error: { type: 'object' }
        }
      }
    },
    performance: {
      type: 'object',
      properties: {
        totalDuration: { type: 'number' },
        llmCalls: { type: 'number' },
        totalTokens: { type: 'number' },
        cost: { type: 'number' }
      }
    }
  },
  required: ['id', 'graphId', 'startTime', 'status'],
  indexes: ['graphId', 'conversationId', 'startTime', 'status']
};
```

### 4. Knowledge Base Collection

**Purpose**: Store domain knowledge and best practices

```typescript
const knowledgeSchema = {
  version: 0,
  primaryKey: 'id',
  type: 'object',
  properties: {
    id: { type: 'string', maxLength: 100 },
    type: { type: 'string', enum: ['best_practice', 'pattern', 'anti_pattern', 'example', 'documentation'] },
    category: { type: 'string', maxLength: 100 },
    title: { type: 'string', maxLength: 200 },
    content: { type: 'string' },
    tags: { type: 'array', items: { type: 'string' } },
    nodeTypes: { type: 'array', items: { type: 'string' } },
    difficulty: { type: 'string', enum: ['beginner', 'intermediate', 'advanced'] },
    priority: { type: 'number', minimum: 1, maximum: 10 },
    embedding: { type: 'array', items: { type: 'number' } }, // Vector embedding for RAG
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    metadata: {
      type: 'object',
      properties: {
        source: { type: 'string' },
        author: { type: 'string' },
        version: { type: 'string' },
        applicableVersions: { type: 'array', items: { type: 'string' } }
      }
    }
  },
  required: ['id', 'type', 'category', 'title', 'content', 'createdAt'],
  indexes: ['type', 'category', 'tags', 'nodeTypes', 'difficulty', 'priority']
};
```

## Data Flow Patterns

### 1. Workflow Creation Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant GraphExecutor
    participant Agents
    participant WorkflowRepo
    participant ConversationRepo

    User->>UI: Create workflow request
    UI->>ConversationRepo: Store user message
    UI->>GraphExecutor: Execute CreateWorkflow graph
    GraphExecutor->>Agents: Orchestrate specialized agents
    Agents->>WorkflowRepo: Generate workflow data
    WorkflowRepo->>UI: Return workflow
    UI->>ConversationRepo: Store assistant response
    UI->>User: Display workflow
```

### 2. Knowledge Retrieval Flow

```mermaid
sequenceDiagram
    participant Agent
    participant KnowledgeRepo
    participant RAGService
    participant LLMService

    Agent->>RAGService: Query for relevant knowledge
    RAGService->>KnowledgeRepo: Vector similarity search
    KnowledgeRepo->>RAGService: Return relevant documents
    RAGService->>Agent: Augmented context
    Agent->>LLMService: Generate with enhanced context
    LLMService->>Agent: Enhanced response
```

### 3. Real-Time Collaboration Flow

```mermaid
sequenceDiagram
    participant User1
    participant User2
    participant RxDB1
    participant RxDB2
    participant SyncService

    User1->>RxDB1: Update workflow
    RxDB1->>SyncService: Sync change
    SyncService->>RxDB2: Replicate change
    RxDB2->>User2: Notify of update
    User2->>User2: UI auto-updates
```

## Repository Pattern Implementation

### Base Repository

```typescript
abstract class BaseRepository<T> {
  protected collection: RxCollection<T>;
  
  constructor(collection: RxCollection<T>) {
    this.collection = collection;
  }
  
  async create(data: T): Promise<RxDocument<T>> {
    return await this.collection.insert(data);
  }
  
  async findById(id: string): Promise<RxDocument<T> | null> {
    return await this.collection.findOne(id).exec();
  }
  
  async findMany(query: MangoQuery<T>): Promise<RxDocument<T>[]> {
    return await this.collection.find(query).exec();
  }
  
  async update(id: string, data: Partial<T>): Promise<RxDocument<T>> {
    const doc = await this.findById(id);
    if (!doc) throw new Error(`Document ${id} not found`);
    return await doc.patch(data);
  }
  
  async delete(id: string): Promise<boolean> {
    const doc = await this.findById(id);
    if (!doc) return false;
    await doc.remove();
    return true;
  }
  
  // Reactive queries
  find$(query: MangoQuery<T>): Observable<RxDocument<T>[]> {
    return this.collection.find(query).$;
  }
  
  findOne$(id: string): Observable<RxDocument<T> | null> {
    return this.collection.findOne(id).$;
  }
}
```

### Specialized Repositories

```typescript
class WorkflowRepository extends BaseRepository<WorkflowDocument> {
  async findByTags(tags: string[]): Promise<RxDocument<WorkflowDocument>[]> {
    return await this.collection.find({
      selector: {
        tags: { $in: tags }
      }
    }).exec();
  }
  
  async findByComplexity(complexity: string): Promise<RxDocument<WorkflowDocument>[]> {
    return await this.collection.find({
      selector: {
        'metadata.complexity': complexity
      }
    }).exec();
  }
  
  // Reactive workflow updates
  watchWorkflow$(id: string): Observable<WorkflowDocument | null> {
    return this.findOne$(id);
  }
}
```

## Data Migration and Versioning

### Schema Evolution

```typescript
const migrationStrategies = {
  0: {
    // Initial schema
  },
  1: {
    // Migration from version 0 to 1
    migrationStrategy: {
      workflows: (oldDoc: any) => {
        // Transform old document to new schema
        return {
          ...oldDoc,
          metadata: {
            nodeCount: oldDoc.workflowJson?.nodes?.length || 0,
            complexity: 'medium', // Default value
            estimatedExecutionTime: null,
            lastValidated: new Date().toISOString(),
            validationStatus: 'valid'
          }
        };
      }
    }
  }
};
```

### Backup and Recovery

```typescript
class BackupService {
  async createBackup(): Promise<string> {
    const collections = await this.db.collections;
    const backup = {};
    
    for (const [name, collection] of Object.entries(collections)) {
      backup[name] = await collection.exportJSON();
    }
    
    return JSON.stringify(backup);
  }
  
  async restoreBackup(backupData: string): Promise<void> {
    const backup = JSON.parse(backupData);
    
    for (const [collectionName, data] of Object.entries(backup)) {
      await this.db[collectionName].importJSON(data);
    }
  }
}
```

## Performance Optimization

### Indexing Strategy

```typescript
const optimizedIndexes = {
  workflows: [
    'name', // Text search
    'status', // Status filtering
    'createdAt', // Time-based queries
    'tags', // Tag-based filtering
    ['status', 'createdAt'], // Compound index for active workflows
    ['metadata.complexity', 'createdAt'] // Complex queries
  ],
  conversations: [
    'sessionId', // Session-based queries
    'userId', // User-specific queries
    'createdAt', // Time-based queries
    ['userId', 'createdAt'], // User timeline
    ['status', 'updatedAt'] // Active conversation queries
  ]
};
```

### Query Optimization

```typescript
class OptimizedQueries {
  // Efficient pagination
  async getWorkflowsPaginated(page: number, limit: number) {
    return await this.workflowCollection
      .find({
        selector: { status: 'active' },
        sort: [{ createdAt: 'desc' }],
        skip: page * limit,
        limit: limit
      })
      .exec();
  }
  
  // Efficient search with indexes
  async searchWorkflows(searchTerm: string) {
    return await this.workflowCollection
      .find({
        selector: {
          $or: [
            { name: { $regex: searchTerm, $options: 'i' } },
            { description: { $regex: searchTerm, $options: 'i' } },
            { tags: { $in: [searchTerm] } }
          ]
        }
      })
      .exec();
  }
}
```

## Security and Privacy

### Data Encryption

```typescript
const encryptionConfig = {
  password: process.env.DB_ENCRYPTION_KEY,
  collections: {
    conversations: {
      encrypted: ['messages.content', 'context.preferences']
    },
    workflows: {
      encrypted: ['workflowJson'] // Encrypt sensitive workflow data
    }
  }
};
```

### Access Control

```typescript
class DataAccessControl {
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    // Implement role-based access control
    const userRole = await this.getUserRole(userId);
    return this.permissions[userRole]?.[resource]?.includes(action) || false;
  }
  
  async filterByPermissions<T>(userId: string, documents: T[]): Promise<T[]> {
    // Filter documents based on user permissions
    return documents.filter(doc => this.canAccess(userId, doc));
  }
}
```

## Monitoring and Analytics

### Performance Metrics

```typescript
class DataMetrics {
  async getCollectionStats() {
    return {
      workflows: await this.workflowCollection.count().exec(),
      conversations: await this.conversationCollection.count().exec(),
      executions: await this.executionCollection.count().exec(),
      knowledge: await this.knowledgeCollection.count().exec()
    };
  }
  
  async getQueryPerformance() {
    // Track query execution times
    return this.queryMetrics.getAverageExecutionTime();
  }
}
```

This data architecture provides a robust foundation for the n8n AI Assistant, ensuring data consistency, performance, and scalability across all supported platforms while maintaining the flexibility needed for future enhancements.
