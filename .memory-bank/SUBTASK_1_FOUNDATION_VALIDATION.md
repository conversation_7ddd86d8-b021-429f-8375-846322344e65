# SUBTASK 1: Foundation Section Validation & Enhancement

## MISSION CONTEXT
You are part of a critical mission to complete the n8n AI Assistant project Memory Bank creation & extraction. This is a systematic extraction of EVERY piece of information from our AI Studio conversation history into a structured Memory Bank that will become the **single source of truth** for AI-to-AI development workflow.

## YOUR SPECIFIC TASK
Validate and enhance the **01_PROJECT_FOUNDATION/** section by:

1. **Reading ALL source materials** in this exact order:
   - `/docs/aistudio_export/1_N8N Agent System Brainstorm.txt` (FIRST - original brainstorm)
   - `/docs/aistudio_export/2_N8n Agentic AI Brain Library Design.txt` (SECOND - evolved discussions)
   - Current memory bank files in `01_PROJECT_FOUNDATION/`
   - `PRD.md` and current `src/` implementation
   - Any other relevant memory bank files

2. **Validate existing content** against source materials:
   - Check if ProjectBrief.md captures the complete mission and scope
   - Verify ProductContext.md has full user personas and UX vision
   - Ensure Glossary.md includes all n8n-specific concepts and key terms
   - Cross-reference with any duplicate files (projectbrief.md, productContext.md, etc.)

3. **Extract missing information** from AI Studio exports:
   - Any project goals/objectives not captured
   - User personas and use cases discussed
   - Problem statements and solution approaches
   - Key terminology and concepts defined
   - Success criteria and metrics

4. **Update/create files** to match the V4.0 structure:
   - 01_ProjectBrief.md (mission, high-level goals, V1 scope)
   - 02_ProductContext.md (user personas, UX vision, problem statement)  
   - 03_Glossary.md (key terms, acronyms, n8n-specific concepts)

## CRITICAL INSTRUCTIONS
- **PRIORITIZE LATEST DECISIONS**: When you find conflicting info, use the most recent version from file 2
- **EXTRACT EVERYTHING**: Don't summarize - capture complete context and rationale
- **MAINTAIN COHERENCE**: Ensure all foundation docs work together as a cohesive whole
- **NO CONFIRMATION NEEDED**: Directly update the memory bank files
- **PRESERVE CONTEXT**: Include enough detail for future AI sessions to understand completely

## QUALITY STANDARDS
- **Completeness**: Every foundational decision and context from AI Studio exports
- **Accuracy**: Latest/final versions of all decisions and concepts
- **Implementation Ready**: Enough detail for Claude Code to understand and implement
- **Cross-Referenced**: Link to related decisions and components

## SUCCESS CRITERIA
- Foundation section completely captures project essence from AI Studio discussions
- All user personas, use cases, and problem statements documented
- Complete glossary of domain concepts and terminology
- Coherent narrative that enables AI-to-AI workflow

**BEGIN IMMEDIATELY** - Read the source files and update the foundation section directly.
