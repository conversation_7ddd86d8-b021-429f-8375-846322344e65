# Project Brief: n8n Agentic AI "Brain" Library (V1.0)

## Core Mission
To create a highly intelligent, multi-platform TypeScript library serving as the "AI Brain" for an n8n assistant. This brain understands natural language requests to design, generate, validate, explain, and optimize n8n workflows, embedding best practices.

## Primary Goals
1. Develop a robust, graph-based AI orchestration engine in pure TypeScript
2. Implement specialized AI agents for n8n workflow design, composition, and validation
3. Integrate seamlessly with LLMs via Vercel AI SDK v5 alpha
4. Ensure multi-platform compatibility (Node.js for development/testing, browser for extensions)
5. Establish a clear, maintainable, and highly decoupled architecture

## Current State Analysis
The project is currently in an intermediate state with:
- Basic engine infrastructure (GraphBuilder, GraphExecutor, BaseAgent)
- Some services implemented (LLMService, various repositories)
- Basic prompt system
- But NOT aligned with the target V3.3.1 architecture

## Immediate Priority
Complete architectural refactoring to align with V3.3.1 structure as specified in the refactor document, then implement missing components.

## Success Criteria
- All V1 graph definitions working (Create New Workflow, Update Existing, Analyze)
- Complete agent ecosystem functional
- Robust prompt engineering subsystem
- Multi-platform persistence layer
- Comprehensive test coverage (>80%)