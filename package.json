{"name": "n8n-ai-assistant-dev", "version": "1.0.0", "description": "", "main": "dist/node/index.js", "module": "dist/esm/index.js", "browser": "dist/browser/index.js", "types": "dist/types/index.d.ts", "files": ["dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc --emitDeclarationOnly", "build:node": "esbuild src/index.ts --bundle --platform=node --format=cjs --outfile=dist/node/index.js", "build:esm": "esbuild src/index.ts --bundle --format=esm --outfile=dist/esm/index.js", "build:browser": "esbuild src/index.ts --bundle --format=esm --outfile=dist/browser/index.js --global-name=n8nAiAssistant --platform=browser", "build": "pnpm run clean && pnpm run build:types && pnpm run build:node && pnpm run build:esm && pnpm run build:browser", "dev": "pnpm run build -- --watch", "test": "vitest", "test:unit": "vitest run src/**/*.test.ts", "test:integration": "vitest run src/**/*.integration.test.ts", "test:graph-executor": "vitest run src/engine/graph/__tests__/GraphExecutor.test.ts", "test:graph-executor-integration": "vitest run src/engine/graph/__tests__/GraphExecutor.integration.test.ts", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:graph-coverage": "vitest run src/engine/graph/**/*.test.ts --coverage", "test:ui": "vitest --ui", "test:graphbuilder": "vitest run src/**/*GraphBuilder*.test.ts", "type-check": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "dependencies": {"@ai-sdk/anthropic": "2.0.0-alpha.3", "@ai-sdk/google": "2.0.0-alpha.3", "@ai-sdk/openai": "2.0.0-alpha.3", "@types/lodash.get": "^4.4.9", "ai": "5.0.0-alpha.3", "gpt-tokenizer": "^2.9.0", "lodash.get": "^4.4.2", "rxdb": "^16.12.0", "zod": "^3.25.20", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/lokijs": "^1.5.14", "@types/node": "^22.15.21", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "esbuild": "^0.20.2", "jsdom": "^25.0.1", "lokijs": "^1.5.12", "rimraf": "^5.0.10", "typescript": "^5.8.3", "vitest": "^2.1.8"}}