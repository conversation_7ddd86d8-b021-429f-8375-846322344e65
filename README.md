# n8n AI Assistant Dev

This package provides the core functionality for the n8n AI Assistant. It's designed to work in both Node.js and browser environments.

## Features

- Cross-environment compatibility (Node.js and browsers)
- AI agents for n8n workflow automation
- Graph-based workflow orchestration
- AI tools for enhanced workflow capabilities

## Installation

```bash
# Using npm
npm install n8n-ai-assistant-dev

# Using yarn
yarn add n8n-ai-assistant-dev

# Using pnpm
pnpm add n8n-ai-assistant-dev
```

## Development Setup

```bash
# Install dependencies
pnpm install

# Build for all environments
pnpm build

# Development mode with watch
pnpm dev
```

## Build Outputs

This package produces three different builds:

- **CommonJS (Node.js)**: `dist/node/index.js`
- **ESM (Modern environments)**: `dist/esm/index.js`
- **<PERSON><PERSON>er (UMD)**: `dist/browser/index.js`

## Usage

### In Node.js

```typescript
import { createAgent, createGraph, createTool, getEnvironment } from 'n8n-ai-assistant-dev';

// Check environment
console.log(getEnvironment().name); // 'node'

// Create an agent
const agent = createAgent({ name: 'WorkflowAssistant' });

// Create a graph
const graph = createGraph({
  name: 'SimpleWorkflow',
  nodes: ['start', 'process', 'end'],
  edges: [
    { from: 'start', to: 'process' },
    { from: 'process', to: 'end' }
  ]
});

// Create a tool
const tool = createTool({
  name: 'TextAnalyzer',
  description: 'Analyzes text using AI',
  execute: async (input) => {
    // Implementation
    return { result: `Analyzed: ${input}` };
  }
});
```

### In Browser

```html
<script src="./dist/browser/index.js"></script>
<script>
  // Global variable: n8nAiAssistant
  const { createAgent, getEnvironment } = window.n8nAiAssistant;
  
  console.log(getEnvironment().name); // 'browser'
  
  const agent = createAgent({ name: 'BrowserAssistant' });
  console.log(agent);
</script>
```

## Environment Detection

You can use the `getEnvironment()` function to detect the current environment:

```typescript
import { getEnvironment } from 'n8n-ai-assistant-dev';

const env = getEnvironment();
if (env.isNode) {
  // Node.js specific code
} else {
  // Browser specific code
}
``` 