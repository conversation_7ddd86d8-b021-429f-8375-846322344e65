/**
 * Environment Detection Utility
 * 
 * Provides utilities for detecting the current execution environment
 * (Node.js vs <PERSON>rowser) to enable platform-specific behavior.
 */

/**
 * Determines if the current environment is Node.js
 * 
 * @returns true if running in Node.js, false if running in browser
 */
export function isNodeEnvironment(): boolean {
  return typeof window === 'undefined' && 
         typeof process !== 'undefined' && 
         process.versions != null && 
         process.versions.node != null;
}

/**
 * Determines if the current environment is a browser
 * 
 * @returns true if running in browser, false if running in Node.js
 */
export function isBrowserEnvironment(): boolean {
  return !isNodeEnvironment();
}

/**
 * Gets basic environment information (compatibility function)
 * 
 * @returns object with basic environment details
 */
export function getEnvironment() {
  const isNode = isNodeEnvironment();
  
  return {
    name: isNode ? 'node' : 'browser',
    isNode,
    isBrowser: !isNode,
  };
}

/**
 * Gets detailed environment information
 * 
 * @returns object with environment details
 */
export function getEnvironmentInfo() {
  const isNode = isNodeEnvironment();
  
  return {
    isNode,
    isBrowser: !isNode,
    name: isNode ? 'node' : 'browser',
    version: isNode 
      ? process.versions.node 
      : (typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'),
    supportsFileSystem: isNode,
    supportsIndexedDB: !isNode && typeof indexedDB !== 'undefined',
    supportsWebWorkers: !isNode && typeof Worker !== 'undefined',
  };
} 