/**
 * UUID Generator Utility
 * 
 * Provides a simple UUID generator function for use throughout the application.
 */

/**
 * Generates a UUID v4 string
 * 
 * @returns A UUID v4 string
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
} 