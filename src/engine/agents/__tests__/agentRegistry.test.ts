/**
 * Agent Registry Tests
 * 
 * Comprehensive unit tests for the agent registry system.
 * Tests cover registry configuration, agent lookup, instantiation,
 * error handling, and utility functions.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { z } from 'zod';
import {
  AGENT_REGISTRY_CONFIG,
  agentRegistryMap,
  getAgentConfig,
  createAgentInstance,
  validateAgentRegistry,
  getAllAgentSlugs,
  getAllAgentEntries,
  isAgentRegistered,
  getAgentsByTaskCategory
} from '../agentRegistry';
import {
  AgentNotFoundError,
  DuplicateAgentSlugError,
  AgentInstantiationError
} from '../types/agentRegistry.types';
import { AgentDependencies, BaseAgentInterface } from '../types/agents.types';
import { LLMTaskCategory } from '../../graph/types/graph.types';
import { BaseAgent } from '../base/BaseAgent';

// Mock dependencies for agent instantiation
const mockAgentDependencies: AgentDependencies = {
  llmService: {
    invokeModel: vi.fn(),
    generateStructuredObject: vi.fn()
  },
  promptBuilder: {
    buildPromptMessagesForAgentCall: vi.fn()
  },
  nodeSpecService: {
    getNodeSpec: vi.fn(),
    findNodes: vi.fn(),
    getAllNodeDescriptors: vi.fn()
  },
  toolExecutor: {
    executeToolCall: vi.fn()
  }
};

// Test agent schema
const TestAgentOutputSchema = z.object({
  result: z.string(),
  data: z.record(z.any())
});

// Mock agent class for testing registry functionality
class MockTestAgent extends BaseAgent<any, typeof TestAgentOutputSchema> {
  static readonly agentSlug = 'mock-test-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  static readonly description = 'A mock agent for testing registry functionality';
  static readonly roleDescription = 'Test agent for unit testing purposes';

  async execute() {
    return this.success({
      thought: 'Mock agent executed successfully',
      payload: {
        result: 'test-result',
        data: { test: true }
      }
    });
  }

  getFinalOutputSchema() {
    return TestAgentOutputSchema;
  }
}

// Mock agent class that throws in constructor
class FailingConstructorAgent extends BaseAgent<any, typeof TestAgentOutputSchema> {
  static readonly agentSlug = 'failing-constructor-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE;

  constructor(dependencies: AgentDependencies) {
    super(dependencies);
    throw new Error('Constructor intentionally fails');
  }

  async execute() {
    return this.failure({ reason: 'Should never execute' });
  }

  getFinalOutputSchema() {
    return TestAgentOutputSchema;
  }
}

// Mock agent class with factory
class FactoryTestAgent extends BaseAgent<any, typeof TestAgentOutputSchema> {
  static readonly agentSlug = 'factory-test-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION;

  private customSetup = false;

  async execute() {
    return this.success({
      thought: 'Factory agent executed',
      payload: {
        result: 'factory-result',
        data: { customSetup: this.customSetup }
      }
    });
  }

  getFinalOutputSchema() {
    return TestAgentOutputSchema;
  }

  performCustomSetup() {
    this.customSetup = true;
  }

  getCustomSetupStatus() {
    return this.customSetup;
  }
}

describe('Agent Registry', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Registry Configuration', () => {
    it('should initialize AGENT_REGISTRY_CONFIG as an array', () => {
      expect(Array.isArray(AGENT_REGISTRY_CONFIG)).toBe(true);
    });

    it('should initialize agentRegistryMap as a Map', () => {
      expect(agentRegistryMap instanceof Map).toBe(true);
    });

    it('should have agentRegistryMap entries matching AGENT_REGISTRY_CONFIG length', () => {
      expect(agentRegistryMap.size).toBe(AGENT_REGISTRY_CONFIG.length);
    });

    it('should not have frozen agentRegistryMap for testing purposes', () => {
      expect(Object.isFrozen(agentRegistryMap)).toBe(false);
    });
  });

  describe('getAgentConfig()', () => {
    it('should return agent config when slug exists', () => {
      // Since the actual registry is empty in the current implementation,
      // we'll test with the mock registry map
      const mockEntrySlug = 'test-agent';
      const mockEntry = {
        slug: mockEntrySlug,
        description: 'Test agent',
        constructor: MockTestAgent,
        defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      };

      // Temporarily add to registry for testing
      const originalGet = agentRegistryMap.get;
      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      const result = getAgentConfig(mockEntrySlug);
      expect(result).toEqual(mockEntry);

      agentRegistryMap.get = originalGet;
    });

    it('should return undefined when slug does not exist', () => {
      const result = getAgentConfig('non-existent-agent');
      expect(result).toBeUndefined();
    });

    it('should return undefined for empty string slug', () => {
      const result = getAgentConfig('');
      expect(result).toBeUndefined();
    });
  });

  describe('createAgentInstance()', () => {
    it('should throw AgentNotFoundError for unknown slug', () => {
      expect(() => {
        createAgentInstance('unknown-agent', mockAgentDependencies);
      }).toThrow(AgentNotFoundError);
    });

    it('should create agent instance successfully with constructor', () => {
      const mockEntry = {
        slug: 'mock-test-agent',
        constructor: MockTestAgent,
        defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      };

      // Mock the map get method to return our test entry
      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      const instance = createAgentInstance('mock-test-agent', mockAgentDependencies);
      
      expect(instance).toBeInstanceOf(MockTestAgent);
      expect(instance.agentSlug).toBe('mock-test-agent');
    });

    it('should create agent instance successfully with factory', () => {
      const mockFactory = vi.fn((deps: AgentDependencies) => {
        const instance = new FactoryTestAgent(deps);
        instance.performCustomSetup();
        return instance;
      });

      const mockEntry = {
        slug: 'factory-test-agent',
        constructor: FactoryTestAgent,
        defaultLlmTaskCategory: LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION,
        factory: mockFactory
      };

      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      const instance = createAgentInstance('factory-test-agent', mockAgentDependencies);
      
      expect(mockFactory).toHaveBeenCalledWith(mockAgentDependencies);
      expect(instance).toBeInstanceOf(FactoryTestAgent);
      expect((instance as FactoryTestAgent).getCustomSetupStatus()).toBe(true);
    });

    it('should throw AgentInstantiationError when constructor fails', () => {
      const mockEntry = {
        slug: 'failing-constructor-agent',
        constructor: FailingConstructorAgent,
        defaultLlmTaskCategory: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
      };

      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      expect(() => {
        createAgentInstance('failing-constructor-agent', mockAgentDependencies);
      }).toThrow(AgentInstantiationError);
    });

    it('should throw AgentInstantiationError when factory fails', () => {
      const failingFactory = vi.fn(() => {
        throw new Error('Factory intentionally fails');
      });

      const mockEntry = {
        slug: 'failing-factory-agent',
        constructor: FactoryTestAgent,
        defaultLlmTaskCategory: LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION,
        factory: failingFactory
      };

      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      expect(() => {
        createAgentInstance('failing-factory-agent', mockAgentDependencies);
      }).toThrow(AgentInstantiationError);
    });

    it('should handle null dependencies gracefully', () => {
      const mockEntry = {
        slug: 'test-agent',
        constructor: MockTestAgent,
        defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      };

      vi.spyOn(agentRegistryMap, 'get').mockReturnValue(mockEntry);

      // Our BaseAgent constructor accepts null dependencies without throwing
      // So this test should reflect the actual behavior - agent creation succeeds
      // but the agent might fail later when trying to use the dependencies
      expect(() => {
        const instance = createAgentInstance('test-agent', null as any);
        expect(instance).toBeInstanceOf(MockTestAgent);
      }).not.toThrow();
    });
  });

  describe('validateAgentRegistry()', () => {
    it('should not throw when registry has no duplicate slugs', () => {
      // Mock AGENT_REGISTRY_CONFIG with unique slugs
      const originalConfig = [...AGENT_REGISTRY_CONFIG];
      AGENT_REGISTRY_CONFIG.length = 0;
      AGENT_REGISTRY_CONFIG.push(
        {
          slug: 'agent-1',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
        },
        {
          slug: 'agent-2',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
        }
      );

      expect(() => {
        validateAgentRegistry();
      }).not.toThrow();

      // Restore original config
      AGENT_REGISTRY_CONFIG.length = 0;
      AGENT_REGISTRY_CONFIG.push(...originalConfig);
    });

    it('should throw DuplicateAgentSlugError when duplicates exist', () => {
      // Mock AGENT_REGISTRY_CONFIG with duplicate slugs
      const originalConfig = [...AGENT_REGISTRY_CONFIG];
      AGENT_REGISTRY_CONFIG.length = 0;
      AGENT_REGISTRY_CONFIG.push(
        {
          slug: 'duplicate-agent',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
        },
        {
          slug: 'duplicate-agent',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
        }
      );

      expect(() => {
        validateAgentRegistry();
      }).toThrow(DuplicateAgentSlugError);

      // Restore original config
      AGENT_REGISTRY_CONFIG.length = 0;
      AGENT_REGISTRY_CONFIG.push(...originalConfig);
    });
  });

  describe('Utility Functions', () => {
    beforeEach(() => {
      // Mock some entries in the registry map for testing
      const mockEntries = [
        {
          slug: 'test-agent-1',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
        },
        {
          slug: 'test-agent-2',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
        },
        {
          slug: 'test-agent-3',
          constructor: MockTestAgent,
          defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
        }
      ];

      vi.spyOn(agentRegistryMap, 'keys').mockReturnValue(
        mockEntries.map(e => e.slug).values()
      );
      vi.spyOn(agentRegistryMap, 'values').mockReturnValue(
        mockEntries.values()
      );
      vi.spyOn(agentRegistryMap, 'has').mockImplementation(
        (slug: string) => mockEntries.some(e => e.slug === slug)
      );
    });

    describe('getAllAgentSlugs()', () => {
      it('should return array of all agent slugs', () => {
        const slugs = getAllAgentSlugs();
        expect(Array.isArray(slugs)).toBe(true);
        expect(slugs).toContain('test-agent-1');
        expect(slugs).toContain('test-agent-2');
        expect(slugs).toContain('test-agent-3');
      });
    });

    describe('getAllAgentEntries()', () => {
      it('should return array of all agent entries', () => {
        const entries = getAllAgentEntries();
        expect(Array.isArray(entries)).toBe(true);
        expect(entries.length).toBe(3);
        expect(entries.every(entry => entry.slug && entry.constructor)).toBe(true);
      });
    });

    describe('isAgentRegistered()', () => {
      it('should return true for registered agent', () => {
        expect(isAgentRegistered('test-agent-1')).toBe(true);
      });

      it('should return false for unregistered agent', () => {
        expect(isAgentRegistered('non-existent-agent')).toBe(false);
      });

      it('should return false for empty string', () => {
        expect(isAgentRegistered('')).toBe(false);
      });
    });

    describe('getAgentsByTaskCategory()', () => {
      it('should return agents filtered by task category', () => {
        const planningAgents = getAgentsByTaskCategory(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING);
        expect(planningAgents.length).toBe(2);
        expect(planningAgents.every(agent => 
          agent.defaultLlmTaskCategory === LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
        )).toBe(true);
      });

      it('should return empty array for category with no agents', () => {
        const utilityAgents = getAgentsByTaskCategory(LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING);
        expect(utilityAgents.length).toBe(0);
      });
    });
  });

  describe('Error Classes', () => {
    describe('AgentNotFoundError', () => {
      it('should create error with correct message and slug', () => {
        const error = new AgentNotFoundError('missing-agent');
        expect(error.message).toContain('missing-agent');
        expect(error.slug).toBe('missing-agent');
        expect(error.name).toBe('AgentNotFoundError');
      });
    });

    describe('DuplicateAgentSlugError', () => {
      it('should create error with correct message and slug', () => {
        const error = new DuplicateAgentSlugError('duplicate-slug');
        expect(error.message).toContain('duplicate-slug');
        expect(error.slug).toBe('duplicate-slug');
        expect(error.name).toBe('DuplicateAgentSlugError');
      });
    });

    describe('AgentInstantiationError', () => {
      it('should create error with correct message, slug, and original error', () => {
        const originalError = new Error('Original error message');
        const error = new AgentInstantiationError('failing-agent', 'Custom message', originalError);
        
        expect(error.message).toContain('failing-agent');
        expect(error.message).toContain('Custom message');
        expect(error.slug).toBe('failing-agent');
        expect(error.originalError).toBe(originalError);
        expect(error.name).toBe('AgentInstantiationError');
      });
    });
  });

  describe('Edge Cases', () => {
    beforeEach(() => {
      vi.restoreAllMocks();
    });

    it('should handle undefined slug in getAgentConfig', () => {
      expect(getAgentConfig(undefined as any)).toBeUndefined();
    });

    it('should handle special characters in slug', () => {
      const specialSlug = 'agent-with-special-chars-!@#$%';
      expect(getAgentConfig(specialSlug)).toBeUndefined();
    });
  });
}); 