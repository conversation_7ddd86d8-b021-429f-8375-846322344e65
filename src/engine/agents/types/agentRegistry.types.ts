/**
 * Agent Registry Types
 * 
 * This file defines TypeScript interfaces and types for the agent registry system.
 * The registry provides centralized management and lookup of AI agent classes,
 * enabling dynamic agent instantiation based on string slugs.
 */

import { LLMTaskCategory } from '../../graph/types/graph.types';
import { AgentDependencies, AgentConstructor, BaseAgentInterface } from './agents.types';
// TODO: Remove when replacing with AgentPromptBuilder
// import type { AgentTemplates } from '../../../prompts/types/templates.types';

// Added from previous attempt, as it's needed for AgentRegistryEntry
export interface LLMModelConfig {
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
  // other model-specific params
}

/**
 * Registry entry for a single agent
 * Contains all metadata and instantiation information for an agent
 */
export interface AgentRegistryEntry<TInput = any, TOutput = any> {
  /** Unique slug identifier for the agent */
  slug: string;
  
  /** Brief description of the agent's purpose (for UI and team awareness) */
  description?: string;
  
  /** Detailed role description for prompts and documentation */
  roleDescription?: string;
  
  /** Constructor function for the agent class */
  constructor: AgentConstructor<TInput, TOutput>;
  
  /** Default LLM task category for this agent */
  defaultLlmTaskCategory: LLMTaskCategory;
  
  /** Default model configuration for this agent's LLM calls */
  defaultModelConfig?: LLMModelConfig;
  
  /** 
   * Agent-specific prompt templates
   * If provided, these templates will be registered with the TemplateRegistry
   * during agent initialization
   */
  // templates?: AgentTemplates; // TODO: Remove when replacing with AgentPromptBuilder
  
  /** 
   * Optional factory function for complex instantiation
   * If provided, this will be used instead of the constructor
   * Useful for agents that need custom setup beyond standard dependency injection
   */
  factory?: (dependencies: AgentDependencies) => BaseAgentInterface<TInput, TOutput>;
}

/**
 * Configuration array type for all registered agents
 * This is the single source of truth for available agents
 */
export type AgentRegistryConfig = AgentRegistryEntry[];

/**
 * Map type for efficient agent lookup by slug
 * Derived from AgentRegistryConfig for O(1) runtime lookups
 */
export type AgentRegistryMap = Map<string, AgentRegistryEntry>;

/**
 * Error classes for agent registry operations
 */
export class AgentNotFoundError extends Error {
  constructor(public readonly slug: string) {
    super(`Agent with slug "${slug}" not found in registry`);
    this.name = 'AgentNotFoundError';
  }
}

export class DuplicateAgentSlugError extends Error {
  constructor(public readonly slug: string) {
    super(`Duplicate agent slug detected: "${slug}". Each agent must have a unique slug.`);
    this.name = 'DuplicateAgentSlugError';
  }
}

export class AgentInstantiationError extends Error {
  constructor(
    public readonly slug: string,
    message: string,
    public readonly originalError?: any
  ) {
    super(`Failed to instantiate agent "${slug}": ${message}`);
    this.name = 'AgentInstantiationError';
  }
} 