/**
 * Agent Types
 * 
 * This file defines the core TypeScript interfaces and types for the AI agent system.
 * These types provide comprehensive coverage for agent dependencies, execution results,
 * and interaction patterns between agents, the GraphExecutor, and the UI.
 */

import { z, ZodTypeAny } from 'zod';
import { ModelMessage } from 'ai';
import { LLMTokenUsage } from '../../llm/types/llmService.types';
import { LLMTaskCategory } from '../../graph/types/graph.types';
import { GraphExecutionContext } from '../../graph/types/execution.types';
import { TaskSession } from '../../../types/tasks.types';

// Forward declarations for services - these will be imported from their respective files
export interface LLMService {
  invokeModel: (...args: any[]) => Promise<any>;
  generateStructuredObject: (...args: any[]) => Promise<any>;
}

export interface PromptBuilderService {
  buildPromptMessagesForAgentCall: (...args: any[]) => Promise<any>;
}

export interface NodeSpecService {
  getNodeSpec: (...args: any[]) => Promise<any>;
  findNodes: (...args: any[]) => Promise<any>;
  getAllNodeDescriptors: (...args: any[]) => Promise<any>;
}

export interface ToolExecutorService {
  executeToolCall: (...args: any[]) => Promise<any>;
}

/**
 * Services injected into every agent instance
 */
export interface AgentDependencies {
  llmService: LLMService;
  promptBuilder: PromptBuilderService;
  nodeSpecService: NodeSpecService;
  toolExecutor?: ToolExecutorService; // Optional if not all agents need tool access
  // Future: additional services or repositories can be added here
}

/**
 * Payload structure for successful agent execution
 */
export interface SuccessPayload<TOutput> {
  thought: string;
  payload: TOutput;
}

/**
 * Details for requesting user clarification (Human-in-the-Loop)
 */
export interface ClarificationRequestDetails {
  interruptId?: string; // Optional: agent can suggest an ID, or Orchestrator generates one
  question_for_user: string;
  suggested_replies?: string[];
  expected_response_schema_id?: string; // Key to a registered Zod schema for user's response
  context_for_ui?: Record<string, any>; // Additional data to help UI render the clarification request
}

/**
 * Details for requesting tool execution by the GraphExecutor
 */
export interface ToolCallRequestDetails {
  needed_tool_calls: {
    tool_name: string;
    tool_args: Record<string, any>;
    // Optional: A unique ID for this specific tool call request within the agent's turn
    // Useful if agent requests multiple tools and needs to map results back.
    tool_call_request_id?: string; 
  }[];
  reason_for_tool_call?: string; // Why the agent needs these tools
  // State the agent wants to preserve to resume its logic after tool execution
  intermediate_agent_state_for_resume?: any; 
}

/**
 * Standardized output structure from an agent's execute method
 * This is what the GraphExecutor expects from every agent call
 */
export interface AgentExecutionResult<TOutputPayload = any> {
  status: 'success' | 'failure' | 'needs_clarification' | 'needs_tool_calls';
  
  // For 'success' status
  result?: {
    thought: string;
    payload: TOutputPayload; // Must match agent's getFinalOutputSchema()
  };
  
  // For 'failure' status
  errorDetails?: {
    message: string;
    type?: string; // Agent-specific error type
    originalError?: any;
  };
  
  // For 'needs_clarification' or 'needs_tool_calls' status
  // Using a union type for better type safety
  clarificationOrToolRequestDetails?: ClarificationRequestDetails | ToolCallRequestDetails;
  
  // Common across all results (populated by LLMService via agent's use)
  tokenUsage?: LLMTokenUsage; 
  cost?: number;
}

/**
 * Simplified view of TaskSession for agent context
 * This prevents agents from accessing sensitive TaskSession fields they shouldn't modify
 */
export interface AgentTaskSessionView {
  id: string;
  llmContextSummary?: string;
  llmContextRelevantHistory?: ModelMessage[];
}

/**
 * Base interface for all AI agents
 * This is the contract that all concrete agent classes must implement
 */
export interface BaseAgentInterface<TInputParams = any, TOutputPayload = any> {
  readonly agentSlug: string;
  
  execute(
    inputs: TInputParams, 
    graphContext: GraphExecutionContext, 
    taskSession: TaskSession
  ): Promise<AgentExecutionResult<TOutputPayload>>;
  
  getFinalOutputSchema(): ZodTypeAny;
}

/**
 * Type for agent constructors in the registry
 */
export type AgentConstructor<TInput = any, TOutput = any> = 
  new (dependencies: AgentDependencies) => BaseAgentInterface<TInput, TOutput>;

/**
 * Error classes for agent-specific errors
 */
export class AgentValidationError extends Error {
  constructor(
    public readonly agentSlug: string,
    message: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'AgentValidationError';
  }
}

export class AgentOutputSchemaValidationError extends AgentValidationError {
  constructor(
    agentSlug: string,
    public readonly expectedSchema: string,
    public readonly actualPayload: any,
    originalError?: any
  ) {
    super(
      agentSlug, 
      `Agent ${agentSlug} produced a success payload that failed schema validation`,
      originalError
    );
    this.name = 'AgentOutputSchemaValidationError';
  }
}

export class AgentSlugNotOverriddenError extends Error {
  constructor(className: string) {
    super(`Agent class ${className} must override static readonly agentSlug property`);
    this.name = 'AgentSlugNotOverriddenError';
  }
} 