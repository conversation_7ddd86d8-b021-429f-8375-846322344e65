/**
 * BaseAgent Tests
 * 
 * Comprehensive unit tests for the BaseAgent abstract class.
 * Tests cover static property validation, constructor behavior, helper methods,
 * schema validation, and error handling.
 */

import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest';
import { z } from 'zod';
import { BaseAgent } from '../BaseAgent';
import {
  AgentDependencies,
  AgentExecutionResult,
  AgentSlugNotOverriddenError,
  AgentOutputSchemaValidationError
} from '../../types/agents.types';
import { LLMTaskCategory } from '../../../graph/types/graph.types';
import { GraphExecutionContext } from '../../../graph/types/execution.types';
import { TaskSession } from '../../../../types/tasks.types';
import type { FunctionRegistry } from '../../../graph/types/builder.types';

// Mock dependencies
const mockLLMService = {
  invokeModel: vi.fn(),
  generateStructuredObject: vi.fn()
};

const mockPromptBuilderService = {
  buildPromptMessagesForAgentCall: vi.fn()
};

const mockNodeSpecService = {
  getNodeSpec: vi.fn(),
  findNodes: vi.fn(),
  getAllNodeDescriptors: vi.fn()
};

const mockToolExecutorService = {
  executeToolCall: vi.fn()
};

const mockAgentDependencies: AgentDependencies = {
  llmService: mockLLMService,
  promptBuilder: mockPromptBuilderService,
  nodeSpecService: mockNodeSpecService,
  toolExecutor: mockToolExecutorService
};

// Mock FunctionRegistry
const mockFunctionRegistry: FunctionRegistry = {
  'test-function': vi.fn()
};

// Mock GraphExecutionContext
const mockGraphContext: GraphExecutionContext = {
  graphDefinitionId: 'test-graph',
  graphDefinitionVersion: '1.0.0',
  taskSessionId: 'test-session',
  currentGraphRunId: 'test-run',
  status: 'RUNNING',
  graphDefinition: {} as any,
  functionRegistry: mockFunctionRegistry,
  initialInputs: {},
  nodeOutputs: new Map(),
  currentNodeIdBeingProcessed: null,
  tokenUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0, byNode: {} },
  services: {}
};

// Mock TaskSession
const mockTaskSession: TaskSession = {
  id: 'test-session',
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date()
};

// Test input and output schemas
const TestInputSchema = z.object({
  message: z.string(),
  priority: z.number()
});

const TestOutputSchema = z.object({
  result: z.string(),
  confidence: z.number()
});

type TestInput = z.infer<typeof TestInputSchema>;
type TestOutput = z.infer<typeof TestOutputSchema>;

// Test agent implementation that properly overrides static properties
class ValidTestAgent extends BaseAgent<TestInput, typeof TestOutputSchema> {
  static readonly agentSlug = 'valid-test-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE;
  static readonly description = 'A test agent for unit testing';
  static readonly roleDescription = 'Test agent that validates functionality';

  async execute(
    inputs: TestInput,
    graphContext: GraphExecutionContext,
    taskSession: TaskSession
  ): Promise<AgentExecutionResult<TestOutput>> {
    return this.success({
      thought: 'Test execution complete',
      payload: {
        result: `Processed: ${inputs.message}`,
        confidence: inputs.priority * 0.1
      }
    });
  }

  getFinalOutputSchema() {
    return TestOutputSchema;
  }
}

// Test agent that doesn't override agentSlug (should cause error)
class InvalidTestAgent extends BaseAgent<TestInput, typeof TestOutputSchema> {
  // Intentionally not overriding agentSlug to test error handling

  async execute(
    inputs: TestInput,
    graphContext: GraphExecutionContext,
    taskSession: TaskSession
  ): Promise<AgentExecutionResult<TestOutput>> {
    return this.success({
      thought: 'This should never execute',
      payload: { result: 'test', confidence: 1.0 }
    });
  }

  getFinalOutputSchema() {
    return TestOutputSchema;
  }
}

// Test agent that returns invalid payload (for schema validation testing)
class InvalidPayloadTestAgent extends BaseAgent<TestInput, typeof TestOutputSchema> {
  static readonly agentSlug = 'invalid-payload-test-agent';

  async execute(
    inputs: TestInput,
    graphContext: GraphExecutionContext,
    taskSession: TaskSession
  ): Promise<AgentExecutionResult<TestOutput>> {
    // Return payload that doesn't match schema
    return this.success({
      thought: 'Test with invalid payload',
      payload: {
        wrongField: 'this should fail validation'
      } as any
    });
  }

  getFinalOutputSchema() {
    return TestOutputSchema;
  }
}

describe('BaseAgent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Constructor and Static Properties', () => {
    it('should successfully create an instance when agentSlug is properly overridden', () => {
      expect(() => {
        const agent = new ValidTestAgent(mockAgentDependencies);
        expect(agent.agentSlug).toBe('valid-test-agent');
      }).not.toThrow();
    });

    it('should throw AgentSlugNotOverriddenError when agentSlug is not overridden', () => {
      expect(() => {
        new InvalidTestAgent(mockAgentDependencies);
      }).toThrow(AgentSlugNotOverriddenError);
    });

    it('should correctly access static properties from the class', () => {
      expect(ValidTestAgent.agentSlug).toBe('valid-test-agent');
      expect(ValidTestAgent.defaultLlmTaskCategory).toBe(LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE);
      expect(ValidTestAgent.description).toBe('A test agent for unit testing');
      expect(ValidTestAgent.roleDescription).toBe('Test agent that validates functionality');
    });

    it('should have default static properties when not overridden', () => {
      class MinimalTestAgent extends BaseAgent<any, any> {
        static readonly agentSlug = 'minimal-test-agent';

        async execute() {
          return this.success({ thought: 'test', payload: {} });
        }

        getFinalOutputSchema() {
          return z.object({});
        }
      }

      expect(MinimalTestAgent.defaultLlmTaskCategory).toBe(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING);
      expect(MinimalTestAgent.description).toBeUndefined();
      expect(MinimalTestAgent.roleDescription).toBeUndefined();
    });
  });

  describe('Helper Methods', () => {
    let agent: ValidTestAgent;

    beforeEach(() => {
      agent = new ValidTestAgent(mockAgentDependencies);
    });

    describe('success()', () => {
      it('should return a properly structured success result with valid payload', () => {
        const result = agent['success']({
          thought: 'Test thought',
          payload: { result: 'test result', confidence: 0.9 }
        });

        expect(result).toEqual({
          status: 'success',
          result: {
            thought: 'Test thought',
            payload: { result: 'test result', confidence: 0.9 }
          }
        });
      });

      it('should validate payload against schema and return failure for invalid payload', () => {
        const result = agent['success']({
          thought: 'Test thought',
          payload: { wrongField: 'invalid' } as any
        });

        expect(result.status).toBe('failure');
        expect(result.errorDetails?.type).toBe('OutputSchemaValidationError');
        expect(result.errorDetails?.message).toContain('schema validation');
      });
    });

    describe('failure()', () => {
      it('should return a properly structured failure result', () => {
        const result = agent['failure']({
          reason: 'Test failure reason',
          errorType: 'TestError',
          originalError: new Error('Original error')
        });

        expect(result).toEqual({
          status: 'failure',
          errorDetails: {
            message: 'Test failure reason',
            type: 'TestError',
            originalError: expect.any(Error)
          }
        });
      });

      it('should use default error type when not provided', () => {
        const result = agent['failure']({
          reason: 'Test failure reason'
        });

        expect(result.errorDetails?.type).toBe('AgentProcessingError');
      });
    });

    describe('requestUserClarification()', () => {
      it('should return a properly structured clarification request', () => {
        const clarificationDetails = {
          question_for_user: 'Please clarify your requirements',
          suggested_replies: ['Option A', 'Option B'],
          expected_response_schema_id: 'user-choice-schema'
        };

        const result = agent['requestUserClarification'](clarificationDetails);

        expect(result).toEqual({
          status: 'needs_clarification',
          clarificationOrToolRequestDetails: clarificationDetails
        });
      });
    });

    describe('requestGraphTools()', () => {
      it('should return a properly structured tool request', () => {
        const toolRequestDetails = {
          needed_tool_calls: [
            {
              tool_name: 'test-tool',
              tool_args: { param1: 'value1' },
              tool_call_request_id: 'req-123'
            }
          ],
          reason_for_tool_call: 'Need to fetch external data',
          intermediate_agent_state_for_resume: { step: 'data-gathering' }
        };

        const result = agent['requestGraphTools'](toolRequestDetails);

        expect(result).toEqual({
          status: 'needs_tool_calls',
          clarificationOrToolRequestDetails: toolRequestDetails
        });
      });
    });

    describe('getJsonSchemaString()', () => {
      it('should convert Zod schema to JSON schema string', () => {
        const schema = z.object({
          name: z.string(),
          age: z.number().optional()
        });

        const jsonSchemaString = agent['getJsonSchemaString'](schema, 'TestSchema');

        expect(jsonSchemaString).toBeTruthy();
        expect(() => JSON.parse(jsonSchemaString)).not.toThrow();
        
        const parsedSchema = JSON.parse(jsonSchemaString);
        // The actual structure from zod-to-json-schema may vary, so let's test what's actually there
        expect(parsedSchema).toBeDefined();
        expect(typeof parsedSchema).toBe('object');
        // More flexible test - just ensure it's a valid schema object
        expect(parsedSchema.$schema || parsedSchema.type || parsedSchema.properties).toBeDefined();
      });

      it('should handle schema conversion errors gracefully', () => {
        // Mock zodToJsonSchema to throw an error
        const originalConsoleError = console.error;
        console.error = vi.fn();

        // Create a problematic schema or mock the conversion failure
        const mockSchema = {} as any;
        
        const result = agent['getJsonSchemaString'](mockSchema);

        expect(console.error).toHaveBeenCalled();
        expect(result).toContain('Schema conversion failed');

        console.error = originalConsoleError;
      });
    });
  });

  describe('Integration Testing', () => {
    it('should execute a complete agent workflow successfully', async () => {
      const agent = new ValidTestAgent(mockAgentDependencies);
      
      const testInput: TestInput = {
        message: 'Test message',
        priority: 5
      };

      const result = await agent.execute(testInput, mockGraphContext, mockTaskSession);

      expect(result.status).toBe('success');
      expect(result.result?.thought).toBe('Test execution complete');
      expect(result.result?.payload.result).toBe('Processed: Test message');
      expect(result.result?.payload.confidence).toBe(0.5);
    });

    it('should handle schema validation failure in real execution', async () => {
      const agent = new InvalidPayloadTestAgent(mockAgentDependencies);
      
      const testInput: TestInput = {
        message: 'Test message',
        priority: 1
      };

      const result = await agent.execute(testInput, mockGraphContext, mockTaskSession);

      expect(result.status).toBe('failure');
      expect(result.errorDetails?.type).toBe('OutputSchemaValidationError');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle dependencies being undefined gracefully', () => {
      expect(() => {
        new ValidTestAgent(undefined as any);
      }).not.toThrow(); // Constructor should accept the dependency even if it's malformed
    });

    it('should handle complex nested schemas correctly', () => {
      const complexSchema = z.object({
        data: z.object({
          items: z.array(z.object({
            id: z.string(),
            metadata: z.record(z.any())
          }))
        }),
        timestamp: z.date()
      });

      class ComplexTestAgent extends BaseAgent<any, typeof complexSchema> {
        static readonly agentSlug = 'complex-test-agent';

        async execute() {
          return this.success({
            thought: 'Complex data processed',
            payload: {
              data: {
                items: [
                  { id: '1', metadata: { key: 'value' } }
                ]
              },
              timestamp: new Date()
            }
          });
        }

        getFinalOutputSchema() {
          return complexSchema;
        }
      }

      const agent = new ComplexTestAgent(mockAgentDependencies);
      expect(agent.agentSlug).toBe('complex-test-agent');
    });
  });
}); 