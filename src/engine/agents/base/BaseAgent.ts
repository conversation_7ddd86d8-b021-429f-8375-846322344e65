/**
 * Base Agent
 * 
 * This abstract base class serves as the foundation for all specialized AI agents 
 * within the n8n AI Assistant system. It provides common functionalities, standardizes 
 * agent structure, and enforces essential contracts that all concrete agents must follow.
 * 
 * Key Responsibilities:
 * - Define the core interface for an agent's execution logic
 * - Manage injected dependencies (LLMService, PromptBuilderService, etc.)
 * - Provide standardized response helpers for different outcomes
 * - Enforce implementation of execute() and getFinalOutputSchema() methods
 * - Validate agent output against declared schemas
 * 
 * Dependencies:
 * - AgentDependencies (injected services)
 * - Zod (for schema validation)
 * - zod-to-json-schema (for utility method)
 * 
 * Usage Example:
 * ```typescript
 * class MyAgent extends BaseAgent<MyInputType, z.ZodType<MyOutputType>> {
 *   static readonly agentSlug = 'my-agent';
 *   static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
 *   
 *   async execute(inputs, graphContext, taskSession) {
 *     // Implementation
 *     return this.success({ thought: "...", payload: result });
 *   }
 *   
 *   getFinalOutputSchema() {
 *     return MyOutputSchema;
 *   }
 * }
 * ```
 */

import { z, ZodTypeAny } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { 
  AgentDependencies, 
  AgentExecutionResult, 
  SuccessPayload,
  ClarificationRequestDetails,
  ToolCallRequestDetails,
  BaseAgentInterface,
  AgentOutputSchemaValidationError,
  AgentSlugNotOverriddenError
} from '../types/agents.types';
import { LLMTaskCategory } from '../../graph/types/graph.types';
import { GraphExecutionContext } from '../../graph/types/execution.types';
import { TaskSession } from '../../../types/tasks.types';

/**
 * Abstract base class for all AI agents
 * 
 * This class provides the common structure and utilities that all concrete agents need,
 * while enforcing the implementation of key methods through TypeScript's abstract class features.
 * 
 * @template TInputParams - The type of inputs this agent expects from the graph
 * @template TOutputPayloadType - The Zod schema type for this agent's successful output payload
 */
export abstract class BaseAgent<TInputParams, TOutputPayloadType extends ZodTypeAny> 
  implements BaseAgentInterface<TInputParams, z.infer<TOutputPayloadType>> {
  
  // === Static Properties (Must be overridden by concrete subclasses) ===
  
  /** 
   * Unique identifier for this agent type
   * Must be overridden by concrete subclasses
   */
  static readonly agentSlug: string = 'override_me_in_subclass';
  
  /** 
   * Default LLM task category for this agent
   * Can be overridden by concrete subclasses
   */
  static readonly defaultLlmTaskCategory: LLMTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  
  /** 
   * Optional brief description of the agent's purpose
   * Used for team awareness and UI display
   */
  static readonly description?: string;
  
  /** 
   * Optional detailed role description for prompts
   * Used by PromptBuilderService for context
   */
  static readonly roleDescription?: string;

  // === Instance Properties ===
  
  /** Instance-level agent slug (populated from static property) */
  public readonly agentSlug: string;
  
  /** Injected service dependencies */
  protected readonly deps: AgentDependencies;

  // === Constructor ===
  
  /**
   * Creates a new BaseAgent instance
   * 
   * @param dependencies - Services required by the agent (LLMService, PromptBuilderService, etc.)
   * @throws {AgentSlugNotOverriddenError} If the concrete class didn't override agentSlug
   */
  constructor(dependencies: AgentDependencies) {
    const staticThis = this.constructor as typeof BaseAgent;
    
    // Validate that the concrete class overrode the agentSlug
    if (staticThis.agentSlug === 'override_me_in_subclass') {
      throw new AgentSlugNotOverriddenError(staticThis.name);
    }
    
    this.agentSlug = staticThis.agentSlug;
    this.deps = dependencies;
  }

  // === Abstract Methods (Must be implemented by concrete subclasses) ===
  
  /**
   * Core execution method for the agent's primary task
   * 
   * This method contains the agent's main logic and typically involves:
   * 1. Processing the provided inputs
   * 2. Making LLM calls via this.deps.llmService
   * 3. Using this.deps.promptBuilder for complex prompts
   * 4. Returning a standardized result using helper methods
   * 
   * @param inputs - Resolved inputs for this agent from the graph
   * @param graphContext - Runtime context of the graph execution
   * @param taskSession - Overall task session context and history
   * @returns Promise resolving to standardized execution result
   */
  abstract execute(
    inputs: TInputParams, 
    graphContext: GraphExecutionContext, 
    taskSession: TaskSession
  ): Promise<AgentExecutionResult<z.infer<TOutputPayloadType>>>;

  /**
   * Returns the Zod schema for this agent's successful output payload
   * 
   * This schema is used for:
   * - Runtime validation of success payloads
   * - Generating JSON schema for LLM prompts
   * - Type safety throughout the system
   * 
   * @returns Zod schema that validates the agent's output payload
   */
  abstract getFinalOutputSchema(): TOutputPayloadType;

  // === Protected Helper Methods for Standardized Responses ===
  
  /**
   * Creates a standardized success response
   * 
   * Automatically validates the payload against the agent's declared schema
   * and returns an appropriate result structure for the GraphExecutor.
   * 
   * @param data - Success data containing thought and payload
   * @returns Standardized success result with validated payload
   */
  protected success(data: SuccessPayload<z.infer<TOutputPayloadType>>): AgentExecutionResult<z.infer<TOutputPayloadType>> {
    // Runtime validation against the agent's declared output schema
    try {
      this.getFinalOutputSchema().parse(data.payload);
    } catch (error: any) {
      console.error(`Agent ${this.agentSlug} SUCCESS payload validation failed:`, error);
      
      // Return a failure result instead of throwing
      return this.failure({
        reason: `Internal: Agent ${this.agentSlug} produced a success payload that failed its own schema validation.`,
        errorType: 'OutputSchemaValidationError',
        originalError: new AgentOutputSchemaValidationError(
          this.agentSlug,
          this.getJsonSchemaString(this.getFinalOutputSchema()),
          data.payload,
          error
        )
      });
    }
    
    return {
      status: 'success',
      result: {
        thought: data.thought,
        payload: data.payload,
      },
    };
  }

  /**
   * Creates a standardized failure response
   * 
   * @param details - Failure details including reason and optional error info
   * @returns Standardized failure result for the GraphExecutor
   */
  protected failure(details: { reason: string; errorType?: string; originalError?: any }): AgentExecutionResult<never> {
    return {
      status: 'failure',
      errorDetails: {
        message: details.reason,
        type: details.errorType || 'AgentProcessingError',
        originalError: details.originalError,
      },
    };
  }

  /**
   * Creates a standardized user clarification request
   * 
   * This signals to the GraphExecutor that the agent needs human input
   * to continue processing. The graph execution will pause until the user responds.
   * 
   * @param details - Clarification request details for the UI
   * @returns Standardized clarification request result
   */
  protected requestUserClarification(details: ClarificationRequestDetails): AgentExecutionResult<never> {
    return {
      status: 'needs_clarification',
      clarificationOrToolRequestDetails: details,
    };
  }

  /**
   * Creates a standardized tool execution request
   * 
   * This signals to the GraphExecutor that the agent needs specific tools
   * to be executed before it can continue. The GraphExecutor will handle
   * the tool execution and provide results back to the agent.
   * 
   * @param details - Tool request details including tool names and arguments
   * @returns Standardized tool request result
   */
  protected requestGraphTools(details: ToolCallRequestDetails): AgentExecutionResult<never> {
    return {
      status: 'needs_tool_calls',
      clarificationOrToolRequestDetails: details,
    };
  }

  // === Protected Utility Methods ===
  
  /**
   * Converts a Zod schema to a JSON schema string
   * 
   * Useful for agents that need to describe schemas in prompts for
   * multi-step internal LLM calls requiring structured JSON output.
   * 
   * @param zodSchema - The Zod schema to convert
   * @param schemaName - Optional name for the schema in the JSON output
   * @returns JSON schema as a formatted string
   */
  protected getJsonSchemaString(zodSchema: ZodTypeAny, schemaName?: string): string {
    try {
      return JSON.stringify(
        zodToJsonSchema(zodSchema, schemaName || 'InternalSchema'), 
        null, 
        2
      );
    } catch (error: any) {
      console.error(`Error converting Zod schema to JSON schema string for agent ${this.agentSlug}:`, error);
      
      // Fallback to a basic schema string rather than throwing
      return JSON.stringify({ 
        type: "object", 
        description: "Schema conversion failed" 
      }, null, 2);
    }
  }
} 