/**
 * Agent Registry
 * 
 * This module provides a central registry for all available AI agent classes.
 * It enables the GraphBuilder to validate agent references and allows the GraphExecutor
 * to dynamically instantiate the correct agent class based on an agentSlug string
 * stored in the GraphDefinition.
 * 
 * Key Features:
 * - Single source of truth for all registered agents
 * - Efficient O(1) lookup by agent slug
 * - Support for both constructor-based and factory-based agent instantiation
 * - Duplicate slug detection and validation
 * - Type-safe agent instantiation with proper error handling
 */

import {
  AgentRegistryEntry,
  AgentRegistryConfig,
  AgentRegistryMap,
  AgentNotFoundError,
  DuplicateAgentSlugError,
  AgentInstantiationError
} from './types/agentRegistry.types';
import {
  AgentDependencies,
  BaseAgentInterface,
  AgentConstructor
} from './types/agents.types';
import { LLMTaskCategory } from '../graph/types/graph.types';

// Re-export the type for external use
export type { AgentRegistryEntry };

// Import placeholder concrete agent classes
// TODO: Replace these with actual agent implementations as they are created
// import { N8nArchitectAgent } from './architect/N8nArchitectAgent';
// import { N8nNodeComposerAgent } from './composer/N8nNodeComposerAgent';
// import { N8nIntentRouterAgent } from './router/N8nIntentRouterAgent';
// import { N8nRequirementsGatheringAgent } from './requirements/N8nRequirementsGatheringAgent';
// import { N8nNodeSelectorAgent } from './selector/N8nNodeSelectorAgent';
// import { N8nWorkflowDocumenterAgent } from './documenter/N8nWorkflowDocumenterAgent';
// import { N8nConnectionComposerAgent } from './connections/N8nConnectionComposerAgent';
// import { N8nBestPracticeValidatorAgent } from './validator/N8nBestPracticeValidatorAgent';
// import { N8nReflectionAgent } from './reflection/N8nReflectionAgent';
// import { N8nSummarizerAgent } from './summarizer/N8nSummarizerAgent';

/**
 * Central configuration array for all registered agents
 * 
 * This is the single source of truth for what agents are available in the system.
 * Each entry contains the agent's slug, constructor, default configuration, and metadata.
 * 
 * When adding new agents:
 * 1. Import the agent class at the top of this file
 * 2. Add an entry to this configuration array
 * 3. Ensure the agent's static properties are correctly referenced
 */
export const AGENT_REGISTRY_CONFIG: AgentRegistryConfig = [
  // TODO: Add actual agent entries as they are implemented
  // Example structure for when agents are available:
  /*
  {
    slug: N8nArchitectAgent.agentSlug,
    description: N8nArchitectAgent.description || "Designs n8n workflow architecture and high-level plans.",
    roleDescription: N8nArchitectAgent.roleDescription || "As an n8n Solution Architect, your role is to analyze requirements and design a high-level plan for workflow automation...",
    constructor: N8nArchitectAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nArchitectAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nNodeComposerAgent.agentSlug,
    description: N8nNodeComposerAgent.description || "Composes valid n8n node JSON configurations.",
    roleDescription: N8nNodeComposerAgent.roleDescription || "As an n8n Node Composer, your role is to generate valid JSON for specific n8n nodes based on schemas and requirements...",
    constructor: N8nNodeComposerAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nNodeComposerAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nIntentRouterAgent.agentSlug,
    description: N8nIntentRouterAgent.description || "Routes user intents to appropriate graph actions.",
    roleDescription: N8nIntentRouterAgent.roleDescription || "As an n8n Intent Router, your role is to analyze user input and determine the most appropriate action or workflow...",
    constructor: N8nIntentRouterAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nIntentRouterAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nRequirementsGatheringAgent.agentSlug,
    description: N8nRequirementsGatheringAgent.description || "Gathers detailed requirements from users with clarification loops.",
    roleDescription: N8nRequirementsGatheringAgent.roleDescription || "As an n8n Requirements Specialist, your role is to elicit comprehensive workflow requirements from users...",
    constructor: N8nRequirementsGatheringAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nRequirementsGatheringAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nNodeSelectorAgent.agentSlug,
    description: N8nNodeSelectorAgent.description || "Selects appropriate n8n nodes based on workflow requirements.",
    roleDescription: N8nNodeSelectorAgent.roleDescription || "As an n8n Node Selector, your role is to choose the most suitable nodes for each step of a workflow...",
    constructor: N8nNodeSelectorAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nNodeSelectorAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nWorkflowDocumenterAgent.agentSlug,
    description: N8nWorkflowDocumenterAgent.description || "Creates comprehensive documentation for n8n workflows.",
    roleDescription: N8nWorkflowDocumenterAgent.roleDescription || "As an n8n Documentation Specialist, your role is to create clear, helpful documentation and sticky notes for workflows...",
    constructor: N8nWorkflowDocumenterAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nWorkflowDocumenterAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nConnectionComposerAgent.agentSlug,
    description: N8nConnectionComposerAgent.description || "Generates connection configurations between n8n nodes.",
    roleDescription: N8nConnectionComposerAgent.roleDescription || "As an n8n Connection Composer, your role is to create valid connection JSON that links workflow nodes...",
    constructor: N8nConnectionComposerAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nConnectionComposerAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nBestPracticeValidatorAgent.agentSlug,
    description: N8nBestPracticeValidatorAgent.description || "Validates workflows against n8n best practices.",
    roleDescription: N8nBestPracticeValidatorAgent.roleDescription || "As an n8n Quality Assurance Specialist, your role is to analyze workflows for adherence to best practices...",
    constructor: N8nBestPracticeValidatorAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nBestPracticeValidatorAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nReflectionAgent.agentSlug,
    description: N8nReflectionAgent.description || "Provides critical analysis and improvement suggestions.",
    roleDescription: N8nReflectionAgent.roleDescription || "As an n8n Reflection Specialist, your role is to critique and refine the outputs of other agents...",
    constructor: N8nReflectionAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nReflectionAgent.defaultLlmTaskCategory,
  },
  {
    slug: N8nSummarizerAgent.agentSlug,
    description: N8nSummarizerAgent.description || "Summarizes conversation history for context management.",
    roleDescription: N8nSummarizerAgent.roleDescription || "As an n8n Conversation Summarizer, your role is to create concise summaries of interactions...",
    constructor: N8nSummarizerAgent as AgentConstructor,
    defaultLlmTaskCategory: N8nSummarizerAgent.defaultLlmTaskCategory,
  },
  */
  
  // Example entry showing factory-based instantiation for complex agents:
  /*
  {
    slug: 'special-stateful-agent',
    description: 'Agent with custom initialization requirements',
    constructor: SpecialStatefulAgent,
    defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
    factory: (deps: AgentDependencies) => {
      const instance = new SpecialStatefulAgent(deps);
      instance.performCustomSetup(); // Example custom setup
      return instance;
    }
  }
  */
];

/**
 * Efficient lookup map derived from AGENT_REGISTRY_CONFIG
 * 
 * This Map provides O(1) lookup by agent slug for runtime agent instantiation.
 * It's automatically populated from AGENT_REGISTRY_CONFIG and includes duplicate detection.
 */
export const agentRegistryMap: AgentRegistryMap = new Map<string, AgentRegistryEntry>();

// Populate the map from the configuration array with duplicate detection
AGENT_REGISTRY_CONFIG.forEach(entry => {
  if (agentRegistryMap.has(entry.slug)) {
    console.warn(`AgentRegistry: Duplicate agent slug detected: "${entry.slug}". The later entry will overwrite the earlier one.`);
  }
  agentRegistryMap.set(entry.slug, entry);
});

// Note: We don't freeze the map to allow for testing with spies/mocks
// In production, the map should be treated as immutable after initialization
// Object.freeze(agentRegistryMap);

/**
 * Retrieves an agent configuration by slug
 * 
 * @param slug - The unique slug identifier for the agent
 * @returns The agent registry entry if found, undefined otherwise
 */
export function getAgentConfig(slug: string): AgentRegistryEntry | undefined {
  return agentRegistryMap.get(slug);
}

/**
 * Creates an instance of an agent by slug with proper error handling
 * 
 * This function handles both constructor-based and factory-based agent instantiation,
 * providing comprehensive error handling and validation.
 * 
 * @param slug - The unique slug identifier for the agent
 * @param dependencies - Services to inject into the agent
 * @returns A properly instantiated agent instance
 * @throws {AgentNotFoundError} If the agent slug is not registered
 * @throws {AgentInstantiationError} If agent instantiation fails
 */
export function createAgentInstance(
  slug: string, 
  dependencies: AgentDependencies
): BaseAgentInterface<any, any> {
  const entry = agentRegistryMap.get(slug);
  
  if (!entry) {
    throw new AgentNotFoundError(slug);
  }
  
  try {
    // Use factory if provided, otherwise use constructor
    if (entry.factory) {
      return entry.factory(dependencies);
    } else {
      return new entry.constructor(dependencies);
    }
  } catch (error: any) {
    throw new AgentInstantiationError(
      slug,
      error.message || 'Unknown instantiation error',
      error
    );
  }
}

/**
 * Validates that all agents in the registry have unique slugs
 * 
 * This function can be called during application startup to ensure registry integrity.
 * 
 * @throws {DuplicateAgentSlugError} If duplicate slugs are found
 */
export function validateAgentRegistry(): void {
  const seenSlugs = new Set<string>();
  
  for (const entry of AGENT_REGISTRY_CONFIG) {
    if (seenSlugs.has(entry.slug)) {
      throw new DuplicateAgentSlugError(entry.slug);
    }
    seenSlugs.add(entry.slug);
  }
}

/**
 * Gets all registered agent slugs
 * 
 * @returns Array of all agent slugs in the registry
 */
export function getAllAgentSlugs(): string[] {
  return Array.from(agentRegistryMap.keys());
}

/**
 * Gets all agent registry entries
 * 
 * @returns Array of all agent registry entries
 */
export function getAllAgentEntries(): AgentRegistryEntry[] {
  return Array.from(agentRegistryMap.values());
}

/**
 * Checks if an agent slug is registered
 * 
 * @param slug - The agent slug to check
 * @returns True if the agent is registered, false otherwise
 */
export function isAgentRegistered(slug: string): boolean {
  return agentRegistryMap.has(slug);
}

/**
 * Gets agents by LLM task category
 * 
 * @param category - The LLM task category to filter by
 * @returns Array of agent entries with the specified default task category
 */
export function getAgentsByTaskCategory(category: LLMTaskCategory): AgentRegistryEntry[] {
  return getAllAgentEntries().filter(entry => entry.defaultLlmTaskCategory === category);
} 