/**
 * Agents Module
 * 
 * This is the main export file for the agents module, providing clean access
 * to all public APIs including types, base classes, and the agent registry.
 */

// Export core types
export type {
  AgentDependencies,
  AgentExecutionResult,
  SuccessPayload,
  ClarificationRequestDetails,
  ToolCallRequestDetails,
  BaseAgentInterface,
  AgentConstructor,
  AgentTaskSessionView,
  LLMService,
  PromptBuilderService,
  NodeSpecService,
  ToolExecutorService
} from './types/agents.types';

// Export agent registry types
export type {
  AgentRegistryEntry,
  AgentRegistryConfig,
  AgentRegistryMap
} from './types/agentRegistry.types';

// Export error classes
export {
  AgentValidationError,
  AgentOutputSchemaValidationError,
  AgentSlugNotOverriddenError
} from './types/agents.types';

export {
  AgentNotFoundError,
  DuplicateAgentSlugError,
  AgentInstantiationError
} from './types/agentRegistry.types';

// Export base agent class
export { BaseAgent } from './base/BaseAgent';

// Export agent registry
export {
  AGENT_REGISTRY_CONFIG,
  agentRegistryMap,
  getAgentConfig,
  createAgentInstance,
  validateAgentRegistry,
  getAllAgentSlugs,
  getAllAgentEntries,
  isAgentRegistered,
  getAgentsByTaskCategory
} from './agentRegistry';

// Agent initialization functions removed - obsolete in V3.3.1 architecture

// Future: Export concrete agent classes when they are implemented
// export { N8nArchitectAgent } from './architect/N8nArchitectAgent';
// export { N8nNodeComposerAgent } from './composer/N8nNodeComposerAgent';
// export { N8nIntentRouterAgent } from './router/N8nIntentRouterAgent';
// ... etc 