/**
 * Graph Executor
 * 
 * Interprets and executes a GraphDefinition, manages runtime state,
 * orchestrates the execution of different node types, handles data flow
 * between nodes, and manages control flow (branching, loops, error handling,
 * HIL pauses).
 */

import {
  GraphDefinition, 
  GraphNodeDefinition, 
  GraphInputValueSource,
  ConditionalBranchNodeDef,
  DataTransformNodeDef,
  AgentCallNodeDef,
  ToolCallNodeDef,
  StartNodeDef,
  EndNodeDef
} from './types/graph.types';

import {
  GraphExecutionContext,
  GraphExecutionContextSnapshot,
  NodeOutputVersion,
  GraphExecutionResult,
  GraphExecutionStatus,
  ExecutionError,
  GraphExecutionError,
  NodeInputResolutionError,
  FunctionRegistryError,
  AgentExecutionError,
  ToolExecutionError,
  LLMTokenUsage
} from './types/execution.types';

import { FunctionRegistry } from './types/builder.types';
import { CompiledGraph } from './GraphBuilder';
import { generateUUID } from '../../utils';
import get from 'lodash.get';

/**
 * Interface for the agent registry entry
 * This is a placeholder and should be replaced with the actual interface
 */
interface AgentRegistryEntry {
  slug: string;
  constructor: new (deps: any) => any;
  factory?: (deps: any) => any;
  defaultLlmTaskCategory?: string;
  description?: string;
}

/**
 * Placeholder for agent registry
 * This should be imported from the actual agent registry
 */
export const agentRegistryMap = new Map<string, AgentRegistryEntry>();

/**
 * Agent execution result interface
 * This is a placeholder and should be replaced with the actual interface
 */
interface AgentExecutionResult {
  status: 'success' | 'failure' | 'interrupted_for_user_input';
  result?: {
    payload?: any;
    thought?: string;
  };
  errorDetails?: ExecutionError;
  clarificationOrToolRequestDetails?: any;
  tokenUsage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost?: number;
}

/**
 * Tool execution result interface
 * This is a placeholder and should be replaced with the actual interface
 */
interface ToolExecutionResult {
  status: 'success' | 'failure';
  output?: any;
  errorDetails?: ExecutionError;
}

/**
 * Interface for services injected into the GraphExecutor
 */
export interface GraphExecutorServices {
  llmService: any; // LLMService
  toolExecutor: any; // ToolExecutorService
  // Add other required services here
}

/**
 * Interface for agent dependencies
 */
export interface AgentDependencies {
  // Services that agents need to function
  [key: string]: any;
}

/**
 * Input resolution result
 */
interface InputResolutionResult {
  resolvedValue: any;
  success: boolean;
  error?: string;
}

/**
 * GraphExecutor class - the core runtime engine of the AI library
 */
export class GraphExecutor {
  private readonly graphDefinition: GraphDefinition;
  private readonly functionRegistry: FunctionRegistry;
  private graphContext: GraphExecutionContext;
  private readonly taskSessionId: string;

  private processingQueue: string[] = []; // Node IDs to process
  private totalStepsExecuted: number = 0;
  private readonly MAX_GRAPH_STEPS: number; // Overall safeguard

  /**
   * Creates a new GraphExecutor instance.
   * 
   * @param compiledGraph - The compiled graph definition and function registry
   * @param initialGraphInputs - Initial inputs to the graph
   * @param injectedServices - Core services required by the executor
   * @param fullAgentDependencies - All services that agents might need
   * @param taskSessionId - ID of the task session this execution belongs to
   * @param existingContextSnapshot - Optional existing context snapshot for resuming execution
   */
  constructor(
    compiledGraph: CompiledGraph,
    initialGraphInputs: Record<string, any>,
    injectedServices: GraphExecutorServices,
    fullAgentDependencies: AgentDependencies,
    taskSessionId: string,
    existingContextSnapshot?: Partial<GraphExecutionContextSnapshot>
  ) {
    this.graphDefinition = compiledGraph.definition;
    this.functionRegistry = compiledGraph.functionRegistry;
    this.taskSessionId = taskSessionId;
    
    // Set global step limit from graph definition or a hardcoded default
    this.MAX_GRAPH_STEPS = this.graphDefinition.defaultMaxIterationsForAllLoops || 200;

    // Initialize token usage
    const initialTokenUsage: LLMTokenUsage = {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
      byNode: {}
    };

    // Initialize nodeOutputs from snapshot if provided
    const initialNodeOutputs = existingContextSnapshot?.nodeOutputs
      ? new Map(existingContextSnapshot.nodeOutputs.map(([k, v]) => [k, [...v]])) // Deep copy arrays
      : new Map<string, NodeOutputVersion[]>();

    // Initialize GraphExecutionContext
    this.graphContext = {
      graphDefinitionId: this.graphDefinition.id,
      graphDefinitionVersion: this.graphDefinition.version,
      taskSessionId,
      currentGraphRunId: existingContextSnapshot?.currentGraphRunId || generateUUID(),
      status: existingContextSnapshot?.status || 'IDLE',
      graphDefinition: this.graphDefinition,
      functionRegistry: this.functionRegistry,
      initialInputs: { ...initialGraphInputs },
      nodeOutputs: initialNodeOutputs,
      currentNodeIdBeingProcessed: existingContextSnapshot?.currentNodeIdBeingProcessed || null,
      tokenUsage: existingContextSnapshot?.tokenUsage || initialTokenUsage,
      activeInterruptDetails: existingContextSnapshot?.activeInterruptDetails,
      lastCompletedNodeId: existingContextSnapshot?.lastCompletedNodeId,
      lastError: existingContextSnapshot?.lastError,
      services: fullAgentDependencies,
      _internalExecutorServices: injectedServices
    };

    // Set up processing queue
    if (!existingContextSnapshot || existingContextSnapshot.status === 'IDLE' || existingContextSnapshot.status === 'RUNNING') {
      this.processingQueue = existingContextSnapshot?.processingQueue?.length 
        ? [...existingContextSnapshot.processingQueue] 
        : [this.graphDefinition.entryNodeId];
    } else {
      this.processingQueue = existingContextSnapshot?.processingQueue || [];
    }
    
    // Set total steps executed
    this.totalStepsExecuted = existingContextSnapshot?.totalStepsExecuted || 0;
  }

  /**
   * Creates a snapshot of the current execution context for persistence
   * 
   * @returns A serializable snapshot of the execution context
   */
  public snapshotExecutionContext(): GraphExecutionContextSnapshot {
    return {
      graphDefinitionId: this.graphContext.graphDefinitionId,
      graphDefinitionVersion: this.graphContext.graphDefinitionVersion,
      taskSessionId: this.graphContext.taskSessionId,
      currentGraphRunId: this.graphContext.currentGraphRunId,
      status: this.graphContext.status,
      initialInputs: JSON.parse(JSON.stringify(this.graphContext.initialInputs)), // Deep copy
      nodeOutputs: Array.from(this.graphContext.nodeOutputs.entries()).map(([k, v]) => 
        [k, JSON.parse(JSON.stringify(v))] as [string, NodeOutputVersion[]]), // Deep copy
      currentNodeIdBeingProcessed: this.graphContext.currentNodeIdBeingProcessed,
      activeInterruptDetails: this.graphContext.activeInterruptDetails 
        ? JSON.parse(JSON.stringify(this.graphContext.activeInterruptDetails)) 
        : undefined,
      lastError: this.graphContext.lastError 
        ? JSON.parse(JSON.stringify(this.graphContext.lastError)) 
        : undefined,
      lastCompletedNodeId: this.graphContext.lastCompletedNodeId,
      processingQueue: [...this.processingQueue],
      totalStepsExecuted: this.totalStepsExecuted,
      tokenUsage: JSON.parse(JSON.stringify(this.graphContext.tokenUsage)) // Deep copy
    };
  }

  /**
   * Create an error output version
   * 
   * @param errorDetails - Details about the error
   * @param runId - Optional run ID
   * @returns A NodeOutputVersion with failure status
   */
  private createErrorOutputVersion(errorDetails: any, runId?: string): NodeOutputVersion {
    return {
      runId: runId || generateUUID(),
      timestamp: Date.now(),
      output: null,
      status: 'failure',
      error: errorDetails,
    };
  }

  /**
   * Run the graph execution
   * 
   * @returns A promise that resolves to the execution result
   */
  public async run(): Promise<GraphExecutionResult> {
    if (this.graphContext.status !== 'RUNNING' && this.graphContext.status !== 'RESUMING' && this.graphContext.status !== 'IDLE') {
      // If graph was paused and not explicitly resumed, or already completed/failed
      return this.createExecutionResult();
    }
    
    // Set status to RUNNING
    this.graphContext.status = 'RUNNING';

    while (this.processingQueue.length > 0 && this.graphContext.status === 'RUNNING') {
      if (this.totalStepsExecuted >= this.MAX_GRAPH_STEPS) {
        this.graphContext.status = 'FAILED';
        this.graphContext.lastError = {
          message: `Graph exceeded maximum steps (${this.MAX_GRAPH_STEPS}). Probable infinite loop.`,
          type: 'MaxStepsExceededError'
        };
        break;
      }

      const currentNodeId = this.processingQueue.shift()!;
      this.graphContext.currentNodeIdBeingProcessed = currentNodeId;
      this.totalStepsExecuted++;

      const nodeDef = this.graphDefinition.nodes.find(n => n.id === currentNodeId);
      if (!nodeDef) {
        this.graphContext.status = 'FAILED';
        this.graphContext.lastError = {
          message: `Node ID "${currentNodeId}" not found in graph definition.`,
          type: 'NodeNotFoundError'
        };
        break;
      }

      let nodeOutputVersion: NodeOutputVersion;
      try {
        const nodeInputs = await this.resolveNodeInputs(nodeDef);
        nodeOutputVersion = await this.executeNode(nodeDef, nodeInputs);
      } catch (error: any) {
        // Handle uncaught errors during input resolution or node execution
        const structuredError: ExecutionError = {
          message: error.message || 'Unknown execution error',
          stack: error.stack,
          type: error.name || 'ExecutorNodeError',
          data: error.data
        };
        
        nodeOutputVersion = this.createErrorOutputVersion(structuredError);
      }
      
      this.storeNodeOutput(currentNodeId, nodeOutputVersion);

      // Handle interruption for user input
      if (nodeOutputVersion.status === 'interrupted_for_user_input') {
        this.graphContext.status = 'PAUSED_FOR_USER_INPUT';
        this.graphContext.activeInterruptDetails = nodeOutputVersion.interruptPayload;
        break;
      }

      if (nodeOutputVersion.status === 'failure') {
        await this.handleNodeFailure(nodeDef, nodeOutputVersion.error);
      } else {
        // Success case - determine next nodes to process
        await this.determineNextNodes(nodeDef, nodeOutputVersion);
      }
      
      this.graphContext.currentNodeIdBeingProcessed = null;
    }
    
    // Handle case where processing queue is empty but status is still RUNNING
    // This can happen in complex graphs (like loops) that don't reach explicit end nodes
    if (this.processingQueue.length === 0 && this.graphContext.status === 'RUNNING') {
      // Check if we have any successful executions
      const hasSuccessfulExecutions = this.graphContext.nodeOutputs.size > 0 && 
        Array.from(this.graphContext.nodeOutputs.values()).some(outputs => 
          outputs.some(output => output.status === 'success')
        );
      
      if (hasSuccessfulExecutions) {
        // Successfully executed nodes but didn't reach end - consider completed
        this.graphContext.status = 'COMPLETED';
      } else {
        // No successful executions - likely a configuration issue
        this.graphContext.status = 'FAILED';
        this.graphContext.lastError = {
          message: 'Graph execution completed without successful node executions.',
          type: 'GraphExecutionError'
        };
      }
    }
    
    return this.createExecutionResult();
  }

  /**
   * Create the final execution result
   * 
   * @returns The final execution result
   */
  private createExecutionResult(): GraphExecutionResult {
    // Determine final graph output if completed successfully
    let finalOutput: any = null;
    
    if (this.graphContext.status === 'COMPLETED' && this.graphContext.lastCompletedNodeId) {
      const endNode = this.graphDefinition.nodes.find(
        n => n.id === this.graphContext.lastCompletedNodeId && n.type === 'end'
      ) as EndNodeDef | undefined;
      
      if (endNode && endNode.inputs?.finalOutput) {
        const resolution = this.resolveSingleInput(
          endNode.inputs.finalOutput, 
          `EndNode(${endNode.id}).finalOutput`
        );
        
        if (resolution.success) {
          finalOutput = resolution.resolvedValue;
        } else {
          // Should not happen if graph def is correct, but handle it
          this.graphContext.status = 'FAILED';
          this.graphContext.lastError = {
            message: `Failed to resolve final output for EndNode ${endNode.id}: ${resolution.error}`,
            type: 'FinalOutputResolutionError'
          };
        }
      }
    }
    
    return {
      status: this.graphContext.status,
      output: finalOutput,
      error: this.graphContext.lastError,
      finalContextSnapshot: this.snapshotExecutionContext()
    };
  }

  /**
   * Resolve all inputs for a node
   * 
   * @param nodeDef - The node definition
   * @returns A record of input field names to their resolved values
   */
  private async resolveNodeInputs(nodeDef: GraphNodeDefinition): Promise<Record<string, any>> {
    const resolvedInputs: Record<string, any> = {};
    
    if (!nodeDef.inputs) {
      return resolvedInputs;
    }
    
    for (const inputFieldName in nodeDef.inputs) {
      const inputValueSource = nodeDef.inputs[inputFieldName];
      const resolution = this.resolveSingleInput(
        inputValueSource, 
        `${nodeDef.type}(${nodeDef.id}).input(${inputFieldName})`
      );
      
      if (resolution.success) {
        resolvedInputs[inputFieldName] = resolution.resolvedValue;
      } else {
        throw new NodeInputResolutionError(
          `Failed to resolve input "${inputFieldName}" for node "${nodeDef.id}": ${resolution.error}`,
          { nodeId: nodeDef.id, inputField: inputFieldName }
        );
      }
    }
    
    return resolvedInputs;
  }

  /**
   * Resolve a single input value source
   * 
   * @param source - The input value source to resolve
   * @param resolutionPathForLogging - Path for better error messages
   * @returns The resolved value, success status, and optional error
   */
  private resolveSingleInput(
    source: GraphInputValueSource,
    resolutionPathForLogging: string
  ): InputResolutionResult {
    try {
      switch (source.type) {
        case 'fromGraphInitialInput': {
          const value = get(this.graphContext.initialInputs, source.path);
          
          if (value !== undefined) {
            return {
              resolvedValue: value,
              success: true
            };
          }
          
          if (source.defaultValue !== undefined) {
            return {
              resolvedValue: source.defaultValue,
              success: true
            };
          }
          
          // Value is undefined and no default
          if (source.zodSchema?.isOptional?.() === false) {
            return {
              resolvedValue: undefined,
              success: false,
              error: `Required initial input path "${source.path}" not found.`
            };
          }
          
          // Value is undefined but optional
          return {
            resolvedValue: undefined,
            success: true
          };
        }
        
        case 'fromNodeOutput': {
          let value: any;
          let found = false;
          
          // Get the node's outputs
          const nodeOutputs = this.graphContext.nodeOutputs.get(source.nodeId);
          if (nodeOutputs && nodeOutputs.length > 0) {
            // Determine which output version to use
            let versionToUse: NodeOutputVersion | undefined;
            
            if (source.outputVersion === 'latest' || source.outputVersion === undefined) {
              // Use the latest version
              versionToUse = nodeOutputs[nodeOutputs.length - 1];
            } else if (typeof source.outputVersion === 'number') {
              // Use the version at the specified index
              versionToUse = nodeOutputs[source.outputVersion];
            } else {
              // Find the version with the specified runId
              versionToUse = nodeOutputs.find(v => v.runId === source.outputVersion);
            }
            
            if (versionToUse) {
              if (versionToUse.status === 'success' || versionToUse.status === 'interrupted_for_user_input') {
                // Extract value using path if specified
                value = source.path ? get(versionToUse.output, source.path) : versionToUse.output;
                found = value !== undefined;
              }
              // If status is 'failure', treat as not found
            }
          }
          
          // If found, return the value
          if (found) {
            return {
              resolvedValue: value,
              success: true
            };
          }
          
          // Try fallback if available
          if (source.fallbackSource) {
            const fallbackResult = this.resolveSingleInput(
              source.fallbackSource,
              `${resolutionPathForLogging} -> fallback`
            );
            
            // If fallback succeeded, return its result
            if (fallbackResult.success) {
              return fallbackResult;
            }
            
            // If fallback failed, continue to try default value from original source
          }
          
          // Use default value if available
          if (source.defaultValue !== undefined) {
            return {
              resolvedValue: source.defaultValue,
              success: true
            };
          }
          
          // No value, no fallback, no default
          if (source.zodSchema?.isOptional?.() === false) {
            return {
              resolvedValue: undefined,
              success: false,
              error: `Required node output from "${source.nodeId}" (path: "${source.path}") not found or source node failed, and no fallback/default provided.`
            };
          }
          
          // If we're resolving a fallback source, be stricter about failing
          // This ensures fallback chains work properly
          if (resolutionPathForLogging.includes('-> fallback')) {
            return {
              resolvedValue: undefined,
              success: false,
              error: `Fallback node output from "${source.nodeId}" (path: "${source.path}") not found or source node failed.`
            };
          }
          
          // If no zodSchema is provided, and no fallback/default, treat as optional by default
          // This allows undefined paths to resolve to undefined without failing
          return {
            resolvedValue: undefined,
            success: true
          };
        }
        
        case 'fromGraphConstant': {
          if (!this.graphDefinition.graphConstants) {
            if (source.zodSchema?.isOptional?.() === false) {
              return {
                resolvedValue: undefined,
                success: false,
                error: `Required graph constant "${source.constantKey}" not found (no constants defined).`
              };
            }
            
            return {
              resolvedValue: undefined,
              success: true
            };
          }
          
          const value = this.graphDefinition.graphConstants[source.constantKey];
          
          if (value !== undefined) {
            return {
              resolvedValue: value,
              success: true
            };
          }
          
          if (source.zodSchema?.isOptional?.() === false) {
            return {
              resolvedValue: undefined,
              success: false,
              error: `Required graph constant "${source.constantKey}" not found.`
            };
          }
          
          return {
            resolvedValue: undefined,
            success: true
          };
        }
        
        case 'staticValue': {
          return {
            resolvedValue: source.value,
            success: true
          };
        }
        
        default:
          return {
            resolvedValue: undefined,
            success: false,
            error: `Unsupported input source type: ${(source as any).type}`
          };
      }
    } catch (e: any) {
      return {
        resolvedValue: undefined,
        success: false,
        error: e.message
      };
    }
  }

  /**
   * Execute a node with the resolved inputs
   * 
   * @param nodeDef - The node definition
   * @param resolvedInputs - The resolved inputs for the node
   * @returns A promise that resolves to the node output version
   */
  private async executeNode(
    nodeDef: GraphNodeDefinition, 
    resolvedInputs: Record<string, any>
  ): Promise<NodeOutputVersion> {
    const runId = generateUUID();
    
    try {
      switch (nodeDef.type) {
        case 'start': {
          // Start nodes simply pass through their inputs
          return {
            runId,
            timestamp: Date.now(),
            output: { ...resolvedInputs },
            status: 'success'
          };
        }
        
        case 'agentCall': {
          const agentNodeDef = nodeDef as AgentCallNodeDef;
          
          // Get agent from registry
          const agentRegistryEntry = agentRegistryMap.get(agentNodeDef.agentSlug);
          if (!agentRegistryEntry) {
            throw new AgentExecutionError(
              `Agent with slug "${agentNodeDef.agentSlug}" not found in registry`,
              { agentSlug: agentNodeDef.agentSlug }
            );
          }
          
          // Instantiate the agent
          const agentInstance = agentRegistryEntry.factory 
            ? agentRegistryEntry.factory(this.graphContext.services)
            : new agentRegistryEntry.constructor(this.graphContext.services);
          
          // Execute the agent
          let agentResult: AgentExecutionResult;
          try {
            agentResult = await agentInstance.execute(
              resolvedInputs, 
              this.graphContext, 
              this.taskSessionId
            );
          } catch (error: any) {
            // Handle agent execution errors
            throw new AgentExecutionError(
              `Error executing agent "${agentNodeDef.agentSlug}": ${error.message}`,
              { agentSlug: agentNodeDef.agentSlug, originalError: error }
            );
          }
          
          // Update token usage if present
          if (agentResult.tokenUsage) {
            this.updateTokenUsage(nodeDef.id, agentResult.tokenUsage);
          }
          
          // Return node output version
          return {
            runId,
            timestamp: Date.now(),
            output: agentResult.result?.payload || null,
            status: agentResult.status,
            error: agentResult.errorDetails,
            thought: agentResult.result?.thought,
            llmTokenUsage: agentResult.tokenUsage,
            interruptPayload: agentResult.clarificationOrToolRequestDetails
          };
        }
        
        case 'toolCall': {
          const toolNodeDef = nodeDef as ToolCallNodeDef;
          
          // Get tool executor service
          const toolExecutor = this.graphContext._internalExecutorServices.toolExecutor;
          if (!toolExecutor) {
            throw new ToolExecutionError(
              'Tool executor service not available',
              { toolName: toolNodeDef.toolName }
            );
          }
          
          // Execute the tool
          let toolResult: ToolExecutionResult;
          try {
            toolResult = await toolExecutor.execute(
              toolNodeDef.toolName, 
              resolvedInputs, 
              this.graphContext,
              this.taskSessionId
            );
          } catch (error: any) {
            // Handle tool execution errors
            throw new ToolExecutionError(
              `Error executing tool "${toolNodeDef.toolName}": ${error.message}`,
              { toolName: toolNodeDef.toolName, originalError: error }
            );
          }
          
          // Return node output version
          return {
            runId,
            timestamp: Date.now(),
            output: toolResult.output,
            status: toolResult.status,
            error: toolResult.errorDetails
          };
        }
        
        case 'conditionalBranch': {
          const conditionalNodeDef = nodeDef as ConditionalBranchNodeDef;
          
          // Get condition function
          const conditionFn = this.functionRegistry[conditionalNodeDef.conditionFunctionKey];
          if (!conditionFn) {
            throw new FunctionRegistryError(
              `Condition function with key "${conditionalNodeDef.conditionFunctionKey}" not found in registry`,
              { functionKey: conditionalNodeDef.conditionFunctionKey }
            );
          }
          
          // Execute the condition function
          let branchLabelOrLabels: string | string[];
          try {
            // Execute the function and ensure it returns a string or string[]
            const result = await Promise.resolve(conditionFn(resolvedInputs, this.graphContext));
            
            // Handle different result types
            if (typeof result === 'string') {
              branchLabelOrLabels = result;
            } else if (Array.isArray(result) && result.every(item => typeof item === 'string')) {
              branchLabelOrLabels = result as string[];
            } else {
              // If result is neither string nor string[], convert it to string
              branchLabelOrLabels = String(result);
            }
          } catch (error: any) {
            // Handle condition function errors
            throw new GraphExecutionError(
              `Error executing condition function: ${error.message}`,
              'ConditionFunctionError',
              { functionKey: conditionalNodeDef.conditionFunctionKey, originalError: error }
            );
          }
          
          // Return node output version
          return {
            runId,
            timestamp: Date.now(),
            output: {
              chosenBranchLabel: branchLabelOrLabels,
              evaluatedInputs: resolvedInputs // For passthrough to next nodes
            },
            status: 'success'
          };
        }
        
        case 'dataTransform': {
          const transformNodeDef = nodeDef as DataTransformNodeDef;
          
          // Get transform function
          const transformFn = this.functionRegistry[transformNodeDef.transformFunctionKey];
          if (!transformFn) {
            throw new FunctionRegistryError(
              `Transform function with key "${transformNodeDef.transformFunctionKey}" not found in registry`,
              { functionKey: transformNodeDef.transformFunctionKey }
            );
          }
          
          // Execute the transform function
          let transformedData: Record<string, any>;
          try {
            const result = await Promise.resolve(transformFn(resolvedInputs, this.graphContext));
            
            // Ensure result is a Record<string, any>
            if (result !== null && typeof result === 'object' && !Array.isArray(result)) {
              transformedData = result as Record<string, any>;
            } else {
              // If result is not an object, wrap it in an object
              transformedData = { result };
            }
          } catch (error: any) {
            // Handle transform function errors
            throw new GraphExecutionError(
              `Error executing transform function: ${error.message}`,
              'TransformFunctionError',
              { functionKey: transformNodeDef.transformFunctionKey, originalError: error }
            );
          }
          
          // Return node output version
          return {
            runId,
            timestamp: Date.now(),
            output: transformedData,
            status: 'success'
          };
        }
        
        case 'end': {
          const endNodeDef = nodeDef as EndNodeDef;
          
          // Set graph context status
          this.graphContext.status = endNodeDef.isErrorEndNode ? 'FAILED' : 'COMPLETED';
          this.graphContext.lastCompletedNodeId = nodeDef.id;
          
          // Return node output version
          return {
            runId,
            timestamp: Date.now(),
            output: resolvedInputs.finalOutput !== undefined ? resolvedInputs.finalOutput : resolvedInputs,
            status: 'success'
          };
        }
        
        default:
          throw new GraphExecutionError(
            `Unsupported node type: ${(nodeDef as any).type}`,
            'UnsupportedNodeTypeError',
            { nodeType: (nodeDef as any).type }
          );
      }
    } catch (error: any) {
      // Return a failure output version
      return this.createErrorOutputVersion(
        {
          message: error.message || 'Unknown node execution error',
          stack: error.stack,
          type: error.name || `NodeExecuteError_${nodeDef.type}`,
          data: error.data
        },
        runId
      );
    }
  }

  /**
   * Store a node's output in the context
   * 
   * @param nodeId - The ID of the node
   * @param outputVersion - The output version to store
   */
  private storeNodeOutput(nodeId: string, outputVersion: NodeOutputVersion): void {
    // Ensure the node has an output array
    if (!this.graphContext.nodeOutputs.has(nodeId)) {
      this.graphContext.nodeOutputs.set(nodeId, []);
    }
    
    // Add the output version to the array
    const nodeOutputs = this.graphContext.nodeOutputs.get(nodeId)!;
    nodeOutputs.push(outputVersion);
  }

  /**
   * Update token usage statistics
   * 
   * @param nodeId - The ID of the node
   * @param tokenUsage - The token usage to add
   */
  private updateTokenUsage(
    nodeId: string, 
    tokenUsage: { promptTokens: number; completionTokens: number; totalTokens: number }
  ): void {
    // Update global token usage
    this.graphContext.tokenUsage.promptTokens += tokenUsage.promptTokens;
    this.graphContext.tokenUsage.completionTokens += tokenUsage.completionTokens;
    this.graphContext.tokenUsage.totalTokens += tokenUsage.totalTokens;
    
    // Update per-node token usage
    this.graphContext.tokenUsage.byNode[nodeId] = this.graphContext.tokenUsage.byNode[nodeId] || {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0
    };
    
    this.graphContext.tokenUsage.byNode[nodeId].promptTokens += tokenUsage.promptTokens;
    this.graphContext.tokenUsage.byNode[nodeId].completionTokens += tokenUsage.completionTokens;
    this.graphContext.tokenUsage.byNode[nodeId].totalTokens += tokenUsage.totalTokens;
  }

  /**
   * Determine the next nodes to process after a node execution
   * 
   * @param executedNodeDef - The node that was executed
   * @param executedNodeOutputVersion - The output of the executed node
   */
  private async determineNextNodes(
    executedNodeDef: GraphNodeDefinition,
    executedNodeOutputVersion: NodeOutputVersion
  ): Promise<void> {
    // For end nodes, we don't need to determine next nodes
    if (executedNodeDef.type === 'end') {
      return;
    }
    
    // Determine the target label to follow
    let targetLabels: string[];
    
    if (executedNodeOutputVersion.status === 'failure') {
      // For failure, follow onFailure edges
      targetLabels = ['onFailure'];
    } else if (executedNodeDef.type === 'conditionalBranch') {
      // For conditional branches, follow the chosen branch label(s)
      const chosenBranchLabel = executedNodeOutputVersion.output.chosenBranchLabel;
      
      if (Array.isArray(chosenBranchLabel)) {
        // Multiple branches to follow
        targetLabels = chosenBranchLabel;
      } else {
        // Single branch to follow
        targetLabels = [chosenBranchLabel];
      }
    } else {
      // For other node types, follow onSuccess edges
      targetLabels = ['onSuccess'];
    }
    
    // Find edges from this node
    const edges = this.graphDefinition.edges.filter(edge => edge.from === executedNodeDef.id);
    
    // If no edges found, this could be a dead-end (though GraphBuilder validation should prevent this)
    if (edges.length === 0) {
      console.warn(`Node "${executedNodeDef.id}" has no outgoing edges.`);
      return;
    }
    
    let matchedEdges = edges.filter(edge => targetLabels.includes(edge.label || 'onSuccess'));
    
    // For conditional branches, if no specific labeled edge found, look for __otherwise__ edge
    if (
      executedNodeDef.type === 'conditionalBranch' &&
      matchedEdges.length === 0 &&
      !targetLabels.includes('__otherwise__')
    ) {
      const otherwiseEdges = edges.filter(edge => edge.label === '__otherwise__');
      matchedEdges = matchedEdges.concat(otherwiseEdges);
    }
    
    // Sort edges by priority if defined
    if (matchedEdges.length > 0) {
      matchedEdges.sort((a, b) => (a.priority || Infinity) - (b.priority || Infinity));
    }
    
    // Add target nodes to the processing queue
    for (const edge of matchedEdges) {
      this.processingQueue.push(edge.to);
    }
    
    // If no edges matched and it's not an end node, warn about potential dead end
    if (matchedEdges.length === 0) {
      console.warn(`No outgoing edge found for node "${executedNodeDef.id}" with status/label "${targetLabels.join(',')}". Graph may terminate unexpectedly.`);
    }
  }

  /**
   * Handle a node failure
   * 
   * @param failedNodeDef - The node that failed
   * @param errorDetails - Details about the error
   */
  private async handleNodeFailure(
    failedNodeDef: GraphNodeDefinition,
    errorDetails?: ExecutionError
  ): Promise<void> {
    // Find an onFailure edge from the failed node
    const onFailureEdge = this.graphDefinition.edges.find(
      edge => edge.from === failedNodeDef.id && edge.label === 'onFailure'
    );
    
    if (onFailureEdge) {
      // If found, follow the onFailure edge
      this.processingQueue.push(onFailureEdge.to);
    } else if (this.graphDefinition.defaultErrorHandlerNodeId) {
      // If no onFailure edge but there's a default error handler, use it
      this.processingQueue.push(this.graphDefinition.defaultErrorHandlerNodeId);
      
      // Store error information for the handler
      const errorNodeId = `__error_${failedNodeDef.id}_${Date.now()}`;
      this.storeNodeOutput(errorNodeId, {
        runId: generateUUID(),
        timestamp: Date.now(),
        output: {
          __CURRENT_NODE_ERROR__: errorDetails,
          __FAILED_NODE_ID__: failedNodeDef.id
        },
        status: 'success'
      });
    } else {
      // No error handling path - mark the graph as failed
      this.graphContext.status = 'FAILED';
      this.graphContext.lastError = errorDetails || {
        message: `Node "${failedNodeDef.id}" failed with no error handler configured.`,
        type: 'UnhandledNodeFailureError'
      };
      
      // Clear the processing queue to terminate execution
      this.processingQueue = [];
    }
  }

  /**
   * Resume execution with user input after a pause
   * 
   * @param userInput - The input provided by the user
   * @returns A promise that resolves to the execution result
   */
  public async resumeWithUserInput(userInput: any): Promise<GraphExecutionResult> {
    // Validate current state
    if (this.graphContext.status !== 'PAUSED_FOR_USER_INPUT') {
      throw new GraphExecutionError(
        'Cannot resume execution - graph is not in PAUSED_FOR_USER_INPUT state',
        'InvalidResumeStateError',
        { currentState: this.graphContext.status }
      );
    }
    
    // Get the node that caused the pause
    const pausedNodeId = this.graphContext.currentNodeIdBeingProcessed;
    if (!pausedNodeId) {
      throw new GraphExecutionError(
        'Cannot resume execution - no paused node identified',
        'MissingPausedNodeError'
      );
    }
    
    const pausedNodeDef = this.graphDefinition.nodes.find(n => n.id === pausedNodeId);
    if (!pausedNodeDef) {
      throw new GraphExecutionError(
        `Cannot resume execution - paused node "${pausedNodeId}" not found`,
        'PausedNodeNotFoundError'
      );
    }
    
    // Create a node output version as if the paused node completed successfully
    const resumedOutput: NodeOutputVersion = {
      runId: generateUUID(),
      timestamp: Date.now(),
      output: { user_response: userInput },
      status: 'success'
    };
    
    // Store the output
    this.storeNodeOutput(pausedNodeId, resumedOutput);
    
    // Clear interrupt details
    this.graphContext.activeInterruptDetails = undefined;
    
    // Set graph status to RESUMING
    this.graphContext.status = 'RESUMING';
    
    // Determine next nodes
    await this.determineNextNodes(pausedNodeDef, resumedOutput);
    
    // Continue execution
    return this.run();
  }

  /**
   * Abort the graph execution
   * 
   * @param reason - Optional reason for the abortion
   * @returns The final execution result
   */
  public abort(reason?: string): GraphExecutionResult {
    this.graphContext.status = 'ABORTED';
    this.graphContext.lastError = {
      message: reason || 'Graph execution was manually aborted.',
      type: 'ManualAbortError'
    };
    
    // Clear the processing queue to prevent further execution
    this.processingQueue = [];
    
    return this.createExecutionResult();
  }
} 