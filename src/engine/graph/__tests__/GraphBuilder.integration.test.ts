/**
 * GraphBuilder Integration Tests
 * 
 * These tests focus on realistic end-to-end scenarios and complex workflows
 * that might be encountered in production usage of the GraphBuilder.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { z } from 'zod';
import { GraphBuilder } from '../GraphBuilder';
import { LLMTaskCategory } from '../types/graph.types';

// Mock agents for realistic scenarios
class N8nIntentRouterAgent {
  static readonly agentSlug = 'n8n-intent-router-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { intent: 'create_workflow', confidence: 0.95 } };
  }
}

class N8nRequirementsGatheringAgent {
  static readonly agentSlug = 'n8n-requirements-gathering-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { requirements: { trigger: 'webhook', actions: ['email'] } } };
  }
}

class N8nArchitectAgent {
  static readonly agentSlug = 'n8n-architect-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { workflowPlan: { nodes: 3, complexity: 'medium' } } };
  }
}

class N8nNodeSelectorAgent {
  static readonly agentSlug = 'n8n-node-selector-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { selectedNodes: ['webhook', 'email', 'code'] } };
  }
}

class N8nNodeComposerAgent {
  static readonly agentSlug = 'n8n-node-composer-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { nodeJson: input.nodeSpec, workflowContext: input.workflowContext } };
  }
}

describe('GraphBuilder Integration Tests', () => {
  let builder: GraphBuilder;

  beforeEach(() => {
    builder = new GraphBuilder('n8n-workflow-creation-graph', 'Complete n8n workflow creation pipeline');
  });

  describe('Real-world n8n Workflow Creation Pipeline', () => {
    it('should build complete workflow creation pipeline', () => {
      // This represents a realistic workflow for creating n8n workflows
      const compiled = builder
        .setVersion('1.0.0')
        .addExpectedInitialInput('userMessage', 'User request for workflow creation', z.string())
        .addExpectedInitialInput('userContext', 'Additional user context', z.object({}), true)
        .addGraphConstant('maxRetries', 3)
        .addGraphConstant('timeoutMs', 30000)
        
        // Step 1: Route user intent
        .addAgentNode('intent-router', N8nIntentRouterAgent, undefined, {
          userInput: builder.graphInput('userMessage'),
          context: builder.graphInput('userContext', {})
        }, 'Determine user intent and route appropriately')
        
        // Step 2: Gather detailed requirements
        .addAgentNode('requirements-gatherer', N8nRequirementsGatheringAgent, undefined, {
          userMessage: builder.graphInput('userMessage'),
          detectedIntent: builder.nodeOutput('intent-router', 'payload.intent'),
          confidence: builder.nodeOutput('intent-router', 'payload.confidence')
        }, 'Gather detailed workflow requirements')
        
        // Step 3: Create high-level architecture
        .addAgentNode('architect', N8nArchitectAgent, undefined, {
          requirements: builder.nodeOutput('requirements-gatherer', 'payload.requirements'),
          userMessage: builder.graphInput('userMessage')
        }, 'Design workflow architecture')
        
        // Step 4: Select specific nodes
        .addAgentNode('node-selector', N8nNodeSelectorAgent, undefined, {
          workflowPlan: builder.nodeOutput('architect', 'payload.workflowPlan'),
          requirements: builder.nodeOutput('requirements-gatherer', 'payload.requirements')
        }, 'Select specific n8n nodes')
        
        // Step 5: Validation checkpoint
        .addConditionalNode('validation-checkpoint', (inputs: any, ctx: any) => {
          const confidence = inputs.confidence || 0;
          const nodeCount = inputs.selectedNodes?.length || 0;
          
          if (confidence > 0.8 && nodeCount > 0) {
            return 'proceed_to_composition';
          } else if (confidence > 0.5) {
            return 'needs_clarification';
          } else {
            return 'insufficient_info';
          }
        }, {
          confidence: builder.nodeOutput('intent-router', 'payload.confidence'),
          selectedNodes: builder.nodeOutput('node-selector', 'payload.selectedNodes'),
          requirements: builder.nodeOutput('requirements-gatherer', 'payload.requirements')
        }, 'Validate if we have enough information to proceed')
        
        // Node composition loop - multiple node creation iterations
        .addLoop('node-composition-loop', 
          builder.nodeOutput('node-selector', 'payload.selectedNodes'),
          (loopBuilder, currentNode, currentIndex, accumulator) => {
            loopBuilder.addAgentNode(`compose-node-${currentIndex}`, N8nNodeComposerAgent, undefined, {
              nodeSpec: currentNode,
              workflowContext: builder.nodeOutput('architect', 'payload')
            });
            
            return {
              lastNodeInBodyId: `compose-node-${currentIndex}`,
              accumulationLogic: {
                type: 'appendItem',
                itemToAppend: loopBuilder.nodeOutput(`compose-node-${currentIndex}`, 'payload.nodeJson')
              }
            };
          },
          { maxIterations: 10 }
        )
        .onLoopComplete('generate-connections') // Must be immediate after addLoop
        
        // Create the remaining nodes
        .addAgentNode('generate-connections', N8nNodeComposerAgent, undefined, {
          nodes: builder.nodeOutput('node-composition-loop_exit_aggregator', 'finalAccumulatedResults'),
          workflowMetadata: builder.nodeOutput('architect', 'payload')
        })
        
        .addDataTransformNode('assemble-workflow', (inputs: any) => {
          return {
            workflow: {
              nodes: inputs.nodes,
              connections: inputs.connections,
              metadata: inputs.metadata
            }
          };
        }, {
          nodes: builder.nodeOutput('node-composition-loop_exit_aggregator', 'finalAccumulatedResults'),
          connections: builder.nodeOutput('generate-connections', 'payload'),
          metadata: builder.nodeOutput('architect', 'payload')
        })
        
        // End states
        .addEndNode('workflow-created', 'Complete workflow created', false,
          builder.nodeOutput('assemble-workflow', 'workflow'))
        .addEndNode('needs-clarification', 'Needs user clarification', false)
        .addEndNode('insufficient-information', 'Insufficient information provided', true)
        
        // Connect routing logic
        .setInitialNode('intent-router')
        
        .setLastAddedNodeId('intent-router')
        .onSuccess('requirements-gatherer')
        .onFailure('insufficient-information')
        
        .setLastAddedNodeId('requirements-gatherer')
        .onSuccess('architect')
        .onFailure('needs-clarification')
        
        .setLastAddedNodeId('architect')
        .onSuccess('node-selector')
        .onFailure('insufficient-information')
        
        .setLastAddedNodeId('node-selector')
        .onSuccess('validation-checkpoint')
        .onFailure('insufficient-information')
        
        // Validation branching
        .setLastAddedNodeId('validation-checkpoint')
        .onBranch('proceed_to_composition', 'node-composition-loop_init_state') // Connect to loop entry
        .onBranch('needs_clarification', 'needs-clarification')
        .onBranch('insufficient_info', 'insufficient-information')
        
        // Connections to final assembly
        .setLastAddedNodeId('generate-connections')
        .onSuccess('assemble-workflow')
        .onFailure('insufficient-information')
        
        // Final assembly to success
        .setLastAddedNodeId('assemble-workflow')
        .onSuccess('workflow-created')
        .onFailure('insufficient-information')
        
        .compile();

      // Validate the complex workflow structure
      expect(compiled.definition.nodes.length).toBeGreaterThan(10);
      expect(compiled.definition.edges.length).toBeGreaterThan(10);
      
      // Check that all major components are present
      const nodeIds = compiled.definition.nodes.map(n => n.id);
      expect(nodeIds).toContain('intent-router');
      expect(nodeIds).toContain('requirements-gatherer');
      expect(nodeIds).toContain('architect');
      expect(nodeIds).toContain('node-selector');
      expect(nodeIds).toContain('validation-checkpoint');
      expect(nodeIds).toContain('generate-connections');
      expect(nodeIds).toContain('assemble-workflow');
      
      // Check loop nodes were created
      const loopNodes = nodeIds.filter(id => id.startsWith('node-composition-loop_'));
      expect(loopNodes.length).toBeGreaterThanOrEqual(4);
      
      // Check end states
      expect(nodeIds).toContain('workflow-created');
      expect(nodeIds).toContain('needs-clarification');
      expect(nodeIds).toContain('insufficient-information');
      
      // Validate function registry has conditional and transform functions
      expect(Object.keys(compiled.functionRegistry).length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Recovery Patterns', () => {
    it('should build resilient workflow with multiple error paths', () => {
      const compiled = builder
        .addExpectedInitialInput('data', 'Input data to process')
        .addGraphConstant('maxRetries', 3)
        
        // Primary processing path
        .addAgentNode('primary-processor', N8nArchitectAgent, undefined, {
          input: builder.graphInput('data')
        })
        
        // Fallback processing path  
        .addAgentNode('fallback-processor', N8nRequirementsGatheringAgent, undefined, {
          input: builder.graphInput('data'),
          reason: builder.staticValue('primary_failed')
        })
        
        // Validation with multiple outcomes
        .addConditionalNode('result-validator', (inputs: any) => {
          if (inputs.primaryResult && inputs.primaryResult.success) {
            return 'primary_success';
          } else if (inputs.fallbackResult && inputs.fallbackResult.success) {
            return 'fallback_success';
          } else {
            return 'all_failed';
          }
        }, {
          primaryResult: builder.nodeOutput('primary-processor', 'payload', { 
            defaultValue: null 
          }),
          fallbackResult: builder.nodeOutput('fallback-processor', 'payload', { 
            defaultValue: null 
          })
        })
        
        // Recovery attempt
        .addDataTransformNode('recovery-attempt', (inputs: any) => {
          return {
            recoveryData: {
              timestamp: Date.now(),
              input: inputs.originalData,
              attempt: 'manual_recovery'
            }
          };
        }, {
          originalData: builder.graphInput('data')
        })
        
        // End states
        .addEndNode('success-primary', 'Primary processing succeeded', false,
          builder.nodeOutput('primary-processor', 'payload'))
        .addEndNode('success-fallback', 'Fallback processing succeeded', false,
          builder.nodeOutput('fallback-processor', 'payload'))
        .addEndNode('recovery-initiated', 'Recovery process initiated', false,
          builder.nodeOutput('recovery-attempt', 'recoveryData'))
        .addEndNode('total-failure', 'All processing attempts failed', true,
          builder.staticValue({ error: 'All processing paths failed' }))
        
        // Wire the flow
        .setInitialNode('primary-processor')
        
        .setLastAddedNodeId('primary-processor')
        .onSuccess('result-validator')
        .onFailure('fallback-processor')
        
        .setLastAddedNodeId('fallback-processor')
        .onSuccess('result-validator')
        .onFailure('recovery-attempt')
        
        .setLastAddedNodeId('result-validator')
        .onBranch('primary_success', 'success-primary')
        .onBranch('fallback_success', 'success-fallback')
        .onBranch('all_failed', 'recovery-attempt')
        
        .setLastAddedNodeId('recovery-attempt')
        .onSuccess('recovery-initiated')
        .onFailure('total-failure')
        
        .compile();

      // Validate error handling structure
      expect(compiled.definition.nodes.length).toBe(9); // 1 default start + 8 added
      
      const endNodes = compiled.definition.nodes.filter(n => n.type === 'end');
      expect(endNodes.length).toBe(4);
      
      // Check that error nodes are properly marked
      const errorNode = endNodes.find(n => n.id === 'total-failure');
      expect((errorNode as any)?.isErrorEndNode).toBe(true);
    });
  });

  describe('Complex Data Flow Patterns', () => {
    it('should handle complex input mappings with deep fallback chains', () => {
      builder
        .addGraphConstant('defaultConfig', { timeout: 5000, retries: 2 })
        .addExpectedInitialInput('primaryData', 'Primary data source')
        .addExpectedInitialInput('secondaryData', 'Secondary data source', undefined, true)
        .addExpectedInitialInput('config', 'Configuration override', undefined, true);

      // Create the nodes that will be referenced
      builder
        .addDataTransformNode('data-merger', (inputs: any) => {
          return {
            mergedData: {
              primary: inputs.primaryData,
              secondary: inputs.secondaryData,
              merged: true
            }
          };
        }, {
          primaryData: builder.graphInput('primaryData'),
          secondaryData: builder.graphInput('secondaryData', null)
        })
        
        .addDataTransformNode('data-validator', (inputs: any) => {
          return {
            validatedData: {
              ...inputs.rawData,
              validated: true,
              timestamp: Date.now()
            }
          };
        }, {
          rawData: builder.graphInput('primaryData')
        });

      // Create a complex data transform with multiple fallback levels
      const compiled = builder
        .addDataTransformNode('complex-data-processor', (inputs: any) => {
          return {
            processed: true,
            data: inputs.finalData,
            config: inputs.finalConfig,
            sources: inputs.dataSources
          };
        }, {
          // Complex fallback chain for data
          finalData: builder.nodeOutput('data-merger', 'mergedData', {
            fallback: builder.nodeOutput('data-validator', 'validatedData', {
              defaultValue: builder.graphInput('primaryData')
            })
          }),
          
          // Config with fallback to constant
          finalConfig: builder.graphInput('config', builder.constant('defaultConfig')),
          
          // Array of data sources
          dataSources: builder.staticValue(['primary', 'secondary', 'default'])
        })
        
        .addEndNode('success', 'Processing completed', false,
          builder.nodeOutput('complex-data-processor'))
        
        .setInitialNode('data-merger')
        .setLastAddedNodeId('data-merger')
        .onSuccess('data-validator')
        
        .setLastAddedNodeId('data-validator')  
        .onSuccess('complex-data-processor')
        
        .setLastAddedNodeId('complex-data-processor')
        .onSuccess('success')
        
        .compile();

      expect(compiled.definition.nodes.length).toBe(5); // start + merger + validator + processor + end
      
      const processorNode = compiled.definition.nodes.find(n => n.id === 'complex-data-processor');
      expect(processorNode?.inputs).toBeDefined();
      
      // Validate complex input structure exists
      const inputs = processorNode?.inputs;
      expect(inputs?.finalData).toBeDefined();
      expect(inputs?.finalConfig).toBeDefined();
      expect(inputs?.dataSources).toBeDefined();
    });
  });

  describe('Performance and Scalability Patterns', () => {
    it('should handle large workflows with many nodes efficiently', () => {
      // Create a workflow with many nodes to test builder performance
      const nodeCount = 50;
      
      // Add many agent nodes
      for (let i = 0; i < nodeCount; i++) {
        builder.addAgentNode(`agent-${i}`, N8nIntentRouterAgent, undefined, {
          input: i === 0 
            ? builder.graphInput('initialData', `data for agent ${i}`)
            : builder.nodeOutput(`agent-${i-1}`, 'payload')
        }, `Agent ${i} in chain`);
      }
      
      // Add an end node
      builder.addEndNode('chain-complete', 'Long chain completed');
      
      // Connect them in sequence
      builder.setInitialNode('agent-0');
      for (let i = 0; i < nodeCount - 1; i++) {
        builder.setLastAddedNodeId(`agent-${i}`).onSuccess(`agent-${i+1}`);
      }
      builder.setLastAddedNodeId(`agent-${nodeCount-1}`).onSuccess('chain-complete');
      
      const compiled = builder.compile();
      
      // Validate large workflow was built correctly
      expect(compiled.definition.nodes.length).toBe(nodeCount + 2); // agents + start + end
      expect(compiled.definition.edges.length).toBe(nodeCount + 1); // initial edge + chain edges
      
      // Validate sequential connections
      for (let i = 0; i < nodeCount - 1; i++) {
        const edge = compiled.definition.edges.find(e => 
          e.from === `agent-${i}` && e.to === `agent-${i+1}`
        );
        expect(edge).toBeDefined();
        expect(edge?.label).toBe('onSuccess');
      }
    });

    it('should handle deeply nested loops efficiently', () => {
      const items = builder.staticValue([
        { category: 'A', items: [1, 2, 3] },
        { category: 'B', items: [4, 5, 6] },
        { category: 'C', items: [7, 8, 9] }
      ]);
      
      const compiled = builder
        .addLoop('outer-loop', items, (outerBuilder, category, categoryIndex, outerAcc) => {
          // Simplified inner processing for performance test
          outerBuilder.addDataTransformNode(
            'process-category-items',
            (inputs: any) => ({ 
              processedCategory: inputs.categoryData.category,
              processedItems: inputs.categoryData.items.map((item: number) => item * 2)
            }),
            { categoryData: category }
          );
          
          return {
            lastNodeInBodyId: 'process-category-items',
            accumulationLogic: {
              type: 'appendItem',
              itemToAppend: outerBuilder.nodeOutput('process-category-items', 'processedItems')
            }
          };
        })
        .onLoopComplete('nested-complete') // Must be immediate after addLoop
        .addEndNode('nested-complete', 'Nested processing complete')
        .compile();

      // Validate nested loop structure
      const outerLoopNodes = compiled.definition.nodes.filter(n => n.id.startsWith('outer-loop_'));
      expect(outerLoopNodes.length).toBeGreaterThanOrEqual(4);
      
      // Should have reasonable total node count (not exponential explosion)
      expect(compiled.definition.nodes.length).toBeLessThan(50);
    });
  });
}); 