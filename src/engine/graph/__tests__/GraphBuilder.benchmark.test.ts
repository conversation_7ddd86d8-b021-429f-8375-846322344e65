/**
 * GraphBuilder Performance Benchmark Tests
 * 
 * These tests focus on performance characteristics and scalability limits
 * of the GraphBuilder for large and complex graph definitions.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { GraphBuilder } from '../GraphBuilder';
import { LLMTaskCategory } from '../types/graph.types';

// Simple mock agent for performance testing
class BenchmarkAgent {
  static readonly agentSlug = 'benchmark-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: input };
  }
}

describe('GraphBuilder Performance Benchmarks', () => {
  let builder: GraphBuilder;

  beforeEach(() => {
    builder = new GraphBuilder('benchmark-graph', 'Performance testing graph');
  });

  describe('Node Creation Performance', () => {
    it('should handle creating many nodes efficiently', () => {
      const nodeCount = 1000;
      const startTime = performance.now();

      // Create many nodes
      for (let i = 0; i < nodeCount; i++) {
        builder.addAgentNode(`agent-${i}`, BenchmarkAgent, undefined, {
          input: builder.staticValue(`input-${i}`)
        });
      }

      builder.addEndNode('end');
      const endTime = performance.now();

      const duration = endTime - startTime;
      console.log(`Created ${nodeCount} nodes in ${duration}ms (${(duration/nodeCount).toFixed(3)}ms per node)`);

      // Should complete in reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds max
      expect(duration / nodeCount).toBeLessThan(5); // 5ms per node max
    });

    it('should handle creating many edges efficiently', () => {
      const nodeCount = 500;
      
      // First create nodes
      for (let i = 0; i < nodeCount; i++) {
        builder.addAgentNode(`agent-${i}`, BenchmarkAgent);
      }
      builder.addEndNode('end');

      const startTime = performance.now();

      // Create sequential edges
      builder.setInitialNode('agent-0');
      for (let i = 0; i < nodeCount - 1; i++) {
        builder.setLastAddedNodeId(`agent-${i}`).onSuccess(`agent-${i + 1}`);
      }
      builder.setLastAddedNodeId(`agent-${nodeCount - 1}`).onSuccess('end');

      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Created ${nodeCount + 1} edges in ${duration}ms`);
      expect(duration).toBeLessThan(2000); // 2 seconds max
    });
  });

  describe('Loop Performance', () => {
    it('should handle complex loops efficiently', () => {
      const itemCount = 100;
      const items = Array.from({ length: itemCount }, (_, i) => ({ 
        id: i, 
        value: `item-${i}`,
        data: Array(10).fill(0).map((_, j) => j * i) // Some complexity
      }));

      const startTime = performance.now();

      builder.addLoop(
        'complex-benchmark-loop',
        builder.staticValue(Array.from({ length: itemCount }, (_, i) => ({ id: i, value: `item-${i}` }))),
        (loopBuilder, currentItem, currentIndex, accumulator) => {
          loopBuilder
            .addDataTransformNode(
              `validate-item-${currentIndex}`,
              (inputs: any) => ({ isValid: inputs.item.id % 2 === 0, item: inputs.item }),
              { item: currentItem }
            )
            .addConditionalNode(
              `condition-${currentIndex}`,
              (inputs: any) => inputs.validationResult.isValid ? 'valid' : 'invalid',
              { validationResult: loopBuilder.nodeOutput(`validate-item-${currentIndex}`) }
            )
            .addDataTransformNode(
              'process-valid-item',
              (inputs: any) => ({ result: `PROCESSED: ${inputs.item.value}` }),
              { item: currentItem }
            )
            .addDataTransformNode(
              'handle-invalid-item',
              (inputs: any) => ({ result: `REJECTED: ${inputs.item.value}` }),
              { item: currentItem }
            );

          loopBuilder
            .setLastAddedNodeId(`condition-${currentIndex}`)
            .onBranch('valid', 'process-valid-item')
            .onBranch('invalid', 'handle-invalid-item');

          return {
            lastNodeInBodyId: 'process-valid-item', // Simplified for benchmark
            accumulationLogic: {
              type: 'appendItem',
              itemToAppend: loopBuilder.nodeOutput('process-valid-item', 'result')
            }
          };
        },
        {
          accumulatorInitialValue: builder.staticValue([]),
          maxIterations: itemCount + 10 // Buffer for safety
        }
      )
      .onLoopComplete('completed') // Must be immediate after addLoop
      .addEndNode('completed');

      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Created complex loop with ${itemCount} iterations in ${duration}ms`);
      expect(duration).toBeLessThan(3000); // 3 seconds max
    });

    it('should handle nested loops without exponential slowdown', () => {
      const outerCount = 5;
      const innerCount = 10;
      
      const nestedData = Array.from({ length: outerCount }, (_, i) => ({
        category: `cat-${i}`,
        items: Array.from({ length: innerCount }, (_, j) => `item-${i}-${j}`)
      }));

      const startTime = performance.now();

      // Simplified nested structure for performance testing
      builder.addLoop(
        'outer-nested-loop',
        builder.staticValue(nestedData),
        (outerBuilder, category, categoryIndex, outerAcc) => {
          // For benchmark, just process the category's items in a simple way
          outerBuilder.addDataTransformNode(
            'process-category',
            (inputs: any) => ({
              processedCategory: inputs.categoryData.category,
              processedItems: inputs.categoryData.items.map((item: string) => `PROCESSED: ${item}`)
            }),
            { categoryData: category }
          );

          return {
            lastNodeInBodyId: 'process-category',
            accumulationLogic: {
              type: 'appendItem',
              itemToAppend: outerBuilder.nodeOutput('process-category', 'processedItems')
            }
          };
        },
        { accumulatorInitialValue: builder.staticValue([]) }
      )
      .onLoopComplete('nested-complete') // Must be immediate after addLoop
      .addEndNode('nested-complete');

      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Created nested loops (${outerCount}x${innerCount}) in ${duration}ms`);
      expect(duration).toBeLessThan(5000); // 5 seconds max
    });
  });

  describe('Compilation Performance', () => {
    it('should compile large graphs efficiently', () => {
      const nodeCount = 500;
      
      // Create a complex graph structure
      for (let i = 0; i < nodeCount; i++) {
        builder.addAgentNode(`agent-${i}`, BenchmarkAgent, undefined, {
          input: i === 0 
            ? builder.graphInput('initialInput', `input for node ${i}`)
            : builder.nodeOutput(`agent-${i-1}`, 'payload'),
          nodeIndex: builder.staticValue(i)
        });
      }

      // Add some conditional branching
      for (let i = 0; i < Math.min(50, nodeCount); i += 10) {
        builder.addConditionalNode(`condition-${i}`, (inputs: any) => {
          return inputs.nodeIndex % 2 === 0 ? 'even' : 'odd';
        }, {
          nodeIndex: builder.nodeOutput(`agent-${i}`, 'nodeIndex')
        });
      }

      builder.addEndNode('success').addEndNode('failure', undefined, true);

      // Connect the flow
      builder.setInitialNode('agent-0');
      for (let i = 0; i < nodeCount - 1; i++) {
        builder.setLastAddedNodeId(`agent-${i}`).onSuccess(`agent-${i+1}`);
      }
      builder.setLastAddedNodeId(`agent-${nodeCount-1}`).onSuccess('success');

      const startTime = performance.now();
      const compiled = builder.compile();
      const endTime = performance.now();

      const duration = endTime - startTime;
      console.log(`Compiled graph with ${compiled.definition.nodes.length} nodes in ${duration}ms`);

      expect(duration).toBeLessThan(1000); // 1 second max
      expect(compiled.definition.nodes.length).toBe(nodeCount + 2 + Math.ceil(50/10) + 1); // agents + ends + conditions + start
    });

    it('should validate large graphs efficiently', () => {
      const nodeCount = 300;
      
      // Create a graph with many references that need validation
      builder
        .addExpectedInitialInput('data', 'Input data')
        .addGraphConstant('config', { setting: 'value' });

      for (let i = 0; i < nodeCount; i++) {
        builder.addAgentNode(`agent-${i}`, BenchmarkAgent, undefined, {
          data: builder.graphInput('data'),
          config: builder.constant('config'),
          previousResult: i > 0 ? builder.nodeOutput(`agent-${i-1}`, 'payload') : builder.staticValue(null),
          nodeId: builder.staticValue(i)
        });
      }

      builder.addEndNode('end');
      
      // Create edges
      builder.setInitialNode('agent-0');
      for (let i = 0; i < nodeCount - 1; i++) {
        builder.setLastAddedNodeId(`agent-${i}`).onSuccess(`agent-${i+1}`);
      }
      builder.setLastAddedNodeId(`agent-${nodeCount-1}`).onSuccess('end');

      const startTime = performance.now();
      
      // This should trigger validation
      const compiled = builder.compile();
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Validated and compiled graph with ${nodeCount} interconnected nodes in ${duration}ms`);
      expect(duration).toBeLessThan(2000); // 2 seconds max
      expect(compiled.definition.nodes.length).toBe(nodeCount + 2); // agents + start + end
    });
  });

  describe('Memory Usage Patterns', () => {
    it('should not have excessive memory growth with large graphs', () => {
      const iterations = 100;
      const nodesPerIteration = 10;
      
      const memoryMeasurements: number[] = [];
      
      for (let iteration = 0; iteration < iterations; iteration++) {
        // Create a fresh builder for each iteration
        const testBuilder = new GraphBuilder(`memory-test-${iteration}`, 'Memory test');
        
        // Add nodes
        for (let i = 0; i < nodesPerIteration; i++) {
          testBuilder.addAgentNode(`agent-${i}`, BenchmarkAgent, undefined, {
            input: builder.staticValue(`test-${iteration}-${i}`)
          });
        }
        
        testBuilder.addEndNode('end');
        
        // Compile
        const compiled = testBuilder.compile();
        
        // Measure memory (if available)
        if (typeof process !== 'undefined' && process.memoryUsage) {
          const memUsage = process.memoryUsage();
          memoryMeasurements.push(memUsage.heapUsed);
        }
        
        // Validate compilation worked
        expect(compiled.definition.nodes.length).toBe(nodesPerIteration + 2);
      }
      
      if (memoryMeasurements.length > 10) {
        const initialMemory = memoryMeasurements[5]; // Skip first few for warmup
        const finalMemory = memoryMeasurements[memoryMeasurements.length - 1];
        const memoryGrowth = finalMemory - initialMemory;
        const growthPerIteration = memoryGrowth / (iterations - 5);
        
        console.log(`Memory growth: ${(memoryGrowth / 1024 / 1024).toFixed(2)}MB total, ${(growthPerIteration / 1024).toFixed(2)}KB per iteration`);
        
        // Memory growth should be reasonable (less than 100KB per iteration)
        expect(growthPerIteration).toBeLessThan(100 * 1024);
      }
    });
  });

  describe('Edge Cases Performance', () => {
    it('should handle deeply nested input references efficiently', () => {
      const depth = 50;
      
      const startTime = performance.now();
      
      // Create a chain of nodes where each references the previous deeply
      for (let i = 0; i < depth; i++) {
        if (i === 0) {
          builder.addAgentNode(`deep-${i}`, BenchmarkAgent, undefined, {
            input: builder.graphInput('rootInput', `root-${i}`)
          });
        } else {
          // Create increasingly deep references with fallbacks
          let currentRef = builder.nodeOutput(`deep-${i-1}`, 'payload');
          
          // Add some fallback depth
          for (let j = 0; j < Math.min(i, 5); j++) {
            if (i - j - 1 >= 0) {
              currentRef = builder.nodeOutput(`deep-${i-1}`, 'payload', {
                fallback: builder.nodeOutput(`deep-${Math.max(0, i-j-2)}`, 'payload')
              });
            }
          }
          
          builder.addAgentNode(`deep-${i}`, BenchmarkAgent, undefined, {
            input: currentRef,
            depth: builder.staticValue(i)
          });
        }
      }
      
      builder.addEndNode('deep-end');
      
      // Connect the chain
      builder.setInitialNode('deep-0');
      for (let i = 0; i < depth - 1; i++) {
        builder.setLastAddedNodeId(`deep-${i}`).onSuccess(`deep-${i+1}`);
      }
      builder.setLastAddedNodeId(`deep-${depth-1}`).onSuccess('deep-end');
      
      const compiled = builder.compile();
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      console.log(`Created deeply nested reference chain (depth ${depth}) in ${duration}ms`);
      
      expect(duration).toBeLessThan(3000); // 3 seconds max
      expect(compiled.definition.nodes.length).toBe(depth + 2); // deep nodes + start + end
    });

    it('should handle many conditional branches efficiently', () => {
      const branchCount = 20;
      const conditionsCount = 10;
      
      const startTime = performance.now();
      
      // Create many conditional nodes with many branches each
      for (let i = 0; i < conditionsCount; i++) {
        const branches: string[] = [];
        
        // Create target nodes for each branch
        for (let j = 0; j < branchCount; j++) {
          const targetId = `target-${i}-${j}`;
          builder.addDataTransformNode(targetId, (inputs: any) => ({
            result: `Branch ${i}-${j} result`,
            inputs
          }), {
            branchInfo: builder.staticValue({ condition: i, branch: j })
          });
          branches.push(targetId);
        }
        
        // Create the conditional node
        builder.addConditionalNode(`condition-${i}`, (inputs: any) => {
          const branchIndex = Math.abs(inputs.value || 0) % branchCount;
          return `branch-${branchIndex}`;
        }, {
          value: builder.staticValue(i)
        });
        
        // Connect all branches
        for (let j = 0; j < branchCount; j++) {
          builder.setLastAddedNodeId(`condition-${i}`)
            .onBranch(`branch-${j}`, `target-${i}-${j}`);
        }
      }
      
      builder.addEndNode('branch-end');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`Created ${conditionsCount} conditionals with ${branchCount} branches each in ${duration}ms`);
      
      expect(duration).toBeLessThan(4000); // 4 seconds max
      
      const compiled = builder.compile();
      expect(compiled.definition.nodes.length).toBe(1 + conditionsCount + (conditionsCount * branchCount) + 1); // start + conditions + targets + end
    });
  });
}); 