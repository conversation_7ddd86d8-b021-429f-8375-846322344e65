# GraphBuilder Test Suite

This directory contains comprehensive automated tests for the `GraphBuilder` class, which is the core component for programmatically creating n8n workflow graphs.

## Test Files Overview

### 1. `GraphBuilder.test.ts` - Core Unit Tests
**Purpose**: Comprehensive unit tests covering all GraphBuilder functionality

**Test Categories**:
- **Basic Construction**: Builder instantiation, version setting, default node creation
- **Configuration Methods**: Initial inputs, constants, loop configurations, error handlers
- **Input Source Factory Methods**: `graphInput()`, `nodeOutput()`, `constant()`, `staticValue()`
- **Node Addition Methods**: All node types (start, end, agent, tool, conditional, data transform)
- **Edge Addition Methods**: `onSuccess()`, `onFailure()`, `onBranch()`, `otherwise()`, `addEdge()`
- **Loop Construction**: Simple and complex loops with different accumulation strategies
- **Input Resolution**: Complex input mappings with fallback chains
- **Validation**: Graph structure validation, error detection
- **Error Handling**: Duplicate IDs, invalid references, type validation
- **Compilation Output**: Verifying GraphDefinition structure and function registry

**Key Features Tested**:
- ✅ Fluent API chaining
- ✅ Type safety and validation
- ✅ Input source resolution with fallbacks
- ✅ Loop construction with nested logic
- ✅ Error detection and reporting
- ✅ Complete workflow scenarios

### 2. `GraphBuilder.integration.test.ts` - Integration Tests
**Purpose**: Real-world scenarios and end-to-end workflow testing

**Test Categories**:
- **Real-world n8n Workflow Creation Pipeline**: Complete workflow from user intent to final n8n JSON
- **Error Handling and Recovery Patterns**: Resilient workflows with multiple fallback paths
- **Complex Data Flow Patterns**: Deep input mappings with multiple fallback levels
- **Performance and Scalability Patterns**: Large graphs, nested loops, memory efficiency

**Realistic Scenarios**:
- Multi-agent workflow creation pipeline
- Error recovery with primary/fallback processors
- Deeply nested input references
- Large-scale workflow processing (50+ nodes)

### 3. `GraphBuilder.benchmark.test.ts` - Performance Tests
**Purpose**: Performance characteristics and scalability limits

**Test Categories**:
- **Node Creation Performance**: Creating 1000+ nodes efficiently
- **Edge Creation Performance**: Sequential edge creation benchmarks
- **Loop Performance**: Complex loops with 100+ iterations
- **Compilation Performance**: Large graph compilation and validation
- **Memory Usage Patterns**: Memory growth tracking across iterations
- **Edge Cases Performance**: Deep nesting, many branches, complex references

**Performance Targets**:
- Node creation: <5ms per node
- Large graph compilation: <1000ms for 500+ nodes
- Memory growth: <100KB per iteration
- Complex loops: <3000ms for 100 iterations

## Testing Framework

### Vitest Configuration
- **Framework**: Vitest with TypeScript support
- **Environment**: jsdom for browser compatibility testing
- **Coverage**: v8 provider with 80% minimum thresholds
- **Global Test Utilities**: Setup in `setup.ts`

### Test Structure
```typescript
describe('Component/Feature', () => {
  beforeEach(() => {
    // Fresh builder instance for each test
    builder = new GraphBuilder('test-id', 'test description');
  });

  it('should test specific behavior', () => {
    // Arrange: Set up test data
    // Act: Execute the functionality
    // Assert: Verify expected outcomes
  });
});
```

## Running Tests

### Basic Commands
```bash
# Run all tests
pnpm test

# Run tests once (CI mode)
pnpm test:run

# Run with coverage
pnpm test:coverage

# Run with UI
pnpm test:ui

# Run only GraphBuilder tests
pnpm test:graphbuilder

# Watch mode for development
pnpm test:watch
```

### Test Categories
```bash
# Run specific test files
npx vitest run src/**/*GraphBuilder.test.ts          # Unit tests
npx vitest run src/**/*GraphBuilder.integration.test.ts  # Integration tests  
npx vitest run src/**/*GraphBuilder.benchmark.test.ts    # Performance tests

# Run tests matching pattern
npx vitest run --grep "Loop Construction"
npx vitest run --grep "should handle creating many nodes"
```

### Coverage Reports
```bash
# Generate coverage report
pnpm test:coverage

# View coverage in browser
open coverage/index.html
```

## Mock Objects and Test Utilities

### Mock Agents
```typescript
class MockAgent {
  static readonly agentSlug = 'test-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  
  constructor(deps: any) {}
  async processInput(input: any) {
    return { success: true, payload: { mocked: true, input } };
  }
}
```

### Test Data Factories
```typescript
// Available in setup.ts
createTestData.simpleInputMappings()    // Basic input mappings
createTestData.complexInputMappings()   // Complex with fallbacks
createTestGraphId()                     // Unique graph IDs
createMockAgent(slug)                   // Dynamic mock agents
```

## Test Patterns and Best Practices

### 1. Test Organization
- **Arrange, Act, Assert**: Clear test structure
- **Descriptive Names**: Test names explain the behavior being tested
- **Focused Tests**: Each test verifies one specific behavior
- **Isolated Tests**: Tests don't depend on each other

### 2. Mock Strategy
- **Agent Mocking**: Use simple mock agents that return predictable results
- **Input Isolation**: Each test creates its own builder instance
- **Deterministic Results**: Avoid random values that make tests flaky

### 3. Validation Patterns
```typescript
// Structure validation
expect(compiled.definition.nodes).toHaveLength(expectedCount);
expect(compiled.definition.edges.length).toBeGreaterThan(0);

// Content validation
const agentNode = compiled.definition.nodes.find(n => n.id === 'agent-1');
expect(agentNode).toEqual({
  id: 'agent-1',
  type: 'agentCall',
  agentSlug: 'test-agent',
  // ... expected properties
});

// Error validation
expect(() => builder.compile()).toThrow('Expected error message');
```

### 4. Performance Testing
```typescript
const startTime = performance.now();
// ... operation to benchmark
const endTime = performance.now();
const duration = endTime - startTime;

console.log(`Operation took ${duration}ms`);
expect(duration).toBeLessThan(maxAllowedTime);
```

## Adding New Tests

### For New GraphBuilder Features
1. **Unit Tests**: Add to `GraphBuilder.test.ts` in appropriate describe block
2. **Integration**: Add realistic scenario to `GraphBuilder.integration.test.ts`
3. **Performance**: Add benchmark to `GraphBuilder.benchmark.test.ts` if performance-critical

### Test Structure Template
```typescript
describe('New Feature', () => {
  it('should handle basic functionality', () => {
    // Test the happy path
  });

  it('should handle edge cases', () => {
    // Test boundary conditions
  });

  it('should validate inputs properly', () => {
    // Test error conditions
  });

  it('should perform efficiently', () => {
    // Performance validation if needed
  });
});
```

## Debugging Tests

### Common Issues
1. **Linter Errors**: Install missing dependencies with `pnpm install`
2. **Type Errors**: Ensure mock objects match expected interfaces
3. **Compilation Errors**: Verify graph structure has required nodes (start, end)
4. **Performance Failures**: Adjust thresholds based on hardware capabilities

### Debug Commands
```bash
# Run single test with detailed output
npx vitest run --reporter=verbose "GraphBuilder.test.ts"

# Debug mode with Node.js inspector
npx vitest run --inspect-brk "GraphBuilder.test.ts"

# Run with environment variable for debug logging
DEBUG=true pnpm test
```

## Coverage Targets

### Current Thresholds
- **Branches**: 80%
- **Functions**: 80%  
- **Lines**: 80%
- **Statements**: 80%

### Focus Areas for Coverage
- All public API methods
- Error handling paths
- Validation logic
- Edge cases and boundary conditions
- Complex internal logic (loops, input resolution)

## Continuous Integration

### GitHub Actions (Future)
```yaml
- name: Run Tests
  run: |
    pnpm install
    pnpm test:run
    pnpm test:coverage

- name: Upload Coverage
  uses: codecov/codecov-action@v1
```

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- Performance benchmarks must stay within limits
- No new linter errors introduced 