/**
 * GraphBuilder Test Suite
 * 
 * Comprehensive tests for the GraphBuilder class functionality including:
 * - Basic node and edge creation
 * - Input source factory methods
 * - Complex workflows with loops
 * - Validation and error handling
 * - Compilation and output verification
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { z } from 'zod';
import { GraphBuilder, CompiledGraph } from '../GraphBuilder';
import { GraphDefinition, LLMTaskCategory } from '../types/graph.types';
import { 
  NodeOutputReference, 
  GraphInitialInputReference, 
  GraphConstantReference, 
  StaticValue,
  InputMappings 
} from '../types/builder.types';

// Mock agent class for testing
class MockAgent {
  static readonly agentSlug = 'test-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  
  constructor(deps: any) {
    // Mock constructor
  }
  
  async processInput(input: any) {
    return { success: true, payload: { mocked: true, input } };
  }
}

class MockRouterAgent {
  static readonly agentSlug = 'router-agent';
  static readonly defaultLlmTaskCategory = LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING;
  
  constructor(deps: any) {}
  
  async processInput(input: any) {
    return { success: true, payload: { route: 'selected_route' } };
  }
}

describe('GraphBuilder', () => {
  let builder: GraphBuilder;
  const testGraphId = 'test-graph';
  const testDescription = 'Test graph for unit testing';

  beforeEach(() => {
    builder = new GraphBuilder(testGraphId, testDescription);
  });

  describe('Basic Construction', () => {
    it('should create a builder with default configuration', () => {
      expect(builder).toBeInstanceOf(GraphBuilder);
    });

    it('should allow setting version', () => {
      const result = builder.setVersion('2.0.0');
      expect(result).toBe(builder); // Should return this for chaining
      
      // Add end node before compilation
      builder.addEndNode('test-end');
      
      const compiled = builder.compile();
      expect(compiled.definition.version).toBe('2.0.0');
    });

    it('should create default start node on construction', () => {
      // Add end node before compilation
      builder.addEndNode('test-end');
      
      const compiled = builder.compile();
      
      expect(compiled.definition.nodes).toHaveLength(2); // start + end
      expect(compiled.definition.nodes[0].type).toBe('start');
      expect(compiled.definition.entryNodeId).toBe(`__start_${testGraphId}__`);
    });
  });

  describe('Configuration Methods', () => {
    it('should add expected initial inputs', () => {
      const schema = z.string();
      
      builder
        .addExpectedInitialInput('userMessage', 'User input message', schema, false)
        .addExpectedInitialInput('optional', 'Optional input', undefined, true)
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const inputs = compiled.definition.expectedInitialInputs!;
      
      expect(inputs['userMessage']).toEqual({
        description: 'User input message',
        zodSchema: schema,
        isOptional: false
      });
      
      expect(inputs['optional']).toEqual({
        description: 'Optional input',
        zodSchema: undefined,
        isOptional: true
      });
    });

    it('should add graph constants', () => {
      builder
        .addGraphConstant('apiUrl', 'https://api.example.com')
        .addGraphConstant('maxRetries', 3)
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const constants = compiled.definition.graphConstants!;
      
      expect(constants['apiUrl']).toBe('https://api.example.com');
      expect(constants['maxRetries']).toBe(3);
    });

    it('should set global default max loop iterations', () => {
      builder
        .setGlobalDefaultMaxLoopIterations(5)
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      expect(compiled.definition.defaultMaxIterationsForAllLoops).toBe(5);
    });
  });

  describe('Input Source Factory Methods', () => {
    it('should create graph input references', () => {
      const inputRef = builder.graphInput('userMessage', 'default value');
      
      expect(inputRef).toEqual({
        _type: 'GraphInitialInputReference',
        inputKey: 'userMessage',
        defaultValue: 'default value',
        zodSchema: undefined
      });
      
      // Add end node before compilation
      builder.addEndNode('test-end');
      
      // Should auto-add the input to expected inputs
      const compiled = builder.compile();
      expect(compiled.definition.expectedInitialInputs!['userMessage']).toBeDefined();
    });

    it('should create node output references', () => {
      const nodeRef = builder.nodeOutput('sourceNode', 'payload.result', {
        defaultValue: null,
        outputVersion: 'latest'
      });
      
      expect(nodeRef).toEqual({
        _type: 'NodeOutputReference',
        nodeId: 'sourceNode',
        path: 'payload.result',
        outputVersion: 'latest',
        defaultValue: null,
        zodSchema: undefined,
        _internalFallbackRef: undefined
      });
    });

    it('should create node output references with fallback', () => {
      const fallback = builder.nodeOutput('fallbackNode', 'payload.fallback');
      const nodeRef = builder.nodeOutput('primaryNode', 'payload.result', {
        fallback: fallback
      });
      
      expect(nodeRef._internalFallbackRef).toBe(fallback);
    });

    it('should create constant references', () => {
      builder.addGraphConstant('testConstant', 'test value');
      
      const constantRef = builder.constant('testConstant');
      
      expect(constantRef).toEqual({
        _type: 'GraphConstantReference',
        constantKey: 'testConstant',
        zodSchema: undefined
      });
    });

    it('should throw error for non-existent constant', () => {
      expect(() => {
        builder.constant('nonExistent');
      }).toThrow('Referenced constant "nonExistent" is not defined');
    });

    it('should create static values', () => {
      const staticRef = builder.staticValue({ key: 'value' });
      
      expect(staticRef).toEqual({
        _type: 'StaticValue',
        value: { key: 'value' },
        zodSchema: undefined
      });
    });
  });

  describe('Node Addition Methods', () => {
    it('should add start nodes', () => {
      builder
        .addStartNode('custom-start', 'Custom start node')
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const startNode = compiled.definition.nodes.find(n => n.id === 'custom-start');
      
      expect(startNode).toEqual({
        id: 'custom-start',
        type: 'start',
        description: 'Custom start node'
      });
    });

    it('should add end nodes', () => {
      builder.addEndNode('end-success', 'Success end node');
      
      const compiled = builder.compile();
      const endNode = compiled.definition.nodes.find(n => n.id === 'end-success');
      
      expect(endNode).toEqual({
        id: 'end-success',
        type: 'end',
        description: 'Success end node',
        isErrorEndNode: undefined,
        inputs: undefined
      });
    });

    it('should add end nodes with error flag and output source', () => {
      const outputSource = builder.staticValue({ error: 'Test error' });
      
      builder.addEndNode('end-error', 'Error end node', true, outputSource);
      
      const compiled = builder.compile();
      const endNode = compiled.definition.nodes.find(n => n.id === 'end-error');
      
      expect((endNode as any)?.isErrorEndNode).toBe(true);
      expect(endNode?.inputs?.finalOutput).toEqual({
        type: 'staticValue',
        value: { error: 'Test error' },
        zodSchema: undefined
      });
    });

    it('should add agent nodes', () => {
      const inputs: InputMappings = {
        message: builder.graphInput('userMessage')
      };
      
      builder
        .addAgentNode(
          'agent-1',
          MockAgent,
          LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION,
          inputs,
          'Test agent node'
        )
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const agentNode = compiled.definition.nodes.find(n => n.id === 'agent-1');
      
      expect(agentNode).toEqual({
        id: 'agent-1',
        type: 'agentCall',
        agentSlug: 'test-agent',
        llmTaskCategory: LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION,
        description: 'Test agent node',
        inputs: {
          message: {
            type: 'fromGraphInitialInput',
            path: 'userMessage',
            zodSchema: undefined,
            defaultValue: undefined
          }
        }
      });
    });

    it('should add tool nodes', () => {
      const inputs: InputMappings = {
        data: builder.staticValue('test data')
      };
      
      builder
        .addToolNode('tool-1', 'json-parser', inputs, 'JSON parser tool')
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const toolNode = compiled.definition.nodes.find(n => n.id === 'tool-1');
      
      expect(toolNode).toEqual({
        id: 'tool-1',
        type: 'toolCall',
        toolName: 'json-parser',
        description: 'JSON parser tool',
        inputs: {
          data: {
            type: 'staticValue',
            value: 'test data',
            zodSchema: undefined
          }
        }
      });
    });

    it('should add conditional nodes', () => {
      const conditionFn = (inputs: any, ctx: any) => {
        return inputs.value > 5 ? 'high' : 'low';
      };
      
      const inputs: InputMappings = {
        value: builder.graphInput('inputValue')
      };
      
      builder
        .addConditionalNode('condition-1', conditionFn, inputs, 'Value condition')
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const condNode = compiled.definition.nodes.find(n => n.id === 'condition-1');
      
      expect(condNode?.type).toBe('conditionalBranch');
      expect(condNode?.description).toBe('Value condition');
      expect(typeof (condNode as any).conditionFunctionKey).toBe('string');
      
      // Check that function was registered
      const functionKey = (condNode as any).conditionFunctionKey;
      expect(compiled.functionRegistry[functionKey]).toBe(conditionFn);
    });

    it('should add data transform nodes', () => {
      const transformFn = (inputs: any, ctx: any) => {
        return { transformed: inputs.data.toUpperCase() };
      };
      
      // Create the referenced node first
      builder.addAgentNode('prev-node', MockAgent);
      
      const inputs: InputMappings = {
        data: builder.nodeOutput('prev-node', 'payload')
      };
      
      builder
        .addDataTransformNode('transform-1', transformFn, inputs, 'Transform data')
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const transformNode = compiled.definition.nodes.find(n => n.id === 'transform-1');
      
      expect(transformNode?.type).toBe('dataTransform');
      expect(transformNode?.description).toBe('Transform data');
      
      // Check that function was registered
      const functionKey = (transformNode as any).transformFunctionKey;
      expect(compiled.functionRegistry[functionKey]).toBe(transformFn);
    });
  });

  describe('Edge Addition Methods', () => {
    beforeEach(() => {
      // Add some nodes for edge testing
      builder
        .addStartNode('start-1')
        .addEndNode('end-1')
        .addAgentNode('agent-1', MockAgent);
    });

    it('should set initial node', () => {
      builder.setInitialNode('agent-1', 'Start with agent');
      
      const compiled = builder.compile();
      const edge = compiled.definition.edges.find(e => 
        e.from === `__start_${testGraphId}__` && e.to === 'agent-1'
      );
      
      expect(edge).toEqual({
        from: `__start_${testGraphId}__`,
        to: 'agent-1',
        label: 'onSuccess',
        description: 'Start with agent',
        isLoopBackEdge: false
      });
    });

    it('should add success edges', () => {
      builder
        .setLastAddedNodeId('agent-1')
        .onSuccess('end-1', 'Agent to end');
      
      const compiled = builder.compile();
      const edge = compiled.definition.edges.find(e => 
        e.from === 'agent-1' && e.to === 'end-1'
      );
      
      expect(edge).toEqual({
        from: 'agent-1',
        to: 'end-1',
        label: 'onSuccess',
        description: 'Agent to end',
        isLoopBackEdge: false
      });
    });

    it('should add failure edges', () => {
      builder.addEndNode('error-end');
      builder
        .setLastAddedNodeId('agent-1')
        .onFailure('error-end', 'Agent failed');
      
      const compiled = builder.compile();
      const edge = compiled.definition.edges.find(e => 
        e.from === 'agent-1' && e.to === 'error-end'
      );
      
      expect(edge?.label).toBe('onFailure');
    });

    it('should add branch edges for conditional nodes', () => {
      const conditionFn = () => 'branch-a';
      
      builder
        .addConditionalNode('condition-1', conditionFn)
        .onBranch('branch-a', 'end-1', 'Branch A path')
        .onBranch('branch-b', 'agent-1', 'Branch B path');
      
      const compiled = builder.compile();
      const edges = compiled.definition.edges.filter(e => e.from === 'condition-1');
      
      expect(edges).toHaveLength(2);
      expect(edges.find(e => e.label === 'branch-a')?.to).toBe('end-1');
      expect(edges.find(e => e.label === 'branch-b')?.to).toBe('agent-1');
    });

    it('should add otherwise edge for conditional nodes', () => {
      const conditionFn = () => 'specific-branch';
      
      builder
        .addConditionalNode('condition-1', conditionFn)
        .onBranch('specific-branch', 'agent-1')
        .otherwise('end-1', 'Default path');
      
      const compiled = builder.compile();
      const otherwiseEdge = compiled.definition.edges.find(e => 
        e.from === 'condition-1' && e.label === '__otherwise__'
      );
      
      expect(otherwiseEdge?.to).toBe('end-1');
    });

    it('should prevent duplicate branch labels', () => {
      const conditionFn = () => 'branch-a';
      
      builder.addConditionalNode('condition-1', conditionFn);
      builder.onBranch('branch-a', 'end-1');
      
      expect(() => {
        builder.onBranch('branch-a', 'agent-1');
      }).toThrow('Branch label "branch-a" is already defined');
    });

    it('should prevent duplicate otherwise branches', () => {
      const conditionFn = () => 'branch';
      
      builder.addConditionalNode('condition-1', conditionFn);
      builder.otherwise('end-1');
      
      expect(() => {
        builder.otherwise('agent-1');
      }).toThrow('otherwise branch is already defined');
    });
  });

  describe('Loop Construction', () => {
    beforeEach(() => {
      builder.addEndNode('end-success');
    });

    it('should create simple append loop', () => {
      const items = builder.staticValue(['a', 'b', 'c']);
      
      builder.addLoop(
        'test-loop',
        items,
        (loopBuilder, currentItem, currentIndex, accumulator, iterNodeId) => {
          loopBuilder.addDataTransformNode(
            'process-item',
            (inputs: any) => ({ processedItem: inputs.item.toUpperCase() }),
            { item: currentItem }
          );
          
          return {
            lastNodeInBodyId: 'process-item',
            accumulationLogic: {
              type: 'appendItem',
              itemToAppend: loopBuilder.nodeOutput('process-item', 'processedItem')
            }
          };
        }
      );
      
      const compiled = builder.compile();
      
      // Check loop structure nodes were created
      const loopNodes = compiled.definition.nodes.filter(n => 
        n.id.startsWith('test-loop_')
      );
      
      expect(loopNodes.length).toBeGreaterThanOrEqual(4); // init, condition, iter, update, exit
      
      // Check loop-related edges
      const loopEdges = compiled.definition.edges.filter(e => 
        e.from.startsWith('test-loop_') || e.to.startsWith('test-loop_')
      );
      
      expect(loopEdges.length).toBeGreaterThan(0);
    });

    it('should create custom transform loop', () => {
      const items = builder.staticValue([1, 2, 3]);
      
      builder.addLoop(
        'sum-loop',
        items,
        (loopBuilder, currentItem, currentIndex, accumulator, iterNodeId) => {
          loopBuilder.addDataTransformNode(
            'add-item',
            (inputs: any) => ({ sum: inputs.currentSum + inputs.item }),
            { 
              currentSum: accumulator,
              item: currentItem 
            }
          );
          
          return {
            lastNodeInBodyId: 'add-item',
            accumulationLogic: {
              type: 'customTransform',
              transformInput: loopBuilder.nodeOutput('add-item', 'sum'),
              transformFunctionKey: 'custom-sum-fn'
            }
          };
        },
        {
          accumulatorInitialValue: builder.staticValue(0),
          maxIterations: 10
        }
      );
      
      const compiled = builder.compile();
      
      // Verify loop was created
      const loopNodes = compiled.definition.nodes.filter(n => 
        n.id.startsWith('sum-loop_')
      );
      
      expect(loopNodes.length).toBeGreaterThanOrEqual(4);
    });

    it('should chain after loop completion', () => {
      const items = builder.staticValue(['test']);
      
      builder
        .addLoop('simple-loop', items, (loopBuilder, item) => ({
          lastNodeInBodyId: 'simple-loop_iteration_data_provider',
          accumulationLogic: {
            type: 'appendItem',
            itemToAppend: item
          }
        }))
        .onLoopComplete('end-success', 'Loop completed successfully');
      
      const compiled = builder.compile();
      
      // Find the loop exit node
      const exitNode = compiled.definition.nodes.find(n => 
        n.id === 'simple-loop_exit_aggregator'
      );
      
      expect(exitNode).toBeDefined();
      
      // Check edge from loop exit to end node
      const exitEdge = compiled.definition.edges.find(e => 
        e.from === 'simple-loop_exit_aggregator' && e.to === 'end-success'
      );
      
      expect(exitEdge?.label).toBe('onLoopComplete');
    });

    it('should prevent duplicate loop IDs', () => {
      const items = builder.staticValue(['test']);
      
      // First loop - should work
      builder.addLoop('duplicate-loop', items, (loopBuilder, item) => ({
        lastNodeInBodyId: 'duplicate-loop_iteration_data_provider', // Use the auto-created node
        accumulationLogic: { type: 'appendItem', itemToAppend: item }
      }));
      
      // Second loop with same ID - should fail
      expect(() => {
        builder.addLoop('duplicate-loop', items, (loopBuilder, item) => ({
          lastNodeInBodyId: 'duplicate-loop_iteration_data_provider',
          accumulationLogic: { type: 'appendItem', itemToAppend: item }
        }));
      }).toThrow('Loop with ID "duplicate-loop" already defined');
    });
  });

  describe('Input Resolution', () => {
    it('should resolve complex input mappings', () => {
      builder
        .addGraphConstant('baseUrl', 'https://api.test.com')
        .addExpectedInitialInput('apiKey', 'API key');
      
      // Create the referenced nodes first
      builder
        .addAgentNode('prev-node', MockAgent)
        .addAgentNode('fallback-node', MockAgent);
      
      const inputs: InputMappings = {
        staticData: builder.staticValue({ type: 'request' }),
        userKey: builder.graphInput('apiKey'),
        apiEndpoint: builder.constant('baseUrl'),
        previousResult: builder.nodeOutput('prev-node', 'payload.data', {
          defaultValue: null,
          fallback: builder.nodeOutput('fallback-node', 'payload')
        })
      };
      
      builder
        .addToolNode('complex-tool', 'api-call', inputs)
        .addEndNode('test-end'); // Add end node before compilation
      
      const compiled = builder.compile();
      const toolNode = compiled.definition.nodes.find(n => n.id === 'complex-tool');
      
      expect(toolNode?.inputs).toEqual({
        staticData: {
          type: 'staticValue',
          value: { type: 'request' },
          zodSchema: undefined
        },
        userKey: {
          type: 'fromGraphInitialInput',
          path: 'apiKey',
          zodSchema: undefined,
          defaultValue: undefined
        },
        apiEndpoint: {
          type: 'fromGraphConstant',
          constantKey: 'baseUrl',
          zodSchema: undefined
        },
        previousResult: {
          type: 'fromNodeOutput',
          nodeId: 'prev-node',
          path: 'payload.data',
          outputVersion: 'latest',
          zodSchema: undefined,
          defaultValue: null,
          fallbackSource: {
            type: 'fromNodeOutput',
            nodeId: 'fallback-node',
            path: 'payload',
            outputVersion: 'latest',
            zodSchema: undefined,
            defaultValue: undefined
          }
        }
      });
    });
  });

  describe('Validation', () => {
    it('should validate basic graph structure', () => {
      builder
        .addAgentNode('agent-1', MockAgent)
        .addEndNode('end-1')
        .setInitialNode('agent-1')
        .onSuccess('end-1');
      
      // Should not throw
      expect(() => builder.compile()).not.toThrow();
    });

    it('should require at least one end node', () => {
      builder.addAgentNode('agent-1', MockAgent);
      
      expect(() => builder.compile()).toThrow('Graph must have at least one end node');
    });

    it('should validate edge references', () => {
      builder
        .addEndNode('end-1')
        .setInitialNode('non-existent-node');
      
      expect(() => builder.compile()).toThrow('non-existent target node');
    });

    it('should validate node input references', () => {
      builder
        .addEndNode('end-1')
        .addToolNode('tool-1', 'test-tool', {
          badInput: builder.nodeOutput('non-existent-node')
        })
        .setInitialNode('tool-1')
        .onSuccess('end-1');
      
      expect(() => builder.compile()).toThrow('references non-existent node');
    });

    it('should validate graph input references', () => {
      builder
        .addEndNode('end-1')
        .addToolNode('tool-1', 'test-tool', {
          badInput: builder.graphInput('undeclaredInput')
        });
      
      // This should actually work because graphInput auto-adds the input
      expect(() => builder.compile()).not.toThrow();
      
      // But if we manually create a bad reference, it should fail
      const badInputs = {
        badInput: {
          _type: 'GraphInitialInputReference' as const,
          inputKey: 'reallyUndeclared'
        }
      };
      
      builder.addToolNode('tool-2', 'test-tool-2', badInputs);
      
      expect(() => builder.compile()).toThrow('references undeclared graph input');
    });

    it('should validate constant references', () => {
      builder
        .addEndNode('end-1')
        .addToolNode('tool-1', 'test-tool', {
          badInput: {
            _type: 'GraphConstantReference' as const,
            constantKey: 'undeclaredConstant'
          }
        })
        .setInitialNode('tool-1')
        .onSuccess('end-1');
      
      expect(() => builder.compile()).toThrow('references undeclared graph constant');
    });
  });

  describe('Error Handling', () => {
    it('should prevent duplicate node IDs', () => {
      builder.addStartNode('duplicate-id');
      
      expect(() => {
        builder.addStartNode('duplicate-id');
      }).toThrow('Node with ID "duplicate-id" already exists');
    });

    it('should require source node for edge methods', () => {
      // Test the actual error condition - manually clear lastAddedNodeIdInternal
      // Since constructor always creates a start node, we need to manually test the null case
      const newBuilder = new GraphBuilder('test-empty', 'Empty test');
      
      // Manually set lastAddedNodeIdInternal to null to test the error condition
      (newBuilder as any).lastAddedNodeIdInternal = null;
      
      expect(() => {
        newBuilder.onSuccess('test-end');
      }).toThrow('GraphBuilder: No source node for onSuccess edge. Add a node first.');
    });

    it('should validate conditional node requirements for branch methods', () => {
      builder.addAgentNode('agent-1', MockAgent);
      
      expect(() => {
        builder.onBranch('test-branch', 'some-node');
      }).toThrow('onBranch can only be used with a conditional branch node');
    });

    it('should validate function types for conditional nodes', () => {
      expect(() => {
        builder.addConditionalNode('bad-condition', 'not a function' as any);
      }).toThrow('conditionFunction must be a function');
    });

    it('should validate function types for transform nodes', () => {
      expect(() => {
        builder.addDataTransformNode('bad-transform', 'not a function' as any);
      }).toThrow('transformFunction must be a function');
    });
  });

  describe('Compilation Output', () => {
    it('should produce valid GraphDefinition structure', () => {
      builder
        .setVersion('1.2.0')
        .addExpectedInitialInput('userMessage', 'User message')
        .addGraphConstant('apiUrl', 'https://api.test.com')
        .setGlobalDefaultMaxLoopIterations(5)
        .addAgentNode('router', MockRouterAgent, undefined, {
          message: builder.graphInput('userMessage')
        })
        .addEndNode('success')
        .addEndNode('error', 'Error end', true)
        .setInitialNode('router')
        .onSuccess('success')
        .onFailure('error');
      
      const compiled = builder.compile();
      
      // Validate CompiledGraph structure
      expect(compiled).toHaveProperty('definition');
      expect(compiled).toHaveProperty('functionRegistry');
      
      const def = compiled.definition;
      
      // Validate GraphDefinition structure
      expect(def.id).toBe(testGraphId);
      expect(def.description).toBe(testDescription);
      expect(def.version).toBe('1.2.0');
      expect(def.entryNodeId).toBe(`__start_${testGraphId}__`);
      
      // Check nodes array
      expect(Array.isArray(def.nodes)).toBe(true);
      expect(def.nodes.length).toBeGreaterThan(0);
      
      // Check edges array
      expect(Array.isArray(def.edges)).toBe(true);
      expect(def.edges.length).toBeGreaterThan(0);
      
      // Check optional fields
      expect(def.expectedInitialInputs).toBeDefined();
      expect(def.graphConstants).toBeDefined();
      expect(def.defaultMaxIterationsForAllLoops).toBe(5);
    });

    it('should include all registered functions in function registry', () => {
      const conditionFn = () => 'test';
      const transformFn = () => ({ test: true });
      
      builder
        .addConditionalNode('condition-1', conditionFn)
        .onBranch('test', 'transform-1')
        .addDataTransformNode('transform-1', transformFn)
        .onSuccess('end-1')
        .addEndNode('end-1')
        .setInitialNode('condition-1');
      
      const compiled = builder.compile();
      
      const functionKeys = Object.keys(compiled.functionRegistry);
      expect(functionKeys).toHaveLength(2);
      
      const conditionKey = functionKeys.find(k => k.startsWith('cond_'));
      const transformKey = functionKeys.find(k => k.startsWith('transform_'));
      
      expect(conditionKey).toBeDefined();
      expect(transformKey).toBeDefined();
      expect(compiled.functionRegistry[conditionKey!]).toBe(conditionFn);
      expect(compiled.functionRegistry[transformKey!]).toBe(transformFn);
    });
  });

  describe('Advanced Workflows', () => {
    it('should build complex multi-agent workflow', () => {
      builder
        .setVersion('1.0.0')
        .addExpectedInitialInput('userRequest', 'User request text')
        .addGraphConstant('maxRetries', 3)
        
        // Route user request
        .addAgentNode('router', MockRouterAgent, undefined, {
          request: builder.graphInput('userRequest')
        })
        
        // Add conditional node for routing logic
        .addConditionalNode('route-condition', (inputs: any) => {
          // Mock routing logic based on router output
          const route = inputs.routerResult?.payload?.route || 'route-a';
          return route === 'route-a' ? 'route-a' : 'route-b';
        }, {
          routerResult: builder.nodeOutput('router', 'payload')
        })
        
        // Process different routes
        .addAgentNode('processor-a', MockAgent, undefined, {
          input: builder.nodeOutput('router', 'payload.route')
        })
        .addAgentNode('processor-b', MockAgent, undefined, {
          input: builder.nodeOutput('router', 'payload.route')
        })
        
        // Validate results
        .addConditionalNode('validator', (inputs: any) => {
          return inputs.result.success ? 'valid' : 'invalid';
        }, {
          result: builder.nodeOutput('processor-a', 'payload', {
            fallback: builder.nodeOutput('processor-b', 'payload')
          })
        })
        
        // End states
        .addEndNode('success', 'Successful completion')
        .addEndNode('failure', 'Failed processing', true)
        
        // Connect the workflow properly
        .setInitialNode('router')
        .setLastAddedNodeId('router')
        .onSuccess('route-condition') // Router success goes to conditional
        .onFailure('failure')
        
        .setLastAddedNodeId('route-condition')
        .onBranch('route-a', 'processor-a')
        .onBranch('route-b', 'processor-b')
        .otherwise('failure')
        
        .setLastAddedNodeId('processor-a')
        .onSuccess('validator')
        .onFailure('failure')
        
        .setLastAddedNodeId('processor-b')  
        .onSuccess('validator')
        .onFailure('failure')
        
        .setLastAddedNodeId('validator')
        .onBranch('valid', 'success')
        .onBranch('invalid', 'failure');
      
      const compiled = builder.compile();
      
      // Validate complex workflow structure (now has route-condition node too)
      expect(compiled.definition.nodes.length).toBeGreaterThan(7); // More nodes now
      expect(compiled.definition.edges.length).toBeGreaterThan(5);
      
      // Check specific nodes exist
      const nodeIds = compiled.definition.nodes.map(n => n.id);
      expect(nodeIds).toContain('router');
      expect(nodeIds).toContain('route-condition');
      expect(nodeIds).toContain('processor-a');
      expect(nodeIds).toContain('processor-b');
      expect(nodeIds).toContain('validator');
      expect(nodeIds).toContain('success');
      expect(nodeIds).toContain('failure');
    });
  });

  describe('Advanced Configuration Methods', () => {
    beforeEach(() => {
      builder
        .addStartNode('start-custom', 'Custom start node')
        .addAgentNode('agent-1', MockAgent)
        .addAgentNode('agent-2', MockAgent)
        .addEndNode('end-1')
        .addEndNode('error-end', 'Error handler end', true);
    });

    it('should set entry point with validation', () => {
      // setEntryPoint actually sets the graph's entry point directly
      // Let me test this differently to see what actually happens
      builder.setEntryPoint('start-custom');
      
      const compiled = builder.compile();
      
      // The entryNodeId should be set to the custom start node
      expect(compiled.definition.entryNodeId).toBe('start-custom');
    });

    it('should validate entry point node exists', () => {
      expect(() => {
        builder.setEntryPoint('non-existent-node');
      }).toThrow('Cannot set entry point to non-existent node');
    });

    it('should validate entry point must be start node', () => {
      expect(() => {
        builder.setEntryPoint('agent-1'); // Not a start node
      }).toThrow('Entry point node "agent-1" must be a start node');
    });

    it('should define per-loop configurations', () => {
      const loopConfig = { maxIterations: 5 };
      
      builder.defineLoopConfig('test-loop', loopConfig);
      
      // This should be stored internally for later use
      expect(() => builder.compile()).not.toThrow();
    });

    it('should register default error handler with validation', () => {
      // Should work with existing error end node
      builder.registerDefaultErrorHandlerNode('error-end');
      
      const compiled = builder.compile();
      expect(compiled.definition.defaultErrorHandlerNodeId).toBe('error-end');
    });

    it('should validate error handler node exists', () => {
      expect(() => {
        builder.registerDefaultErrorHandlerNode('non-existent-error-node');
      }).toThrow('Cannot register non-existent error handler node');
    });
  });

  describe('Direct Edge Addition', () => {
    beforeEach(() => {
      builder
        .addAgentNode('agent-1', MockAgent)
        .addAgentNode('agent-2', MockAgent)
        .addConditionalNode('condition-1', () => 'branch-a')
        .addEndNode('end-1');
    });

    it('should add direct edges between any nodes', () => {
      builder.addEdge('agent-1', 'agent-2', 'onSuccess', 'Direct connection');
      builder.addEdge('agent-2', 'end-1', 'onSuccess', 'To end');
      
      const compiled = builder.compile();
      
      const edge1 = compiled.definition.edges.find(e => 
        e.from === 'agent-1' && e.to === 'agent-2'
      );
      const edge2 = compiled.definition.edges.find(e => 
        e.from === 'agent-2' && e.to === 'end-1'
      );
      
      expect(edge1).toEqual({
        from: 'agent-1',
        to: 'agent-2',
        label: 'onSuccess',
        description: 'Direct connection',
        isLoopBackEdge: false
      });
      
      expect(edge2).toBeDefined();
    });

    it('should validate from node exists', () => {
      expect(() => {
        builder.addEdge('non-existent-from', 'agent-2', 'onSuccess');
      }).toThrow('Source node "non-existent-from"');
    });

    it('should allow custom edge labels', () => {
      builder.addEdge('condition-1', 'agent-2', 'custom-branch', 'Custom routing');
      
      const compiled = builder.compile();
      const edge = compiled.definition.edges.find(e => 
        e.from === 'condition-1' && e.label === 'custom-branch'
      );
      
      expect(edge?.to).toBe('agent-2');
      expect(edge?.description).toBe('Custom routing');
    });
  });

  describe('Cycle Detection', () => {
    beforeEach(() => {
      builder
        .addAgentNode('agent-a', MockAgent)
        .addAgentNode('agent-b', MockAgent)
        .addAgentNode('agent-c', MockAgent)
        .addEndNode('end-success');
    });

    it('should allow valid loop cycles with isLoopBackEdge', () => {
      // Create a loop structure - this should be allowed
      const items = builder.staticValue(['a', 'b']);
      
      builder.addLoop('valid-loop', items, (loopBuilder, item) => ({
        lastNodeInBodyId: 'valid-loop_iteration_data_provider',
        accumulationLogic: { type: 'appendItem', itemToAppend: item }
      }));
      
      // Should compile without cycle detection errors
      expect(() => builder.compile()).not.toThrow();
    });

    it('should not flag disconnected components as cycles', () => {
      // Create separate unconnected nodes
      builder
        .addAgentNode('isolated-1', MockAgent)
        .addAgentNode('isolated-2', MockAgent)
        .setInitialNode('agent-a')
        .onSuccess('end-success');
      
      // Should compile fine - no cycles present
      expect(() => builder.compile()).not.toThrow();
    });

    // Note: These cycle detection tests may not work as expected 
    // if cycle detection only runs during graph traversal rather than
    // during compilation. This would need investigation of the actual
    // implementation to determine when cycle detection occurs.
  });

  describe('Loop Internal Structure Testing', () => {
    it('should create all required internal loop nodes', () => {
      const items = builder.staticValue(['item1', 'item2']);
      
      builder.addLoop('detailed-loop', items, (loopBuilder, item) => {
        loopBuilder.addDataTransformNode('process-item', (inputs: any) => ({
          result: `processed: ${inputs.item}`
        }), { item });
        
        return {
          lastNodeInBodyId: 'process-item',
          accumulationLogic: { type: 'appendItem', itemToAppend: loopBuilder.nodeOutput('process-item', 'result') }
        };
      });
      
      builder.addEndNode('test-end'); // Add required end node
      
      const compiled = builder.compile();
      const loopNodes = compiled.definition.nodes.filter(n => n.id.startsWith('detailed-loop_'));
      
      // Should have: init_state, condition, iteration_data_provider, update_state, exit_aggregator
      expect(loopNodes.length).toBeGreaterThanOrEqual(5);
      
      // Check specific required nodes exist (use actual naming)
      const nodeIds = loopNodes.map(n => n.id);
      expect(nodeIds).toContain('detailed-loop_init_state');
      expect(nodeIds).toContain('detailed-loop_condition'); // Not condition_check
      expect(nodeIds).toContain('detailed-loop_iteration_data_provider');
      expect(nodeIds).toContain('detailed-loop_update_state');
      expect(nodeIds).toContain('detailed-loop_exit_aggregator');
    });

    it('should wire loop edges correctly including loop back edge', () => {
      const items = builder.staticValue(['test']);
      
      builder.addLoop('edge-test-loop', items, (loopBuilder, item) => ({
        lastNodeInBodyId: 'edge-test-loop_iteration_data_provider',
        accumulationLogic: { type: 'appendItem', itemToAppend: item }
      }));
      
      builder.addEndNode('test-end'); // Add required end node
      
      const compiled = builder.compile();
      const loopEdges = compiled.definition.edges.filter(e => 
        e.from.startsWith('edge-test-loop_') || e.to.startsWith('edge-test-loop_')
      );
      
      // Should have the critical loop back edge marked as isLoopBackEdge: true
      const loopBackEdge = loopEdges.find(e => e.isLoopBackEdge === true);
      expect(loopBackEdge).toBeDefined();
      expect(loopBackEdge?.from).toBe('edge-test-loop_update_state');
      expect(loopBackEdge?.to).toBe('edge-test-loop_condition'); // Not condition_check
    });

    it('should register loop entry and exit points in internal maps', () => {
      const items = builder.staticValue(['test']);
      
      builder.addLoop('mapping-test-loop', items, (loopBuilder, item) => ({
        lastNodeInBodyId: 'mapping-test-loop_iteration_data_provider',
        accumulationLogic: { type: 'appendItem', itemToAppend: item }
      }));
      
      // Test that onLoopComplete works (which requires proper map registration)
      builder.onLoopComplete('end-node');
      builder.addEndNode('end-node');
      
      const compiled = builder.compile();
      
      // Should find edge connecting loop exit to the target node
      const exitEdge = compiled.definition.edges.find(e => 
        e.from === 'mapping-test-loop_exit_aggregator' && e.to === 'end-node'
      );
      expect(exitEdge).toBeDefined();
      expect(exitEdge?.label).toBe('onLoopComplete');
    });

    it('should use maxIterations from defineLoopConfig when specified', () => {
      const items = builder.staticValue(['test']);
      
      builder
        .defineLoopConfig('config-test-loop', { maxIterations: 15 })
        .addLoop('config-test-loop', items, (loopBuilder, item) => ({
          lastNodeInBodyId: 'config-test-loop_iteration_data_provider',
          accumulationLogic: { type: 'appendItem', itemToAppend: item }
        }));
      
      builder.addEndNode('test-end'); // Add required end node
      
      const compiled = builder.compile();
      
      // Check that compilation succeeds (configuration is used internally)
      expect(compiled.definition).toBeDefined();
      expect(compiled.functionRegistry).toBeDefined();
      
      // The per-loop configuration is typically stored in the loop nodes themselves
      // rather than in the global defaultMaxLoopIterations
      const loopNodes = compiled.definition.nodes.filter(n => n.id.startsWith('config-test-loop_'));
      expect(loopNodes.length).toBeGreaterThan(0);
    });
  });
}); 