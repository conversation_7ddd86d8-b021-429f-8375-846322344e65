/**
 * Graph Builder
 * 
 * A fluent, type-safe API for programmatically defining GraphDefinition objects.
 * This is the primary way developers will construct executable AI workflows.
 */

import {
  GraphDefinition, 
  GraphNodeDefinition, 
  GraphEdgeDefinition,
  GraphInputValueSource, 
  GraphNodeInputMapping,
  StartNodeDef,
  EndNodeDef,
  AgentCallNodeDef,
  ToolCallNodeDef,
  ConditionalBranchNodeDef,
  DataTransformNodeDef,
  LLMTaskCategory,
  GraphInputValueSource_NodeOutput_Recursive,
  LoopConfig
} from './types/graph.types';

import {
  BaseResolvableInputValue,
  NodeOutputReference,
  GraphInitialInputReference,
  GraphConstantReference,
  StaticValue,
  ResolvableInputValue,
  InputMappings,
  LoopConstructPorts,
  RegisteredGraphFunction,
  FunctionRegistry,
  LoopAccumulationLogic,
  AgentDependencies,
  BaseN8nAgent
} from './types/builder.types';

import { ZodTypeAny } from 'zod';

// Re-export FunctionRegistry type for use by GraphExecutor
export { FunctionRegistry } from './types/builder.types';

/**
 * Result of compiling a graph definition.
 */
export interface CompiledGraph {
  /**
   * The graph definition object
   */
  definition: GraphDefinition;
  
  /**
   * Registry of functions used by the graph
   */
  functionRegistry: FunctionRegistry;
}

/**
 * GraphBuilder class provides a fluent API for creating graph definitions.
 * It abstracts the complexities of raw GraphDefinition objects and provides
 * type safety, validation, and helper methods for common patterns.
 */
export class GraphBuilder {
  private readonly graphId: string;
  private readonly graphDescription: string;
  private graphVersion: string = '1.0.0';

  private nodesMap: Map<string, GraphNodeDefinition> = new Map();
  private edgesList: GraphEdgeDefinition[] = [];

  private graphEntryPointId: string;

  private readonly expectedInitialInputsConfig: NonNullable<GraphDefinition['expectedInitialInputs']> = {};
  private readonly graphConstantsMap: NonNullable<GraphDefinition['graphConstants']> = {};

  private globalDefaultMaxIterations?: number;
  private readonly loopConfigs: Record<string, LoopConfig> = {};

  private registeredDefaultErrorHandlerNodeId?: string;

  private lastAddedNodeIdInternal: string | null = null;
  private definedBranchLabelsForLastConditionalInternal: Set<string> = new Set();

  // For managing logical loop IDs and their actual entry/exit node IDs
  private readonly loopIdToEntryNodeIdMap: Map<string, string> = new Map();
  private readonly loopIdToCompleteExitNodeIdMap: Map<string, string> = new Map();

  // For registering functions passed to addConditionalNode/addDataTransformNode
  private readonly functionStore: Map<string, RegisteredGraphFunction> = new Map();
  private functionKeyCounter: number = 0;

  /**
   * Creates a new GraphBuilder instance.
   * 
   * @param id - Unique identifier for the graph
   * @param description - Human-readable description of the graph's purpose
   */
  constructor(id: string, description: string) {
    this.graphId = id;
    this.graphDescription = description;
    
    // Create a default start node
    const defaultStartNodeId = `__start_${id}__`;
    this.addNodeInternal(defaultStartNodeId, { type: 'start', description: 'Default graph entry point' } as StartNodeDef);
    this.graphEntryPointId = defaultStartNodeId;
    this.lastAddedNodeIdInternal = defaultStartNodeId;
  }

  /**
   * Generates a unique function key for registering functions.
   * 
   * @param prefix - Prefix for the key (e.g., 'cond' for conditional functions)
   * @returns A unique function key
   */
  private generateFunctionKey(prefix: string): string {
    this.functionKeyCounter++;
    return `${prefix}_${this.graphId}_${this.functionKeyCounter}`;
  }

  /**
   * Internal method to add a node to the graph.
   * 
   * @param nodeId - Unique identifier for the node
   * @param nodeDefWithoutId - Node definition without the ID field
   */
  private addNodeInternal(nodeId: string, nodeDefWithoutId: Omit<StartNodeDef, 'id'> | Omit<EndNodeDef, 'id'> | Omit<AgentCallNodeDef, 'id'> | Omit<ToolCallNodeDef, 'id'> | Omit<ConditionalBranchNodeDef, 'id'> | Omit<DataTransformNodeDef, 'id'>): void {
    if (this.nodesMap.has(nodeId)) {
      throw new Error(`GraphBuilder: Node with ID "${nodeId}" already exists in graph "${this.graphId}".`);
    }
    const nodeWithId = { ...nodeDefWithoutId, id: nodeId } as GraphNodeDefinition;
    this.nodesMap.set(nodeId, nodeWithId);
    this.lastAddedNodeIdInternal = nodeId;
    
    // If it's not a conditional node, clear the branch labels set
    if (nodeDefWithoutId.type !== 'conditionalBranch') {
      this.definedBranchLabelsForLastConditionalInternal.clear();
    } else {
      // It's a new conditional node, so reset the branch labels set
      this.definedBranchLabelsForLastConditionalInternal = new Set();
    }
  }

  /**
   * Resolves a single input value source to a GraphInputValueSource.
   * 
   * @param resolvable - The resolvable input value
   * @returns A GraphInputValueSource object
   */
  private resolveSingleInputValueSource(resolvable: ResolvableInputValue): GraphInputValueSource {
    switch (resolvable._type) {
      case 'GraphInitialInputReference':
        return {
          type: 'fromGraphInitialInput',
          path: (resolvable as GraphInitialInputReference).inputKey,
          zodSchema: resolvable.zodSchema,
          defaultValue: resolvable.defaultValue
        };
      
      case 'NodeOutputReference': {
        const nodeRef = resolvable as NodeOutputReference;
        const mainSourceResolved: GraphInputValueSource = {
          type: 'fromNodeOutput',
          nodeId: nodeRef.nodeId,
          path: nodeRef.path,
          outputVersion: nodeRef.outputVersion || 'latest',
          zodSchema: nodeRef.zodSchema,
          defaultValue: nodeRef.defaultValue
        };
        
        // Handle fallback if present - implement the refined approach
        if (nodeRef._internalFallbackRef) {
          const fallbackBuilderRef = nodeRef._internalFallbackRef;
          if (fallbackBuilderRef._type !== 'NodeOutputReference') {
            throw new Error('GraphBuilder: Fallback for NodeOutputReference must be another NodeOutputReference.');
          }
          
          // Ensure the fallback itself doesn't have another fallback (to avoid deep nesting)
          if (fallbackBuilderRef._internalFallbackRef) {
            throw new Error('GraphBuilder: Fallback for NodeOutputReference cannot have its own fallback. Only one level of fallback is supported.');
          }
          
          // Create a properly typed fallback source
          const resolvedFallbackSource: GraphInputValueSource_NodeOutput_Recursive = {
            type: 'fromNodeOutput',
            nodeId: fallbackBuilderRef.nodeId,
            path: fallbackBuilderRef.path,
            outputVersion: fallbackBuilderRef.outputVersion || 'latest',
            zodSchema: fallbackBuilderRef.zodSchema,
            defaultValue: fallbackBuilderRef.defaultValue
            // No deeper fallbackSource
          };
          
          // Add the fallback to the main source with correct typing
          (mainSourceResolved as any).fallbackSource = resolvedFallbackSource;
        }
        
        return mainSourceResolved;
      }
      
      case 'GraphConstantReference':
        return {
          type: 'fromGraphConstant',
          constantKey: (resolvable as GraphConstantReference).constantKey,
          zodSchema: resolvable.zodSchema
        };
      
      case 'StaticValue':
        return {
          type: 'staticValue',
          value: (resolvable as StaticValue).value,
          zodSchema: resolvable.zodSchema
        };
      
      default:
        throw new Error(`GraphBuilder: Unsupported ResolvableInputValue type: ${(resolvable as any)._type}`);
    }
  }

  /**
   * Resolves a map of input mappings to GraphNodeInputMapping.
   * 
   * @param mappings - The input mappings to resolve
   * @returns A GraphNodeInputMapping object or undefined if no mappings provided
   */
  private resolveInputMappings(mappings?: InputMappings): GraphNodeInputMapping | undefined {
    if (!mappings) return undefined;
    
    const resolved: GraphNodeInputMapping = {};
    
    for (const key in mappings) {
      resolved[key] = this.resolveSingleInputValueSource(mappings[key]);
    }
    
    return resolved;
  }

  /**
   * Internal method to add an edge to the graph.
   * 
   * @param fromNodeIdOrLogical - Source node ID or logical loop ID
   * @param toNodeIdOrLogical - Target node ID or logical loop ID
   * @param label - Edge label (e.g., 'onSuccess', 'onFailure')
   * @param description - Human-readable description of the edge
   * @param isEntryPointEdge - Whether this is an edge from the entry point
   * @param isLoopBackEdge - Whether this is an intentional loop back edge
   */
  private addEdgeInternal(
    fromNodeIdOrLogical: string | null,
    toNodeIdOrLogical: string,
    label?: string,
    description?: string,
    isEntryPointEdge: boolean = false,
    isLoopBackEdge: boolean = false
  ): void {
    // Handle entry point edge
    if (isEntryPointEdge && !fromNodeIdOrLogical) {
      fromNodeIdOrLogical = this.graphEntryPointId;
    }
    
    // Validate source node
    if (!fromNodeIdOrLogical) {
      throw new Error('GraphBuilder: No source node specified for edge.');
    }
    
    // Resolve logical IDs to actual node IDs
    const actualFrom = this.loopIdToCompleteExitNodeIdMap.get(fromNodeIdOrLogical) || 
                       this.loopIdToEntryNodeIdMap.get(fromNodeIdOrLogical) || 
                       fromNodeIdOrLogical;
                       
    const actualTo = this.loopIdToEntryNodeIdMap.get(toNodeIdOrLogical) || toNodeIdOrLogical;
    
    // Validate source node exists - only validate the from node here
    if (!this.nodesMap.has(actualFrom)) {
      throw new Error(`GraphBuilder: Source node "${actualFrom}" (from original "${fromNodeIdOrLogical}") does not exist in graph "${this.graphId}".`);
    }
    
    // DO NOT validate actualTo here. It will be validated in _validateGraphStructure.
    
    // Add the edge
    this.edgesList.push({
      from: actualFrom,
      to: actualTo,
      label: label || 'onSuccess',
      description,
      isLoopBackEdge
    });
  }

  /**
   * Validates the graph structure before compilation.
   */
  private _validateGraphStructure(): void {
    // Validate entry point
    if (!this.nodesMap.has(this.graphEntryPointId)) {
      throw new Error(`GraphBuilder: Entry point node "${this.graphEntryPointId}" does not exist.`);
    }
    
    const entryNode = this.nodesMap.get(this.graphEntryPointId);
    if (entryNode?.type !== 'start') {
      throw new Error(`GraphBuilder: Entry point node "${this.graphEntryPointId}" is not a start node.`);
    }
    
    // Check for at least one end node
    let hasEndNode = false;
    for (const node of this.nodesMap.values()) {
      if (node.type === 'end') {
        hasEndNode = true;
        break;
      }
    }
    
    if (!hasEndNode) {
      throw new Error('GraphBuilder: Graph must have at least one end node.');
    }
    
    // Validate all node references in edges
    for (const edge of this.edgesList) {
      if (!this.nodesMap.has(edge.from)) {
        throw new Error(`GraphBuilder: Edge references non-existent source node "${edge.from}".`);
      }
      
      if (!this.nodesMap.has(edge.to)) {
        throw new Error(`GraphBuilder: Edge references non-existent target node "${edge.to}". Please ensure node "${edge.to}" is added to the graph.`);
      }
    }
    
    // Validate node input references
    for (const node of this.nodesMap.values()) {
      if (!node.inputs) continue;
      
      for (const inputKey in node.inputs) {
        const input = node.inputs[inputKey];
        
        if (input.type === 'fromNodeOutput') {
          if (!this.nodesMap.has(input.nodeId)) {
            throw new Error(`GraphBuilder: Node "${node.id}" input "${inputKey}" references non-existent node "${input.nodeId}".`);
          }
          
          // Also check fallback if present
          if ('fallbackSource' in input && input.fallbackSource && !this.nodesMap.has(input.fallbackSource.nodeId)) {
            throw new Error(`GraphBuilder: Node "${node.id}" input "${inputKey}" fallback references non-existent node "${input.fallbackSource.nodeId}".`);
          }
        } else if (input.type === 'fromGraphInitialInput') {
          if (!(input.path in this.expectedInitialInputsConfig)) {
            throw new Error(`GraphBuilder: Node "${node.id}" input "${inputKey}" references undeclared graph input "${input.path}".`);
          }
        } else if (input.type === 'fromGraphConstant') {
          if (!(input.constantKey in this.graphConstantsMap)) {
            throw new Error(`GraphBuilder: Node "${node.id}" input "${inputKey}" references undeclared graph constant "${input.constantKey}".`);
          }
        }
      }
    }
    
    // Enhanced cycle detection with path tracking for better error messages
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const path: string[] = []; // For error reporting
    
    const checkForCycles = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId); // Add to path for error reporting
      
      // Find all outgoing edges
      const outgoingEdges = this.edgesList.filter(edge => edge.from === nodeId);
      
      for (const edge of outgoingEdges) {
        // Skip edges marked as intentional loop back edges
        if (edge.isLoopBackEdge) {
          continue;
        }
        
        if (recursionStack.has(edge.to)) {
          // We found a cycle - add the target to the path for complete cycle reporting
          path.push(edge.to);
          throw new Error(`GraphBuilder: Unexpected cycle detected in graph: ${path.join(' -> ')}`);
        }
        
        if (!visited.has(edge.to)) {
          if (checkForCycles(edge.to)) {
            return true;
          }
        }
      }
      
      recursionStack.delete(nodeId);
      path.pop(); // Remove from path when backtracking
      return false;
    };
    
    // Start checking from the entry point for connected graphs
    if (checkForCycles(this.graphEntryPointId)) {
      // This shouldn't be reached as checkForCycles throws directly if it finds a cycle
      throw new Error('GraphBuilder: Unexpected cycle detected in graph.');
    }
  }

  // -------------------------------------------------------------------------
  // PUBLIC API METHODS
  // -------------------------------------------------------------------------

  // Configuration Methods
  
  /**
   * Sets the version of the graph definition.
   * 
   * @param version - The version string
   * @returns The builder instance for chaining
   */
  public setVersion(version: string): this {
    this.graphVersion = version;
    return this;
  }
  
  /**
   * Sets the entry point of the graph to a specific start node.
   * 
   * @param nodeId - ID of the start node to use as entry point
   * @returns The builder instance for chaining
   */
  public setEntryPoint(nodeId: string): this {
    // Validate node exists
    if (!this.nodesMap.has(nodeId)) {
      throw new Error(`GraphBuilder: Cannot set entry point to non-existent node "${nodeId}".`);
    }
    
    // Validate node is a start node
    const node = this.nodesMap.get(nodeId);
    if (node?.type !== 'start') {
      throw new Error(`GraphBuilder: Entry point node "${nodeId}" must be a start node.`);
    }
    
    this.graphEntryPointId = nodeId;
    return this;
  }
  
  /**
   * Adds an expected initial input to the graph.
   * 
   * @param key - Key of the input
   * @param description - Description of the input
   * @param zodSchema - Optional Zod schema for validation
   * @param isOptional - Whether the input is optional
   * @returns The builder instance for chaining
   */
  public addExpectedInitialInput(
    key: string,
    description: string,
    zodSchema?: ZodTypeAny,
    isOptional?: boolean
  ): this {
    this.expectedInitialInputsConfig[key] = {
      description,
      zodSchema,
      isOptional
    };
    return this;
  }
  
  /**
   * Adds a constant to the graph.
   * 
   * @param key - Key of the constant
   * @param value - Value of the constant
   * @returns The builder instance for chaining
   */
  public addGraphConstant(key: string, value: any): this {
    this.graphConstantsMap[key] = value;
    return this;
  }
  
  /**
   * Sets the global default maximum iterations for all loops.
   * 
   * @param iterations - Maximum number of iterations
   * @returns The builder instance for chaining
   */
  public setGlobalDefaultMaxLoopIterations(iterations: number): this {
    if (iterations <= 0) {
      throw new Error('GraphBuilder: Maximum iterations must be a positive number.');
    }
    this.globalDefaultMaxIterations = iterations;
    return this;
  }
  
  /**
   * Defines configuration for a specific loop type.
   * 
   * @param loopId - ID of the loop type to configure
   * @param config - Configuration for the loop
   * @returns The builder instance for chaining
   */
  public defineLoopConfig(loopId: string, config: LoopConfig): this {
    this.loopConfigs[loopId] = config;
    return this;
  }
  
  /**
   * Registers a default error handler node for the graph.
   * 
   * @param handlerNodeId - ID of the handler node
   * @returns The builder instance for chaining
   */
  public registerDefaultErrorHandlerNode(handlerNodeId: string): this {
    // Validate node exists
    if (!this.nodesMap.has(handlerNodeId)) {
      throw new Error(`GraphBuilder: Cannot register non-existent error handler node "${handlerNodeId}".`);
    }
    
    this.registeredDefaultErrorHandlerNodeId = handlerNodeId;
    return this;
  }

  // Input Source Factory Methods
  
  /**
   * Creates a reference to a graph initial input.
   * 
   * @param inputKey - Key of the input
   * @param defaultValue - Optional default value if input is not provided
   * @param zodSchema - Optional Zod schema for validation
   * @returns A GraphInitialInputReference object
   */
  public graphInput<T = any>(
    inputKey: string,
    defaultValue?: T,
    zodSchema?: ZodTypeAny
  ): GraphInitialInputReference<T> {
    // Auto-add the input if it doesn't exist
    if (!(inputKey in this.expectedInitialInputsConfig)) {
      this.addExpectedInitialInput(
        inputKey,
        `Auto-generated description for input ${inputKey}`,
        zodSchema,
        defaultValue !== undefined
      );
    }
    
    return {
      _type: 'GraphInitialInputReference',
      inputKey,
      defaultValue,
      zodSchema
    };
  }
  
  /**
   * Creates a reference to an output from another node.
   * 
   * @param nodeId - ID of the source node
   * @param path - Optional path to a specific field in the output
   * @param options - Additional options (defaultValue, zodSchema, outputVersion, fallback)
   * @returns A NodeOutputReference object
   */
  public nodeOutput<T = any>(
    nodeId: string,
    path?: string,
    options?: {
      defaultValue?: T;
      zodSchema?: ZodTypeAny;
      outputVersion?: 'latest' | number | string;
      fallback?: NodeOutputReference<T>;
    }
  ): NodeOutputReference<T> {
    // Ensure we have an options object
    const opts = options || {};
    
    return {
      _type: 'NodeOutputReference',
      nodeId,
      path,
      outputVersion: opts.outputVersion,
      defaultValue: opts.defaultValue,
      zodSchema: opts.zodSchema,
      _internalFallbackRef: opts.fallback
    };
  }
  
  /**
   * Creates a reference to a graph constant.
   * 
   * @param constantKey - Key of the constant
   * @param zodSchema - Optional Zod schema for validation
   * @returns A GraphConstantReference object
   */
  public constant<T = any>(
    constantKey: string,
    zodSchema?: ZodTypeAny
  ): GraphConstantReference<T> {
    // Validate constant exists
    if (!(constantKey in this.graphConstantsMap)) {
      throw new Error(`GraphBuilder: Referenced constant "${constantKey}" is not defined. Use addGraphConstant() first.`);
    }
    
    return {
      _type: 'GraphConstantReference',
      constantKey,
      zodSchema
    };
  }
  
  /**
   * Creates a static value.
   * 
   * @param value - The static value
   * @param zodSchema - Optional Zod schema for validation
   * @returns A StaticValue object
   */
  public staticValue<T = any>(
    value: T,
    zodSchema?: ZodTypeAny
  ): StaticValue<T> {
    return {
      _type: 'StaticValue',
      value,
      zodSchema
    };
  }

  // Node Addition Methods

  /**
   * Adds a start node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param description - Human-readable description of the node's purpose
   * @returns The builder instance for chaining
   */
  public addStartNode(id: string, description?: string): this {
    this.addNodeInternal(id, {
      type: 'start',
      description
    } as StartNodeDef);
    return this;
  }

  /**
   * Adds an end node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param description - Human-readable description of the node's purpose
   * @param isErrorEndNode - Whether this is an error end node
   * @param outputSource - Optional source for the final output of the graph
   * @returns The builder instance for chaining
   */
  public addEndNode(
    id: string,
    description?: string,
    isErrorEndNode?: boolean,
    outputSource?: ResolvableInputValue
  ): this {
    const inputs = outputSource
      ? { finalOutput: this.resolveSingleInputValueSource(outputSource) }
      : undefined;

    this.addNodeInternal(id, {
      type: 'end',
      description,
      isErrorEndNode,
      inputs
    } as EndNodeDef);
    return this;
  }

  /**
   * Adds an agent call node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param agentClass - Agent class constructor
   * @param llmTaskCategoryOverride - Optional override for the LLM task category
   * @param inputs - Input mappings for the agent
   * @param description - Human-readable description of the node's purpose
   * @returns The builder instance for chaining
   */
  public addAgentNode<TInputParams, TOutputPayload>(
    id: string, 
    agentClass: new (deps: AgentDependencies) => BaseN8nAgent<TInputParams, TOutputPayload>,
    llmTaskCategoryOverride?: LLMTaskCategory,
    inputs?: InputMappings,
    description?: string
  ): this {
    // 1. Extract agent slug from the agent class static property
    const staticSlug = (agentClass as any).agentSlug;
    if (!staticSlug) {
      throw new Error(`GraphBuilder: Agent class "${agentClass.name}" is missing the static 'agentSlug' property.`);
    }

    // 2. Determine LLM task category - in priority order:
    // 1. Override passed to this method
    // 2. Look up from AGENT_REGISTRY_CONFIG if available
    // 3. Static default on the agent class
    // 4. Fallback to a default category
    
    // Try to look up the agent in AGENT_REGISTRY_CONFIG if it exists in the global scope
    // This is a no-op if AGENT_REGISTRY_CONFIG doesn't exist
    let agentConfig;
    try {
      // @ts-ignore - AGENT_REGISTRY_CONFIG might not exist at compile time
      if (typeof AGENT_REGISTRY_CONFIG !== 'undefined') {
        // @ts-ignore
        agentConfig = AGENT_REGISTRY_CONFIG.find(entry => entry.slug === staticSlug);
      }
    } catch (error) {
      // If AGENT_REGISTRY_CONFIG is not available, continue with fallback approach
    }
    
    const categoryToUse = llmTaskCategoryOverride || 
                          (agentConfig?.defaultLlmTaskCategory) ||
                          (agentClass as any).defaultLlmTaskCategory || 
                          LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;

    // 3. Resolve inputs
    const resolvedInputs = this.resolveInputMappings(inputs);

    // 4. Add the node definition
    this.addNodeInternal(id, {
      type: 'agentCall',
      agentSlug: staticSlug,
      llmTaskCategory: categoryToUse,
      inputs: resolvedInputs,
      description: description || agentConfig?.description || (agentClass as any).description
    } as AgentCallNodeDef);

    return this;
  }

  /**
   * Adds a tool call node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param toolName - Unique name of the registered tool
   * @param inputs - Input mappings for the tool
   * @param description - Human-readable description of the node's purpose
   * @returns The builder instance for chaining
   */
  public addToolNode(
    id: string,
    toolName: string,
    inputs?: InputMappings,
    description?: string
  ): this {
    const resolvedInputs = this.resolveInputMappings(inputs);

    this.addNodeInternal(id, {
      type: 'toolCall',
      toolName,
      inputs: resolvedInputs,
      description
    } as ToolCallNodeDef);
    return this;
  }

  /**
   * Adds a conditional branch node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param conditionFunction - Function to determine which edge to follow
   * @param inputs - Input mappings for the condition
   * @param description - Human-readable description of the node's purpose
   * @returns The builder instance for chaining
   */
  public addConditionalNode(
    id: string,
    conditionFunction: RegisteredGraphFunction,
    inputs?: InputMappings,
    description?: string
  ): this {
    // Validate the function
    if (typeof conditionFunction !== 'function') {
      throw new Error('GraphBuilder: conditionFunction must be a function.');
    }

    // Register the function
    const functionKey = this.generateFunctionKey('cond');
    this.functionStore.set(functionKey, conditionFunction);

    const resolvedInputs = this.resolveInputMappings(inputs);

    this.addNodeInternal(id, {
      type: 'conditionalBranch',
      conditionFunctionKey: functionKey,
      inputs: resolvedInputs,
      description
    } as ConditionalBranchNodeDef);

    // Reset branch labels for this new conditional
    this.definedBranchLabelsForLastConditionalInternal.clear();
    return this;
  }

  /**
   * Adds a data transform node to the graph.
   * 
   * @param id - Unique identifier for the node
   * @param transformFunction - Function to transform the input data
   * @param inputs - Input mappings for the transform
   * @param description - Human-readable description of the node's purpose
   * @returns The builder instance for chaining
   */
  public addDataTransformNode(
    id: string,
    transformFunction: RegisteredGraphFunction,
    inputs?: InputMappings,
    description?: string
  ): this {
    // Validate the function
    if (typeof transformFunction !== 'function') {
      throw new Error('GraphBuilder: transformFunction must be a function.');
    }

    // Register the function
    const functionKey = this.generateFunctionKey('transform');
    this.functionStore.set(functionKey, transformFunction);

    const resolvedInputs = this.resolveInputMappings(inputs);

    this.addNodeInternal(id, {
      type: 'dataTransform',
      transformFunctionKey: functionKey,
      inputs: resolvedInputs,
      description
    } as DataTransformNodeDef);
    return this;
  }

  // Edge Addition Methods

  /**
   * Sets the initial node of the graph.
   * 
   * @param targetNodeId - ID of the node to connect from the entry point
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public setInitialNode(targetNodeId: string, description?: string): this {
    this.addEdgeInternal(this.graphEntryPointId, targetNodeId, 'onSuccess', description, true);
    
    // Update lastAddedNodeIdInternal to maintain fluent API contract
    // This allows subsequent .onSuccess(), .onFailure(), etc. to work from the initial node
    this.lastAddedNodeIdInternal = targetNodeId;
    
    return this;
  }

  /**
   * Adds an edge from the last added node to the target node, labeled with 'onSuccess'.
   * 
   * @param toNodeId - ID of the target node
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public onSuccess(toNodeId: string, description?: string): this {
    if (!this.lastAddedNodeIdInternal) {
      throw new Error('GraphBuilder: No source node for onSuccess edge. Add a node first.');
    }
    this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, 'onSuccess', description);
    return this;
  }

  /**
   * Adds an edge from the last added node to the target node, labeled with 'onFailure'.
   * 
   * @param toNodeId - ID of the target node
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public onFailure(toNodeId: string, description?: string): this {
    if (!this.lastAddedNodeIdInternal) {
      throw new Error('GraphBuilder: No source node for onFailure edge. Add a node first.');
    }
    this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, 'onFailure', description);
    return this;
  }

  /**
   * Adds an edge from the last added conditional node to the target node, with a custom branch label.
   * 
   * @param branchLabel - Label for the branch
   * @param toNodeId - ID of the target node
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public onBranch(branchLabel: string, toNodeId: string, description?: string): this {
    if (!this.lastAddedNodeIdInternal) {
      throw new Error('GraphBuilder: No source node for onBranch edge. Add a node first.');
    }

    // Validate the last added node is a conditional branch
    const lastNode = this.nodesMap.get(this.lastAddedNodeIdInternal);
    if (lastNode?.type !== 'conditionalBranch') {
      throw new Error(`GraphBuilder: onBranch can only be used with a conditional branch node. Last added node "${this.lastAddedNodeIdInternal}" is of type "${lastNode?.type}".`);
    }

    // Validate the branch label isn't reserved
    if (branchLabel === '__otherwise__') {
      throw new Error('GraphBuilder: Cannot use "__otherwise__" as a branch label. Use otherwise() method instead.');
    }

    // Validate the branch label isn't already used
    if (this.definedBranchLabelsForLastConditionalInternal.has(branchLabel)) {
      throw new Error(`GraphBuilder: Branch label "${branchLabel}" is already defined for node "${this.lastAddedNodeIdInternal}".`);
    }

    this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, branchLabel, description);
    this.definedBranchLabelsForLastConditionalInternal.add(branchLabel);
    return this;
  }

  /**
   * Adds a default edge from the last added conditional node to the target node.
   * This edge is taken if no other branch conditions match.
   * 
   * @param toNodeId - ID of the target node
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public otherwise(toNodeId: string, description?: string): this {
    if (!this.lastAddedNodeIdInternal) {
      throw new Error('GraphBuilder: No source node for otherwise edge. Add a node first.');
    }

    // Validate the last added node is a conditional branch
    const lastNode = this.nodesMap.get(this.lastAddedNodeIdInternal);
    if (lastNode?.type !== 'conditionalBranch') {
      throw new Error(`GraphBuilder: otherwise can only be used with a conditional branch node. Last added node "${this.lastAddedNodeIdInternal}" is of type "${lastNode?.type}".`);
    }

    // Validate the otherwise branch isn't already defined
    if (this.definedBranchLabelsForLastConditionalInternal.has('__otherwise__')) {
      throw new Error(`GraphBuilder: otherwise branch is already defined for node "${this.lastAddedNodeIdInternal}".`);
    }

    this.addEdgeInternal(this.lastAddedNodeIdInternal, toNodeId, '__otherwise__', description);
    this.definedBranchLabelsForLastConditionalInternal.add('__otherwise__');
    return this;
  }

  /**
   * Adds an explicit edge between two nodes.
   * 
   * @param fromNodeId - ID of the source node
   * @param toNodeId - ID of the target node
   * @param label - Label for the edge
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public addEdge(
    fromNodeId: string,
    toNodeId: string,
    label?: string,
    description?: string
  ): this {
    this.addEdgeInternal(fromNodeId, toNodeId, label, description);
    return this;
  }

  /**
   * Manually sets the last added node ID.
   * This is useful for continuing to chain edges from a specific node.
   * 
   * @param nodeId - ID of the node to set as last added
   * @returns The builder instance for chaining
   */
  public setLastAddedNodeId(nodeId: string): this {
    if (!this.nodesMap.has(nodeId)) {
      throw new Error(`GraphBuilder: Cannot set last added node ID to non-existent node "${nodeId}".`);
    }
    this.lastAddedNodeIdInternal = nodeId;
    return this;
  }

  /**
   * Compiles the graph definition.
   * This performs validation and returns the final GraphDefinition and FunctionRegistry.
   * 
   * @returns The compiled graph object
   */
  public compile(): CompiledGraph {
    // Validate the graph structure
    this._validateGraphStructure();
    
    // Create the graph definition
    const definition: GraphDefinition = {
      id: this.graphId,
      description: this.graphDescription,
      version: this.graphVersion,
      entryNodeId: this.graphEntryPointId,
      nodes: Array.from(this.nodesMap.values()),
      edges: this.edgesList,
    };
    
    // Add optional properties if they have values
    if (Object.keys(this.expectedInitialInputsConfig).length > 0) {
      definition.expectedInitialInputs = this.expectedInitialInputsConfig;
    }
    
    if (Object.keys(this.graphConstantsMap).length > 0) {
      definition.graphConstants = this.graphConstantsMap;
    }
    
    if (this.globalDefaultMaxIterations !== undefined) {
      definition.defaultMaxIterationsForAllLoops = this.globalDefaultMaxIterations;
    }
    
    if (Object.keys(this.loopConfigs).length > 0) {
      definition.loopConfigs = this.loopConfigs;
    }
    
    if (this.registeredDefaultErrorHandlerNodeId) {
      definition.defaultErrorHandlerNodeId = this.registeredDefaultErrorHandlerNodeId;
    }
    
    return {
      definition,
      functionRegistry: Object.fromEntries(this.functionStore)
    };
  }

  /**
   * Adds a loop construct to the graph.
   * This creates several internal nodes to handle iteration logic, state management, and results aggregation.
   * 
   * @param loopId - Unique identifier for the loop
   * @param itemsToIterateSource - Source for the array of items to iterate over
   * @param loopBodyBuilderFn - Function that builds the loop body using the provided builder
   * @param options - Optional configuration for the loop
   * @returns The builder instance for chaining
   */
  public addLoop<TItem = any, TAcc = any>(
    loopId: string,
    itemsToIterateSource: ResolvableInputValue<TItem[]>,
    loopBodyBuilderFn: (
      builder: GraphBuilder,
      currentItemRef: NodeOutputReference<TItem>,
      currentIndexRef: NodeOutputReference<number>,
      currentAccumulatorRef: NodeOutputReference<TAcc>,
      iterationDataProviderNodeId: string
    ) => {
        lastNodeInBodyId: string;
        accumulationLogic: LoopAccumulationLogic<any, TAcc>;
    },
    options?: {
      accumulatorInitialValue?: ResolvableInputValue<TAcc>;
      maxIterations?: number;
    }
  ): this {
    if (this.loopIdToEntryNodeIdMap.has(loopId)) {
      throw new Error(`GraphBuilder: Loop with ID "${loopId}" already defined.`);
    }

    // 1. Define internal node IDs for this loop instance
    const initNodeId = `${loopId}_init_state`;
    const conditionNodeId = `${loopId}_condition`;
    const iterDataProviderNodeId = `${loopId}_iteration_data_provider`;
    const updateStateNodeId = `${loopId}_update_state`;
    const exitAggregatorNodeId = `${loopId}_exit_aggregator`;

    // Register loop's logical entry and main success exit points
    this.loopIdToEntryNodeIdMap.set(loopId, initNodeId);
    this.loopIdToCompleteExitNodeIdMap.set(loopId, exitAggregatorNodeId);

    // Determine max iterations for this loop
    const maxIter = options?.maxIterations || 
                   (this.loopConfigs[loopId]?.maxIterations) || 
                   this.globalDefaultMaxIterations || 
                   10; // Sensible default

    // 2. Add initNodeId (DataTransformNode)
    // Output: { items: TItem[], currentIndex: 0, accumulator: TAcc, isLoopActive: true, iterationCount: 0, maxIterations: maxIter }
    const initTransformFnKey = this.generateFunctionKey(`loop_${loopId}_init`);
    this.functionStore.set(initTransformFnKey, ((inputs: Record<string, any>) => ({
      items: inputs.rawItems || [],
      currentIndex: 0,
      accumulator: inputs.initialAcc,
      isLoopActive: true, // Flag to control loop continuation
      iterationCount: 0, // Track iterations within the loop state itself
      maxIterations: inputs.maxIterations || maxIter, // Store max iterations in the state
    })) as RegisteredGraphFunction);

    this.addNodeInternal(initNodeId, {
      type: 'dataTransform',
      transformFunctionKey: initTransformFnKey,
      inputs: this.resolveInputMappings({
        rawItems: itemsToIterateSource,
        initialAcc: options?.accumulatorInitialValue || this.staticValue([] as unknown as TAcc),
        maxIterations: this.staticValue(maxIter),
      }),
      description: `Initialize state for loop: ${loopId}`
    } as DataTransformNodeDef);

    // 3. Add conditionNodeId (ConditionalBranchNode)
    // Inputs: currentLoopState (from initNodeId OR updateStateNodeId)
    // Outputs branch labels: '_loop_body_path_' or '_loop_exit_path_'
    const conditionFnKey = this.generateFunctionKey(`loop_${loopId}_cond`);
    this.functionStore.set(conditionFnKey, 
      ((inputs: Record<string, any>, graphCtx: any) => {
        const loopState = inputs.currentLoopState;
        
        // Use iteration count from loop state itself
        if (loopState.isLoopActive &&
            loopState.currentIndex < loopState.items.length &&
            loopState.iterationCount < loopState.maxIterations) {
          return '_loop_body_path_';
        }
        return '_loop_exit_path_';
      }) as RegisteredGraphFunction
    );

    this.addNodeInternal(conditionNodeId, {
      type: 'conditionalBranch',
      conditionFunctionKey: conditionFnKey,
      inputs: this.resolveInputMappings({
        // This is the key: gets latest state from update node, falls back to init node
        currentLoopState: this.nodeOutput(updateStateNodeId, '/', {
          fallback: this.nodeOutput(initNodeId)
        })
      }),
      description: `Condition for loop: ${loopId}`
    } as ConditionalBranchNodeDef);

    // Connect init node to condition node for first check
    this.addEdgeInternal(initNodeId, conditionNodeId, 'onSuccess');
    
    // 4. Add iterDataProviderNodeId (DataTransformNode)
    // Input: LoopStateObject (from conditionNodeId IF _loop_body_path_ is taken)
    // Output: { currentItem, currentIndex, currentAccumulatorSnapshot, _internalFullLoopState }
    const iterDataFnKey = this.generateFunctionKey(`loop_${loopId}_iter_data`);
    this.functionStore.set(iterDataFnKey,
      ((inputs: Record<string, any>) => {
        const loopState = inputs.loopStateFromCondition;
        return {
          currentItem: loopState.items[loopState.currentIndex],
          currentIndex: loopState.currentIndex,
          currentAccumulatorSnapshot: loopState.accumulator,
          _internalFullLoopState: loopState, // Pass through for update node
        };
      }) as RegisteredGraphFunction
    );

    this.addNodeInternal(iterDataProviderNodeId, {
      type: 'dataTransform',
      transformFunctionKey: iterDataFnKey,
      inputs: this.resolveInputMappings({
        // Access the currentLoopState from the conditional node's evaluatedInputs
        loopStateFromCondition: this.nodeOutput(conditionNodeId, 'evaluatedInputs.currentLoopState')
      }),
      description: `Provides data for loop ${loopId} iteration`
    } as DataTransformNodeDef);

    // Connect condition node to iteration data provider when condition passes
    this.addEdgeInternal(conditionNodeId, iterDataProviderNodeId, '_loop_body_path_');

    // 5. Call user's loopBodyBuilderFn to build the loop body
    const bodyConfig = loopBodyBuilderFn(
      this, // Pass the builder instance
      this.nodeOutput(iterDataProviderNodeId, 'currentItem'),
      this.nodeOutput(iterDataProviderNodeId, 'currentIndex'),
      this.nodeOutput(iterDataProviderNodeId, 'currentAccumulatorSnapshot'),
      iterDataProviderNodeId
    );

    // 6. Add updateStateNodeId (DataTransformNode)
    // Inputs: result from loop body, and _internalFullLoopState from iterDataProviderNodeId
    const updateStateFnKey = this.generateFunctionKey(`loop_${loopId}_update`);
    this.functionStore.set(updateStateFnKey,
      ((inputs: Record<string, any>, graphCtx: any) => {
        let newAccumulator: TAcc;
        const previousLoopState = inputs.previousLoopState;
        
        // Handle different accumulation logic types
        if (bodyConfig.accumulationLogic.type === 'appendItem') {
          // For appendItem, simply add the item to the accumulator
          // Use Array.concat to ensure we have an array
          newAccumulator = Array.isArray(previousLoopState.accumulator) 
            ? [...previousLoopState.accumulator, inputs.itemProcessingResult] as unknown as TAcc
            : [inputs.itemProcessingResult] as unknown as TAcc;
        } else { // customTransform
          // For customTransform, call the registered transform function
          const customTransformFn = graphCtx.functionRegistry[bodyConfig.accumulationLogic.transformFunctionKey];
          if (!customTransformFn) {
            throw new Error(`Custom accumulation function ${bodyConfig.accumulationLogic.transformFunctionKey} not found.`);
          }
          newAccumulator = customTransformFn(
            previousLoopState.accumulator, 
            inputs.itemProcessingResult, 
            previousLoopState.currentIndex
          );
        }

        // Increment the index for next iteration
        const newIndex = previousLoopState.currentIndex + 1;
        
        // Increment iteration count in loop state itself
        const newIterationCount = previousLoopState.iterationCount + 1;
        
        // Return updated loop state
        return {
          items: previousLoopState.items,
          currentIndex: newIndex,
          accumulator: newAccumulator,
          isLoopActive: newIndex < previousLoopState.items.length && newIterationCount < previousLoopState.maxIterations,
          iterationCount: newIterationCount,
          maxIterations: previousLoopState.maxIterations,
        };
      }) as RegisteredGraphFunction
    );

    // Prepare inputs for update state node based on accumulation logic type
    let updateStateInputs: InputMappings = {
      previousLoopState: this.nodeOutput(iterDataProviderNodeId, '_internalFullLoopState')
    };

    if (bodyConfig.accumulationLogic.type === 'appendItem') {
      updateStateInputs.itemProcessingResult = bodyConfig.accumulationLogic.itemToAppend;
    } else { // customTransform
      updateStateInputs.itemProcessingResult = bodyConfig.accumulationLogic.transformInput;
      
      // Register the custom transform function if provided
      if (typeof bodyConfig.accumulationLogic.transformFunctionKey === 'function') {
        const transformFn = bodyConfig.accumulationLogic.transformFunctionKey as unknown as RegisteredGraphFunction;
        const transformFnKey = this.generateFunctionKey(`loop_${loopId}_custom_acc_transform`);
        this.functionStore.set(transformFnKey, transformFn);
        bodyConfig.accumulationLogic.transformFunctionKey = transformFnKey;
      }
    }

    this.addNodeInternal(updateStateNodeId, {
      type: 'dataTransform',
      transformFunctionKey: updateStateFnKey,
      inputs: this.resolveInputMappings(updateStateInputs),
      description: `Updates state for loop: ${loopId}`
    } as DataTransformNodeDef);

    // Connect the last node in the body to the update state node
    this.addEdgeInternal(bodyConfig.lastNodeInBodyId, updateStateNodeId, 'onSuccess');
    
    // Connect update state back to condition node - explicitly mark as loop back edge
    this.addEdgeInternal(updateStateNodeId, conditionNodeId, 'onSuccess', undefined, false, true);

    // 7. Add exitAggregatorNodeId (DataTransformNode)
    // Input: Final LoopStateObject (from conditionNodeId when _loop_exit_path_ taken)
    const exitAggregatorFnKey = this.generateFunctionKey(`loop_${loopId}_exit_agg`);
    this.functionStore.set(exitAggregatorFnKey,
      ((inputs: Record<string, any>) => ({
        finalAccumulatedResults: inputs.finalLoopState.accumulator,
        itemsProcessed: inputs.finalLoopState.currentIndex,
        totalItems: inputs.finalLoopState.items.length,
      })) as RegisteredGraphFunction
    );

    this.addNodeInternal(exitAggregatorNodeId, {
      type: 'dataTransform',
      transformFunctionKey: exitAggregatorFnKey,
      inputs: this.resolveInputMappings({
        // Access the currentLoopState from the conditional node's evaluatedInputs
        finalLoopState: this.nodeOutput(conditionNodeId, 'evaluatedInputs.currentLoopState')
      }),
      description: `Aggregates final results for loop: ${loopId}`
    } as DataTransformNodeDef);

    // Connect condition node to exit aggregator when loop should exit
    this.addEdgeInternal(conditionNodeId, exitAggregatorNodeId, '_loop_exit_path_');

    // Set lastAddedNodeId to the primary success exit for chaining .onLoopComplete()
    this.lastAddedNodeIdInternal = exitAggregatorNodeId;
    return this;
  }

  /**
   * Adds an edge from the last added loop's complete exit node to the target node.
   * 
   * @param targetNodeId - ID of the target node
   * @param description - Description of the edge
   * @returns The builder instance for chaining
   */
  public onLoopComplete(targetNodeId: string, description?: string): this {
    if (!this.lastAddedNodeIdInternal) {
      throw new Error('GraphBuilder: No source node for onLoopComplete edge. Add a loop first.');
    }
    
    // Validate the last added node is a loop exit
    const loopExitNodeIds = Array.from(this.loopIdToCompleteExitNodeIdMap.values());
    if (!loopExitNodeIds.includes(this.lastAddedNodeIdInternal)) {
      throw new Error(`GraphBuilder: onLoopComplete can only be used after addLoop() and must chain from its completion exit point. Current lastAddedNodeId: "${this.lastAddedNodeIdInternal}".`);
    }
    
    this.addEdgeInternal(this.lastAddedNodeIdInternal, targetNodeId, 'onLoopComplete', description);
    return this;
  }
} 