/**
 * Graph Types
 * 
 * This file defines the core type definitions for the graph-based orchestration engine.
 * These types represent the structure of executable graphs, their nodes, edges, 
 * and how nodes receive inputs.
 */

import { z, ZodTypeAny } from 'zod';

/**
 * Categories for different types of LLM tasks, used to determine 
 * which LLM and prompt strategy to use for a given agent call.
 */
export enum LLMTaskCategory {
  CORE_ORCHESTRATION_AND_PLANNING = 'CORE_ORCHESTRATION_AND_PLANNING',
  N8N_CODE_GENERATION_COMPOSITION = 'N8N_CODE_GENERATION_COMPOSITION',
  KNOWLEDGE_AUGMENTED_EXPLANATION = 'KNOWLEDGE_AUGMENTED_EXPLANATION',
  ANALYTICAL_VALIDATION_CRITIQUE = 'ANALYTICAL_VALIDATION_CRITIQUE',
  UTILITY_CLASSIFICATION_ROUTING = 'UTILITY_CLASSIFICATION_ROUTING', // on_device_preferred potential
  UTILITY_CONVERSATION_SUMMARIZATION = 'UTILITY_CONVERSATION_SUMMARIZATION', // on_device_preferred potential
}

/**
 * Forward declaration for recursive fallback type
 */
export interface GraphInputValueSource_NodeOutput_Recursive {
  type: 'fromNodeOutput'; 
  nodeId: string; 
  path?: string; 
  outputVersion?: 'latest' | number | string;
  zodSchema?: ZodTypeAny; 
  defaultValue?: any;
  // No deeper fallback from a fallback for simplicity
}

/**
 * Defines how a node's input field should be populated.
 * This allows for flexible data flow between nodes in the graph.
 */
export type GraphInputValueSource =
  | { 
      type: 'fromGraphInitialInput'; 
      path: string; // Key of the initial input
      zodSchema?: ZodTypeAny; // Optional Zod schema for this input
      defaultValue?: any; 
    }
  | { 
      type: 'fromNodeOutput'; 
      nodeId: string; // ID of the source node
      path?: string; // Optional path to a specific field in the source node's output object (e.g., 'payload.user_name')
      outputVersion?: 'latest' | number | string /* runId */; // Default to 'latest'
      zodSchema?: ZodTypeAny; // Optional Zod schema for the expected data type from this source
      defaultValue?: any; 
      fallbackSource?: GraphInputValueSource_NodeOutput_Recursive; // Fallback if primary source fails/is undefined
    }
  | { 
      type: 'fromGraphConstant'; 
      constantKey: string; // Key of a graph-level constant
      zodSchema?: ZodTypeAny;
    }
  | { 
      type: 'staticValue'; 
      value: any; 
      zodSchema?: ZodTypeAny;
    };

/**
 * Maps input field names to their value sources for a graph node.
 */
export interface GraphNodeInputMapping {
  [inputFieldName: string]: GraphInputValueSource;
}

/**
 * Base definition for all graph nodes.
 */
export interface BaseGraphNodeDefinition {
  id: string; // Unique within the graph
  description?: string;
  inputs?: GraphNodeInputMapping; // How this node receives its inputs
  timeoutMs?: number; // Optional timeout for this node's execution
}

/**
 * Start node definition - entry point for the graph.
 */
export interface StartNodeDef extends BaseGraphNodeDefinition {
  type: 'start';
}

/**
 * End node definition - terminal point for the graph.
 */
export interface EndNodeDef extends BaseGraphNodeDefinition {
  type: 'end';
  isErrorEndNode?: boolean; // If true, signifies this is a terminal error state for the graph
  // The 'inputs' to an EndNode define the final output of the graph if this EndNode is reached.
}

/**
 * Agent call node definition - executes an AI agent.
 */
export interface AgentCallNodeDef extends BaseGraphNodeDefinition {
  type: 'agentCall';
  agentSlug: string; // Unique slug to identify the agent logic/class
  llmTaskCategory: LLMTaskCategory; // Specifies which LLM configuration to use
  // The specific Zod schema for this agent's output payload is not part of GraphDefinition
  // but is known by the agent itself and the PromptBuilderService for prompt injection.
}

/**
 * Tool call node definition - executes a tool.
 */
export interface ToolCallNodeDef extends BaseGraphNodeDefinition {
  type: 'toolCall';
  toolName: string; // Unique name of the registered tool
}

/**
 * Conditional branch node definition - routes execution based on a condition.
 */
export interface ConditionalBranchNodeDef extends BaseGraphNodeDefinition {
  type: 'conditionalBranch';
  // The actual conditionFunction (JS function) is not stored in the serializable GraphDefinition.
  // Instead, a key or reference to a registered function is stored.
  // The GraphExecutor will resolve this key to the actual function at runtime.
  conditionFunctionKey: string; 
  // The function signature would be: 
  // (inputs: Record<string, any>, graphContext: GraphExecutionContext) => string | string[];
  // Returns one or more edge labels to follow.
}

/**
 * Data transform node definition - transforms data between nodes.
 */
export interface DataTransformNodeDef extends BaseGraphNodeDefinition {
  type: 'dataTransform';
  // Similar to conditionFunctionKey, stores a key to a registered transform function.
  // The function signature: 
  // (inputs: Record<string, any>, graphContext: GraphExecutionContext) => Record<string, any>;
  transformFunctionKey: string; 
}

/**
 * Union type of all possible node definitions.
 */
export type GraphNodeDefinition =
  | StartNodeDef
  | EndNodeDef
  | AgentCallNodeDef
  | ToolCallNodeDef
  | ConditionalBranchNodeDef
  | DataTransformNodeDef;

/**
 * Definition for a directed edge connecting two nodes in the graph.
 */
export interface GraphEdgeDefinition {
  id?: string; // Optional unique ID for the edge itself
  from: string; // Source GraphNodeDefinition.id
  to: string;   // Target GraphNodeDefinition.id
  label?: string; // e.g., 'onSuccess', 'onFailure', '__otherwise__', custom branch label from ConditionalBranchNodeDef
  description?: string;
  priority?: number; // Optional, for conditional branches if multiple edges could be chosen
  isLoopBackEdge?: boolean; // Marker for edges that are intentionally part of a loop construct (used by GraphBuilder for cycle detection)
}

/**
 * Loop configuration
 */
export interface LoopConfig {
  maxIterations?: number;
  // Other loop-specific configs can be added here
}

/**
 * Top-level definition of an executable graph.
 */
export interface GraphDefinition {
  id: string; // Unique ID for the graph type (e.g., 'createNewWorkflowGraph_v1')
  description: string;
  version: string; // For managing updates to graph structures

  entryNodeId: string; // ID of a node with type 'start'

  nodes: GraphNodeDefinition[];
  edges: GraphEdgeDefinition[];

  // Metadata for defining expected inputs when this graph is called
  expectedInitialInputs?: {
    [inputKey: string]: {
      description: string;
      zodSchema?: ZodTypeAny; // For validation by OrchestratorService/GraphExecutor
      isOptional?: boolean;
    };
  };

  // Static constants available throughout this graph's execution via GraphInputValueSource
  graphConstants?: {
    [constantKey: string]: any;
  };
  
  // Loop control defaults - used by GraphBuilder.addLoop() when constructing loop nodes
  // The loop-related fields are primarily used by the GraphBuilder, not directly by GraphExecutor.
  // Loop iteration control is handled by the conditional functions created by GraphBuilder.addLoop()
  defaultMaxIterationsForAllLoops?: number;
  defaultMaxLoopIterations?: { // Per specific loopId defined in the graph
    [loopId: string]: number;
  };
  loopConfigs?: Record<string, LoopConfig>; // Full loop configuration by loop ID

  // Default error handling
  defaultErrorHandlerNodeId?: string; // ID of a node to route to if a node fails and has no specific onFailure edge
} 