/**
 * Graph Builder Types
 * 
 * This file defines helper types for the GraphBuilder's fluent API.
 * These types make the builder's method signatures type-safe and expressive.
 */

import { ZodTypeAny } from 'zod';

// --- Agent Types (Placeholder definitions) ---

/**
 * Dependencies injected into agent instances
 */
export interface AgentDependencies {
  // This will be defined more fully later
  // Could include: LLMService, PromptBuilderService, etc.
  [key: string]: any;
}

/**
 * Base agent interface with generic input and output types
 */
export interface BaseN8nAgent<TInputParams = any, TOutputPayload = any> {
  // This will be defined more fully later
  // Could include methods like: execute, getPrompt, etc.
  processInput(input: TInputParams): Promise<TOutputPayload>;
}

// --- Input Value Representation for Builder API ---

/**
 * Base interface for resolvable input values
 */
export interface BaseResolvableInputValue {
  _type: string;
  zodSchema?: ZodTypeAny;
  defaultValue?: any;
}

/**
 * Reference to an output from another node in the graph
 */
export interface NodeOutputReference<T = any> extends BaseResolvableInputValue {
  _type: 'NodeOutputReference';
  nodeId: string;
  path?: string; // e.g., 'payload.user.name'
  outputVersion?: 'latest' | number | string /* runId */;
  // For internal use by builder to construct GraphInputValueSource with fallback
  _internalFallbackRef?: NodeOutputReference<T>;
}

/**
 * Reference to an initial input of the graph
 */
export interface GraphInitialInputReference<T = any> extends BaseResolvableInputValue {
  _type: 'GraphInitialInputReference';
  inputKey: string;
}

/**
 * Reference to a constant defined in the graph
 */
export interface GraphConstantReference<T = any> extends BaseResolvableInputValue {
  _type: 'GraphConstantReference';
  constantKey: string;
}

/**
 * A static value to be used directly
 */
export interface StaticValue<T = any> extends BaseResolvableInputValue {
  _type: 'StaticValue';
  value: T;
}

/**
 * Union type of all possible input value sources
 */
export type ResolvableInputValue<T = any> =
  | NodeOutputReference<T>
  | GraphInitialInputReference<T>
  | GraphConstantReference<T>
  | StaticValue<T>;

/**
 * Mapping of input field names to their value sources
 */
export type InputMappings = Record<string, ResolvableInputValue>;

/**
 * Interface for loop construct ports, returned by addLoop
 */
export interface LoopConstructPorts {
  loopId: string;
  // Use with builder.addEdge(previousNode, loop.entryTargetId)
  // This is the ID of the actual first node *inside* the loop construct (e.g., init_state node)
  entryTargetId: string;

  // Use with builder.setLastAddedNodeId(loop.completeExitNodeId).onSuccess(...)
  // This is the ID of the node that outputs the final accumulator on normal completion
  completeExitNodeId: string;
}

/**
 * Type for registered graph functions (conditional or transform)
 */
export type RegisteredGraphFunction =
  | ((inputs: Record<string, any>, graphContext: any) => string | string[]) // Conditional
  | ((inputs: Record<string, any>, graphContext: any) => Record<string, any>); // DataTransform

/**
 * Registry for storing functions by key
 */
export interface FunctionRegistry {
  [key: string]: RegisteredGraphFunction;
}

/**
 * Type for loop accumulation logic
 */
export type LoopAccumulationLogic<TItem = any, TAcc = any> = 
  | { 
      type: 'appendItem'; 
      itemToAppend: ResolvableInputValue<TItem>; 
    } 
  | { 
      type: 'customTransform'; 
      transformInput: ResolvableInputValue<any>; 
      transformFunctionKey: string; 
    }; 