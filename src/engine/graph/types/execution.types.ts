/**
 * Graph Execution Types
 * 
 * This file defines types related to the execution of graphs, including
 * execution context, token usage, and other runtime-related concepts.
 */

import { FunctionRegistry } from './builder.types';
import { GraphDefinition } from './graph.types';

/**
 * Possible states of graph execution
 */
export type GraphExecutionStatus = 
  | 'IDLE'                 // Graph is initialized but not yet running
  | 'RUNNING'              // Graph is actively executing nodes
  | 'PAUSED_FOR_USER_INPUT'// Execution paused for human-in-the-loop input
  | 'COMPLETED'            // Successfully reached an end node
  | 'FAILED'               // Execution failed due to an error
  | 'ABORTED'              // Execution was manually aborted
  | 'RESUMING';            // Resuming execution after a pause

/**
 * Represents the context in which a graph is executed.
 * This is passed to node execution functions and provides access to
 * the graph definition, function registry, and other execution state.
 */
export interface GraphExecutionContext {
  /**
   * ID of the graph definition being executed
   */
  graphDefinitionId: string;
  
  /**
   * Version of the graph definition being executed
   */
  graphDefinitionVersion: string;
  
  /**
   * ID of the task session this graph execution belongs to
   */
  taskSessionId: string;
  
  /**
   * Unique ID for this specific graph execution run
   */
  currentGraphRunId: string;
  
  /**
   * Current status of the graph execution
   */
  status: GraphExecutionStatus;
  
  /**
   * The graph definition being executed
   */
  graphDefinition: GraphDefinition;
  
  /**
   * Registry of functions used by the graph
   */
  functionRegistry: FunctionRegistry;
  
  /**
   * Initial inputs provided to the graph
   */
  initialInputs: Record<string, any>;
  
  /**
   * Versioned outputs from each node that has executed
   */
  nodeOutputs: Map<string, NodeOutputVersion[]>;
  
  /**
   * ID of the node currently being processed, if any
   */
  currentNodeIdBeingProcessed: string | null;
  
  /**
   * Queue of node IDs waiting to be processed
   */
  processingQueue?: string[];
  
  /**
   * Records token usage for LLM calls
   */
  tokenUsage: LLMTokenUsage;
  
  /**
   * Details about the active user interrupt if execution is paused
   */
  activeInterruptDetails?: any;
  
  /**
   * ID of the last node that completed execution
   */
  lastCompletedNodeId?: string;
  
  /**
   * Error that caused the graph to fail, if applicable
   */
  lastError?: ExecutionError;
  
  /**
   * Services used directly by the executor
   */
  _internalExecutorServices?: any;
  
  /**
   * Services available to agents and other components during execution
   */
  services: any;
}

/**
 * Snapshot of GraphExecutionContext for persistence and resumption
 */
export interface GraphExecutionContextSnapshot {
  graphDefinitionId: string;
  graphDefinitionVersion: string;
  taskSessionId: string;
  currentGraphRunId: string;
  status: GraphExecutionStatus;
  initialInputs: Record<string, any>;
  nodeOutputs: [string, NodeOutputVersion[]][]; // Serializable form of Map
  currentNodeIdBeingProcessed: string | null;
  activeInterruptDetails?: any;
  lastError?: any;
  lastCompletedNodeId?: string;
  processingQueue: string[];
  totalStepsExecuted: number;
  tokenUsage: LLMTokenUsage;
}

/**
 * Structured execution error information
 */
export interface ExecutionError {
  /**
   * Error message
   */
  message: string;
  
  /**
   * Error stack trace
   */
  stack?: string;
  
  /**
   * Type of error
   */
  type?: string;
  
  /**
   * Additional error data
   */
  data?: any;
}

/**
 * Represents token usage metrics for LLM calls.
 */
export interface LLMTokenUsage {
  /**
   * Total number of prompt tokens used
   */
  promptTokens: number;
  
  /**
   * Total number of completion tokens used
   */
  completionTokens: number;
  
  /**
   * Total tokens used (prompt + completion)
   */
  totalTokens: number;
  
  /**
   * Detailed breakdown of token usage by node
   */
  byNode: Record<string, {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  }>;
}

/**
 * Represents a version of a node's output.
 * Nodes can produce multiple outputs during execution, and this
 * allows referring to specific versions.
 */
export interface NodeOutputVersion {
  /**
   * The run ID for this output
   */
  runId?: string;
  
  /**
   * The version identifier (could be a run ID, iteration number, etc.)
   */
  version?: string | number;
  
  /**
   * Timestamp when this output was produced
   */
  timestamp: number;
  
  /**
   * The output data
   */
  output: any;
  
  /**
   * Status of the node execution
   */
  status: 'success' | 'failure' | 'interrupted_for_user_input';
  
  /**
   * Error details if status is 'failure'
   */
  error?: ExecutionError;
  
  /**
   * Agent thought process, if applicable
   */
  thought?: string;
  
  /**
   * Token usage for this node execution
   */
  llmTokenUsage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  
  /**
   * Payload for user interruption, if applicable
   */
  interruptPayload?: any;
}

/**
 * Result of a graph execution
 */
export interface GraphExecutionResult {
  /**
   * Final status of the execution
   */
  status: GraphExecutionStatus;
  
  /**
   * Final output of the graph, if successful
   */
  output?: any;
  
  /**
   * Error that caused the graph to fail, if applicable
   */
  error?: ExecutionError;
  
  /**
   * Final execution context snapshot, for debugging or persistence
   */
  finalContextSnapshot?: GraphExecutionContextSnapshot;
}

/**
 * Custom error types for graph execution
 */
export class GraphExecutionError extends Error {
  type: string;
  data?: any;
  
  constructor(message: string, type: string = 'GraphExecutionError', data?: any) {
    super(message);
    this.name = 'GraphExecutionError';
    this.type = type;
    this.data = data;
  }
}

export class NodeInputResolutionError extends GraphExecutionError {
  constructor(message: string, data?: any) {
    super(message, 'NodeInputResolutionError', data);
    this.name = 'NodeInputResolutionError';
  }
}

export class FunctionRegistryError extends GraphExecutionError {
  constructor(message: string, data?: any) {
    super(message, 'FunctionRegistryError', data);
    this.name = 'FunctionRegistryError';
  }
}

export class AgentExecutionError extends GraphExecutionError {
  constructor(message: string, data?: any) {
    super(message, 'AgentExecutionError', data);
    this.name = 'AgentExecutionError';
  }
}

export class ToolExecutionError extends GraphExecutionError {
  constructor(message: string, data?: any) {
    super(message, 'ToolExecutionError', data);
    this.name = 'ToolExecutionError';
  }
} 