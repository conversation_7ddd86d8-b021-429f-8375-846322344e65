import { describe, it, expect } from 'vitest';
import {
  mergeMessages,
  canMergeMessages,
  mergeMessageContent,
  mergeMessagesWithSemanticAnalysis,
  type MergeResult,
  type MergeOptions
} from '../MessageMerger';
import { ModelMessage } from 'ai';

describe('MessageMerger', () => {
  describe('mergeMessages', () => {
    it('should handle empty array', () => {
      const result = mergeMessages([]);
      expect(result).toEqual({
        messages: [],
        mergeCount: 0
      });
    });

    it('should handle single message', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual(messages);
      expect(result.mergeCount).toBe(0);
      expect(result.warnings).toBeUndefined();
    });

    it('should merge adjacent messages with same role and string content', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: 'How are you?' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual([
        { role: 'user', content: 'Hello\n\nHow are you?' }
      ]);
      expect(result.mergeCount).toBe(1);
    });

    it('should not merge messages with different roles', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual(messages);
      expect(result.mergeCount).toBe(0);
    });

    it('should not merge system messages by default', () => {
      const messages: ModelMessage[] = [
        { role: 'system', content: 'You are helpful.' },
        { role: 'system', content: 'Be concise.' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual(messages);
      expect(result.mergeCount).toBe(0);
    });

    it('should merge system messages when explicitly enabled', () => {
      const messages: ModelMessage[] = [
        { role: 'system', content: 'You are helpful.' },
        { role: 'system', content: 'Be concise.' }
      ];

      const result = mergeMessages(messages, { mergeSystemMessages: true });
      expect(result.messages).toEqual([
        { role: 'system', content: 'You are helpful.\n\nBe concise.' }
      ]);
      expect(result.mergeCount).toBe(1);
    });

    it('should never merge tool messages', () => {
      const messages: ModelMessage[] = [
        {
          role: 'tool',
          content: [{ type: 'tool-result', toolCallId: 'call1', toolName: 'search', result: 'result1' }]
        },
        {
          role: 'tool',
          content: [{ type: 'tool-result', toolCallId: 'call2', toolName: 'search', result: 'result2' }]
        }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual(messages);
      expect(result.mergeCount).toBe(0);
    });

    it('should not merge assistant messages containing tool calls', () => {
      const messages: ModelMessage[] = [
        {
          role: 'assistant',
          content: [
            { type: 'text', text: 'I will search for that.' },
            { type: 'tool-call', toolCallId: 'call1', toolName: 'search', args: { query: 'test' } }
          ]
        },
        {
          role: 'assistant',
          content: 'Here are the results.'
        }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual(messages);
      expect(result.mergeCount).toBe(0);
    });

    it('should skip empty messages entirely', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: '' },
        { role: 'user', content: 'How are you?' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual([
        { role: 'user', content: 'Hello\n\nHow are you?' }
      ]);
      expect(result.mergeCount).toBe(1);
      expect(result.warnings).toContain('Message 1: Empty message skipped');
    });

    it('should merge multiple adjacent messages of same role', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'First' },
        { role: 'user', content: 'Second' },
        { role: 'user', content: 'Third' },
        { role: 'assistant', content: 'Response' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual([
        { role: 'user', content: 'First\n\nSecond\n\nThird' },
        { role: 'assistant', content: 'Response' }
      ]);
      expect(result.mergeCount).toBe(2);
    });

    it('should preserve message IDs when merging', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'First' },
        { role: 'user', content: 'Second', id: 'msg-123' }
      ];

      const result = mergeMessages(messages);
      expect(result.messages).toEqual([
        { role: 'user', content: 'First\n\nSecond', id: 'msg-123' }
      ]);
    });

    it('should use custom string separator', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: 'World' }
      ];

      const result = mergeMessages(messages, { stringSeparator: ' | ' });
      expect(result.messages).toEqual([
        { role: 'user', content: 'Hello | World' }
      ]);
    });
  });

  describe('canMergeMessages', () => {
    it('should return false for different roles', () => {
      const msg1: ModelMessage = { role: 'user', content: 'Hello' };
      const msg2: ModelMessage = { role: 'assistant', content: 'Hi' };

      expect(canMergeMessages(msg1, msg2)).toBe(false);
    });

    it('should return true for same role user messages', () => {
      const msg1: ModelMessage = { role: 'user', content: 'Hello' };
      const msg2: ModelMessage = { role: 'user', content: 'World' };

      expect(canMergeMessages(msg1, msg2)).toBe(true);
    });

    it('should return false for system messages by default', () => {
      const msg1: ModelMessage = { role: 'system', content: 'Be helpful' };
      const msg2: ModelMessage = { role: 'system', content: 'Be concise' };

      expect(canMergeMessages(msg1, msg2)).toBe(false);
    });

    it('should return true for system messages when explicitly enabled', () => {
      const msg1: ModelMessage = { role: 'system', content: 'Be helpful' };
      const msg2: ModelMessage = { role: 'system', content: 'Be concise' };

      expect(canMergeMessages(msg1, msg2, { mergeSystemMessages: true })).toBe(true);
    });

    it('should return false for tool messages', () => {
      const msg1: ModelMessage = {
        role: 'tool',
        content: [{ type: 'tool-result', toolCallId: 'call1', toolName: 'search', result: 'result1' }]
      };
      const msg2: ModelMessage = {
        role: 'tool',
        content: [{ type: 'tool-result', toolCallId: 'call2', toolName: 'search', result: 'result2' }]
      };

      expect(canMergeMessages(msg1, msg2)).toBe(false);
    });

    it('should return false for assistant messages with tool calls', () => {
      const msg1: ModelMessage = {
        role: 'assistant',
        content: [
          { type: 'text', text: 'I will help' },
          { type: 'tool-call', toolCallId: 'call1', toolName: 'search', args: {} }
        ]
      };
      const msg2: ModelMessage = { role: 'assistant', content: 'Here you go' };

      expect(canMergeMessages(msg1, msg2)).toBe(false);
    });

    it('should respect preserveSemanticBoundaries option', () => {
      const msg1: ModelMessage = { role: 'user', content: 'Hello' };
      const msg2: ModelMessage = { role: 'user', content: 'World' };

      expect(canMergeMessages(msg1, msg2, { preserveSemanticBoundaries: true })).toBe(false);
    });
  });

  describe('mergeMessageContent', () => {
    it('should merge two strings with default separator', () => {
      const result = mergeMessageContent('Hello', 'World');
      expect(result).toBe('Hello\n\nWorld');
    });

    it('should merge two strings with custom separator', () => {
      const result = mergeMessageContent('Hello', 'World', { separator: ' | ' });
      expect(result).toBe('Hello | World');
    });

    it('should handle empty strings', () => {
      expect(mergeMessageContent('', 'World')).toBe('World');
      expect(mergeMessageContent('Hello', '')).toBe('Hello');
      expect(mergeMessageContent('', '')).toBe('');
    });

    it('should handle whitespace-only strings', () => {
      expect(mergeMessageContent('  ', 'World')).toBe('World');
      expect(mergeMessageContent('Hello', '  ')).toBe('Hello');
      expect(mergeMessageContent('  ', '   ')).toBe('   '); // Keeps longer whitespace
    });

    it('should merge string and array (string first)', () => {
      const array = [{ type: 'text', text: 'World' }];
      const result = mergeMessageContent('Hello', array);
      
      expect(result).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'World' }
      ]);
    });

    it('should merge array and string (array first)', () => {
      const array = [{ type: 'text', text: 'Hello' }];
      const result = mergeMessageContent(array, 'World');
      
      expect(result).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'World' }
      ]);
    });

    it('should merge two arrays', () => {
      const array1 = [{ type: 'text', text: 'Hello' }];
      const array2 = [{ type: 'text', text: 'World' }];
      const result = mergeMessageContent(array1, array2);
      
      expect(result).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'World' }
      ]);
    });

    it('should handle null/undefined content', () => {
      expect(mergeMessageContent(null, 'World')).toBe('World');
      expect(mergeMessageContent('Hello', null)).toBe('Hello');
      expect(mergeMessageContent(null, null)).toBe('');
      expect(mergeMessageContent(undefined, 'World')).toBe('World');
    });

    it('should handle complex content parts', () => {
      const array1 = [
        { type: 'text', text: 'Hello' },
        { type: 'tool-call', toolCallId: 'call1', toolName: 'search', args: {} }
      ];
      const array2 = [{ type: 'text', text: 'World' }];
      
      const result = mergeMessageContent(array1, array2);
      expect(result).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'tool-call', toolCallId: 'call1', toolName: 'search', args: {} },
        { type: 'text', text: 'World' }
      ]);
    });

    it('should fallback to JSON stringify for non-string, non-array content', () => {
      const obj1 = { data: 'test1' };
      const obj2 = { data: 'test2' };
      
      const result = mergeMessageContent(obj1, obj2);
      expect(result).toBe('{"data":"test1"}\n\n{"data":"test2"}');
    });
  });

  describe('edge cases and error handling', () => {
    it('should throw error for non-array input', () => {
      expect(() => mergeMessages(null as any)).toThrow('Messages must be an array');
      expect(() => mergeMessages('not an array' as any)).toThrow('Messages must be an array');
    });

    it('should handle invalid message structures gracefully', () => {
      const invalidMessages = [
        { role: 'user', content: 'Valid message' },
        null,
        { role: 'assistant' }, // Missing content
        { content: 'Missing role' },
        { role: 'user', content: 'Another valid message' }
      ];

      const result = mergeMessages(invalidMessages as any);
      
      expect(result.messages).toEqual([
        { role: 'user', content: 'Valid message\n\nAnother valid message' }
      ]);
      expect(result.warnings).toBeDefined();
      expect(result.warnings?.some(w => w.includes('Invalid message structure'))).toBe(true);
    });

    it('should handle merge failures gracefully', () => {
      // This tests the catch block in the merge logic
      // We can simulate this by providing problematic content structures
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: 'World' }
      ];

      // Normal case should work
      const result = mergeMessages(messages);
      expect(result.mergeCount).toBe(1);
    });

    it('should preserve original messages when merging fails', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: 'World' }
      ];

      const result = mergeMessages(messages);
      
      // Original messages should not be mutated
      expect(messages[0].content).toBe('Hello');
      expect(messages[1].content).toBe('World');
    });
  });

  describe('performance and complex scenarios', () => {
    it('should handle large message arrays efficiently', () => {
      const messages: ModelMessage[] = [];
      for (let i = 0; i < 1000; i++) {
        messages.push({ role: 'user', content: `Message ${i}` });
      }

      const result = mergeMessages(messages);
      
      expect(result.messages).toHaveLength(1);
      expect(result.mergeCount).toBe(999);
      expect(result.messages[0].content).toContain('Message 0');
      expect(result.messages[0].content).toContain('Message 999');
    });

    it('should handle mixed content types in conversation', () => {
      const messages: ModelMessage[] = [
        { role: 'system', content: 'You are helpful' },
        { role: 'user', content: 'Hello' },
        { role: 'user', content: [{ type: 'text', text: 'How are you?' }] },
        {
          role: 'assistant',
          content: [
            { type: 'text', text: 'I am well' },
            { type: 'tool-call', toolCallId: 'call1', toolName: 'search', args: {} }
          ]
        },
        {
          role: 'tool',
          content: [{ type: 'tool-result', toolCallId: 'call1', toolName: 'search', result: 'Found info' }]
        },
        { role: 'assistant', content: 'Based on the search...' }
      ];

      const result = mergeMessages(messages);
      
      // Should merge the two user messages but keep others separate
      expect(result.messages).toHaveLength(5);
      expect(result.mergeCount).toBe(1);
      
      // Check that user messages were merged
      const userMessage = result.messages.find(m => m.role === 'user');
      expect(userMessage?.content).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'How are you?' }
      ]);
    });
  });

  describe('mergeMessagesWithSemanticAnalysis', () => {
    it('should use custom semantic boundary detector', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Question 1' },
        { role: 'user', content: 'Question 2' }
      ];

      // Custom detector that prevents merging
      const semanticBoundaryDetector = () => true;

      const result = mergeMessagesWithSemanticAnalysis(messages, {
        semanticBoundaryDetector
      });

      // Should not merge due to semantic boundary
      expect(result.messages).toHaveLength(2);
      expect(result.mergeCount).toBe(0);
    });

    it('should fall back to normal merging without detector', () => {
      const messages: ModelMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'user', content: 'World' }
      ];

      const result = mergeMessagesWithSemanticAnalysis(messages);
      
      expect(result.messages).toHaveLength(1);
      expect(result.mergeCount).toBe(1);
    });
  });
});