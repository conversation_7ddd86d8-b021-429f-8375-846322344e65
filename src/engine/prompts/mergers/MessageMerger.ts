/**
 * MessageMerger.ts
 * 
 * Handles the complex logic of merging adjacent, role-compatible ModelMessages.
 * Supports merging string + string, string + array, and array + array content
 * while preserving important message boundaries and conversation flow integrity.
 */

import { ModelMessage, TextPart, ToolCallPart, ToolResultPart, ImagePart } from 'ai';

/**
 * Result of message merging operation
 */
export interface MergeResult {
  messages: ModelMessage[];
  mergeCount: number;
  warnings?: string[];
}

/**
 * Configuration options for message merging
 */
export interface MergeOptions {
  /** Whether to merge system messages (default: false) */
  mergeSystemMessages?: boolean;
  /** Whether to preserve semantic boundaries even for same-role messages (default: false) */
  preserveSemanticBoundaries?: boolean;
  /** Custom separator for string merging (default: '\n\n') */
  stringSeparator?: string;
}

/**
 * Merges adjacent messages with same role where appropriate
 * 
 * @param messages - Array of ModelMessages to merge
 * @param options - Configuration options for merging behavior
 * @returns MergeResult with merged messages, count, and any warnings
 */
export function mergeMessages(messages: ModelMessage[], options: MergeOptions = {}): MergeResult {
  if (!Array.isArray(messages)) {
    throw new Error('Messages must be an array');
  }

  if (messages.length === 0) {
    return {
      messages: [],
      mergeCount: 0
    };
  }

  const {
    mergeSystemMessages = false,
    preserveSemanticBoundaries = false,
    stringSeparator = '\n\n'
  } = options;

  const warnings: string[] = [];
  const mergedMessages: ModelMessage[] = [];
  let mergeCount = 0;

  for (let i = 0; i < messages.length; i++) {
    const currentMessage = messages[i];
    
    // Validate message structure
    if (!isValidMessageStructure(currentMessage)) {
      warnings.push(`Message ${i}: Invalid message structure, skipping`);
      continue;
    }

    // Skip empty messages entirely
    if (isEmptyMessage(currentMessage)) {
      warnings.push(`Message ${i}: Empty message skipped`);
      continue;
    }

    const lastMerged = mergedMessages[mergedMessages.length - 1];
    
    if (lastMerged && canMergeMessages(lastMerged, currentMessage, { mergeSystemMessages, preserveSemanticBoundaries })) {
      try {
        const mergedContent = mergeMessageContent(
          lastMerged.content,
          currentMessage.content,
          { separator: stringSeparator }
        );
        
        lastMerged.content = mergedContent;
        mergeCount++;
        
        // Preserve any additional properties from the later message
        if ((currentMessage as any).id && !(lastMerged as any).id) {
          (lastMerged as any).id = (currentMessage as any).id;
        }
      } catch (error) {
        warnings.push(`Message ${i}: Failed to merge with previous message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        mergedMessages.push(currentMessage);
      }
    } else {
      mergedMessages.push({ ...currentMessage });
    }
  }

  return {
    messages: mergedMessages,
    mergeCount,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Determines if two messages can be safely merged
 * 
 * @param msg1 - First message
 * @param msg2 - Second message
 * @param options - Merge behavior options
 * @returns true if messages can be merged
 */
export function canMergeMessages(
  msg1: ModelMessage, 
  msg2: ModelMessage, 
  options: { mergeSystemMessages?: boolean; preserveSemanticBoundaries?: boolean } = {}
): boolean {
  const { mergeSystemMessages = false, preserveSemanticBoundaries = false } = options;
  
  // Must have identical roles
  if (msg1.role !== msg2.role) {
    return false;
  }

  // Check role-specific merge rules
  switch (msg1.role) {
    case 'system':
      // System messages generally should not merge unless explicitly allowed
      return mergeSystemMessages && !preserveSemanticBoundaries;
      
    case 'tool':
      // Tool messages should never merge - each represents a distinct tool result
      return false;
      
    case 'assistant':
      // Don't merge assistant messages containing tool calls
      if (containsToolCalls(msg1) || containsToolCalls(msg2)) {
        return false;
      }
      break;
      
    case 'user':
      // User messages can generally be merged unless preserving boundaries
      if (preserveSemanticBoundaries) {
        return false;
      }
      break;
      
    default:
      return false;
  }

  // Check content type compatibility
  return areContentTypesCompatible(msg1.content, msg2.content);
}

/**
 * Merges content from two messages
 * 
 * @param content1 - Content from first message
 * @param content2 - Content from second message  
 * @param options - Merge options
 * @returns Merged content
 */
export function mergeMessageContent(
  content1: string | any[],
  content2: string | any[],
  options: { separator?: string } = {}
): string | any[] {
  const { separator = '\n\n' } = options;

  // Handle null/undefined content
  if (content1 == null && content2 == null) {
    return '';
  }
  if (content1 == null) {
    return content2;
  }
  if (content2 == null) {
    return content1;
  }

  const isString1 = typeof content1 === 'string';
  const isString2 = typeof content2 === 'string';
  const isArray1 = Array.isArray(content1);
  const isArray2 = Array.isArray(content2);

  if (isString1 && isString2) {
    // String + String merging
    return mergeStringContent(content1, content2, separator);
  }

  if (isString1 && isArray2) {
    // String + Array: Convert string to TextPart and merge arrays
    const textPart: TextPart = { type: 'text', text: content1 };
    return [textPart, ...content2];
  }

  if (isArray1 && isString2) {
    // Array + String: Convert string to TextPart and merge arrays
    const textPart: TextPart = { type: 'text', text: content2 };
    return [...content1, textPart];
  }

  if (isArray1 && isArray2) {
    // Array + Array: Direct concatenation
    return [...content1, ...content2];
  }

  // Fallback: Convert both to strings and merge
  const str1 = isString1 ? content1 : JSON.stringify(content1);
  const str2 = isString2 ? content2 : JSON.stringify(content2);
  return mergeStringContent(str1, str2, separator);
}

/**
 * Merges two string contents with appropriate separator
 */
function mergeStringContent(str1: string, str2: string, separator: string): string {
  const trimmed1 = str1.trim();
  const trimmed2 = str2.trim();

  if (!trimmed1 && !trimmed2) {
    // Both empty - preserve the longer whitespace string or return empty
    return str1.length >= str2.length ? str1 : str2;
  }

  if (!trimmed1) {
    return str2;
  }

  if (!trimmed2) {
    return str1;
  }

  // Both have content - merge with separator
  return trimmed1 + separator + trimmed2;
}

/**
 * Checks if message structure is valid for merging consideration
 */
function isValidMessageStructure(message: any): message is ModelMessage {
  return (
    message &&
    typeof message === 'object' &&
    typeof message.role === 'string' &&
    message.content !== undefined
  );
}

/**
 * Checks if a message is effectively empty
 */
function isEmptyMessage(message: ModelMessage): boolean {
  if (message.content == null) {
    return true;
  }

  if (typeof message.content === 'string') {
    return message.content.trim() === '';
  }

  if (Array.isArray(message.content)) {
    return message.content.length === 0 || 
           message.content.every(part => 
             part.type === 'text' && (!part.text || part.text.trim() === '')
           );
  }

  return false;
}

/**
 * Checks if message contains tool calls
 */
function containsToolCalls(message: ModelMessage): boolean {
  if (!Array.isArray(message.content)) {
    return false;
  }

  return message.content.some((part: any) => part.type === 'tool-call');
}

/**
 * Checks if two content types are compatible for merging
 */
function areContentTypesCompatible(content1: any, content2: any): boolean {
  // Both null/undefined
  if (content1 == null && content2 == null) {
    return true;
  }

  // One null/undefined
  if (content1 == null || content2 == null) {
    return true;
  }

  const isString1 = typeof content1 === 'string';
  const isString2 = typeof content2 === 'string';
  const isArray1 = Array.isArray(content1);
  const isArray2 = Array.isArray(content2);

  // Compatible combinations:
  // - string + string
  // - string + array
  // - array + string  
  // - array + array
  return (isString1 || isArray1) && (isString2 || isArray2);
}

/**
 * Advanced merging with semantic boundary detection
 * This function provides more sophisticated merging logic for future use
 */
export function mergeMessagesWithSemanticAnalysis(
  messages: ModelMessage[],
  options: MergeOptions & {
    /** Function to detect semantic boundaries between messages */
    semanticBoundaryDetector?: (msg1: ModelMessage, msg2: ModelMessage) => boolean;
  } = {}
): MergeResult {
  const { semanticBoundaryDetector, ...baseOptions } = options;

  if (semanticBoundaryDetector) {
    // Use custom semantic boundary detection
    const enhancedOptions = {
      ...baseOptions,
      preserveSemanticBoundaries: true
    };

    // Override canMergeMessages logic to use custom detector
    const customCanMerge = (msg1: ModelMessage, msg2: ModelMessage) => {
      if (!canMergeMessages(msg1, msg2, { mergeSystemMessages: enhancedOptions.mergeSystemMessages })) {
        return false;
      }
      return !semanticBoundaryDetector(msg1, msg2);
    };

    // For now, fall back to basic merging
    // In a more sophisticated implementation, we would inject the custom logic
    return mergeMessages(messages, enhancedOptions);
  }

  return mergeMessages(messages, options);
}