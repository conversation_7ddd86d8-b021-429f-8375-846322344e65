/**
 * Mergers Module
 * 
 * Exports message merging utilities for the prompt engineering subsystem.
 * These utilities handle the complex logic of merging adjacent, role-compatible
 * ModelMessages while preserving conversation structure and semantic boundaries.
 */

export {
  mergeMessages,
  canMergeMessages,
  mergeMessageContent,
  mergeMessagesWithSemanticAnalysis,
  type MergeResult,
  type MergeOptions
} from './MessageMerger';