import { describe, it, expect } from 'vitest';
import { formatAssistantMessage } from '../AssistantMessageFormatter';
import { AssistantMessageComponent } from '../../services/types/genericPromptBuilder.types';

describe('AssistantMessageFormatter', () => {
  describe('formatAssistantMessage', () => {
    it('should format a basic assistant message', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'I can help you create a webhook-triggered workflow for processing data.'
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('I can help you create a webhook-triggered workflow for processing data.');
    });

    it('should trim leading and trailing whitespace', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: '   \n  Here is your n8n workflow structure  \n   '
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Here is your n8n workflow structure');
    });

    it('should preserve internal formatting and line breaks', () => {
      const multilineContent = `Based on your requirements, here's the recommended workflow structure:

1. **Webhook Trigger**: Receives incoming data
2. **Data Processing**: Parse and validate JSON
3. **Conditional Logic**: Check data quality
4. **External API Call**: Send processed data

This approach ensures reliability and proper error handling.`;

      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: multilineContent
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(multilineContent);
      expect(result.contentString).toContain('**Webhook Trigger**');
      expect(result.contentString).toContain('This approach ensures reliability');
    });

    it('should handle assistant message with summary flag', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'Previously, we discussed creating a data processing workflow with webhook trigger and validation steps.',
        isSummary: true
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('[SUMMARY] Previously, we discussed creating a data processing workflow with webhook trigger and validation steps.');
    });

    it('should handle assistant message without summary flag', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'Let me provide you with the specific node configuration.',
        isSummary: false
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Let me provide you with the specific node configuration.');
      expect(result.contentString).not.toContain('[SUMMARY]');
    });

    it('should handle assistant message with undefined summary flag', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'Here are the node parameters you requested.'
        // isSummary is undefined
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Here are the node parameters you requested.');
      expect(result.contentString).not.toContain('[SUMMARY]');
    });

    it('should handle content with JSON examples', () => {
      const contentWithJson = `Here's the HTTP Request node configuration:

\`\`\`json
{
  "id": "http-request-1",
  "name": "Fetch User Data",
  "type": "n8n-nodes-base.httpRequest",
  "parameters": {
    "url": "https://api.example.com/users/{{$json.userId}}",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer {{$credentials.apiToken}}"
    }
  }
}
\`\`\`

This configuration will dynamically fetch user data based on the incoming webhook payload.`;

      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: contentWithJson
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(contentWithJson);
      expect(result.contentString).toContain('```json');
      expect(result.contentString).toContain('"type": "n8n-nodes-base.httpRequest"');
      expect(result.contentString).toContain('{{$json.userId}}');
    });

    it('should handle content with n8n expressions', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'Use {{$json.data.email}} to access the email field and $("Webhook").json.timestamp for the timestamp.'
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Use {{$json.data.email}} to access the email field and $("Webhook").json.timestamp for the timestamp.');
    });

    it('should throw error for null component', () => {
      expect(() => formatAssistantMessage(null as any)).toThrow(
        'Invalid AssistantMessageComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatAssistantMessage(undefined as any)).toThrow(
        'Invalid AssistantMessageComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatAssistantMessage('invalid' as any)).toThrow(
        'Invalid AssistantMessageComponent: component is required'
      );
    });

    it('should throw error for missing content', () => {
      const component = {
        type: 'assistant_message'
      } as any;

      expect(() => formatAssistantMessage(component)).toThrow(
        'Invalid AssistantMessageComponent: content is required and must be a string'
      );
    });

    it('should throw error for non-string content', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: { response: 'test' } as any
      };

      expect(() => formatAssistantMessage(component)).toThrow(
        'Invalid AssistantMessageComponent: content is required and must be a string'
      );
    });

    it('should throw error for empty content after trimming', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: '   \n\n   \t  '
      };

      expect(() => formatAssistantMessage(component)).toThrow(
        'Invalid AssistantMessageComponent: content cannot be empty'
      );
    });

    it('should handle summary messages with complex content', () => {
      const complexSummaryContent = `We configured:
- Webhook trigger with JSON parsing
- HTTP Request node for API calls
- IF node for conditional processing
- Set node for data transformation

Next steps: Add error handling and testing.`;

      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: complexSummaryContent,
        isSummary: true
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(`[SUMMARY] ${complexSummaryContent}`);
      expect(result.contentString).toContain('- Webhook trigger');
      expect(result.contentString).toContain('Next steps:');
    });

    it('should handle very long assistant responses', () => {
      const longContent = 'The workflow configuration is as follows. '.repeat(200);
      
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: longContent
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(longContent.trim());
      expect(result.contentString.length).toBeGreaterThan(1000);
    });

    it('should handle content with unicode and technical symbols', () => {
      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: 'Configuration completed ✅ → Next: API调用 λ(x) = processed_data'
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Configuration completed ✅ → Next: API调用 λ(x) = processed_data');
    });

    it('should handle markdown formatting in responses', () => {
      const markdownContent = `## Workflow Configuration

### Nodes:
1. **Webhook Trigger**
   - Path: \`/api/webhook\`
   - Method: \`POST\`

2. **HTTP Request**
   - URL: \`{{ $json.apiEndpoint }}\`
   - Headers: \`Authorization: Bearer token\`

### Best Practices:
- Always validate input data
- Use proper error handling
- *Test thoroughly* before deployment`;

      const component: AssistantMessageComponent = {
        type: 'assistant_message',
        content: markdownContent
      };

      const result = formatAssistantMessage(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(markdownContent);
      expect(result.contentString).toContain('## Workflow Configuration');
      expect(result.contentString).toContain('*Test thoroughly*');
    });
  });
});