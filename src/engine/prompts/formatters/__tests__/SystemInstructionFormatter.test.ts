import { describe, it, expect } from 'vitest';
import { formatSystemInstruction } from '../SystemInstructionFormatter';
import { SystemInstructionComponent } from '../../services/types/genericPromptBuilder.types';

describe('SystemInstructionFormatter', () => {
  describe('formatSystemInstruction', () => {
    it('should format a basic system instruction', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: 'You are a helpful AI assistant for n8n workflow automation.'
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('You are a helpful AI assistant for n8n workflow automation.');
    });

    it('should trim leading and trailing whitespace', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: '   \n  You are an expert n8n architect.  \n   '
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('You are an expert n8n architect.');
    });

    it('should preserve internal formatting and line breaks', () => {
      const multilineContent = `You are an expert n8n workflow architect.

Your responsibilities include:
- Analyzing user requirements
- Designing workflow structures
- Selecting appropriate nodes

Please follow these guidelines:
1. Always consider best practices
2. Ensure error handling
3. Optimize for performance`;

      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: multilineContent
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(multilineContent);
      expect(result.contentString).toContain('Your responsibilities include:');
      expect(result.contentString).toContain('- Analyzing user requirements');
      expect(result.contentString).toContain('1. Always consider best practices');
    });

    it('should handle content with special characters', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: 'Use {{$json}} syntax and $("Node Name") references for n8n expressions.'
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('Use {{$json}} syntax and $("Node Name") references for n8n expressions.');
    });

    it('should handle content with JSON examples', () => {
      const contentWithJson = `You are an n8n node composer. Here's an example node structure:

{
  "id": "node-123",
  "name": "HTTP Request",
  "type": "n8n-nodes-base.httpRequest",
  "parameters": {
    "url": "https://api.example.com"
  }
}

Follow this structure for all nodes.`;

      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: contentWithJson
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(contentWithJson);
      expect(result.contentString).toContain('"type": "n8n-nodes-base.httpRequest"');
    });

    it('should throw error for null component', () => {
      expect(() => formatSystemInstruction(null as any)).toThrow(
        'Invalid SystemInstructionComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatSystemInstruction(undefined as any)).toThrow(
        'Invalid SystemInstructionComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatSystemInstruction('invalid' as any)).toThrow(
        'Invalid SystemInstructionComponent: component is required'
      );
    });

    it('should throw error for missing content', () => {
      const component = {
        type: 'system_instruction'
      } as any;

      expect(() => formatSystemInstruction(component)).toThrow(
        'Invalid SystemInstructionComponent: content is required and must be a string'
      );
    });

    it('should throw error for non-string content', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: 123 as any
      };

      expect(() => formatSystemInstruction(component)).toThrow(
        'Invalid SystemInstructionComponent: content is required and must be a string'
      );
    });

    it('should throw error for empty content after trimming', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: '   \n\n   \t  '
      };

      expect(() => formatSystemInstruction(component)).toThrow(
        'Invalid SystemInstructionComponent: content cannot be empty'
      );
    });

    it('should handle content with only whitespace that becomes empty', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: ''
      };

      expect(() => formatSystemInstruction(component)).toThrow(
        'Invalid SystemInstructionComponent: content is required and must be a string'
      );
    });

    it('should handle very long content', () => {
      const longContent = 'You are an expert. '.repeat(1000);
      
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: longContent
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(longContent.trim());
      expect(result.contentString.length).toBeGreaterThan(1000);
    });

    it('should handle content with unicode characters', () => {
      const component: SystemInstructionComponent = {
        type: 'system_instruction',
        content: 'You are an AI assistant. 你好! 🤖 Помощник для n8n workflows.'
      };

      const result = formatSystemInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('You are an AI assistant. 你好! 🤖 Помощник для n8n workflows.');
    });
  });
});