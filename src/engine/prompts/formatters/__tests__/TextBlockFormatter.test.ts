import { describe, it, expect } from 'vitest';
import { formatTextBlock } from '../TextBlockFormatter';
import { TextBlockComponent } from '../../services/types/genericPromptBuilder.types';

describe('TextBlockFormatter', () => {
  describe('formatTextBlock', () => {
    it('should format text block with specified role', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: 'This is a system-level text block with configuration details.'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('This is a system-level text block with configuration details.');
    });

    it('should default to user role when no role specified', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        content: 'This text block has no explicit role specified.'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('This text block has no explicit role specified.');
    });

    it('should handle all valid role types', () => {
      const roles: Array<'system' | 'user' | 'assistant'> = ['system', 'user', 'assistant'];
      
      roles.forEach(role => {
        const component: TextBlockComponent = {
          type: 'text_block',
          role,
          content: `Content for ${role} role`
        };

        const result = formatTextBlock(component);
        
        expect(result.role).toBe(role);
        expect(result.contentString).toBe(`Content for ${role} role`);
      });
    });

    it('should trim leading and trailing whitespace', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'assistant',
        content: '   \n  Important configuration information  \n   '
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Important configuration information');
    });

    it('should preserve internal formatting and line breaks', () => {
      const multilineContent = `Available n8n nodes:

Core Nodes:
- HTTP Request
- Webhook
- Code (JavaScript)
- IF (Conditional)

Data Nodes:
- Set (Transform data)
- Merge (Combine data)
- Split In Batches

Use these nodes to build your workflow.`;

      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: multilineContent
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(multilineContent);
      expect(result.contentString).toContain('Core Nodes:');
      expect(result.contentString).toContain('- HTTP Request');
      expect(result.contentString).toContain('Use these nodes');
    });

    it('should handle content with technical syntax', () => {
      const technicalContent = `n8n Expression Syntax:

Basic access: {{$json.field}}
Node reference: $("Node Name").json.data
Array access: {{$json.items[0].value}}
Conditional: {{$json.status === "active" ? "enabled" : "disabled"}}

Remember to use proper syntax for expressions.`;

      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: technicalContent
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(technicalContent);
      expect(result.contentString).toContain('{{$json.field}}');
      expect(result.contentString).toContain('$("Node Name")');
    });

    it('should handle content with JSON examples', () => {
      const jsonContent = `Example node configuration:

\`\`\`json
{
  "id": "webhook-1",
  "name": "Webhook",
  "type": "n8n-nodes-base.webhook",
  "parameters": {
    "path": "my-webhook",
    "httpMethod": "POST"
  }
}
\`\`\`

This creates a webhook endpoint for receiving data.`;

      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'assistant',
        content: jsonContent
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe(jsonContent);
      expect(result.contentString).toContain('```json');
      expect(result.contentString).toContain('"type": "n8n-nodes-base.webhook"');
    });

    it('should throw error for null component', () => {
      expect(() => formatTextBlock(null as any)).toThrow(
        'Invalid TextBlockComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatTextBlock(undefined as any)).toThrow(
        'Invalid TextBlockComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatTextBlock('invalid' as any)).toThrow(
        'Invalid TextBlockComponent: component is required'
      );
    });

    it('should throw error for missing content', () => {
      const component = {
        type: 'text_block',
        role: 'user'
      } as any;

      expect(() => formatTextBlock(component)).toThrow(
        'Invalid TextBlockComponent: content is required and must be a string'
      );
    });

    it('should throw error for non-string content', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: { text: 'content' } as any
      };

      expect(() => formatTextBlock(component)).toThrow(
        'Invalid TextBlockComponent: content is required and must be a string'
      );
    });

    it('should throw error for invalid role', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'invalid-role' as any,
        content: 'Some content'
      };

      expect(() => formatTextBlock(component)).toThrow(
        'Invalid TextBlockComponent: role must be "system", "user", or "assistant"'
      );
    });

    it('should throw error for empty content after trimming', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'user',
        content: '   \n\n   \t  '
      };

      expect(() => formatTextBlock(component)).toThrow(
        'Invalid TextBlockComponent: content cannot be empty'
      );
    });

    it('should handle content with special characters and symbols', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: 'Special chars: @#$%^&*(){}[]|\\:";\'<>?,./`~'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('Special chars: @#$%^&*(){}[]|\\:";\'<>?,./`~');
    });

    it('should handle unicode and international characters', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'assistant',
        content: 'Unicode support: 你好 🌍 Привет العربية 🚀'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('assistant');
      expect(result.contentString).toBe('Unicode support: 你好 🌍 Привет العربية 🚀');
    });

    it('should handle very long text content', () => {
      const longContent = 'This is a very long text block content. '.repeat(300);
      
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: longContent
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(longContent.trim());
      expect(result.contentString.length).toBeGreaterThan(1000);
    });

    it('should handle URLs and links in content', () => {
      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'user',
        content: 'Visit https://docs.n8n.io for documentation and https://community.n8n.io for community support.'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('Visit https://docs.n8n.io for documentation and https://community.n8n.io for community support.');
    });

    it('should handle markdown formatting', () => {
      const markdownContent = `# Important Notes

## Configuration:
- **Required**: API key
- *Optional*: Webhook path
- ~~Deprecated~~: Old endpoint

> Remember to test your workflow before deployment

[Documentation](https://docs.n8n.io)`;

      const component: TextBlockComponent = {
        type: 'text_block',
        role: 'system',
        content: markdownContent
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(markdownContent);
      expect(result.contentString).toContain('# Important Notes');
      expect(result.contentString).toContain('**Required**');
      expect(result.contentString).toContain('[Documentation]');
    });

    it('should handle mixed content with code blocks', () => {
      const mixedContent = `Here's how to use n8n expressions:

Basic field access:
\`\`\`javascript
{{$json.userName}}
\`\`\`

Conditional logic:
\`\`\`javascript
{{$json.status === "active" ? "User is active" : "User is inactive"}}
\`\`\`

Use these patterns in your workflow nodes.`;

      const component: TextBlockComponent = {
        type: 'text_block',
        content: mixedContent
        // No role specified, should default to 'user'
      };

      const result = formatTextBlock(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(mixedContent);
      expect(result.contentString).toContain('```javascript');
      expect(result.contentString).toContain('{{$json.userName}}');
    });
  });
});