import { describe, it, expect } from 'vitest';
import { formatUserMessage } from '../UserMessageFormatter';
import { UserMessageComponent } from '../../services/types/genericPromptBuilder.types';

describe('UserMessageFormatter', () => {
  describe('formatUserMessage', () => {
    it('should format a basic user message', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: 'Create a workflow that processes incoming webhooks'
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('Create a workflow that processes incoming webhooks');
    });

    it('should trim leading and trailing whitespace', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: '   \n  Help me build an email automation workflow  \n   '
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('Help me build an email automation workflow');
    });

    it('should preserve internal formatting and line breaks', () => {
      const multilineContent = `I need help creating a workflow with these requirements:

1. Trigger: Webhook
2. Process: Parse JSON data
3. Condition: Check if email is valid
4. Action: Send to external API

Please provide the complete workflow structure.`;

      const component: UserMessageComponent = {
        type: 'user_message',
        content: multilineContent
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(multilineContent);
      expect(result.contentString).toContain('1. Trigger: Webhook');
      expect(result.contentString).toContain('Please provide the complete workflow structure.');
    });

    it('should handle content with special characters and n8n syntax', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: 'How do I use {{$json.data}} and $("Previous Node").json expressions?'
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('How do I use {{$json.data}} and $("Previous Node").json expressions?');
    });

    it('should handle content with code blocks and JSON', () => {
      const contentWithCode = `I have this JSON data:

\`\`\`json
{
  "user": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
\`\`\`

How do I extract the email field in n8n?`;

      const component: UserMessageComponent = {
        type: 'user_message',
        content: contentWithCode
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(contentWithCode);
      expect(result.contentString).toContain('```json');
      expect(result.contentString).toContain('"email": "<EMAIL>"');
    });

    it('should throw error for null component', () => {
      expect(() => formatUserMessage(null as any)).toThrow(
        'Invalid UserMessageComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatUserMessage(undefined as any)).toThrow(
        'Invalid UserMessageComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatUserMessage('invalid' as any)).toThrow(
        'Invalid UserMessageComponent: component is required'
      );
    });

    it('should throw error for missing content', () => {
      const component = {
        type: 'user_message'
      } as any;

      expect(() => formatUserMessage(component)).toThrow(
        'Invalid UserMessageComponent: content is required and must be a string'
      );
    });

    it('should throw error for non-string content', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: { message: 'test' } as any
      };

      expect(() => formatUserMessage(component)).toThrow(
        'Invalid UserMessageComponent: content is required and must be a string'
      );
    });

    it('should throw error for empty content after trimming', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: '   \n\n   \t  '
      };

      expect(() => formatUserMessage(component)).toThrow(
        'Invalid UserMessageComponent: content cannot be empty'
      );
    });

    it('should handle content with HTML-like tags', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: 'I want to parse <div>content</div> from HTML responses'
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('I want to parse <div>content</div> from HTML responses');
    });

    it('should handle very long user messages', () => {
      const longContent = 'I need help with n8n. '.repeat(500);
      
      const component: UserMessageComponent = {
        type: 'user_message',
        content: longContent
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(longContent.trim());
      expect(result.contentString.length).toBeGreaterThan(1000);
    });

    it('should handle content with unicode and emoji', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: '请帮我创建一个工作流程 🚀 для автоматизации'
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('请帮我创建一个工作流程 🚀 для автоматизации');
    });

    it('should handle content with URLs and links', () => {
      const component: UserMessageComponent = {
        type: 'user_message',
        content: 'I want to call https://api.example.com/users endpoint in my workflow'
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('I want to call https://api.example.com/users endpoint in my workflow');
    });

    it('should handle mixed content types', () => {
      const mixedContent = `Here's my issue:

1. API endpoint: https://api.github.com/repos/owner/repo
2. Expected response: {"name": "repo-name", "stars": 100}
3. n8n expression needed: {{$json.stars}}

Can you help? 🤔`;

      const component: UserMessageComponent = {
        type: 'user_message',
        content: mixedContent
      };

      const result = formatUserMessage(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(mixedContent);
      expect(result.contentString).toContain('{{$json.stars}}');
      expect(result.contentString).toContain('🤔');
    });
  });
});