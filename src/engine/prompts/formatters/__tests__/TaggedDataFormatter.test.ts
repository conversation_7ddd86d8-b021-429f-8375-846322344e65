import { describe, it, expect } from 'vitest';
import { formatTaggedData } from '../TaggedDataFormatter';
import { TaggedDataComponent } from '../../services/types/genericPromptBuilder.types';

describe('TaggedDataFormatter', () => {
  describe('formatTaggedData', () => {
    it('should format tagged data with string content', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: 'user_request',
        data: 'Create a workflow for processing emails'
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe(
        '<user_request>\nCreate a workflow for processing emails\n</user_request>'
      );
    });

    it('should format tagged data with object content', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'workflow_config',
        data: {
          trigger: 'webhook',
          nodes: ['http_request', 'code'],
          priority: 'high'
        }
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toContain('<workflow_config>');
      expect(result.contentString).toContain('</workflow_config>');
      expect(result.contentString).toContain('"trigger": "webhook"');
      expect(result.contentString).toContain('"nodes": [');
      expect(result.contentString).toContain('"priority": "high"');
    });

    it('should format tagged data with array content', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'available_nodes',
        data: ['webhook', 'http_request', 'code', 'if']
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toContain('<available_nodes>');
      expect(result.contentString).toContain('</available_nodes>');
      expect(result.contentString).toContain('"webhook"');
      expect(result.contentString).toContain('"http_request"');
      expect(result.contentString).toContain('"code"');
      expect(result.contentString).toContain('"if"');
    });

    it('should format tagged data with number content', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: 'max_retries',
        data: 3
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toBe('<max_retries>\n3\n</max_retries>');
    });

    it('should format tagged data with boolean content', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'enable_logging',
        data: true
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe('<enable_logging>\ntrue\n</enable_logging>');
    });

    it('should handle pre-formatted string data', () => {
      const xmlData = '<config><setting>value</setting></config>';
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'xml_config',
        data: xmlData,
        isPreformattedString: true
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toBe(
        '<xml_config>\n<config><setting>value</setting></config>\n</xml_config>'
      );
    });

    it('should sanitize invalid tag names', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: 'user@request#with$special%chars!',
        data: 'test content'
      };

      const result = formatTaggedData(component);

      expect(result.contentString).toContain('<user_request_with_special_chars_>');
      expect(result.contentString).toContain('</user_request_with_special_chars_>');
    });

    it('should handle data with description property', () => {
      const dataWithDescription = {
        description: 'This is a configuration object',
        value: 42,
        name: 'test'
      };

      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'config',
        data: dataWithDescription
      };

      const result = formatTaggedData(component);

      expect(result.contentString).toContain('<config description="This is a configuration object">');
      expect(result.contentString).toContain('</config>');
      expect(result.contentString).toContain('"value": 42');
    });

    it('should throw error for null component', () => {
      expect(() => formatTaggedData(null as any)).toThrow(
        'Invalid TaggedDataComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatTaggedData(undefined as any)).toThrow(
        'Invalid TaggedDataComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatTaggedData('invalid' as any)).toThrow(
        'Invalid TaggedDataComponent: component is required'
      );
    });

    it('should throw error for missing tag', () => {
      const component = {
        type: 'tagged_data',
        role: 'user',
        data: 'test'
      } as any;

      expect(() => formatTaggedData(component)).toThrow(
        'Invalid TaggedDataComponent: tag is required and must be a string'
      );
    });

    it('should throw error for non-string tag', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: 123 as any,
        data: 'test'
      };

      expect(() => formatTaggedData(component)).toThrow(
        'Invalid TaggedDataComponent: tag is required and must be a string'
      );
    });

    it('should throw error for undefined data', () => {
      const component = {
        type: 'tagged_data',
        role: 'user',
        tag: 'test',
        data: undefined
      } as any;

      expect(() => formatTaggedData(component)).toThrow(
        'Invalid TaggedDataComponent: data is required'
      );
    });

    it('should throw error for null data', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: 'test',
        data: null as any
      };

      expect(() => formatTaggedData(component)).toThrow(
        'Invalid TaggedDataComponent: data is required'
      );
    });

    it('should handle different role types', () => {
      const roles: Array<'system' | 'user' | 'assistant'> = ['system', 'user', 'assistant'];
      
      roles.forEach(role => {
        const component: TaggedDataComponent = {
          type: 'tagged_data',
          role,
          tag: 'test',
          data: 'content'
        };

        const result = formatTaggedData(component);
        expect(result.role).toBe(role);
      });
    });

    it('should handle empty string tag gracefully', () => {
      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'user',
        tag: '',
        data: 'test'
      };

      expect(() => formatTaggedData(component)).toThrow(
        'Invalid TaggedDataComponent: tag is required and must be a string'
      );
    });

    it('should handle complex nested objects', () => {
      const complexData = {
        level1: {
          level2: {
            array: [1, 2, { nested: true }],
            nullValue: null,
            boolValue: false
          }
        },
        topLevelArray: ['a', 'b', 'c']
      };

      const component: TaggedDataComponent = {
        type: 'tagged_data',
        role: 'system',
        tag: 'complex_data',
        data: complexData
      };

      const result = formatTaggedData(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toContain('<complex_data>');
      expect(result.contentString).toContain('</complex_data>');
      expect(result.contentString).toContain('"level1"');
      expect(result.contentString).toContain('"nested": true');
    });
  });
});