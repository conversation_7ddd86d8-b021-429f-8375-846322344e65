import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { formatJsonSchemaInstruction } from '../JsonSchemaInstructionFormatter';
import { JsonSchemaInstructionComponent } from '../../services/types/genericPromptBuilder.types';

describe('JsonSchemaInstructionFormatter', () => {
  describe('formatJsonSchemaInstruction', () => {
    it('should format a basic JSON schema instruction', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number()
      });

      const component: JsonSchemaInstructionComponent = {
        type: 'json_schema_instruction',
        role: 'system',
        zodSchema: schema
      };

      const result = formatJsonSchemaInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toContain('Your output MUST conform to the following JSON schema:');
      expect(result.contentString).toContain('```json');
      expect(result.contentString).toContain('```');
      expect(result.contentString).toContain('"type": "object"');
      expect(result.contentString).toContain('"properties"');
      expect(result.contentString).toContain('Please ensure your response is valid JSON');
    });

    it('should use custom instruction prefix when provided', () => {
      const schema = z.string();
      const customPrefix = 'Please format your response according to this schema:';

      const component: JsonSchemaInstructionComponent = {
        type: 'json_schema_instruction',
        role: 'user',
        zodSchema: schema,
        instructionPrefix: customPrefix
      };

      const result = formatJsonSchemaInstruction(component);

      expect(result.role).toBe('user');
      expect(result.contentString).toContain(customPrefix);
      expect(result.contentString).not.toContain('Your output MUST conform');
    });

    it('should use custom schema name when provided', () => {
      const schema = z.object({ id: z.string() });
      const schemaName = 'CustomOutputSchema';

      const component: JsonSchemaInstructionComponent = {
        type: 'json_schema_instruction',
        role: 'system',
        zodSchema: schema,
        schemaName
      };

      const result = formatJsonSchemaInstruction(component);

      // The schema name affects the internal JSON schema generation
      expect(result.contentString).toContain('```json');
      expect(result.role).toBe('system');
    });

    it('should handle complex nested schemas', () => {
      const schema = z.object({
        user: z.object({
          name: z.string(),
          email: z.string().email(),
          preferences: z.array(z.string())
        }),
        metadata: z.record(z.any()).optional()
      });

      const component: JsonSchemaInstructionComponent = {
        type: 'json_schema_instruction',
        role: 'system',
        zodSchema: schema
      };

      const result = formatJsonSchemaInstruction(component);

      expect(result.role).toBe('system');
      expect(result.contentString).toContain('"type": "object"');
      expect(result.contentString).toContain('"properties"');
    });

    it('should throw error for null component', () => {
      expect(() => formatJsonSchemaInstruction(null as any)).toThrow(
        'Invalid JsonSchemaInstructionComponent: component is required'
      );
    });

    it('should throw error for undefined component', () => {
      expect(() => formatJsonSchemaInstruction(undefined as any)).toThrow(
        'Invalid JsonSchemaInstructionComponent: component is required'
      );
    });

    it('should throw error for non-object component', () => {
      expect(() => formatJsonSchemaInstruction('invalid' as any)).toThrow(
        'Invalid JsonSchemaInstructionComponent: component is required'
      );
    });

    it('should throw error for missing zodSchema', () => {
      const component = {
        type: 'json_schema_instruction',
        role: 'system'
      } as any;

      expect(() => formatJsonSchemaInstruction(component)).toThrow(
        'Invalid JsonSchemaInstructionComponent: zodSchema is required'
      );
    });

    it('should throw error for null zodSchema', () => {
      const component: JsonSchemaInstructionComponent = {
        type: 'json_schema_instruction',
        role: 'system',
        zodSchema: null as any
      };

      expect(() => formatJsonSchemaInstruction(component)).toThrow(
        'Invalid JsonSchemaInstructionComponent: zodSchema is required'
      );
    });

    it('should handle different role types', () => {
      const schema = z.string();
      
      const roles: Array<'system' | 'user' | 'assistant'> = ['system', 'user', 'assistant'];
      
      roles.forEach(role => {
        const component: JsonSchemaInstructionComponent = {
          type: 'json_schema_instruction',
          role,
          zodSchema: schema
        };

        const result = formatJsonSchemaInstruction(component);
        expect(result.role).toBe(role);
      });
    });

    it('should handle primitive schema types', () => {
      const primitiveSchemas = [
        z.string(),
        z.number(),
        z.boolean(),
        z.array(z.string()),
        z.record(z.string())
      ];

      primitiveSchemas.forEach(schema => {
        const component: JsonSchemaInstructionComponent = {
          type: 'json_schema_instruction',
          role: 'system',
          zodSchema: schema
        };

        const result = formatJsonSchemaInstruction(component);
        expect(result.role).toBe('system');
        expect(result.contentString).toContain('```json');
      });
    });
  });
});