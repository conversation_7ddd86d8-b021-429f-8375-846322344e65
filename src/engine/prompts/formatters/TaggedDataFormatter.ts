import { TaggedDataComponent } from '../services/types/genericPromptBuilder.types';
import { FormattedMessage } from './JsonSchemaInstructionFormatter';

/**
 * Formats tagged data blocks with XML-style tags.
 * Handles different data types appropriately and includes description if provided.
 * 
 * @param component - The tagged data component to format
 * @returns Formatted message with role and content string
 * @throws Error if component is invalid
 */
export function formatTaggedData(component: TaggedDataComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid TaggedDataComponent: component is required');
  }

  if (!component.tag || typeof component.tag !== 'string') {
    throw new Error('Invalid TaggedDataComponent: tag is required and must be a string');
  }

  if (component.data === undefined || component.data === null) {
    throw new Error('Invalid TaggedDataComponent: data is required');
  }

  try {
    // Sanitize tag name to ensure it's valid XML
    const sanitizedTag = component.tag.replace(/[^a-zA-Z0-9_-]/g, '_');
    
    let formattedData: string;

    if (component.isPreformattedString) {
      // Data is already a pre-formatted string (like XML or Markdown)
      formattedData = typeof component.data === 'string' ? component.data : String(component.data);
    } else {
      // Format data based on its type
      if (typeof component.data === 'string') {
        formattedData = component.data;
      } else if (typeof component.data === 'object') {
        formattedData = JSON.stringify(component.data, null, 2);
      } else {
        formattedData = String(component.data);
      }
    }

    // Create the XML-style tagged content
    const contentParts: string[] = [];
    
    // Add opening tag with description if provided
    if (component.data && typeof component.data === 'object' && 'description' in component.data && component.data.description) {
      contentParts.push(`<${sanitizedTag} description="${String(component.data.description)}">`);
    } else {
      contentParts.push(`<${sanitizedTag}>`);
    }
    
    // Add the formatted data
    contentParts.push(formattedData);
    
    // Add closing tag
    contentParts.push(`</${sanitizedTag}>`);

    return {
      role: component.role,
      contentString: contentParts.join('\n')
    };
  } catch (error) {
    throw new Error(`Failed to format tagged data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}