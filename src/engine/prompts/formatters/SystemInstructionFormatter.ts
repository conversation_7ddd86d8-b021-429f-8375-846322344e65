import { SystemInstructionComponent } from '../services/types/genericPromptBuilder.types';
import { FormattedMessage } from './JsonSchemaInstructionFormatter';

/**
 * Formats system instructions.
 * System instructions define the LLM's role, behavior, and context.
 * 
 * @param component - The system instruction component to format
 * @returns Formatted message with system role and instruction content
 * @throws Error if component is invalid
 */
export function formatSystemInstruction(component: SystemInstructionComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid SystemInstructionComponent: component is required');
  }

  if (!component.content || typeof component.content !== 'string') {
    throw new Error('Invalid SystemInstructionComponent: content is required and must be a string');
  }

  // Trim whitespace but preserve internal formatting
  const trimmedContent = component.content.trim();
  
  if (trimmedContent.length === 0) {
    throw new Error('Invalid SystemInstructionComponent: content cannot be empty');
  }

  return {
    role: 'system',
    contentString: trimmedContent
  };
}