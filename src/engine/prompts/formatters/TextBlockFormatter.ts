import { TextBlockComponent } from '../services/types/genericPromptBuilder.types';
import { FormattedMessage } from './JsonSchemaInstructionFormatter';

/**
 * Formats plain text blocks.
 * Text blocks are flexible components that can be used for any role.
 * 
 * @param component - The text block component to format
 * @returns Formatted message with specified or default role and content
 * @throws Error if component is invalid
 */
export function formatTextBlock(component: TextBlockComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid TextBlockComponent: component is required');
  }

  if (!component.content || typeof component.content !== 'string') {
    throw new Error('Invalid TextBlockComponent: content is required and must be a string');
  }

  // Validate role if provided
  if (component.role && !['system', 'user', 'assistant'].includes(component.role)) {
    throw new Error('Invalid TextBlockComponent: role must be "system", "user", or "assistant"');
  }

  // Trim whitespace but preserve internal formatting
  const trimmedContent = component.content.trim();
  
  if (trimmedContent.length === 0) {
    throw new Error('Invalid TextBlockComponent: content cannot be empty');
  }

  // Use specified role or default to 'user'
  const role = component.role || 'user';

  return {
    role,
    contentString: trimmedContent
  };
}