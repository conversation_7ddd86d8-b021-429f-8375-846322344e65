import { UserMessageComponent } from '../services/types/genericPromptBuilder.types';
import { FormattedMessage } from './JsonSchemaInstructionFormatter';

/**
 * Formats user messages.
 * User messages represent input from the user or requests to the LLM.
 * 
 * @param component - The user message component to format
 * @returns Formatted message with user role and message content
 * @throws Error if component is invalid
 */
export function formatUserMessage(component: UserMessageComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid UserMessageComponent: component is required');
  }

  if (!component.content || typeof component.content !== 'string') {
    throw new Error('Invalid UserMessageComponent: content is required and must be a string');
  }

  // Trim whitespace but preserve internal formatting
  const trimmedContent = component.content.trim();
  
  if (trimmedContent.length === 0) {
    throw new Error('Invalid UserMessageComponent: content cannot be empty');
  }

  return {
    role: 'user',
    contentString: trimmedContent
  };
}