import { zodToJsonSchema } from 'zod-to-json-schema';
import { JsonSchemaInstructionComponent } from '../services/types/genericPromptBuilder.types';

/**
 * Formatted message structure returned by formatters
 */
export interface FormattedMessage {
  role: string;
  contentString: string;
}

/**
 * Formats JSON schema instructions for LLM output formatting.
 * Converts Zod schema to JSON schema and creates instruction text for the LLM.
 * 
 * @param component - The JSON schema instruction component to format
 * @returns Formatted message with role and content string
 * @throws Error if schema conversion fails
 */
export function formatJsonSchemaInstruction(component: JsonSchemaInstructionComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid JsonSchemaInstructionComponent: component is required');
  }

  if (!component.zodSchema) {
    throw new Error('Invalid JsonSchemaInstructionComponent: zodSchema is required');
  }

  try {
    // Convert Zod schema to JSON schema
    const jsonSchema = zodToJsonSchema(component.zodSchema, {
      name: component.schemaName || 'OutputSchema',
      $refStrategy: 'none', // Avoid $ref for simpler schema representation
    });

    // Create instruction text for LLM
    const instructionPrefix = component.instructionPrefix || 'Your output MUST conform to the following JSON schema:';
    const schemaString = JSON.stringify(jsonSchema, null, 2);
    
    const contentString = `${instructionPrefix}\n\n\`\`\`json\n${schemaString}\n\`\`\`\n\nPlease ensure your response is valid JSON that strictly follows this schema.`;

    return {
      role: component.role,
      contentString
    };
  } catch (error) {
    throw new Error(`Failed to format JSON schema instruction: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}