import { AssistantMessageComponent } from '../services/types/genericPromptBuilder.types';
import { FormattedMessage } from './JsonSchemaInstructionFormatter';

/**
 * Formats assistant messages.
 * Assistant messages represent previous responses from the LLM or examples.
 * 
 * @param component - The assistant message component to format
 * @returns Formatted message with assistant role and message content
 * @throws Error if component is invalid
 */
export function formatAssistantMessage(component: AssistantMessageComponent): FormattedMessage {
  if (!component || typeof component !== 'object') {
    throw new Error('Invalid AssistantMessageComponent: component is required');
  }

  if (!component.content || typeof component.content !== 'string') {
    throw new Error('Invalid AssistantMessageComponent: content is required and must be a string');
  }

  // Trim whitespace but preserve internal formatting
  const trimmedContent = component.content.trim();
  
  if (trimmedContent.length === 0) {
    throw new Error('Invalid AssistantMessageComponent: content cannot be empty');
  }

  // Add special formatting hint if this is a summary
  let contentString = trimmedContent;
  if (component.isSummary) {
    contentString = `[SUMMARY] ${trimmedContent}`;
  }

  return {
    role: 'assistant',
    contentString
  };
}