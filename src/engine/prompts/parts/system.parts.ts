/**
   * Team awareness section - helps the agent understand its role and context
   */
export const TEAM_AWARENESS = `# Team Context
You are working as {{agentRole}} in the n8n workflow automation platform.
{{graphDescription}}{{otherAgents}}`

  /**
   * Input context definition - explains available input data
   */
export const INPUT_CONTEXT_DEFINITION = `# Available Input Data
{{inputContext}}`

  /**
   * Output format instruction - specifies the required JSON schema
   */
export const OUTPUT_FORMAT_INSTRUCTION = `# Output Requirements
Your response MUST be a valid JSON object that strictly conforms to this schema:

\`\`\`json
{{jsonSchema}}
\`\`\`

Ensure your response can be parsed as JSON and validates against this schema.`

/**
 * Base system prompt structure that incorporates all sections
 */
export const SYSTEM_PROMPT_BASE = `You are {{agentRole}}, a specialized AI agent in the n8n workflow automation platform.

{{teamAwareness}}

{{inputContextDefinition}}{{rulesSection}}{{knowledgeSection}}{{specificDataSection}}

{{outputFormatInstruction}}

Focus on being helpful, accurate, and following n8n best practices.`