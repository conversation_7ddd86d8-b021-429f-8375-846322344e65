/**
 * ToolContentNormalizer.ts
 * 
 * Ensures tool content follows proper Vercel AI SDK v5 format.
 * Handles normalization of tool calls (for assistant messages) and tool results (for tool messages).
 */

import { ToolResultPart, ToolCallPart } from 'ai';

/**
 * Result of tool content normalization
 */
export interface ToolNormalizationResult {
  content: ToolResultPart[] | ToolCallPart[];
  warnings?: string[];
}

/**
 * Legacy tool call formats that might be encountered
 */
interface LegacyToolCall {
  id?: string;
  type?: 'function' | 'tool_call';
  function?: {
    name: string;
    arguments: string | object;
  };
  name?: string;
  arguments?: string | object;
  tool_call_id?: string;
}

/**
 * Legacy tool result formats
 */
interface LegacyToolResult {
  tool_call_id?: string;
  toolCallId?: string;
  id?: string;
  content?: any;
  result?: any;
  output?: any;
  name?: string;
}

/**
 * Normalizes tool content based on message role
 * 
 * @param role - Message role ('tool' for results, 'assistant' for calls)
 * @param content - Raw content to normalize
 * @returns Normalized content with warnings
 */
export function normalizeToolContent(
  role: 'tool' | 'assistant',
  content: any
): ToolNormalizationResult {
  if (content === null || content === undefined) {
    throw new Error(`Tool content cannot be null or undefined for role: ${role}`);
  }

  const warnings: string[] = [];

  if (role === 'tool') {
    return normalizeToolResults(content, warnings);
  } else if (role === 'assistant') {
    return normalizeToolCalls(content, warnings);
  }

  throw new Error(`Invalid role for tool content normalization: ${role}. Must be 'tool' or 'assistant'`);
}

/**
 * Normalizes content for tool result messages (role: 'tool')
 */
function normalizeToolResults(content: any, warnings: string[]): ToolNormalizationResult {
  // Handle array of tool results
  if (Array.isArray(content)) {
    const normalizedParts: ToolResultPart[] = [];
    
    for (let i = 0; i < content.length; i++) {
      const part = content[i];
      const normalizedPart = normalizeToolResultPart(part, i, warnings);
      if (normalizedPart) {
        normalizedParts.push(normalizedPart);
      }
    }
    
    return {
      content: normalizedParts,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  // Handle single tool result (convert to array)
  if (typeof content === 'object') {
    warnings.push('Converting single tool result object to array format');
    const normalizedPart = normalizeToolResultPart(content, 0, warnings);
    
    if (!normalizedPart) {
      throw new Error('Failed to normalize tool result');
    }
    
    return {
      content: [normalizedPart],
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  // Handle string content (create minimal tool result)
  if (typeof content === 'string') {
    warnings.push('Converting string content to tool-result format, using generated toolCallId');
    
    return {
      content: [{
        type: 'tool-result',
        toolCallId: `generated_${Date.now()}`,
        toolName: 'unknown_tool',
        result: content
      }],
      warnings
    };
  }

  throw new Error(`Invalid tool result content type: ${typeof content}. Expected object, array, or string`);
}

/**
 * Normalizes content for tool call messages (role: 'assistant')
 */
function normalizeToolCalls(content: any, warnings: string[]): ToolNormalizationResult {
  // Handle array content
  if (Array.isArray(content)) {
    const normalizedParts: ToolCallPart[] = [];
    
    for (let i = 0; i < content.length; i++) {
      const part = content[i];
      
      // Check if this looks like a tool call
      if (part && typeof part === 'object' && (
        part.type === 'tool-call' || 
        part.type === 'function_call' || 
        part.type === 'tool_call' ||
        part.function ||
        part.name ||
        part.toolName ||
        part.toolCallId ||
        part.arguments
      )) {
        try {
          const normalizedPart = normalizeToolCallPart(part, i, warnings);
          if (normalizedPart) {
            normalizedParts.push(normalizedPart);
          }
        } catch (error) {
          // Re-throw critical errors that indicate fundamentally invalid data
          const errorMessage = error instanceof Error ? error.message : 'Normalization failed';
          if (errorMessage.includes('Failed to parse') || 
              errorMessage.includes('must have a name') ||
              errorMessage.includes('JSON string')) {
            throw error;
          }
          // Otherwise treat as warning for recoverable issues
          warnings.push(`Tool call ${i}: ${errorMessage}`);
        }
      } else {
        // Skip non-tool-call parts with appropriate warnings
        if (part === null || part === undefined || typeof part !== 'object') {
          warnings.push(`Tool call ${i}: Skipping invalid part (not an object)`);
        } else {
          // It's an object but doesn't look like a tool call (e.g., text part, image part)
          // Skip silently for mixed content arrays
          continue;
        }
      }
    }
    
    return {
      content: normalizedParts,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  // Handle single tool call object (convert to array)
  if (typeof content === 'object') {
    warnings.push('Converting single tool call object to array format');
    const normalizedPart = normalizeToolCallPart(content, 0, warnings);
    
    if (!normalizedPart) {
      throw new Error('Failed to normalize tool call');
    }
    
    return {
      content: [normalizedPart],
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  throw new Error(`Invalid tool call content type: ${typeof content}. Expected object or array`);
}

/**
 * Normalizes a single tool result part
 */
function normalizeToolResultPart(
  part: any, 
  index: number, 
  warnings: string[]
): ToolResultPart | null {
  if (!part || typeof part !== 'object') {
    warnings.push(`Tool result ${index}: Skipping invalid part (not an object)`);
    return null;
  }

  const partLocation = `Tool result ${index}`;

  // If already properly formatted
  if (part.type === 'tool-result' && part.toolCallId && part.toolName && part.result !== undefined) {
    return {
      type: 'tool-result',
      toolCallId: part.toolCallId,
      toolName: part.toolName,
      result: part.result
    };
  }

  // Extract toolCallId from various possible locations
  let toolCallId: string;
  if (part.toolCallId && typeof part.toolCallId === 'string') {
    toolCallId = part.toolCallId;
  } else if (part.tool_call_id && typeof part.tool_call_id === 'string') {
    toolCallId = part.tool_call_id;
    warnings.push(`${partLocation}: Converting 'tool_call_id' to 'toolCallId'`);
  } else if (part.id && typeof part.id === 'string') {
    toolCallId = part.id;
    warnings.push(`${partLocation}: Using 'id' as 'toolCallId'`);
  } else {
    warnings.push(`${partLocation}: Missing toolCallId, generating one`);
    toolCallId = `generated_${Date.now()}_${index}`;
  }

  // Extract toolName - required in AI SDK v5
  let toolName: string;
  if (part.toolName && typeof part.toolName === 'string') {
    toolName = part.toolName;
  } else if (part.name && typeof part.name === 'string') {
    toolName = part.name;
    warnings.push(`${partLocation}: Converting 'name' to 'toolName'`);
  } else {
    // For AI SDK v5, toolName is required. If not provided, generate a reasonable default
    toolName = 'unknown_tool';
    warnings.push(`${partLocation}: Missing toolName, using 'unknown_tool'`);
  }

  // Extract result from various possible locations
  let result: any;
  if (part.result !== undefined) {
    result = part.result;
  } else if (part.content !== undefined) {
    result = part.content;
    warnings.push(`${partLocation}: Converting 'content' to 'result'`);
  } else if (part.output !== undefined) {
    result = part.output;
    warnings.push(`${partLocation}: Converting 'output' to 'result'`);
  } else {
    warnings.push(`${partLocation}: No result content found, using empty object`);
    result = {};
  }

  return {
    type: 'tool-result',
    toolCallId,
    toolName,
    result
  };
}

/**
 * Normalizes a single tool call part
 */
function normalizeToolCallPart(
  part: any, 
  index: number, 
  warnings: string[]
): ToolCallPart | null {
  if (!part || typeof part !== 'object') {
    warnings.push(`Tool call ${index}: Skipping invalid part (not an object)`);
    return null;
  }

  const partLocation = `Tool call ${index}`;

  // If already properly formatted
  if (part.type === 'tool-call' && part.toolCallId && part.toolName && part.args !== undefined) {
    return {
      type: 'tool-call',
      toolCallId: part.toolCallId,
      toolName: part.toolName,
      args: part.args
    };
  }

  // Extract toolCallId
  let toolCallId: string;
  if (part.toolCallId && typeof part.toolCallId === 'string') {
    toolCallId = part.toolCallId;
  } else if (part.id && typeof part.id === 'string') {
    toolCallId = part.id;
  } else if (part.tool_call_id && typeof part.tool_call_id === 'string') {
    toolCallId = part.tool_call_id;
    warnings.push(`${partLocation}: Converting 'tool_call_id' to 'toolCallId'`);
  } else {
    warnings.push(`${partLocation}: Missing toolCallId, generating one`);
    toolCallId = `generated_${Date.now()}_${index}`;
  }

  // Extract tool name
  let toolName: string;
  if (part.toolName && typeof part.toolName === 'string') {
    toolName = part.toolName;
  } else if (part.name && typeof part.name === 'string') {
    toolName = part.name;
    warnings.push(`${partLocation}: Converting 'name' to 'toolName'`);
  } else if (part.function?.name && typeof part.function.name === 'string') {
    toolName = part.function.name;
    warnings.push(`${partLocation}: Converting 'function.name' to 'toolName'`);
  } else {
    throw new Error(`${partLocation}: Tool call must have a name/toolName`);
  }

  // Extract arguments
  let args: any = {};
  if (part.args !== undefined) {
    args = part.args;
  } else if (part.arguments !== undefined) {
    if (typeof part.arguments === 'string') {
      try {
        args = JSON.parse(part.arguments);
        warnings.push(`${partLocation}: Parsed 'arguments' JSON string to object`);
      } catch {
        throw new Error(`${partLocation}: Failed to parse arguments JSON string`);
      }
    } else {
      args = part.arguments;
      warnings.push(`${partLocation}: Converting 'arguments' to 'args'`);
    }
  } else if (part.function?.arguments !== undefined) {
    if (typeof part.function.arguments === 'string') {
      try {
        args = JSON.parse(part.function.arguments);
        warnings.push(`${partLocation}: Parsed 'function.arguments' JSON string to object`);
      } catch {
        throw new Error(`${partLocation}: Failed to parse function.arguments JSON string`);
      }
    } else {
      args = part.function.arguments;
      warnings.push(`${partLocation}: Converting 'function.arguments' to 'args'`);
    }
  }

  // Handle legacy type conversion
  if (part.type && part.type !== 'tool-call') {
    warnings.push(`${partLocation}: Converting type '${part.type}' to 'tool-call'`);
  }

  return {
    type: 'tool-call',
    toolCallId,
    toolName,
    args
  };
}

/**
 * Type guard for ToolResultPart
 */
export function isValidToolResultPart(part: any): part is ToolResultPart {
  return (
    part !== null &&
    part !== undefined &&
    typeof part === 'object' &&
    part.type === 'tool-result' &&
    typeof part.toolCallId === 'string' &&
    typeof part.toolName === 'string' &&
    part.result !== undefined
  );
}

/**
 * Type guard for ToolCallPart
 */
export function isValidToolCallPart(part: any): part is ToolCallPart {
  return (
    part !== null &&
    part !== undefined &&
    typeof part === 'object' &&
    part.type === 'tool-call' &&
    typeof part.toolCallId === 'string' &&
    typeof part.toolName === 'string' &&
    part.args !== undefined
  );
}

/**
 * Validates tool content for a specific role
 * This is stricter than normalization - it checks if the content is already valid
 * without needing extensive transformation
 */
export function validateToolContent(role: 'tool' | 'assistant', content: any): boolean {
  try {
    if (content === null || content === undefined) {
      return false;
    }

    // For validation, we want to check if content is already reasonably valid
    // without the permissive transformations that the normalizer does
    
    if (role === 'tool') {
      // Tool content should be array of tool results or a string
      if (typeof content === 'string') {
        return true; // String content is valid for tool messages
      }
      
      if (Array.isArray(content)) {
        return content.every(part => {
          // For validation, require more explicit structure
          return part && 
                 typeof part === 'object' && 
                 (part.type === 'tool-result' || part.toolCallId || part.tool_call_id || part.result !== undefined || part.toolName);
        });
      }
      
      // Single object
      if (typeof content === 'object') {
        return content.type === 'tool-result' || content.toolCallId || content.tool_call_id || content.result !== undefined || content.toolName;
      }
      
      return false;
    } else {
      // Assistant content should be array of tool calls or an object
      if (Array.isArray(content)) {
        return content.every(part => {
          if (!part || typeof part !== 'object') return false;
          
          // For validation, require clearer tool call indicators
          return part.type === 'tool-call' || 
                 part.type === 'function_call' ||
                 part.toolName || 
                 part.name || 
                 part.function?.name;
        });
      }
      
      // Single object
      if (typeof content === 'object') {
        return content.type === 'tool-call' || 
               content.type === 'function_call' ||
               content.toolName || 
               content.name || 
               content.function?.name;
      }
      
      return false;
    }
  } catch {
    return false;
  }
}