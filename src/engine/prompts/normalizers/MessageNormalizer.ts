/**
 * MessageNormalizer.ts
 * 
 * Ensures ModelMessage[] conform to Vercel AI SDK v5 standards.
 * Handles role conversion, content format normalization, tool calls compatibility,
 * and message structure validation.
 */

import { ModelMessage, ToolCallPart, ToolResultPart, TextPart, ImagePart } from 'ai';

/**
 * Result of message normalization process
 */
export interface NormalizationResult {
  messages: ModelMessage[];
  warnings?: string[];
}

/**
 * Content types by role in AI SDK v5
 */
type UserContent = string | Array<TextPart | ImagePart>;
type AssistantContent = string | Array<TextPart | ToolCallPart>;
type ToolContent = Array<ToolResultPart>;
type SystemContent = string;

/**
 * Valid roles for ModelMessage in Vercel AI SDK v5
 */
type ValidRole = 'system' | 'user' | 'assistant' | 'tool';

/**
 * Legacy tool call format that might be encountered
 */
interface LegacyToolCall {
  id?: string;
  type?: 'function';
  function?: {
    name: string;
    arguments: string | object;
  };
  name?: string;
  arguments?: string | object;
}

/**
 * Normalizes an array of messages to ensure they conform to Vercel AI SDK v5 standards
 * 
 * @param messages - Array of messages to normalize
 * @returns Normalized messages with any warnings
 */
export function normalizeMessages(messages: ModelMessage[]): NormalizationResult {
  if (!Array.isArray(messages)) {
    throw new Error('Messages must be an array');
  }

  const warnings: string[] = [];
  const normalizedMessages: ModelMessage[] = [];

  for (let i = 0; i < messages.length; i++) {
    try {
      const normalized = normalizeMessage(messages[i], i);
      normalizedMessages.push(normalized.message);
      if (normalized.warnings) {
        warnings.push(...normalized.warnings);
      }
    } catch (error) {
      throw new Error(`Failed to normalize message at index ${i}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return {
    messages: normalizedMessages,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Normalizes a single message
 */
function normalizeMessage(message: any, index: number): { message: ModelMessage; warnings?: string[] } {
  if (!message || typeof message !== 'object') {
    throw new Error(`Message at index ${index} must be an object`);
  }

  const warnings: string[] = [];
  
  // Normalize the role
  const normalizedRole = normalizeRole(message.role, index, warnings);
  
  // Normalize the content
  const normalizedContent = normalizeContent(message.content, normalizedRole, index, warnings);
  
  // Handle legacy tool_calls property
  if (message.tool_calls && normalizedRole === 'assistant') {
    warnings.push(`Message ${index}: Converting legacy tool_calls property to modern format`);
    const toolCallParts = convertLegacyToolCalls(message.tool_calls, index);
    
    // If content is string, convert to array and add tool calls
    if (typeof normalizedContent === 'string') {
      const parts: Array<TextPart | ToolCallPart> = [];
      if (normalizedContent.trim()) {
        parts.push({ type: 'text', text: normalizedContent });
      }
      parts.push(...toolCallParts);
      return {
        message: {
          role: normalizedRole,
          content: parts
        },
        warnings
      };
    } else if (Array.isArray(normalizedContent)) {
      // Add tool calls to existing content parts (for assistant role)
      return {
        message: {
          role: normalizedRole,
          content: [...(normalizedContent as Array<TextPart | ToolCallPart>), ...toolCallParts]
        },
        warnings
      };
    }
  }

  // Create the normalized message with role-specific content types
  let normalizedMessage: ModelMessage;
  
  if (normalizedRole === 'system') {
    normalizedMessage = {
      role: 'system',
      content: normalizedContent as SystemContent
    };
  } else if (normalizedRole === 'user') {
    normalizedMessage = {
      role: 'user',
      content: normalizedContent as UserContent
    };
  } else if (normalizedRole === 'assistant') {
    normalizedMessage = {
      role: 'assistant',
      content: normalizedContent as AssistantContent
    };
  } else if (normalizedRole === 'tool') {
    normalizedMessage = {
      role: 'tool',
      content: normalizedContent as ToolContent
    };
  } else {
    throw new Error(`Message ${index}: Unsupported role: ${normalizedRole}`);
  }

  // Preserve other valid properties if they exist
  if (message.id && typeof message.id === 'string') {
    (normalizedMessage as any).id = message.id;
  }

  return {
    message: normalizedMessage,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Normalizes message role to valid Vercel AI SDK v5 role
 */
function normalizeRole(role: any, index: number, warnings: string[]): ValidRole {
  if (typeof role !== 'string') {
    throw new Error(`Message ${index}: role must be a string`);
  }

  const validRoles: ValidRole[] = ['system', 'user', 'assistant', 'tool'];
  
  // Handle exact matches
  if (validRoles.includes(role as ValidRole)) {
    return role as ValidRole;
  }

  // Handle common legacy role mappings
  const roleMappings: Record<string, ValidRole> = {
    'human': 'user',
    'ai': 'assistant',
    'bot': 'assistant',
    'function': 'tool',
    'tool_result': 'tool'
  };

  const mappedRole = roleMappings[role.toLowerCase()];
  if (mappedRole) {
    warnings.push(`Message ${index}: Converted role '${role}' to '${mappedRole}'`);
    return mappedRole;
  }

  throw new Error(`Message ${index}: Invalid role '${role}'. Must be one of: ${validRoles.join(', ')}`);
}

/**
 * Normalizes message content format
 */
function normalizeContent(
  content: any, 
  role: ValidRole, 
  index: number, 
  warnings: string[]
): SystemContent | UserContent | AssistantContent | ToolContent {
  if (content === null || content === undefined) {
    throw new Error(`Message ${index}: content is required`);
  }

  // Handle string content - valid for all roles
  if (typeof content === 'string') {
    // For tool role, strings need to be converted to ToolResultPart array
    if (role === 'tool') {
      warnings.push(`Message ${index}: Converting string content to tool-result format`);
      return [{
        type: 'tool-result',
        toolCallId: `generated_${Date.now()}`,
        toolName: 'unknown_tool',
        result: content
      }];
    }
    return content;
  }

  // Handle array content
  if (Array.isArray(content)) {
    return normalizeContentParts(content, role, index, warnings);
  }

  // Handle object content (convert to array)
  if (typeof content === 'object') {
    // For tool role, ensure we create a proper tool result array
    if (role === 'tool') {
      warnings.push(`Message ${index}: Converting object content to tool-result array format`);
      const normalizedPart = normalizeContentPart(content, role, index, 0, warnings);
      return normalizedPart ? [normalizedPart as ToolResultPart] : [];
    } else {
      warnings.push(`Message ${index}: Converting object content to array format`);
      return normalizeContentParts([content], role, index, warnings);
    }
  }

  throw new Error(`Message ${index}: content must be a string, object, or array`);
}

/**
 * Normalizes content parts array
 */
function normalizeContentParts(
  parts: any[], 
  role: ValidRole, 
  index: number, 
  warnings: string[]
): Array<TextPart | ImagePart> | Array<TextPart | ToolCallPart> | Array<ToolResultPart> {
  if (!Array.isArray(parts)) {
    throw new Error(`Message ${index}: content parts must be an array`);
  }

  const normalizedParts: any[] = [];

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    
    if (!part || typeof part !== 'object') {
      throw new Error(`Message ${index}: content part ${i} must be an object`);
    }

    const normalizedPart = normalizeContentPart(part, role, index, i, warnings);
    if (normalizedPart) {
      normalizedParts.push(normalizedPart);
    }
  }

  // Type assertion based on role
  if (role === 'user') {
    return normalizedParts as Array<TextPart | ImagePart>;
  } else if (role === 'assistant') {
    return normalizedParts as Array<TextPart | ToolCallPart>;
  } else if (role === 'tool') {
    return normalizedParts as Array<ToolResultPart>;
  }

  // Fallback for system (shouldn't reach here as system uses strings)
  return normalizedParts as Array<TextPart | ImagePart>;
}

/**
 * Normalizes a single content part
 */
function normalizeContentPart(
  part: any, 
  role: ValidRole, 
  messageIndex: number, 
  partIndex: number, 
  warnings: string[]
): TextPart | ImagePart | ToolCallPart | ToolResultPart | null {
  if (!part.type) {
    throw new Error(`Message ${messageIndex}, part ${partIndex}: type is required`);
  }

  const partLocation = `Message ${messageIndex}, part ${partIndex}`;

  switch (part.type) {
    case 'text':
      if (typeof part.text !== 'string') {
        throw new Error(`${partLocation}: text part must have a string 'text' property`);
      }
      return { type: 'text', text: part.text };

    case 'image':
      // Images are only supported in user messages
      if (role !== 'user') {
        warnings.push(`${partLocation}: image part should only be in user messages, skipping`);
        return null;
      }
      if (typeof part.image !== 'string') {
        throw new Error(`${partLocation}: image part must have a string 'image' property`);
      }
      return { type: 'image', image: part.image };

    case 'tool-call':
      // Tool calls are only supported in assistant messages
      if (role !== 'assistant') {
        warnings.push(`${partLocation}: tool-call part should only be in assistant messages, skipping`);
        return null;
      }
      return normalizeToolCallPart(part, partLocation);

    case 'tool-result':
      // Tool results are only supported in tool messages
      if (role !== 'tool') {
        warnings.push(`${partLocation}: tool-result part should only be in tool messages, skipping`);
        return null;
      }
      return normalizeToolResultPart(part, partLocation);

    // Handle legacy type names
    case 'function_call':
    case 'tool_call':
      if (role !== 'assistant') {
        warnings.push(`${partLocation}: legacy tool-call part should only be in assistant messages, skipping`);
        return null;
      }
      warnings.push(`${partLocation}: Converting legacy type '${part.type}' to 'tool-call'`);
      return normalizeToolCallPart({ ...part, type: 'tool-call' }, partLocation);

    case 'function_result':
    case 'tool_result_content':
      if (role !== 'tool') {
        warnings.push(`${partLocation}: legacy tool-result part should only be in tool messages, skipping`);
        return null;
      }
      warnings.push(`${partLocation}: Converting legacy type '${part.type}' to 'tool-result'`);
      return normalizeToolResultPart({ ...part, type: 'tool-result' }, partLocation);

    default:
      warnings.push(`${partLocation}: Unknown content part type '${part.type}', skipping`);
      return null;
  }
}

/**
 * Normalizes tool call part
 */
function normalizeToolCallPart(part: any, location: string): ToolCallPart {
  if (!part.toolCallId || typeof part.toolCallId !== 'string') {
    throw new Error(`${location}: tool-call part must have a string 'toolCallId' property`);
  }

  if (!part.toolName || typeof part.toolName !== 'string') {
    throw new Error(`${location}: tool-call part must have a string 'toolName' property`);
  }

  // args can be any object
  const args = part.args || part.arguments || {};

  return {
    type: 'tool-call',
    toolCallId: part.toolCallId,
    toolName: part.toolName,
    args: args
  };
}

/**
 * Normalizes tool result part
 */
function normalizeToolResultPart(part: any, location: string): ToolResultPart {
  if (!part.toolCallId || typeof part.toolCallId !== 'string') {
    throw new Error(`${location}: tool-result part must have a string 'toolCallId' property`);
  }

  // Extract toolName - required in AI SDK v5
  let toolName: string;
  if (part.toolName && typeof part.toolName === 'string') {
    toolName = part.toolName;
  } else if (part.name && typeof part.name === 'string') {
    toolName = part.name;
  } else {
    // For AI SDK v5, toolName is required. If not provided, generate a reasonable default
    toolName = 'unknown_tool';
  }

  // result can be any value
  const result = part.result !== undefined ? part.result : part.content;

  return {
    type: 'tool-result',
    toolCallId: part.toolCallId,
    toolName: toolName,
    result: result
  };
}

/**
 * Converts legacy tool_calls to modern ToolCallPart format
 */
function convertLegacyToolCalls(toolCalls: LegacyToolCall[], messageIndex: number): ToolCallPart[] {
  if (!Array.isArray(toolCalls)) {
    throw new Error(`Message ${messageIndex}: tool_calls must be an array`);
  }

  return toolCalls.map((call, i) => {
    const callLocation = `Message ${messageIndex}, tool_calls[${i}]`;
    
    // Generate ID if missing
    const toolCallId = call.id || `call_${Date.now()}_${i}`;
    
    // Extract tool name
    let toolName: string;
    if (call.function?.name) {
      toolName = call.function.name;
    } else if (call.name) {
      toolName = call.name;
    } else {
      throw new Error(`${callLocation}: tool call must have a name`);
    }

    // Extract arguments
    let args: any = {};
    if (call.function?.arguments) {
      if (typeof call.function.arguments === 'string') {
        try {
          args = JSON.parse(call.function.arguments);
        } catch {
          throw new Error(`${callLocation}: failed to parse function arguments JSON`);
        }
      } else {
        args = call.function.arguments;
      }
    } else if (call.arguments) {
      if (typeof call.arguments === 'string') {
        try {
          args = JSON.parse(call.arguments);
        } catch {
          throw new Error(`${callLocation}: failed to parse arguments JSON`);
        }
      } else {
        args = call.arguments;
      }
    }

    return {
      type: 'tool-call',
      toolCallId,
      toolName,
      args
    };
  });
}

/**
 * Type guard to check if a message is a valid ModelMessage
 */
export function isValidMessage(message: any): message is ModelMessage {
  try {
    if (!message || typeof message !== 'object') {
      return false;
    }

    // Check role
    const validRoles: ValidRole[] = ['system', 'user', 'assistant', 'tool'];
    if (!validRoles.includes(message.role)) {
      return false;
    }

    // Check content
    if (message.content === null || message.content === undefined) {
      return false;
    }

    if (typeof message.content === 'string') {
      return true;
    }

    if (Array.isArray(message.content)) {
      return message.content.every((part: any) => isValidContentPart(part));
    }

    return false;
  } catch {
    return false;
  }
}

/**
 * Type guard for valid content part
 */
function isValidContentPart(part: any): boolean {
  if (!part || typeof part !== 'object' || !part.type) {
    return false;
  }

  switch (part.type) {
    case 'text':
      return typeof part.text === 'string';
    case 'image':
      return typeof part.image === 'string';
    case 'tool-call':
      return typeof part.toolCallId === 'string' && 
             typeof part.toolName === 'string' && 
             part.args !== undefined;
    case 'tool-result':
      return typeof part.toolCallId === 'string' && 
             part.result !== undefined;
    default:
      return false;
  }
}