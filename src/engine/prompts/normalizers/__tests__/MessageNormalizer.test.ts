import { describe, it, expect } from 'vitest';
import { normalizeMessages, isValidMessage, type NormalizationResult } from '../MessageNormalizer';
import { ModelMessage } from 'ai';

describe('MessageNormalizer', () => {
  describe('normalizeMessages', () => {
    it('should pass through valid Vercel AI SDK v5 messages unchanged', () => {
      const validMessages: ModelMessage[] = [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: 'Hello, how are you?'
        },
        {
          role: 'assistant',
          content: 'I am doing well, thank you!'
        }
      ];

      const result = normalizeMessages(validMessages);

      expect(result.messages).toEqual(validMessages);
      expect(result.warnings).toBeUndefined();
    });

    it('should handle messages with array content', () => {
      const messages: ModelMessage[] = [
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Hello' },
            { type: 'image', image: 'data:image/png;base64,abc123' }
          ]
        }
      ];

      const result = normalizeMessages(messages);

      expect(result.messages).toEqual(messages);
      expect(result.warnings).toBeUndefined();
    });

    it('should convert legacy roles to standard roles', () => {
      const messagesWithLegacyRoles = [
        { role: 'human', content: 'Hello' },
        { role: 'ai', content: 'Hi there!' },
        { role: 'bot', content: 'How can I help?' },
        { role: 'function', content: 'Function result' }
      ];

      const result = normalizeMessages(messagesWithLegacyRoles);

      expect(result.messages).toEqual([
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
        { role: 'assistant', content: 'How can I help?' },
        { 
          role: 'tool', 
          content: [{
            type: 'tool-result',
            toolCallId: expect.stringMatching(/^generated_\d+$/),
            toolName: 'unknown_tool',
            result: 'Function result'
          }]
        }
      ]);
      expect(result.warnings).toEqual([
        "Message 0: Converted role 'human' to 'user'",
        "Message 1: Converted role 'ai' to 'assistant'",
        "Message 2: Converted role 'bot' to 'assistant'",
        "Message 3: Converted role 'function' to 'tool'",
        "Message 3: Converting string content to tool-result format"
      ]);
    });

    it('should handle tool calls in assistant messages', () => {
      const messages = [
        {
          role: 'assistant',
          content: [
            { type: 'text', text: 'I will search for that.' },
            {
              type: 'tool-call',
              toolCallId: 'call_123',
              toolName: 'search',
              args: { query: 'test' }
            }
          ]
        }
      ];

      const result = normalizeMessages(messages);

      expect(result.messages).toEqual(messages);
      expect(result.warnings).toBeUndefined();
    });

    it('should convert legacy tool_calls property to modern format', () => {
      const messagesWithLegacyToolCalls = [
        {
          role: 'assistant',
          content: 'I will call a function.',
          tool_calls: [
            {
              id: 'call_123',
              type: 'function',
              function: {
                name: 'search',
                arguments: '{"query": "test"}'
              }
            }
          ]
        }
      ];

      const result = normalizeMessages(messagesWithLegacyToolCalls);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0].role).toBe('assistant');
      expect(Array.isArray(result.messages[0].content)).toBe(true);
      
      const content = result.messages[0].content as any[];
      expect(content).toHaveLength(2);
      expect(content[0]).toEqual({ type: 'text', text: 'I will call a function.' });
      expect(content[1]).toEqual({
        type: 'tool-call',
        toolCallId: 'call_123',
        toolName: 'search',
        args: { query: 'test' }
      });

      expect(result.warnings).toContain('Message 0: Converting legacy tool_calls property to modern format');
    });

    it('should handle tool results', () => {
      const messages = [
        {
          role: 'tool',
          content: [
            {
              type: 'tool-result',
              toolCallId: 'call_123',
              toolName: 'search',
              result: { data: 'search results' }
            }
          ]
        }
      ];

      const result = normalizeMessages(messages);

      expect(result.messages).toEqual(messages);
      expect(result.warnings).toBeUndefined();
    });

    it('should handle legacy content part types', () => {
      const messagesWithLegacyParts = [
        {
          role: 'assistant',
          content: [
            {
              type: 'function_call',
              toolCallId: 'call_123',
              toolName: 'search',
              args: { query: 'test' }
            }
          ]
        },
        {
          role: 'tool',
          content: [
            {
              type: 'function_result',
              toolCallId: 'call_123',
              result: 'success'
            }
          ]
        }
      ];

      const result = normalizeMessages(messagesWithLegacyParts);

      expect(result.messages[0].content).toEqual([
        {
          type: 'tool-call',
          toolCallId: 'call_123',
          toolName: 'search',
          args: { query: 'test' }
        }
      ]);

      expect(result.messages[1].content).toEqual([
        {
          type: 'tool-result',
          toolCallId: 'call_123',
          toolName: 'unknown_tool',
          result: 'success'
        }
      ]);

      expect(result.warnings).toContain("Message 0, part 0: Converting legacy type 'function_call' to 'tool-call'");
      expect(result.warnings).toContain("Message 1, part 0: Converting legacy type 'function_result' to 'tool-result'");
    });

    it('should handle object content by converting to array', () => {
      const messagesWithObjectContent = [
        {
          role: 'user',
          content: { type: 'text', text: 'Hello' }
        }
      ];

      const result = normalizeMessages(messagesWithObjectContent);

      expect(result.messages[0].content).toEqual([
        { type: 'text', text: 'Hello' }
      ]);
      expect(result.warnings).toContain('Message 0: Converting object content to array format');
    });

    it('should skip unknown content part types with warning', () => {
      const messagesWithUnknownParts = [
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Hello' },
            { type: 'unknown_type', data: 'something' },
            { type: 'text', text: 'World' }
          ]
        }
      ];

      const result = normalizeMessages(messagesWithUnknownParts);

      expect(result.messages[0].content).toEqual([
        { type: 'text', text: 'Hello' },
        { type: 'text', text: 'World' }
      ]);
      expect(result.warnings).toContain("Message 0, part 1: Unknown content part type 'unknown_type', skipping");
    });

    it('should preserve valid message properties', () => {
      const messagesWithExtraProps = [
        {
          role: 'user',
          content: 'Hello',
          id: 'msg_123'
        }
      ];

      const result = normalizeMessages(messagesWithExtraProps);

      expect(result.messages[0]).toEqual({
        role: 'user',
        content: 'Hello',
        id: 'msg_123'
      });
    });

    it('should throw error for non-array input', () => {
      expect(() => normalizeMessages('not an array' as any)).toThrow('Messages must be an array');
    });

    it('should throw error for invalid role', () => {
      const messagesWithInvalidRole = [
        { role: 'invalid_role', content: 'Hello' }
      ];

      expect(() => normalizeMessages(messagesWithInvalidRole)).toThrow(
        "Failed to normalize message at index 0: Message 0: Invalid role 'invalid_role'. Must be one of: system, user, assistant, tool"
      );
    });

    it('should throw error for missing content', () => {
      const messagesWithMissingContent = [
        { role: 'user' }
      ];

      expect(() => normalizeMessages(messagesWithMissingContent)).toThrow(
        'Failed to normalize message at index 0: Message 0: content is required'
      );
    });

    it('should throw error for non-object message', () => {
      const invalidMessages = ['not an object'];

      expect(() => normalizeMessages(invalidMessages as any)).toThrow(
        'Failed to normalize message at index 0: Message at index 0 must be an object'
      );
    });

    it('should handle complex multi-turn conversation with tools', () => {
      const complexConversation = [
        {
          role: 'system',
          content: 'You are a helpful assistant with search capabilities.'
        },
        {
          role: 'user',
          content: 'Search for information about TypeScript'
        },
        {
          role: 'assistant',
          content: 'I will search for TypeScript information.',
          tool_calls: [
            {
              id: 'call_1',
              type: 'function',
              function: {
                name: 'web_search',
                arguments: '{"query": "TypeScript programming language"}'
              }
            }
          ]
        },
        {
          role: 'tool',
          content: [
            {
              type: 'tool-result',
              toolCallId: 'call_1',
              result: 'TypeScript is a strongly typed programming language that builds on JavaScript.'
            }
          ]
        },
        {
          role: 'assistant',
          content: 'Based on my search, TypeScript is a strongly typed programming language that builds on JavaScript.'
        }
      ];

      const result = normalizeMessages(complexConversation);

      expect(result.messages).toHaveLength(5);
      expect(result.messages[0].content).toBe('You are a helpful assistant with search capabilities.');
      expect(result.messages[1].content).toBe('Search for information about TypeScript');
      
      // Check the assistant message with tool call was converted properly
      const assistantContent = result.messages[2].content as any[];
      expect(assistantContent).toHaveLength(2);
      expect(assistantContent[0]).toEqual({ type: 'text', text: 'I will search for TypeScript information.' });
      expect(assistantContent[1]).toEqual({
        type: 'tool-call',
        toolCallId: 'call_1',
        toolName: 'web_search',
        args: { query: 'TypeScript programming language' }
      });

      // Check tool result message
      expect(result.messages[3].content).toEqual([
        {
          type: 'tool-result',
          toolCallId: 'call_1',
          toolName: 'unknown_tool',
          result: 'TypeScript is a strongly typed programming language that builds on JavaScript.'
        }
      ]);

      expect(result.warnings).toContain('Message 2: Converting legacy tool_calls property to modern format');
    });

    it('should handle empty arrays', () => {
      const result = normalizeMessages([]);
      expect(result.messages).toEqual([]);
      expect(result.warnings).toBeUndefined();
    });

    it('should handle messages with null/undefined content gracefully', () => {
      const messagesWithNullContent = [
        { role: 'user', content: null }
      ];

      expect(() => normalizeMessages(messagesWithNullContent)).toThrow(
        'Failed to normalize message at index 0: Message 0: content is required'
      );
    });
  });

  describe('isValidMessage', () => {
    it('should return true for valid string content message', () => {
      const message: ModelMessage = {
        role: 'user',
        content: 'Hello'
      };

      expect(isValidMessage(message)).toBe(true);
    });

    it('should return true for valid array content message', () => {
      const message: ModelMessage = {
        role: 'assistant',
        content: [
          { type: 'text', text: 'Hello' },
          {
            type: 'tool-call',
            toolCallId: 'call_123',
            toolName: 'search',
            args: { query: 'test' }
          }
        ]
      };

      expect(isValidMessage(message)).toBe(true);
    });

    it('should return false for invalid role', () => {
      const message = {
        role: 'invalid',
        content: 'Hello'
      };

      expect(isValidMessage(message)).toBe(false);
    });

    it('should return false for missing content', () => {
      const message = {
        role: 'user'
      };

      expect(isValidMessage(message)).toBe(false);
    });

    it('should return false for null message', () => {
      expect(isValidMessage(null)).toBe(false);
    });

    it('should return false for non-object message', () => {
      expect(isValidMessage('string')).toBe(false);
    });

    it('should return false for invalid content parts', () => {
      const message = {
        role: 'user',
        content: [
          { type: 'text' } // missing text property
        ]
      };

      expect(isValidMessage(message)).toBe(false);
    });

    it('should return true for tool messages', () => {
      const message: ModelMessage = {
        role: 'tool',
        content: [
          {
            type: 'tool-result',
            toolCallId: 'call_123',
            result: 'success'
          }
        ]
      };

      expect(isValidMessage(message)).toBe(true);
    });
  });
});