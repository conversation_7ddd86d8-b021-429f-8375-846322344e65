import { describe, it, expect } from 'vitest';
import {
  normalizeToolContent,
  isValidToolResultPart,
  isValidToolCallPart,
  validateToolContent,
  type ToolNormalizationResult
} from '../ToolContentNormalizer';
import { ToolResultPart, ToolCallPart } from 'ai';

describe('ToolContentNormalizer', () => {
  describe('normalizeToolContent', () => {
    describe('for tool role (tool results)', () => {
      it('should pass through valid tool result arrays unchanged', () => {
        const validContent: ToolResultPart[] = [
          {
            type: 'tool-result',
            toolCallId: 'call_123',
            toolName: 'search',
            result: { data: 'search results' }
          }
        ];

        const result = normalizeToolContent('tool', validContent);

        expect(result.content).toEqual(validContent);
        expect(result.warnings).toBeUndefined();
      });

      it('should convert single tool result object to array', () => {
        const singleResult = {
          type: 'tool-result',
          toolCallId: 'call_123',
          toolName: 'search',
          result: 'success'
        };

        const result = normalizeToolContent('tool', singleResult);

        expect(result.content).toEqual([singleResult]);
        expect(result.warnings).toContain('Converting single tool result object to array format');
      });

      it('should convert string content to tool result format', () => {
        const stringContent = 'Operation completed successfully';

        const result = normalizeToolContent('tool', stringContent);

        expect(result.content).toHaveLength(1);
        expect(result.content[0].type).toBe('tool-result');
        expect(result.content[0].result).toBe(stringContent);
        expect(typeof result.content[0].toolCallId).toBe('string');
        expect(result.warnings).toContain('Converting string content to tool-result format, using generated toolCallId');
      });

      it('should handle legacy tool result formats', () => {
        const legacyResults = [
          {
            tool_call_id: 'call_123',
            content: 'legacy result 1'
          },
          {
            id: 'call_456',
            output: 'legacy result 2'
          }
        ];

        const result = normalizeToolContent('tool', legacyResults);

        expect(result.content).toHaveLength(2);
        expect(result.content[0]).toEqual({
          type: 'tool-result',
          toolCallId: 'call_123',
          toolName: 'unknown_tool',
          result: 'legacy result 1'
        });
        expect(result.content[1]).toEqual({
          type: 'tool-result',
          toolCallId: 'call_456',
          toolName: 'unknown_tool',
          result: 'legacy result 2'
        });

        expect(result.warnings).toContain("Tool result 0: Converting 'tool_call_id' to 'toolCallId'");
        expect(result.warnings).toContain("Tool result 0: Converting 'content' to 'result'");
        expect(result.warnings).toContain("Tool result 0: Missing toolName, using 'unknown_tool'");
        expect(result.warnings).toContain("Tool result 1: Using 'id' as 'toolCallId'");
        expect(result.warnings).toContain("Tool result 1: Converting 'output' to 'result'");
        expect(result.warnings).toContain("Tool result 1: Missing toolName, using 'unknown_tool'");
      });

      it('should generate toolCallId if missing', () => {
        const resultWithoutId = {
          result: 'some result'
        };

        const result = normalizeToolContent('tool', [resultWithoutId]);

        expect(result.content).toHaveLength(1);
        expect(result.content[0].type).toBe('tool-result');
        expect(result.content[0].result).toBe('some result');
        expect(typeof result.content[0].toolCallId).toBe('string');
        expect(result.warnings).toContain('Tool result 0: Missing toolCallId, generating one');
      });

      it('should handle missing result content', () => {
        const resultWithoutContent = {
          toolCallId: 'call_123'
        };

        const result = normalizeToolContent('tool', [resultWithoutContent]);

        expect(result.content[0]).toEqual({
          type: 'tool-result',
          toolCallId: 'call_123',
          toolName: 'unknown_tool',
          result: {}
        });
        expect(result.warnings).toContain('Tool result 0: No result content found, using empty object');
        expect(result.warnings).toContain("Tool result 0: Missing toolName, using 'unknown_tool'");
      });

      it('should skip invalid parts with warnings', () => {
        const mixedContent = [
          {
            type: 'tool-result',
            toolCallId: 'call_123',
            result: 'valid result'
          },
          null,
          'invalid string',
          {
            type: 'tool-result',
            toolCallId: 'call_456',
            result: 'another valid result'
          }
        ];

        const result = normalizeToolContent('tool', mixedContent);

        expect(result.content).toHaveLength(2);
        expect(result.content[0].result).toBe('valid result');
        expect(result.content[1].result).toBe('another valid result');
        expect(result.warnings).toContain('Tool result 1: Skipping invalid part (not an object)');
        expect(result.warnings).toContain('Tool result 2: Skipping invalid part (not an object)');
      });
    });

    describe('for assistant role (tool calls)', () => {
      it('should pass through valid tool call arrays unchanged', () => {
        const validContent: ToolCallPart[] = [
          {
            type: 'tool-call',
            toolCallId: 'call_123',
            toolName: 'search',
            args: { query: 'test' }
          }
        ];

        const result = normalizeToolContent('assistant', validContent);

        expect(result.content).toEqual(validContent);
        expect(result.warnings).toBeUndefined();
      });

      it('should convert single tool call object to array', () => {
        const singleCall = {
          type: 'tool-call',
          toolCallId: 'call_123',
          toolName: 'search',
          args: { query: 'test' }
        };

        const result = normalizeToolContent('assistant', singleCall);

        expect(result.content).toEqual([singleCall]);
        expect(result.warnings).toContain('Converting single tool call object to array format');
      });

      it('should handle legacy tool call formats', () => {
        const legacyCalls = [
          {
            id: 'call_123',
            name: 'search',
            arguments: { query: 'test1' }
          },
          {
            tool_call_id: 'call_456',
            function: {
              name: 'calculate',
              arguments: '{"x": 10, "y": 20}'
            }
          }
        ];

        const result = normalizeToolContent('assistant', legacyCalls);

        expect(result.content).toHaveLength(2);
        expect(result.content[0]).toEqual({
          type: 'tool-call',
          toolCallId: 'call_123',
          toolName: 'search',
          args: { query: 'test1' }
        });
        expect(result.content[1]).toEqual({
          type: 'tool-call',
          toolCallId: 'call_456',
          toolName: 'calculate',
          args: { x: 10, y: 20 }
        });

        expect(result.warnings).toContain("Tool call 0: Converting 'name' to 'toolName'");
        expect(result.warnings).toContain("Tool call 0: Converting 'arguments' to 'args'");
        expect(result.warnings).toContain("Tool call 1: Converting 'tool_call_id' to 'toolCallId'");
        expect(result.warnings).toContain("Tool call 1: Converting 'function.name' to 'toolName'");
        expect(result.warnings).toContain("Tool call 1: Parsed 'function.arguments' JSON string to object");
      });

      it('should handle mixed content arrays (filter tool calls)', () => {
        const mixedContent = [
          { type: 'text', text: 'I will search for that.' },
          {
            type: 'tool-call',
            toolCallId: 'call_123',
            toolName: 'search',
            args: { query: 'test' }
          },
          { type: 'image', image: 'data:image/png;base64,abc' },
          {
            name: 'calculate',
            arguments: { x: 1, y: 2 }
          }
        ];

        const result = normalizeToolContent('assistant', mixedContent);

        expect(result.content).toHaveLength(2);
        expect(result.content[0]).toEqual({
          type: 'tool-call',
          toolCallId: 'call_123',
          toolName: 'search',
          args: { query: 'test' }
        });
        expect(result.content[1].toolName).toBe('calculate');
        expect(result.content[1].args).toEqual({ x: 1, y: 2 });
      });

      it('should generate toolCallId if missing', () => {
        const callWithoutId = {
          name: 'search',
          arguments: { query: 'test' }
        };

        const result = normalizeToolContent('assistant', [callWithoutId]);

        expect(result.content).toHaveLength(1);
        expect(result.content[0].type).toBe('tool-call');
        expect(result.content[0].toolName).toBe('search');
        expect(result.content[0].args).toEqual({ query: 'test' });
        expect(typeof result.content[0].toolCallId).toBe('string');
        expect(result.warnings).toContain('Tool call 0: Missing toolCallId, generating one');
        expect(result.warnings).toContain("Tool call 0: Converting 'name' to 'toolName'");
        expect(result.warnings).toContain("Tool call 0: Converting 'arguments' to 'args'");
      });

      it('should handle string arguments parsing', () => {
        const callWithStringArgs = {
          toolCallId: 'call_123',
          toolName: 'search',
          arguments: '{"query": "test", "limit": 10}'
        };

        const result = normalizeToolContent('assistant', [callWithStringArgs]);

        expect(result.content[0].args).toEqual({ query: 'test', limit: 10 });
        expect(result.warnings).toContain("Tool call 0: Parsed 'arguments' JSON string to object");
      });

      it('should throw error for invalid JSON in arguments', () => {
        const callWithInvalidJson = {
          toolCallId: 'call_123',
          toolName: 'search',
          arguments: '{"invalid": json}'
        };

        expect(() => normalizeToolContent('assistant', [callWithInvalidJson])).toThrow(
          'Tool call 0: Failed to parse arguments JSON string'
        );
      });

      it('should throw error for missing tool name', () => {
        const callWithoutName = {
          toolCallId: 'call_123',
          args: { query: 'test' }
        };

        expect(() => normalizeToolContent('assistant', [callWithoutName])).toThrow(
          'Tool call 0: Tool call must have a name/toolName'
        );
      });

      it('should skip invalid parts with warnings', () => {
        const mixedContent = [
          {
            type: 'tool-call',
            toolCallId: 'call_123',
            toolName: 'search',
            args: { query: 'test' }
          },
          null,
          'invalid string',
          {
            name: 'calculate',
            arguments: { x: 1, y: 2 }
          }
        ];

        const result = normalizeToolContent('assistant', mixedContent);

        expect(result.content).toHaveLength(2);
        expect(result.content[0].toolName).toBe('search');
        expect(result.content[1].toolName).toBe('calculate');
        expect(result.warnings).toContain('Tool call 1: Skipping invalid part (not an object)');
        expect(result.warnings).toContain('Tool call 2: Skipping invalid part (not an object)');
      });
    });

    it('should throw error for invalid role', () => {
      expect(() => normalizeToolContent('user' as any, 'content')).toThrow(
        "Invalid role for tool content normalization: user. Must be 'tool' or 'assistant'"
      );
    });

    it('should throw error for null/undefined content', () => {
      expect(() => normalizeToolContent('tool', null)).toThrow(
        'Tool content cannot be null or undefined for role: tool'
      );

      expect(() => normalizeToolContent('assistant', undefined)).toThrow(
        'Tool content cannot be null or undefined for role: assistant'
      );
    });

    it('should throw error for invalid content types', () => {
      expect(() => normalizeToolContent('tool', 123)).toThrow(
        'Invalid tool result content type: number. Expected object, array, or string'
      );

      expect(() => normalizeToolContent('assistant', 123)).toThrow(
        'Invalid tool call content type: number. Expected object or array'
      );
    });
  });

  describe('isValidToolResultPart', () => {
    it('should return true for valid tool result part', () => {
      const validPart: ToolResultPart = {
        type: 'tool-result',
        toolCallId: 'call_123',
        toolName: 'search',
        result: { data: 'test' }
      };

      expect(isValidToolResultPart(validPart)).toBe(true);
    });

    it('should return false for missing properties', () => {
      expect(isValidToolResultPart({
        type: 'tool-result',
        toolCallId: 'call_123',
        toolName: 'search'
        // missing result
      })).toBe(false);

      expect(isValidToolResultPart({
        type: 'tool-result',
        toolName: 'search',
        result: 'test'
        // missing toolCallId
      })).toBe(false);

      expect(isValidToolResultPart({
        type: 'tool-result',
        toolCallId: 'call_123',
        result: 'test'
        // missing toolName
      })).toBe(false);

      expect(isValidToolResultPart({
        toolCallId: 'call_123',
        toolName: 'search',
        result: 'test'
        // missing type
      })).toBe(false);
    });

    it('should return false for wrong type', () => {
      expect(isValidToolResultPart({
        type: 'tool-call',
        toolCallId: 'call_123',
        result: 'test'
      })).toBe(false);
    });

    it('should return false for non-objects', () => {
      expect(isValidToolResultPart(null)).toBe(false);
      expect(isValidToolResultPart('string')).toBe(false);
      expect(isValidToolResultPart(123)).toBe(false);
    });
  });

  describe('isValidToolCallPart', () => {
    it('should return true for valid tool call part', () => {
      const validPart: ToolCallPart = {
        type: 'tool-call',
        toolCallId: 'call_123',
        toolName: 'search',
        args: { query: 'test' }
      };

      expect(isValidToolCallPart(validPart)).toBe(true);
    });

    it('should return false for missing properties', () => {
      expect(isValidToolCallPart({
        type: 'tool-call',
        toolCallId: 'call_123',
        toolName: 'search'
        // missing args
      })).toBe(false);

      expect(isValidToolCallPart({
        type: 'tool-call',
        toolCallId: 'call_123',
        args: {}
        // missing toolName
      })).toBe(false);

      expect(isValidToolCallPart({
        type: 'tool-call',
        toolName: 'search',
        args: {}
        // missing toolCallId
      })).toBe(false);
    });

    it('should return false for wrong type', () => {
      expect(isValidToolCallPart({
        type: 'tool-result',
        toolCallId: 'call_123',
        toolName: 'search',
        args: {}
      })).toBe(false);
    });

    it('should return false for non-objects', () => {
      expect(isValidToolCallPart(null)).toBe(false);
      expect(isValidToolCallPart('string')).toBe(false);
      expect(isValidToolCallPart(123)).toBe(false);
    });
  });

  describe('validateToolContent', () => {
    it('should return true for valid tool content', () => {
      const validToolContent = [
        {
          type: 'tool-result',
          toolCallId: 'call_123',
          result: 'success'
        }
      ];

      expect(validateToolContent('tool', validToolContent)).toBe(true);

      const validAssistantContent = [
        {
          type: 'tool-call',
          toolCallId: 'call_123',
          toolName: 'search',
          args: { query: 'test' }
        }
      ];

      expect(validateToolContent('assistant', validAssistantContent)).toBe(true);
    });

    it('should return false for invalid content', () => {
      expect(validateToolContent('tool', null)).toBe(false);
      expect(validateToolContent('assistant', 'invalid')).toBe(false);
      expect(validateToolContent('tool', [{ invalid: 'data' }])).toBe(false);
    });

    it('should handle edge cases', () => {
      // Empty arrays should be valid
      expect(validateToolContent('tool', [])).toBe(true);
      expect(validateToolContent('assistant', [])).toBe(true);
    });
  });

  describe('complex scenarios', () => {
    it('should handle complex legacy OpenAI format conversion', () => {
      const legacyOpenAIFormat = {
        role: 'assistant',
        content: null,
        tool_calls: [
          {
            id: 'call_abc123',
            type: 'function',
            function: {
              name: 'get_current_weather',
              arguments: '{"location": "Boston, MA"}'
            }
          }
        ]
      };

      // First normalize the message (this would be done by MessageNormalizer)
      // Then extract and normalize just the tool calls content
      const result = normalizeToolContent('assistant', legacyOpenAIFormat.tool_calls);

      expect(result.content).toHaveLength(1);
      expect(result.content[0]).toEqual({
        type: 'tool-call',
        toolCallId: 'call_abc123',
        toolName: 'get_current_weather',
        args: { location: 'Boston, MA' }
      });
    });

    it('should handle complex multi-tool scenario', () => {
      const multiToolContent = [
        {
          id: 'call_1',
          function: {
            name: 'search_web',
            arguments: '{"query": "TypeScript tutorials"}'
          }
        },
        {
          toolCallId: 'call_2',
          toolName: 'calculate',
          args: { expression: '2 + 2' }
        },
        {
          name: 'send_email',
          arguments: {
            to: '<EMAIL>',
            subject: 'Results'
          }
        }
      ];

      const result = normalizeToolContent('assistant', multiToolContent);

      expect(result.content).toHaveLength(3);
      
      // First tool (legacy OpenAI format)
      expect(result.content[0]).toEqual({
        type: 'tool-call',
        toolCallId: 'call_1',
        toolName: 'search_web',
        args: { query: 'TypeScript tutorials' }
      });

      // Second tool (already proper format)
      expect(result.content[1]).toEqual({
        type: 'tool-call',
        toolCallId: 'call_2',
        toolName: 'calculate',
        args: { expression: '2 + 2' }
      });

      // Third tool (simple legacy format)
      expect(result.content[2].type).toBe('tool-call');
      expect(result.content[2].toolName).toBe('send_email');
      expect(result.content[2].args).toEqual({
        to: '<EMAIL>',
        subject: 'Results'
      });
      expect(typeof result.content[2].toolCallId).toBe('string');
    });
  });
});