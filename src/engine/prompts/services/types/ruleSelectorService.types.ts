/**
 * Rule Selector Service Types
 * 
 * Defines TypeScript interfaces and types for the RuleSelectorService,
 * which is responsible for selecting relevant rules to inject into agent prompts.
 */

import { N8nRule } from '../../../../types/rules.types';

/**
 * Context information for rule selection
 */
export interface RuleSelectionContext {
  /** The agent slug for which rules should be selected */
  agentSlug: string;
  
  /** Optional task description to help with contextual rule selection */
  taskDescription?: string;
  
  /** Optional specific node types being used (for more targeted rule selection) */
  nodeTypes?: string[];
  
  /** Optional workflow context keywords */
  workflowKeywords?: string[];
  
  // Future expansion options for V2:
  // currentGraphContext?: GraphExecutionContext;
  // currentTaskSession?: TaskSession;
  // userPreferences?: RuleSelectionPreferences;
}

/**
 * Result of rule selection operation
 */
export interface SelectedRules {
  /** Rules that always apply to the agent */
  alwaysApplyRules: N8nRule[];
  
  /** Rules that apply based on current context */
  contextualRules: N8nRule[];
  
  /** Total number of rules considered during selection */
  totalRulesConsidered: number;
  
  /** Keywords extracted from task description (for debugging/insight) */
  extractedKeywords?: string[];
}

/**
 * Configuration options for rule selection
 */
export interface RuleSelectionOptions {
  /** Maximum number of contextual rules to return (default: 5) */
  maxContextualRules?: number;
  
  /** Whether to include base (built-in) rules (default: true) */
  includeBaseRules?: boolean;
  
  /** Whether to include custom user rules (default: true) */
  includeCustomRules?: boolean;
  
  /** Minimum priority level for contextual rules (default: all) */
  minPriority?: N8nRule['priority'];
  
  /** Specific rule categories to include/exclude */
  categoryFilter?: {
    include?: string[];
    exclude?: string[];
  };
  
  /** Options for keyword extraction from task description */
  keywordExtractionOptions?: KeywordExtractionOptions;
}

/**
 * Keyword extraction configuration
 */
export interface KeywordExtractionOptions {
  /** Maximum number of keywords to extract (default: 10) */
  maxKeywords?: number;
  
  /** Minimum word length to consider (default: 3) */
  minWordLength?: number;
  
  /** Whether to include stop words (default: false) */
  includeStopWords?: boolean;
  
  /** Custom stop words to filter out */
  customStopWords?: string[];
}

/**
 * Result of keyword extraction
 */
export interface KeywordExtractionResult {
  /** Extracted keywords in order of relevance/frequency */
  keywords: string[];
  
  /** Word frequency map */
  frequencies: Record<string, number>;
  
  /** Total words processed */
  totalWordsProcessed: number;
}

/**
 * Rule scoring result for contextual selection
 */
export interface RuleScoringResult {
  /** The rule being scored */
  rule: N8nRule;
  
  /** Calculated relevance score */
  score: number;
  
  /** Matched keywords that contributed to the score */
  matchedKeywords: string[];
  
  /** Additional scoring factors */
  scoringFactors: {
    keywordMatches: number;
    priorityWeight: number;
    categoryBonus: number;
    totalScore: number;
  };
} 