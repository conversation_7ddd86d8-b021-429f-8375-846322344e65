// import { LLMTokenUsage as BaseLLMTokenUsage } from '../../types/tasks.types'; // Don't use this complex one
import { ModelMessage } from 'ai';
import { TaskSession } from '../../../../types/tasks.types';
import { LLMTaskCategory } from '@/engine/graph/types/graph.types';

// Define a simpler LLMTokenUsage specific to this service's needs for summarization calls.
export interface LLMTokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost?: number; // Optional cost
}

export interface GetHistoryOptions {
  targetModelContextWindow: number;
  // Max tokens specifically budgeted for the history part of the prompt
  // (already accounts for tokens used by system message, current agent task, etc.)
  tokensAvailableForHistory: number;
  // Max number of raw recent messages to keep AFTER a summarization event.
  maxMessagesToKeepAfterSummary?: number;
  // Max number of raw recent messages to consider for summarization input if no prior summary exists.
  maxRecentMessagesForInitialSummaryInput?: number;
  // Summarizer agent details
  summarizerAgentSlug?: string;
  summarizerTaskCategory?: LLMTaskCategory;
  // Optional: Force summarization, e.g., for testing or specific scenarios
  forceSummarization?: boolean;
}

export interface HistoryForPromptResult {
  historyMessages: ModelMessage[]; // Ready for prompt (summary + recent turns)
  summaryWasPerformedThisCall: boolean;
  // The TaskSession object, potentially updated with a new summary and summaryLastMessageId
  updatedTaskSession: TaskSession;
  finalHistoryTokenCount: number;
  summarizerTokenUsage?: LLMTokenUsage | null; // Uses the local, simpler LLMTokenUsage
}

// Internal type for the _performSummarization method result
export interface InternalSummarizationOutcome {
  success: boolean;
  newSummaryText?: string;
  summarizerTokenUsage?: LLMTokenUsage; // Uses the local, simpler LLMTokenUsage
  error?: string;
  lastMessageIdInSummarizerInput?: string; // Added to track what was summarized
} 