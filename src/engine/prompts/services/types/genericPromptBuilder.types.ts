import { ModelMessage } from 'ai'; // MessageContent for more complex content later
import { z, ZodTypeAny } from 'zod';

export type PromptComponentRole = 'system' | 'user' | 'assistant';

export interface SystemInstructionComponent {
  type: 'system_instruction';
  content: string;
  // No explicit role needed, it's implied by type
}

export interface UserMessageComponent {
  type: 'user_message';
  content: string; // For V1, simple string. Could be MessageContent for V2.
}

export interface AssistantMessageComponent {
  type: 'assistant_message';
  content: string; // For V1, simple string.
  isSummary?: boolean; // Hint for special formatting if needed
}

export interface HistoryBlockComponent {
  type: 'history_block';
  messages: ModelMessage[]; // Pre-fetched and managed history messages
}

export interface JsonSchemaInstructionComponent {
  type: 'json_schema_instruction';
  role: PromptComponentRole; // Usually 'system' or part of 'user' message
  instructionPrefix?: string; // e.g., "Your output MUST conform to the following JSON schema:"
  zodSchema: ZodTypeAny;
  schemaName?: string; // For root object name in schema, passed to zod-to-json-schema
}

export interface TaggedDataComponent {
  type: 'tagged_data';
  role: PromptComponentRole; // Where this data block should appear
  tag: string; // e.g., 'user_request', 'available_tools', 'retrieved_rules'
  data: any; // Data to be stringified (JSON.stringify for objects/arrays)
  // Optional: if data is already a pre-formatted string (like XML or Markdown)
  isPreformattedString?: boolean;
}

export interface TextBlockComponent { // For general text injection at a specific role
  type: 'text_block';
  role: PromptComponentRole;
  content: string;
}

export type PromptComponent =
  | SystemInstructionComponent
  | UserMessageComponent
  | AssistantMessageComponent
  | HistoryBlockComponent
  | JsonSchemaInstructionComponent
  | TaggedDataComponent
  | TextBlockComponent;

export interface AssembledPromptResult {
  messages: ModelMessage[];
  tokenCount: number;
}

// Injected tokenizer function types
export type TokenCountFunction = (text: string, modelId?: string) => number;
export type ChatTokenCountFunction = (messages: ModelMessage[], modelId?: string) => number; 