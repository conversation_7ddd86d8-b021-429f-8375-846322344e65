/**
 * Prompt Builder Service Types
 * 
 * This file defines all TypeScript interfaces and types for the PromptBuilderService component.
 * These types provide comprehensive coverage for dynamic prompt construction, context management,
 * and integration with various services for contextual information injection.
 */

import { ModelMessage } from 'ai';
import { z, ZodTypeAny } from 'zod';
import { LLMTaskCategory } from '../../../graph/types/graph.types';
import { GraphDefinition } from '../../../graph/types/graph.types';

/**
 * Dynamic data passed to an agent for a specific LLM call
 */
export interface AgentCallSpecificData {
  [placeholderKey: string]: any;
}

/**
 * Options to override PromptBuilderService behavior for a specific call
 */
export interface PromptBuilderOptions {
  overrideOutputJsonSchemaString?: string;
  maxContextTokensForThisCall?: number;
  includeKnowledgeSnippets?: boolean;
  includeRules?: boolean;
}

/**
 * Result of assembling a prompt
 */
export interface AssembledPrompt {
  messages: ModelMessage[];
  promptTokenCount: number;
  contextWasSummarized: boolean;
}

/**
 * Function signature for counting tokens in a single string.
 */
export type TokenCountFunction = (text: string) => number;

/**
 * Function signature for counting tokens in an array of ModelMessages.
 * This might involve special formatting or concatenation before counting.
 */
export type ChatTokenCountFunction = (messages: ModelMessage[]) => number;

/**
 * Configuration entry for a single agent
 */
export interface AgentConfigEntry {
  slug: string;
  description?: string;
  defaultLlmTaskCategory: LLMTaskCategory;
  outputSchema?: ZodTypeAny;
  roleDescription?: string;
}

/**
 * Registry of all agent configurations
 */
export interface AgentConfigRegistry {
  [agentSlug: string]: AgentConfigEntry;
}

/**
 * Task session type (placeholder - will be replaced with actual TaskSession from persistence layer)
 */
export interface TaskSession {
  id: string;
  llmContextSummary?: string;
  llmContextRelevantHistory?: ModelMessage[];
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Rule type (placeholder - will be replaced with actual N8nRule)
 */
export interface N8nRule {
  id: string;
  name: string;
  descriptionForLLM: string;
  type: 'ALWAYS_APPLY' | 'CONTEXTUAL_SUGGESTION' | 'ANTI_PATTERN_CHECK';
  appliesToAgents: string[];
  triggerKeywordsOrContext?: string[];
}

/**
 * Knowledge chunk with similarity score
 */
export interface KnowledgeChunkWithScore {
  id: string;
  sourceType: string;
  chunkText: string;
  score: number;
  metadata?: Record<string, any>;
}

/**
 * Context for rule selection
 */
export interface RuleSelectionContext {
  agentSlug: string;
  taskCategory?: LLMTaskCategory;
  contextData?: Record<string, any>;
  userRequest?: string;
}

/**
 * Rule selector service interface
 */
export interface RuleSelectorService {
  getRulesForAgentPrompt(context: RuleSelectionContext): Promise<N8nRule[]>;
}

/**
 * Knowledge repository interface
 */
export interface KnowledgeRepository {
  retrieveSnippets(query: string, topK?: number): Promise<KnowledgeChunkWithScore[]>;
  vectorSearch?(queryEmbedding: number[], topK?: number, filters?: Record<string, any>): Promise<KnowledgeChunkWithScore[]>;
}

/**
 * Task session manager interface
 */
export interface TaskSessionManager {
  getTaskSession(sessionId: string): Promise<TaskSession | null>;
  updateTaskSession(taskSession: TaskSession): Promise<TaskSession>;
  createTaskSession(initialData: Partial<TaskSession>): Promise<TaskSession>;
}

/**
 * Context window management result
 */
export interface ContextWindowManagementResult {
  updatedTaskSession: TaskSession;
  historyMessagesForPrompt: ModelMessage[];
  summarized: boolean;
}

/**
 * Standard prompt sections
 */
export interface StandardPromptSections {
  teamAwareness: string;
  inputContextDefinition: string;
  outputFormatInstruction: string;
}

/**
 * Template for prompt construction
 */
export interface PromptTemplate {
  key: string;
  content: string;
  requiredPlaceholders: string[];
  optionalPlaceholders?: string[];
}

/**
 * Base error for prompt building operations
 */
export class PromptBuilderError extends Error {
  constructor(
    message: string,
    public readonly errorType: string = 'PromptBuilderError',
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'PromptBuilderError';
  }
}

/**
 * Error when agent configuration is not found
 */
export class AgentConfigNotFoundError extends PromptBuilderError {
  constructor(agentSlug: string) {
    super(`Agent configuration not found for: ${agentSlug}`, 'AgentConfigNotFoundError');
    this.name = 'AgentConfigNotFoundError';
  }
}

/**
 * Error when template is not found
 */
export class TemplateNotFoundError extends PromptBuilderError {
  constructor(templateKey: string, agentSlug?: string) {
    const location = agentSlug ? ` for agent '${agentSlug}'` : '';
    super(`Template '${templateKey}' not found${location}`, 'TemplateNotFoundError');
    this.name = 'TemplateNotFoundError';
  }
}

/**
 * Error when context window is exceeded
 */
export class ContextWindowOverflowError extends PromptBuilderError {
  constructor(tokenCount: number, limit: number) {
    super(`Context window exceeded: ${tokenCount} tokens > ${limit} limit`, 'ContextWindowOverflowError');
    this.name = 'ContextWindowOverflowError';
  }
}

/**
 * Error when summarization fails
 */
export class SummarizationFailedError extends PromptBuilderError {
  constructor(message: string, originalError?: Error) {
    super(message, 'SummarizationFailedError', originalError);
    this.name = 'SummarizationFailedError';
  }
}

/**
 * Configuration for context window management.
 */
export interface ContextWindowConfig {
  // Default max number of raw recent messages to keep AFTER a summarization event.
  maxMessagesToKeepAfterSummary: number;
  // Default max number of raw recent messages to consider for summarization input if no prior summary exists.
  maxRecentMessagesForInitialSummaryInput: number;
  // Default maximum number of tokens for the output of a summarization call.
  maxSummaryTokens: number;
  // Default name/slug of the summarizer agent
  defaultSummarizerAgentSlug: string;
  // Default task category for the summarizer agent
  defaultSummarizerTaskCategory: string; // Assuming LLMTaskCategory will be a string enum or similar
  // TODO: Remove these deprecated properties when replacing with AgentPromptBuilder
  summarizationThreshold: number;
  maxHistoryMessagesAfterSummarization: number;
}

export const DEFAULT_CONTEXT_WINDOW_CONFIG: ContextWindowConfig = {
  maxMessagesToKeepAfterSummary: 3,
  maxRecentMessagesForInitialSummaryInput: 20,
  maxSummaryTokens: 500, // Adjust as needed
  defaultSummarizerAgentSlug: 'n8n-summarizer-agent', // From project summary
  defaultSummarizerTaskCategory: 'UTILITY_CONVERSATION_SUMMARIZATION', // From project summary
  // TODO: Remove these deprecated properties when replacing with AgentPromptBuilder
  summarizationThreshold: 0.7,
  maxHistoryMessagesAfterSummarization: 10,
}; 