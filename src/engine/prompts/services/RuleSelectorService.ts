/**
 * Rule Selector Service
 * 
 * Selects and provides a relevant subset of N8nRules (both bundled base rules 
 * and user-defined custom rules) to the PromptBuilderService for injection 
 * into an agent's prompt.
 * 
 * Key Features:
 * - Combines base rules and custom rules from repository
 * - Filters rules based on agent applicability
 * - V1 keyword-based contextual rule selection
 * - Priority-based rule ordering
 * - Configurable limits on contextual rules
 * - Comprehensive logging and error handling
 * 
 * Usage Example:
 * ```typescript
 * const ruleSelector = new RuleSelectorService(customRulesRepo);
 * const rules = await ruleSelector.getRulesForAgentPrompt({
 *   agentSlug: 'n8n-architect-agent',
 *   taskDescription: 'Create a webhook workflow with error handling'
 * });
 * ```
 */

import { CustomRulesRepository } from '../../../persistence/repositories/CustomRulesRepository';
import { N8nRule, RuleType } from '../../../types/rules.types';
import { loadBaseN8nRules } from '../../../config/baseN8nRules.config';
import {
  RuleSelectionContext,
  SelectedRules,
  RuleSelectionOptions,
  KeywordExtractionOptions,
  KeywordExtractionResult,
  RuleScoringResult
} from './types/ruleSelectorService.types';

/**
 * Default stop words for keyword extraction (English)
 */
const DEFAULT_STOP_WORDS = [
  'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
  'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
  'to', 'was', 'will', 'with', 'would', 'this', 'can', 'should',
  'could', 'have', 'had', 'but', 'not', 'or', 'if', 'then', 'when',
  'where', 'what', 'how', 'why', 'who', 'which', 'there', 'here',
  'use', 'using', 'used', 'make', 'get', 'set', 'add', 'create'
];

/**
 * Priority weights for scoring (higher number = higher priority)
 */
const PRIORITY_WEIGHTS: Record<N8nRule['priority'], number> = {
  critical: 5,
  high: 4,
  medium: 3,
  low: 2,
  informational: 1
};

/**
 * Keywords that trigger category bonuses
 */
const ERROR_HANDLING_KEYWORDS = ['error', 'fail', 'exception'];
const PERFORMANCE_KEYWORDS = ['performance', 'slow', 'fast', 'optimize'];

/**
 * Service for selecting relevant rules for agent prompts
 */
export class RuleSelectorService {
  private readonly customRulesRepo: CustomRulesRepository;
  private readonly baseRules: N8nRule[];
  private readonly defaultMaxContextualRules = 5;
  private readonly defaultKeywordExtractionOptions: KeywordExtractionOptions = {
    maxKeywords: 10,
    minWordLength: 3,
    includeStopWords: false,
    customStopWords: []
  };

  /**
   * Creates a new RuleSelectorService instance
   * @param customRulesRepo - Repository for accessing custom user-defined rules
   */
  constructor(customRulesRepo: CustomRulesRepository) {
    this.customRulesRepo = customRulesRepo;
    this.baseRules = loadBaseN8nRules();
    console.log(`RuleSelectorService: Initialized with ${this.baseRules.length} base rules`);
  }

  /**
   * Gets rules for an agent prompt with contextual selection
   * @param context - Rule selection context
   * @param options - Optional configuration for rule selection
   * @returns Promise resolving to selected rules
   */
  public async getRulesForAgentPrompt(
    context: RuleSelectionContext,
    options: RuleSelectionOptions = {}
  ): Promise<N8nRule[]> {
    try {
      const selectedRules = await this.selectRules(context, options);
      
      // Combine always-apply and contextual rules
      const finalRules = [
        ...selectedRules.alwaysApplyRules,
        ...selectedRules.contextualRules
      ];

      // Remove duplicates by ID (shouldn't happen but safety check)
      const uniqueRules = finalRules.filter((rule, index, array) =>
        array.findIndex(r => r.id === rule.id) === index
      );

      console.log(
        `RuleSelectorService: Selected ${uniqueRules.length} rules for agent ${context.agentSlug} ` +
        `(${selectedRules.alwaysApplyRules.length} always-apply, ${selectedRules.contextualRules.length} contextual)`
      );

      return uniqueRules;
    } catch (error) {
      console.error('RuleSelectorService: Failed to get rules for agent prompt:', error);
      throw new Error(`Failed to select rules for agent prompt: ${error}`);
    }
  }

  /**
   * Selects rules with detailed breakdown
   * @param context - Rule selection context
   * @param options - Optional configuration for rule selection
   * @returns Promise resolving to detailed selected rules
   */
  public async selectRules(
    context: RuleSelectionContext,
    options: RuleSelectionOptions = {}
  ): Promise<SelectedRules> {
    const {
      maxContextualRules = this.defaultMaxContextualRules,
      includeBaseRules = true,
      includeCustomRules = true,
      minPriority,
      categoryFilter,
      keywordExtractionOptions
    } = options;

    // Step 1: Gather all applicable rules
    const allApplicableRules = await this.gatherApplicableRules(
      context.agentSlug,
      includeBaseRules,
      includeCustomRules
    );

    // Step 2: Apply category filters if specified
    const filteredRules = this.applyFilters(allApplicableRules, { categoryFilter, minPriority });

    // Step 3: Separate always-apply and contextual rules
    const alwaysApplyRules = filteredRules.filter(rule => rule.type === 'ALWAYS_APPLY');
    const potentialContextualRules = filteredRules.filter(rule => rule.type === 'CONTEXTUAL_SUGGESTION');

    // Step 4: Select contextual rules based on task description
    let selectedContextualRules: N8nRule[] = [];
    let extractedKeywords: string[] = [];

    if (context.taskDescription && potentialContextualRules.length > 0) {
      const keywordResult = this.extractKeywords(context.taskDescription, keywordExtractionOptions);
      extractedKeywords = keywordResult.keywords;

      const scoredRules = this.scoreRulesForContext(
        potentialContextualRules,
        keywordResult,
        context
      );

      // Select top rules by score
      selectedContextualRules = scoredRules
        .sort((a, b) => {
          // Sort by score descending, then by priority weight descending
          if (b.score !== a.score) {
            return b.score - a.score;
          }
          return PRIORITY_WEIGHTS[b.rule.priority] - PRIORITY_WEIGHTS[a.rule.priority];
        })
        .slice(0, maxContextualRules)
        .map(result => result.rule);
    }

    return {
      alwaysApplyRules,
      contextualRules: selectedContextualRules,
      totalRulesConsidered: allApplicableRules.length,
      extractedKeywords
    };
  }

  /**
   * Gathers all rules applicable to an agent from base and custom sources
   * @private
   */
  private async gatherApplicableRules(
    agentSlug: string,
    includeBaseRules: boolean,
    includeCustomRules: boolean
  ): Promise<N8nRule[]> {
    const allRules: N8nRule[] = [];

    // Add base rules if enabled
    if (includeBaseRules) {
      const applicableBaseRules = this.baseRules.filter(rule =>
        rule.isActive && rule.appliesToAgents.includes(agentSlug)
      );
      allRules.push(...applicableBaseRules);
    }

    // Add custom rules if enabled
    if (includeCustomRules) {
      const customRules = await this.customRulesRepo.getActiveRulesForAgent(agentSlug);
      allRules.push(...customRules);
    }

    return allRules;
  }

  /**
   * Applies filters to rules based on options
   * @private
   */
  private applyFilters(
    rules: N8nRule[],
    filters: { categoryFilter?: RuleSelectionOptions['categoryFilter']; minPriority?: N8nRule['priority'] }
  ): N8nRule[] {
    let filteredRules = rules;

    // Apply category filter
    if (filters.categoryFilter) {
      const { include, exclude } = filters.categoryFilter;
      
      if (include && include.length > 0) {
        filteredRules = filteredRules.filter(rule => 
          rule.category && include.includes(rule.category)
        );
      }
      
      if (exclude && exclude.length > 0) {
        filteredRules = filteredRules.filter(rule => 
          !rule.category || !exclude.includes(rule.category)
        );
      }
    }

    // Apply minimum priority filter
    if (filters.minPriority) {
      const minWeight = PRIORITY_WEIGHTS[filters.minPriority];
      filteredRules = filteredRules.filter(rule => 
        PRIORITY_WEIGHTS[rule.priority] >= minWeight
      );
    }

    return filteredRules;
  }

  /**
   * Extracts keywords from text for contextual matching
   * @private
   */
  private extractKeywords(
    text: string,
    options: KeywordExtractionOptions = {}
  ): KeywordExtractionResult {
    const {
      maxKeywords = this.defaultKeywordExtractionOptions.maxKeywords!,
      minWordLength = this.defaultKeywordExtractionOptions.minWordLength!,
      includeStopWords = this.defaultKeywordExtractionOptions.includeStopWords!,
      customStopWords = this.defaultKeywordExtractionOptions.customStopWords!
    } = options;

    // Normalize text and split into words
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
      .split(/\s+/)
      .filter(word => word.length >= minWordLength);

    // Build stop words list
    const stopWords = new Set([
      ...(includeStopWords ? [] : DEFAULT_STOP_WORDS),
      ...customStopWords
    ]);

    // Filter out stop words and count frequencies
    const frequencies: Record<string, number> = {};
    let totalWordsProcessed = 0;

    for (const word of words) {
      if (!stopWords.has(word)) {
        frequencies[word] = (frequencies[word] || 0) + 1;
        totalWordsProcessed++;
      }
    }

    // Sort by frequency and take top keywords
    const keywords = Object.entries(frequencies)
      .sort(([, a], [, b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word]) => word);

    return {
      keywords,
      frequencies,
      totalWordsProcessed
    };
  }

  /**
   * Scores rules for contextual relevance
   * @private
   */
  private scoreRulesForContext(
    rules: N8nRule[],
    keywordResult: KeywordExtractionResult,
    context: RuleSelectionContext
  ): RuleScoringResult[] {
    return rules.map(rule => {
      const scoringFactors = {
        keywordMatches: 0,
        priorityWeight: PRIORITY_WEIGHTS[rule.priority],
        categoryBonus: 0,
        totalScore: 0
      };

      const matchedKeywords: string[] = [];

      // Score based on keyword matches in trigger context
      if (rule.triggerContext?.keywords && keywordResult.keywords.length > 0) {
        const ruleKeywords = rule.triggerContext.keywords.map(k => k.toLowerCase());
        
        for (const keyword of keywordResult.keywords) {
          if (ruleKeywords.some(rk => rk.includes(keyword) || keyword.includes(rk))) {
            scoringFactors.keywordMatches++;
            matchedKeywords.push(keyword);
          }
        }
      }

      // Score based on node types if provided in context
      if (rule.triggerContext?.nodeTypes && context.nodeTypes && context.nodeTypes.length > 0) {
        const nodeTypeMatches = rule.triggerContext.nodeTypes.filter(nodeType =>
          context.nodeTypes!.some(contextType => 
            contextType.toLowerCase().includes(nodeType.toLowerCase()) ||
            nodeType.toLowerCase().includes(contextType.toLowerCase())
          )
        );
        scoringFactors.keywordMatches += nodeTypeMatches.length * 2; // Higher weight for node type matches
      }

      // Bonus for certain categories in specific contexts
      if (rule.category) {
        if (keywordResult.keywords.some(k => ERROR_HANDLING_KEYWORDS.includes(k)) &&
            rule.category === 'error-handling') {
          scoringFactors.categoryBonus = 2;
        } else if (keywordResult.keywords.some(k => PERFORMANCE_KEYWORDS.includes(k)) &&
                   rule.category === 'performance') {
          scoringFactors.categoryBonus = 2;
        }
      }

      // Calculate total score
      scoringFactors.totalScore = 
        scoringFactors.keywordMatches * 3 + // Primary scoring factor
        scoringFactors.priorityWeight * 0.5 + // Secondary factor
        scoringFactors.categoryBonus; // Bonus factor

      return {
        rule,
        score: scoringFactors.totalScore,
        matchedKeywords,
        scoringFactors
      };
    }).filter(result => result.score > 0); // Only return rules with some relevance
  }

  /**
   * Gets rules by type (utility method)
   * @param type - Rule type to filter by
   * @param agentSlug - Optional agent slug to filter by
   * @returns Promise resolving to rules of the specified type
   */
  public async getRulesByType(type: RuleType, agentSlug?: string): Promise<N8nRule[]> {
    try {
      let allRulesConsidered: N8nRule[];
      
      if (agentSlug && agentSlug !== '*') {
        // Get rules applicable to specific agent
        allRulesConsidered = await this.gatherApplicableRules(agentSlug, true, true);
      } else {
        // No agent specified, or wildcard means consider ALL rules of the type
        const customRules = await this.customRulesRepo.getAllRules({ isActive: true });
        allRulesConsidered = [...this.baseRules.filter(r => r.isActive), ...customRules];
      }

      // Filter by rule type
      const filteredRules = allRulesConsidered.filter(rule => rule.type === type);

      return filteredRules;
    } catch (error) {
      console.error(`RuleSelectorService: Failed to get rules by type ${type}:`, error);
      throw new Error(`Failed to get rules by type: ${error}`);
    }
  }

  /**
   * Gets count of available rules for an agent
   * @param agentSlug - Agent slug to count rules for
   * @returns Promise resolving to rule counts by type
   */
  public async getRuleCountsForAgent(agentSlug: string): Promise<{
    alwaysApply: number;
    contextual: number;
    antiPattern: number;
    total: number;
  }> {
    try {
      const allRules = await this.gatherApplicableRules(agentSlug, true, true);

      const counts = {
        alwaysApply: allRules.filter(r => r.type === 'ALWAYS_APPLY').length,
        contextual: allRules.filter(r => r.type === 'CONTEXTUAL_SUGGESTION').length,
        antiPattern: allRules.filter(r => r.type === 'ANTI_PATTERN_CHECK').length,
        total: allRules.length
      };

      return counts;
    } catch (error) {
      console.error(`RuleSelectorService: Failed to get rule counts for agent ${agentSlug}:`, error);
      throw new Error(`Failed to get rule counts: ${error}`);
    }
  }
} 