/**
 * RuleSelectorService Unit Tests
 * 
 * Comprehensive test suite for the RuleSelectorService class, covering:
 * - Constructor and dependency injection
 * - Rule selection logic for agent prompts
 * - Keyword extraction and matching (V1 implementation)
 * - Rule scoring and prioritization
 * - Filtering and options handling
 * - Base rule and custom rule integration
 * - Edge cases and error handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RuleSelectorService } from '../RuleSelectorService';
import { CustomRulesRepository } from '../../persistence/repositories/CustomRulesRepository';
import { N8nRule, RuleType } from '../../types/rules.types';
import {
  RuleSelectionContext,
  SelectedRules,
  RuleSelectionOptions,
  KeywordExtractionResult,
  RuleScoringResult
} from '../types/ruleSelectorService.types';
import {
  createMockCustomRulesRepository,
  createTestRuleSelectionContext,
  createTestBaseRules,
  createTestCustomRules,
  createTestKeywordExtractionResult,
  createTestRuleScoringResults,
  setupMockRepositoryResponses,
  assertRuleSelectionResults,
  TestTaskDescriptions,
  TestAgentSlugs,
} from './ruleSelectorTestUtils';

// Mock the base rules loading
const mockLoadBaseN8nRules = vi.hoisted(() => vi.fn());
vi.mock('../../config/baseN8nRules', () => ({
  loadBaseN8nRules: mockLoadBaseN8nRules,
}));

describe('RuleSelectorService', () => {
  let service: RuleSelectorService;
  let mockRepository: ReturnType<typeof createMockCustomRulesRepository>;
  let baseRules: N8nRule[];
  let customRules: N8nRule[];

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup base rules
    baseRules = createTestBaseRules();
    mockLoadBaseN8nRules.mockReturnValue(baseRules);

    // Setup custom rules
    customRules = createTestCustomRules('n8n-architect-agent');

    // Setup mock repository
    mockRepository = createMockCustomRulesRepository();
    setupMockRepositoryResponses(mockRepository, 'n8n-architect-agent');

    // Create service instance
    service = new RuleSelectorService(mockRepository as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should create instance with repository and load base rules', () => {
      expect(service).toBeInstanceOf(RuleSelectorService);
      expect(mockLoadBaseN8nRules).toHaveBeenCalled();
    });

    it('should log the number of loaded base rules', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      // Create new service to trigger constructor logging
      new RuleSelectorService(mockRepository as any);
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('RuleSelectorService: Initialized with')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('getRulesForAgentPrompt()', () => {
    const context = createTestRuleSelectionContext({
      agentSlug: 'n8n-architect-agent',
      taskDescription: TestTaskDescriptions.webhook,
    });

    it('should return relevant rules for agent prompt', async () => {
      const result = await service.getRulesForAgentPrompt(context);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      
      // All rules should apply to the specified agent
      assertRuleSelectionResults(result, {
        agentSlug: 'n8n-architect-agent',
        hasAlwaysApplyRules: true,
      });
    });

    it('should combine base rules and custom rules', async () => {
      const result = await service.getRulesForAgentPrompt(context);

      // Should contain both base and custom rule IDs
      const ruleIds = result.map(rule => rule.id);
      const hasBaseRules = ruleIds.some(id => id.startsWith('base-rule-'));
      const hasCustomRules = ruleIds.some(id => id.startsWith('custom-rule-'));

      expect(hasBaseRules).toBe(true);
      expect(hasCustomRules).toBe(true);
    });

    it('should remove duplicate rules by ID', async () => {
      // Add duplicate rule to custom rules
      const duplicateRule = { ...baseRules[0], source: 'user_defined' as const };
      mockRepository.getActiveRulesForAgent.mockResolvedValue([...customRules, duplicateRule]);

      const result = await service.getRulesForAgentPrompt(context);

      // Check that each rule ID appears only once
      const ruleIds = result.map(rule => rule.id);
      const uniqueIds = new Set(ruleIds);
      expect(ruleIds.length).toBe(uniqueIds.size);
    });

    it('should handle empty task description gracefully', async () => {
      const contextWithoutDescription = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: undefined,
      });

      const result = await service.getRulesForAgentPrompt(contextWithoutDescription);

      expect(Array.isArray(result)).toBe(true);
      // Should still return always-apply rules
      assertRuleSelectionResults(result, {
        hasAlwaysApplyRules: true,
      });
    });

    it('should handle repository errors gracefully', async () => {
      mockRepository.getActiveRulesForAgent.mockRejectedValue(new Error('Repository error'));

      await expect(service.getRulesForAgentPrompt(context)).rejects.toThrow(
        'Failed to select rules for agent prompt'
      );
    });

    it('should log rule selection results', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await service.getRulesForAgentPrompt(context);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('RuleSelectorService: Selected')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('selectRules()', () => {
    const context = createTestRuleSelectionContext({
      agentSlug: 'n8n-architect-agent',
      taskDescription: TestTaskDescriptions.webhook,
    });

    it('should return detailed rule selection breakdown', async () => {
      const result = await service.selectRules(context);

      expect(result).toHaveProperty('alwaysApplyRules');
      expect(result).toHaveProperty('contextualRules');
      expect(result).toHaveProperty('totalRulesConsidered');
      expect(result).toHaveProperty('extractedKeywords');

      expect(Array.isArray(result.alwaysApplyRules)).toBe(true);
      expect(Array.isArray(result.contextualRules)).toBe(true);
      expect(typeof result.totalRulesConsidered).toBe('number');
    });

    it('should separate always-apply and contextual rules', async () => {
      const result = await service.selectRules(context);

      result.alwaysApplyRules.forEach(rule => {
        expect(rule.type).toBe('ALWAYS_APPLY');
      });

      result.contextualRules.forEach(rule => {
        expect(rule.type).toBe('CONTEXTUAL_SUGGESTION');
      });
    });

    it('should limit contextual rules to default maximum', async () => {
      const result = await service.selectRules(context);

      expect(result.contextualRules.length).toBeLessThanOrEqual(5); // Default max
    });

    it('should respect custom max contextual rules limit', async () => {
      const options: RuleSelectionOptions = {
        maxContextualRules: 2,
      };

      const result = await service.selectRules(context, options);

      expect(result.contextualRules.length).toBeLessThanOrEqual(2);
    });

    it('should include extracted keywords when task description provided', async () => {
      const result = await service.selectRules(context);

      expect(result.extractedKeywords).toBeDefined();
      expect(Array.isArray(result.extractedKeywords)).toBe(true);
      expect(result.extractedKeywords!.length).toBeGreaterThan(0);
    });

    it('should not include contextual rules without task description', async () => {
      const contextWithoutDescription = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: undefined,
      });

      const result = await service.selectRules(contextWithoutDescription);

      expect(result.contextualRules).toHaveLength(0);
      expect(result.extractedKeywords).toEqual([]);
    });

    it('should filter by category when specified', async () => {
      const options: RuleSelectionOptions = {
        categoryFilter: {
          include: ['error-handling'],
        },
      };

      const result = await service.selectRules(context, options);

      result.alwaysApplyRules.concat(result.contextualRules).forEach(rule => {
        if (rule.category) {
          expect(rule.category).toBe('error-handling');
        }
      });
    });

    it('should exclude categories when specified', async () => {
      const options: RuleSelectionOptions = {
        categoryFilter: {
          exclude: ['performance'],
        },
      };

      const result = await service.selectRules(context, options);

      result.alwaysApplyRules.concat(result.contextualRules).forEach(rule => {
        expect(rule.category).not.toBe('performance');
      });
    });

    it('should filter by minimum priority', async () => {
      const options: RuleSelectionOptions = {
        minPriority: 'high',
      };

      const result = await service.selectRules(context, options);

      result.alwaysApplyRules.concat(result.contextualRules).forEach(rule => {
        expect(['critical', 'high']).toContain(rule.priority);
      });
    });

    it('should disable base rules when requested', async () => {
      const options: RuleSelectionOptions = {
        includeBaseRules: false,
      };

      const result = await service.selectRules(context, options);

      const allRules = result.alwaysApplyRules.concat(result.contextualRules);
      allRules.forEach(rule => {
        expect(rule.source).not.toBe('builtin');
      });
    });

    it('should disable custom rules when requested', async () => {
      const options: RuleSelectionOptions = {
        includeCustomRules: false,
      };

      const result = await service.selectRules(context, options);

      const allRules = result.alwaysApplyRules.concat(result.contextualRules);
      allRules.forEach(rule => {
        expect(rule.source).not.toBe('user_defined');
      });
    });
  });

  describe('Keyword Extraction', () => {
    it('should extract relevant keywords from task description', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'Create a webhook workflow with error handling and API integration',
      });

      const result = await service.selectRules(context);

      expect(result.extractedKeywords).toContain('webhook');
      expect(result.extractedKeywords).toContain('error');
      expect(result.extractedKeywords).toContain('api');
    });

    it('should handle text with punctuation and special characters', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'Build a robust, high-performance workflow! It should handle HTTP requests & responses.',
      });

      const result = await service.selectRules(context);

      expect(result.extractedKeywords).toBeDefined();
      expect(result.extractedKeywords!.length).toBeGreaterThan(0);
    });

    it('should filter out stop words', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'This is a workflow that should be created with the best practices',
      });

      const result = await service.selectRules(context);

      // Common stop words should not be in extracted keywords
      expect(result.extractedKeywords).not.toContain('this');
      expect(result.extractedKeywords).not.toContain('that');
      expect(result.extractedKeywords).not.toContain('with');
    });

    it('should limit number of extracted keywords', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'workflow automation integration performance optimization security validation error handling data transformation api webhook trigger response status monitoring logging debug trace analysis metrics',
      });

      const result = await service.selectRules(context);

      expect(result.extractedKeywords!.length).toBeLessThanOrEqual(10); // Default limit
    });

    it('should handle empty or very short text', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'a',
      });

      const result = await service.selectRules(context);

      expect(result.extractedKeywords).toBeDefined();
      expect(Array.isArray(result.extractedKeywords)).toBe(true);
    });
  });

  describe('Rule Scoring and Selection', () => {
    it('should score rules based on keyword matches', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook api error handling performance',
      });

      const result = await service.selectRules(context);

      // Rules with matching keywords should be selected
      expect(result.contextualRules.length).toBeGreaterThan(0);
    });

    it('should prioritize rules with higher scores', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook performance optimization batch requests',
      });

      const result = await service.selectRules(context);

      if (result.contextualRules.length > 1) {
        // Check that rules are generally ordered by relevance
        // (exact order depends on scoring algorithm)
        expect(result.contextualRules[0]).toBeDefined();
      }
    });

    it('should handle rules without trigger context', async () => {
      // Create a rule without trigger context
      const ruleWithoutContext: N8nRule = {
        ...baseRules[0],
        id: 'rule-without-context',
        triggerContext: undefined,
      };

      mockLoadBaseN8nRules.mockReturnValue([...baseRules, ruleWithoutContext]);
      
      // Create new service to pick up updated base rules
      service = new RuleSelectorService(mockRepository as any);

      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook api integration',
      });

      const result = await service.selectRules(context);

      // Should handle gracefully without errors
      expect(result).toBeDefined();
    });

    it('should boost scoring for node type matches', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook workflow',
        nodeTypes: ['Webhook', 'HTTP Request'],
      });

      const result = await service.selectRules(context);

      // Rules that match node types should have higher priority
      expect(result.contextualRules.length).toBeGreaterThan(0);
    });

    it('should apply category bonuses for relevant contexts', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'performance optimization and batch processing',
      });

      const result = await service.selectRules(context);

      // Performance rules should get bonus points for performance-related context
      const performanceRules = result.contextualRules.filter(rule => 
        rule.category === 'performance'
      );
      expect(performanceRules.length).toBeGreaterThan(0);
    });
  });

  describe('getRulesByType()', () => {
    it('should return rules of specific type for agent', async () => {
      const result = await service.getRulesByType('ALWAYS_APPLY', 'n8n-architect-agent');

      expect(Array.isArray(result)).toBe(true);
      result.forEach(rule => {
        expect(rule.type).toBe('ALWAYS_APPLY');
        expect(rule.appliesToAgents).toContain('n8n-architect-agent');
      });
    });

    it('should return all rules of type when no agent specified', async () => {
      const result = await service.getRulesByType('CONTEXTUAL_SUGGESTION');

      expect(Array.isArray(result)).toBe(true);
      result.forEach(rule => {
        expect(rule.type).toBe('CONTEXTUAL_SUGGESTION');
      });
    });

    it('should handle repository errors', async () => {
      mockRepository.getActiveRulesForAgent.mockRejectedValue(new Error('Repository error'));

      await expect(service.getRulesByType('ALWAYS_APPLY', 'test-agent')).rejects.toThrow(
        'Failed to get rules by type'
      );
    });
  });

  describe('getRuleCountsForAgent()', () => {
    it('should return counts by rule type', async () => {
      const result = await service.getRuleCountsForAgent('n8n-architect-agent');

      expect(result).toHaveProperty('alwaysApply');
      expect(result).toHaveProperty('contextual');
      expect(result).toHaveProperty('antiPattern');
      expect(result).toHaveProperty('total');

      expect(typeof result.alwaysApply).toBe('number');
      expect(typeof result.contextual).toBe('number');
      expect(typeof result.antiPattern).toBe('number');
      expect(typeof result.total).toBe('number');

      expect(result.total).toBe(
        result.alwaysApply + result.contextual + result.antiPattern
      );
    });

    it('should handle agent with no rules', async () => {
      const result = await service.getRuleCountsForAgent('unknown-agent');

      expect(result.total).toBeGreaterThanOrEqual(0);
    });

    it('should handle repository errors', async () => {
      mockRepository.getActiveRulesForAgent.mockRejectedValue(new Error('Repository error'));

      await expect(service.getRuleCountsForAgent('test-agent')).rejects.toThrow(
        'Failed to get rule counts'
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle agent with no applicable rules', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'non-existent-agent',
        taskDescription: 'some task',
      });

      mockRepository.getActiveRulesForAgent.mockResolvedValue([]);

      const result = await service.getRulesForAgentPrompt(context);

      expect(Array.isArray(result)).toBe(true);
      // May still have some base rules if they apply to all agents
    });

    it('should handle very long task descriptions', async () => {
      const longDescription = 'workflow '.repeat(1000) + 'automation performance optimization';
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: longDescription,
      });

      const result = await service.selectRules(context);

      expect(result).toBeDefined();
      expect(result.extractedKeywords).toBeDefined();
    });

    it('should handle special characters in task description', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'workflow with émojis 🚀 and spëcial chars ñ Ω',
      });

      const result = await service.selectRules(context);

      expect(result).toBeDefined();
      expect(result.extractedKeywords).toBeDefined();
    });

    it('should handle concurrent rule selection requests', async () => {
      const context1 = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook workflow',
      });

      const context2 = createTestRuleSelectionContext({
        agentSlug: 'n8n-node-composer-agent',
        taskDescription: 'api integration',
      });

      const [result1, result2] = await Promise.all([
        service.getRulesForAgentPrompt(context1),
        service.getRulesForAgentPrompt(context2),
      ]);

      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(Array.isArray(result1)).toBe(true);
      expect(Array.isArray(result2)).toBe(true);
    });

    it('should maintain performance with large rule sets', async () => {
      // Create a large number of rules
      const largeRuleSet = Array.from({ length: 100 }, (_, index) => ({
        ...baseRules[0],
        id: `large-rule-${index}`,
        name: `Large Rule ${index}`,
      }));

      mockLoadBaseN8nRules.mockReturnValue(largeRuleSet);
      service = new RuleSelectorService(mockRepository as any);

      const context = createTestRuleSelectionContext({
        agentSlug: 'n8n-architect-agent',
        taskDescription: 'webhook api performance optimization',
      });

      const startTime = Date.now();
      const result = await service.getRulesForAgentPrompt(context);
      const endTime = Date.now();

      expect(result).toBeDefined();
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Integration with Different Agent Types', () => {
    it('should work with architect agent', async () => {
      const context = createTestRuleSelectionContext({
        agentSlug: TestAgentSlugs.architect,
        taskDescription: TestTaskDescriptions.complex,
      });

      const result = await service.getRulesForAgentPrompt(context);

      assertRuleSelectionResults(result, {
        agentSlug: TestAgentSlugs.architect,
        hasAlwaysApplyRules: true,
      });
    });

    it('should work with node composer agent', async () => {
      mockRepository.getActiveRulesForAgent.mockResolvedValue(
        createTestCustomRules(TestAgentSlugs.nodeComposer)
      );

      const context = createTestRuleSelectionContext({
        agentSlug: TestAgentSlugs.nodeComposer,
        taskDescription: TestTaskDescriptions.apiIntegration,
      });

      const result = await service.getRulesForAgentPrompt(context);

      assertRuleSelectionResults(result, {
        agentSlug: TestAgentSlugs.nodeComposer,
      });
    });

    it('should work with validator agent', async () => {
      mockRepository.getActiveRulesForAgent.mockResolvedValue(
        createTestCustomRules(TestAgentSlugs.validator)
      );

      const context = createTestRuleSelectionContext({
        agentSlug: TestAgentSlugs.validator,
        taskDescription: TestTaskDescriptions.errorHandling,
      });

      const result = await service.getRulesForAgentPrompt(context);

      assertRuleSelectionResults(result, {
        agentSlug: TestAgentSlugs.validator,
      });
    });
  });
}); 