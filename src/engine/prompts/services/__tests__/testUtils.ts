/**
 * Test Utilities for LLMService and other Engine Services
 * 
 * Common utilities, mocks, and helpers for testing engine services.
 */

import { vi, expect } from 'vitest';
import type { 
  UserSettingsService,
  ChromeAIService,
  ModelPricingConfig,
  UserModelPreference
} from '../../llm/types/llmService.types';
import { LLMTaskCategory } from '../../graph/types/graph.types';

/**
 * Create a mock UserSettingsService with configurable overrides
 */
export function createMockUserSettingsService(
  overrides: Partial<UserSettingsService> = {}
): UserSettingsService {
  const defaults: UserSettingsService = {
    isFeatureEnabled: vi.fn().mockResolvedValue(false),
    getModelPreference: vi.fn().mockResolvedValue(null),
    getApiKeyForProvider: vi.fn().mockResolvedValue('test-api-key'),
    getSetting: vi.fn().mockResolvedValue(3)
  };
  
  return { ...defaults, ...overrides };
}

/**
 * Create a mock ChromeAIService with configurable overrides
 */
export function createMockChromeAIService(
  overrides: Partial<ChromeAIService> = {}
): ChromeAIService {
  const defaults: ChromeAIService = {
    isAvailable: vi.fn().mockResolvedValue(false),
    generateText: vi.fn().mockResolvedValue('Chrome AI response'),
    streamText: vi.fn()
  };
  
  return { ...defaults, ...overrides };
}

/**
 * Create a realistic mock pricing configuration
 */
export function createMockPricingConfig(): ModelPricingConfig {
  return {
    openai: {
      'gpt-4o': {
        inputCostPerToken: 0.000005,
        outputCostPerToken: 0.000015,
        capabilities: {
          maxTokens: 4096,
          contextWindow: 128000,
          supportsImages: true,
          supportsPromptCache: false
        }
      },
      'gpt-4o-mini': {
        inputCostPerToken: 0.00000015,
        outputCostPerToken: 0.0000006,
        capabilities: {
          maxTokens: 16384,
          contextWindow: 128000,
          supportsImages: true,
          supportsPromptCache: false
        }
      }
    },
    anthropic: {
      'claude-3-5-sonnet-20241022': {
        inputCostPerToken: 0.000003,
        outputCostPerToken: 0.000015,
        capabilities: {
          maxTokens: 8192,
          contextWindow: 200000,
          supportsImages: true,
          supportsComputerUse: true,
          supportsPromptCache: true
        }
      }
    },
    google: {
      'gemini-1.5-pro': {
        inputCostPerToken: 0.00000125,
        outputCostPerToken: 0.000005,
        capabilities: {
          maxTokens: 8192,
          contextWindow: 2000000,
          supportsImages: true,
          supportsPromptCache: false
        }
      }
    }
  };
}

/**
 * Create mock model preferences for different scenarios
 */
export const mockModelPreferences = {
  openAI: {
    provider: 'openai',
    modelId: 'gpt-4o',
    temperature: 0.7
  } as UserModelPreference,
  
  anthropic: {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-20241022',
    temperature: 0.5
  } as UserModelPreference,
  
  google: {
    provider: 'google',
    modelId: 'gemini-1.5-pro',
    temperature: 0.3
  } as UserModelPreference
};

/**
 * Utility to collect all parts from a ReadableStream
 */
export async function collectStreamParts(stream: ReadableStream): Promise<any[]> {
  const reader = stream.getReader();
  const parts: any[] = [];
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      parts.push(value);
    }
  } finally {
    reader.releaseLock();
  }
  
  return parts;
}

/**
 * Create mock responses for different scenarios
 */
export const mockResponses = {
  successfulText: {
    text: 'This is a successful response',
    usage: { inputTokens: 10, outputTokens: 20 },
    finishReason: 'stop'
  },
  
  withToolCalls: {
    text: '',
    toolCalls: [
      {
        toolCallId: 'call_123',
        toolName: 'test_tool',
        args: { param: 'value' }
      }
    ],
    usage: { inputTokens: 15, outputTokens: 25 },
    finishReason: 'tool_calls'
  },
  
  rateLimitError: new Error('rate limit exceeded'),
  serverError: new Error('server error 500'),
  authError: new Error('invalid api key'),
  abortError: new Error('Request aborted')
};

/**
 * Setup common mocks for external dependencies
 */
export function setupExternalMocks() {
  // Mock gpt-tokenizer
  vi.mock('gpt-tokenizer', () => ({
    encode: vi.fn().mockReturnValue([1, 2, 3, 4]), // 4 tokens
    encodeChat: vi.fn().mockReturnValue([1, 2, 3, 4, 5]) // 5 tokens
  }));

  // Mock Vercel AI SDK
  vi.mock('ai', () => ({
    generateText: vi.fn(),
    streamText: vi.fn(),
    generateObject: vi.fn(),
    streamObject: vi.fn(),
    generateId: vi.fn(() => 'test-id-123'),
    createUIMessageStream: vi.fn()
  }));

  // Mock provider SDKs
  vi.mock('@ai-sdk/openai', () => ({
    createOpenAI: vi.fn()
  }));

  vi.mock('@ai-sdk/anthropic', () => ({
    createAnthropic: vi.fn()
  }));

  vi.mock('@ai-sdk/google', () => ({
    createGoogleGenerativeAI: vi.fn()
  }));
}

/**
 * Test scenarios for different LLM task categories
 */
export const testScenarios = {
  chromeAISuitable: [
    LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
    LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION
  ],
  
  cloudLLMRequired: [
    LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
    LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION,
    LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION,
    LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
  ]
};

/**
 * Assert that a result matches expected LLM output structure
 */
export function assertValidLLMResult(result: any, expectedStatus: string = 'success') {
  expect(result).toBeDefined();
  expect(result.status).toBe(expectedStatus);
  expect(result.details).toBeDefined();
  expect(result.details.modelProvider).toBeDefined();
  expect(result.details.modelName).toBeDefined();
  expect(result.details.tokenUsage).toBeDefined();
  expect(typeof result.details.tokenUsage.promptTokens).toBe('number');
  expect(typeof result.details.tokenUsage.completionTokens).toBe('number');
  expect(typeof result.details.tokenUsage.totalTokens).toBe('number');
  
  if (expectedStatus === 'success') {
    expect(result.content).toBeDefined();
  } else {
    expect(result.error).toBeDefined();
    expect(result.error.message).toBeDefined();
    expect(result.error.type).toBeDefined();
  }
} 