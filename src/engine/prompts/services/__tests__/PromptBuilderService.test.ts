/**
 * PromptBuilderService Unit Tests
 * 
 * These tests validate the PromptBuilderService functionality including:
 * - Standard section injection (Team Awareness, Input Context, Output Format)
 * - Dynamic data formatting
 * - Rule and knowledge integration
 * - Context window management and summarization
 * - Error handling
 * - Template system integration
 * - Tokenizer injection
 */

import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest';
import { ModelMessage } from 'ai';
import { z } from 'zod';

import { PromptBuilderService } from '../PromptBuilderService';
import { LLMTaskCategory } from '../../graph/types/graph.types';
import {
  AgentCallSpecificData,
  PromptBuilderOptions,
  AgentConfigRegistry,
  TaskSession,
  N8nRule,
  KnowledgeChunkWithScore,
  RuleSelectorService,
  KnowledgeRepository,
  TaskSessionManager,
  RuleSelectionContext,
  AgentConfigNotFoundError,
  SummarizationFailedError,
  DEFAULT_CONTEXT_WINDOW_CONFIG,
  TokenCountFunction,
  ChatTokenCountFunction
} from '../types/promptBuilder.types';
import { LLMService } from '../LLMService';
// TODO: Remove when replacing with AgentPromptBuilder
// import { TemplateRegistry } from '../../../prompts/TemplateRegistry';

// Mock template registry
const mockTemplateRegistry = {
  loadCommonTemplate: vi.fn(),
  fillTemplate: vi.fn(),
  loadAgentTemplate: vi.fn(),
  validateTemplate: vi.fn(),
  getTemplatePlaceholders: vi.fn(),
  clearCache: vi.fn()
} as any;

// Mock tokenizer functions
const mockCountTokens = vi.fn().mockReturnValue(100);
const mockCountChatTokens = vi.fn().mockReturnValue(150);

// Mock base templates
const mockBaseTemplates = {
  TEAM_AWARENESS: 'You are {{agentRole}} working on {{graphDescription}}{{otherAgents}}',
  INPUT_CONTEXT_DEFINITION: 'Available data: {{inputContext}}',
  OUTPUT_FORMAT_INSTRUCTION: 'Output schema: {{jsonSchema}}',
  SYSTEM_PROMPT_BASE: '{{agentRole}}\n{{teamAwareness}}\n{{inputContextDefinition}}\n{{outputFormatInstruction}}'
};

describe.skip('PromptBuilderService (DEPRECATED - will be replaced by AgentPromptBuilder)', () => {
  let promptBuilderService: PromptBuilderService;
  let mockLlmService: any;
  let mockRuleSelectorService: any;
  let mockKnowledgeRepository: any;
  let mockTaskSessionManager: any;
  let mockAgentConfigs: AgentConfigRegistry;
  let mockTaskSession: TaskSession;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Reset tokenizer mock return values
    mockCountTokens.mockReturnValue(100);
    mockCountChatTokens.mockReturnValue(150);

    // Mock LLMService
    mockLlmService = {
      invokeModel: vi.fn().mockResolvedValue({
        status: 'success',
        content: 'Default mock response',
        details: {
          modelProvider: 'openai',
          modelName: 'gpt-4o-mini',
          tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 }
        }
      }),
      getModelCapabilitiesForCategory: vi.fn().mockResolvedValue({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        contextWindow: 128000,
        pricing: {}
      })
    } as any;

    // Mock RuleSelectorService
    mockRuleSelectorService = {
      getRulesForAgentPrompt: vi.fn()
    } as any;

    // Mock KnowledgeRepository
    mockKnowledgeRepository = {
      retrieveSnippets: vi.fn(),
      vectorSearch: vi.fn()
    } as any;

    // Mock TaskSessionManager
    mockTaskSessionManager = {
      getTaskSession: vi.fn(),
      updateTaskSession: vi.fn(),
      createTaskSession: vi.fn()
    } as any;

    // Mock agent configurations
    mockAgentConfigs = {
      'n8n-architect-agent': {
        slug: 'n8n-architect-agent',
        description: 'Designs high-level workflow plans',
        defaultLlmTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        roleDescription: 'Workflow Architecture Agent',
        outputSchema: undefined
      },
      'n8n-node-composer-agent': {
        slug: 'n8n-node-composer-agent',
        description: 'Generates JSON for n8n nodes',
        defaultLlmTaskCategory: LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION,
        roleDescription: 'Node Composition Agent',
        outputSchema: undefined
      }
    };

    // Mock task session
    mockTaskSession = {
      id: 'test-session-123',
      llmContextSummary: 'Previous conversation about creating a workflow',
      llmContextRelevantHistory: [
        { role: 'user', content: 'I want to create an email notification workflow' },
        { role: 'assistant', content: 'I can help you design that workflow. Let me gather requirements.' }
      ],
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Mock template registry
    mockTemplateRegistry.loadCommonTemplate.mockImplementation((key: string) => {
      return mockBaseTemplates[key as keyof typeof mockBaseTemplates] || `Mock template for ${key}`;
    });

    mockTemplateRegistry.loadAgentTemplate.mockImplementation(async (agentSlug: string, templateKey: string) => {
      // For now, always return the SYSTEM_PROMPT_BASE for 'system' template key as fallback
      if (templateKey === 'system') {
        return mockBaseTemplates.SYSTEM_PROMPT_BASE;
      }
      return `Mock ${templateKey} template for ${agentSlug}`;
    });

    mockTemplateRegistry.fillTemplate.mockImplementation((template: string, data: Record<string, string>) => {
      let result = template;
      for (const [key, value] of Object.entries(data)) {
        result = result.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
      }
      return result;
    });

    // Create service instance with mocked dependencies
    promptBuilderService = new PromptBuilderService(
      mockLlmService,
      mockRuleSelectorService,
      mockKnowledgeRepository,
      mockTaskSessionManager,
      mockAgentConfigs,
      {
        contextWindowConfig: DEFAULT_CONTEXT_WINDOW_CONFIG,
        templateRegistry: mockTemplateRegistry,
        countTokens: mockCountTokens,
        countChatTokens: mockCountChatTokens
      }
    );
  });

  describe('buildPromptMessagesForAgentCall', () => {
    it('should build a complete prompt with all standard sections', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const graphDefinition = {
        id: 'test-graph',
        description: 'Email notification workflow',
        version: '1.0.0',
        entryNodeId: 'start',
        nodes: [
          { id: 'start', type: 'start' },
          { id: 'architect', type: 'agentCall', agentSlug: 'n8n-architect-agent' },
          { id: 'composer', type: 'agentCall', agentSlug: 'n8n-node-composer-agent' }
        ],
        edges: [],
        expectedInitialInputs: {},
        graphConstants: {}
      } as any;
      
      const specificData: AgentCallSpecificData = {
        userRequest: 'Create a workflow that sends email notifications when a webhook is triggered',
        _CORE_AGENT_TASK_DESCRIPTION: 'Design the architecture for this workflow'
      };

      // Mock service responses
      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([
        {
          id: 'rule-1',
          name: 'Use descriptive node names',
          descriptionForLLM: 'Always use clear, descriptive names for workflow nodes',
          type: 'ALWAYS_APPLY',
          appliesToAgents: ['n8n-architect-agent']
        }
      ]);

      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([
        {
          id: 'kb-1',
          sourceType: 'documentation',
          chunkText: 'n8n workflows start with a trigger node and can have multiple action nodes',
          score: 0.95
        }
      ]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        graphDefinition,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.messages).toHaveLength(3); // System + History (Summary + Assistant merged) + User (History user + Task merged)
      expect(result.promptTokenCount).toBe(150); // From mock
      expect(result.contextWasSummarized).toBe(false);

      // Verify LLM service was called for model capabilities
      expect(mockLlmService.getModelCapabilitiesForCategory).toHaveBeenCalledWith(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      );

      // Check system message contains expected content
      const systemMessage = result.messages[0];
      expect(systemMessage.role).toBe('system');
      expect(systemMessage.content).toContain('Workflow Architecture Agent');

      // Verify template registry was used
      expect(mockTemplateRegistry.loadCommonTemplate).toHaveBeenCalledWith('TEAM_AWARENESS');
      expect(mockTemplateRegistry.loadCommonTemplate).toHaveBeenCalledWith('INPUT_CONTEXT_DEFINITION');
      expect(mockTemplateRegistry.loadCommonTemplate).toHaveBeenCalledWith('OUTPUT_FORMAT_INSTRUCTION');
      expect(mockTemplateRegistry.loadAgentTemplate).toHaveBeenCalledWith('n8n-architect-agent', 'system');

      // Verify rule service was called with proper context
      expect(mockRuleSelectorService.getRulesForAgentPrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          agentSlug: 'n8n-architect-agent',
          taskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
          contextData: specificData,
          userRequest: 'Create a workflow that sends email notifications when a webhook is triggered'
        })
      );

      // Check user message contains core task
      const userMessage = result.messages[result.messages.length - 1];
      expect(userMessage.role).toBe('user');
      expect(userMessage.content).toBe('Design the architecture for this workflow');

      // Verify tokenizer functions were used
      expect(mockCountChatTokens).toHaveBeenCalled();
    });

    it('should handle agent without graph definition', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = {
        task: 'Analyze workflow requirements'
      };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(result.messages).toBeDefined();
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          graphDescription: 'You are working independently on this task.'
        })
      );
    });

    it('should throw error for unknown agent', async () => {
      // Arrange
      const unknownAgentSlug = 'unknown-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };

      // Act & Assert
      await expect(
        promptBuilderService.buildPromptMessagesForAgentCall(
          unknownAgentSlug,
          null,
          mockTaskSession,
          specificData
        )
      ).rejects.toThrow(AgentConfigNotFoundError);
    });

    it('should handle empty specific data', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = {};

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          inputContext: 'No specific input data provided for this call.'
        })
      );
      
      const userMessage = result.messages[result.messages.length - 1];
      expect(userMessage.content).toBe('Please process the provided context data and complete your assigned task.');
    });

    it('should use override JSON schema when provided', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };
      const overrideSchema = JSON.stringify({
        type: 'object',
        properties: { customField: { type: 'string' } }
      });

      const options: PromptBuilderOptions = {
        overrideOutputJsonSchemaString: overrideSchema
      };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData,
        options
      );

      // Assert
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          jsonSchema: overrideSchema
        })
      );
    });

    it('should correctly convert Zod schemas to JSON schemas', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };

      // Define a real Zod schema for the agent
      const testOutputSchema = z.object({
        status: z.enum(['success', 'failure']),
        workflowPlan: z.object({
          steps: z.array(z.string()),
          estimatedComplexity: z.number()
        }),
        recommendations: z.array(z.string()).optional()
      });

      // Update the agent config to include this schema
      const updatedAgentConfigs = {
        ...mockAgentConfigs,
        'n8n-architect-agent': {
          ...mockAgentConfigs['n8n-architect-agent'],
          outputSchema: testOutputSchema
        }
      };

      const testPromptBuilderService = new PromptBuilderService(
        mockLlmService,
        mockRuleSelectorService,
        mockKnowledgeRepository,
        mockTaskSessionManager,
        updatedAgentConfigs,
        {
          contextWindowConfig: DEFAULT_CONTEXT_WINDOW_CONFIG,
          templateRegistry: mockTemplateRegistry,
          countTokens: mockCountTokens,
          countChatTokens: mockCountChatTokens
        }
      );

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await testPromptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          jsonSchema: expect.stringContaining('status')
        })
      );
    });

    it('should skip knowledge when disabled in options', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { userRequest: 'test' };
      const options: PromptBuilderOptions = {
        includeKnowledgeSnippets: false
      };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);

      // Act
      await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData,
        options
      );

      // Assert
      expect(mockKnowledgeRepository.retrieveSnippets).not.toHaveBeenCalled();
    });
  });

  describe('context window management', () => {
    it('should trigger summarization when context exceeds threshold', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = {
        userRequest: 'Create a complex workflow with multiple integration points'
      };

      // Mock long history to trigger summarization
      const longHistory: ModelMessage[] = Array(10).fill(null).map((_, i) => ({
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `Previous conversation message ${i} with some detailed content about n8n workflows`
      }));

      const longTaskSession: TaskSession = {
        ...mockTaskSession,
        llmContextSummary: 'Previous conversation summary with lots of context',
        llmContextRelevantHistory: longHistory
      };

      // Mock high token counts to exceed threshold
      (mockCountTokens as any).mockReturnValue(50000); // High token count
      (mockCountChatTokens as any).mockReturnValue(60000); // Even higher for chat

      // Mock model capabilities with low context window to force summarization
      mockLlmService.getModelCapabilitiesForCategory.mockResolvedValue({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        contextWindow: 10000, // Low context window
        pricing: {}
      });

      // Mock summarization response with specific content for verification
      const newSummaryContent = 'Summarized conversation history about workflow creation and integration points';
      mockLlmService.invokeModel.mockResolvedValue({
        status: 'success',
        content: newSummaryContent,
        details: {
          modelProvider: 'openai',
          modelName: 'gpt-4o-mini',
          tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 }
        }
      });

      mockTaskSessionManager.updateTaskSession.mockResolvedValue(longTaskSession);
      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        longTaskSession,
        specificData
      );

      // Assert basic summarization flags
      expect(result.contextWasSummarized).toBe(true);
      expect(mockLlmService.invokeModel).toHaveBeenCalledWith(
        LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
        expect.any(Array),
        expect.objectContaining({
          temperature: 0.1,
          maxOutputTokens: DEFAULT_CONTEXT_WINDOW_CONFIG.maxSummaryTokens
        })
      );
      expect(mockTaskSessionManager.updateTaskSession).toHaveBeenCalled();

      // Enhanced assertions: Verify final prompt messages contain the new summary
      expect(result.messages).toBeDefined();
      expect(result.messages.length).toBeGreaterThan(2); // At least system + summary + user message

      // Find the summary message in the final prompt
      const summaryMessage = result.messages.find(msg => 
        msg.role === 'assistant' && 
        typeof msg.content === 'string' && 
        msg.content.includes('Previous Conversation Summary')
      );
      expect(summaryMessage).toBeDefined();
      expect(summaryMessage!.content).toContain(newSummaryContent);

      // Verify that TaskSessionManager.updateTaskSession was called with correct data
      const updateTaskSessionCall = mockTaskSessionManager.updateTaskSession.mock.calls[0];
      expect(updateTaskSessionCall).toBeDefined();
      const updatedSession = updateTaskSessionCall[0];
      
      // Verify the session was updated with new summary
      expect(updatedSession.llmContextSummary).toBe(newSummaryContent);
      
      // Verify that history was correctly trimmed based on maxHistoryMessagesAfterSummarization and token limits
      expect(updatedSession.llmContextRelevantHistory).toBeDefined();
      expect(updatedSession.llmContextRelevantHistory.length).toBeLessThanOrEqual(
        DEFAULT_CONTEXT_WINDOW_CONFIG.maxHistoryMessagesAfterSummarization
      );
      
      // The kept messages should be the most recent ones from the original history
      // Since we mocked 60000 tokens for chat messages, and the algorithm should keep only messages that fit
      if (updatedSession.llmContextRelevantHistory.length > 0) {
        // Verify the kept messages are from the end of the original history (most recent)
        const lastKeptMessage = updatedSession.llmContextRelevantHistory[updatedSession.llmContextRelevantHistory.length - 1];
        const lastOriginalMessage = longHistory[longHistory.length - 1];
        expect(lastKeptMessage.content).toBe(lastOriginalMessage.content);
      }

      // Verify that the final prompt messages structure contains expected elements
      // The structure should be: [system, summary, ...keptHistory, currentUserMessage]
      expect(result.messages[0].role).toBe('system'); // First should be system message
      expect(result.messages[result.messages.length - 1].role).toBe('user'); // Last should be current user message
      
      // The summary should be present (we already verified this above)
      // If there are kept messages, they should be in the prompt between summary and final user message
      if (updatedSession.llmContextRelevantHistory.length > 0) {
        // Count non-system, non-final-user messages that should include summary + kept history
        const middleMessages = result.messages.slice(1, -1);
        expect(middleMessages.length).toBeGreaterThanOrEqual(1); // At least the summary
        
        // Verify that some of the kept messages appear in the middle section
        const keptMessageContents = updatedSession.llmContextRelevantHistory.map(msg => msg.content);
        const middleMessageContents = middleMessages.map(msg => msg.content);
        
        // At least some kept messages should be present in the middle messages
        const hasKeptMessagesInPrompt = keptMessageContents.some(keptContent =>
          middleMessageContents.some(middleContent => 
            typeof middleContent === 'string' && middleContent.includes(keptContent)
          )
        );
        expect(hasKeptMessagesInPrompt).toBe(true);
      } else {
        // If no kept messages, we should still have at least the summary between system and user
        const middleMessages = result.messages.slice(1, -1);
        expect(middleMessages.length).toBeGreaterThanOrEqual(1); // Just the summary
      }
    });

    it('should handle summarization failures gracefully', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = {
        userRequest: 'Create a workflow that sends emails when new customers sign up'
      };

      const longTaskSession: TaskSession = {
        ...mockTaskSession,
        llmContextSummary: 'Previous conversation about creating a workflow'.repeat(50),
        llmContextRelevantHistory: Array(6).fill(null).map((_, i) => ({
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Previous message ${i}`
        }))
      };

      // Mock high token counts and low context window to force summarization
      (mockCountTokens as any).mockReturnValue(50000);
      (mockCountChatTokens as any).mockReturnValue(60000);
      
      mockLlmService.getModelCapabilitiesForCategory.mockResolvedValue({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        contextWindow: 10000,
        pricing: {}
      });

      // Mock summarization failure
      mockLlmService.invokeModel.mockResolvedValue({
        status: 'failure',
        error: { message: 'Summarization failed', type: 'LLMError' },
        details: {
          modelProvider: 'openai',
          modelName: 'gpt-4o-mini',
          tokenUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
        }
      });

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act & Assert
      await expect(
        promptBuilderService.buildPromptMessagesForAgentCall(
          agentSlug,
          null,
          longTaskSession,
          specificData
        )
      ).rejects.toThrow(SummarizationFailedError);
    });
  });

  describe('service integration', () => {
    it('should handle rule service failures gracefully', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };

      mockRuleSelectorService.getRulesForAgentPrompt.mockRejectedValue(
        new Error('Rules service unavailable')
      );
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert - should complete successfully without rules
      expect(result.messages).toBeDefined();
    });

    it('should handle knowledge repository failures gracefully', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockRejectedValue(
        new Error('Knowledge service unavailable')
      );

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert - should complete successfully without knowledge
      expect(result.messages).toBeDefined();
    });
  });

  describe('data formatting', () => {
    it('should format complex data structures correctly', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = {
        userRequest: 'Create workflow',
        workflowConfig: {
          trigger: 'webhook',
          actions: ['email', 'slack'],
          settings: { timeout: 30000 }
        },
        tags: ['automation', 'notifications']
      };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(result.messages).toBeDefined();
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          specificDataSection: expect.stringContaining('<userrequest>')
        })
      );
    });

    it('should use injected tokenizer functions', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'Short task' };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(result.promptTokenCount).toBe(150); // From mock
      expect(mockCountChatTokens).toHaveBeenCalled();
      // Note: mockCountTokens may not be called in simple cases without summarization
    });
  });

  describe('team awareness generation', () => {
    it('should generate team awareness with multiple agents', async () => {
      // Arrange
      const agentSlug = 'n8n-architect-agent';
      const graphDefinition = {
        id: 'multi-agent-graph',
        description: 'Complex workflow with multiple agents',
        nodes: [
          { id: 'architect', type: 'agentCall', agentSlug: 'n8n-architect-agent' },
          { id: 'composer', type: 'agentCall', agentSlug: 'n8n-node-composer-agent' },
          { id: 'validator', type: 'agentCall', agentSlug: 'n8n-validator-agent' }
        ]
      } as any;

      const specificData: AgentCallSpecificData = { task: 'test' };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await promptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        graphDefinition,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          graphDescription: 'Complex workflow with multiple agents',
          otherAgents: expect.stringContaining('Node Composition Agent')
        })
      );
    });
  });

  describe('tokenizer injection', () => {
    it('should use custom tokenizer functions when provided', async () => {
      // Arrange
      const customCountTokens = vi.fn().mockReturnValue(200);
      const customCountChatTokens = vi.fn().mockReturnValue(300);

      const customPromptBuilderService = new PromptBuilderService(
        mockLlmService,
        mockRuleSelectorService,
        mockKnowledgeRepository,
        mockTaskSessionManager,
        mockAgentConfigs,
        {
          countTokens: customCountTokens,
          countChatTokens: customCountChatTokens,
          templateRegistry: mockTemplateRegistry
        }
      );

      const agentSlug = 'n8n-architect-agent';
      const specificData: AgentCallSpecificData = { task: 'test' };

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockKnowledgeRepository.retrieveSnippets.mockResolvedValue([]);

      // Act
      const result = await customPromptBuilderService.buildPromptMessagesForAgentCall(
        agentSlug,
        null,
        mockTaskSession,
        specificData
      );

      // Assert
      expect(result.promptTokenCount).toBe(300); // From custom tokenizer
      expect(customCountChatTokens).toHaveBeenCalled();
      // Note: customCountTokens may not be called in simple cases without summarization
    });
  });
}); 