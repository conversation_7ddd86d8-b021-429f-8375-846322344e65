/**
 * GenericPromptBuilder.test.ts
 * 
 * Comprehensive tests for the rewritten GenericPromptBuilder using the new micro-utilities architecture
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { z } from 'zod';
import { ModelMessage } from 'ai';

import { GenericPromptBuilder, type BuildOptions, type AssembledPrompt } from '../GenericPromptBuilder';
import type {
  PromptComponent,
  SystemInstructionComponent,
  UserMessageComponent,
  AssistantMessageComponent,
  HistoryBlockComponent,
  JsonSchemaInstructionComponent,
  TaggedDataComponent,
  TextBlockComponent,
  TokenCountFunction,
  ChatTokenCountFunction,
} from '../types/genericPromptBuilder.types';

// Mock tokenizer functions
const mockCountTokens: TokenCountFunction = vi.fn((text: string) => text.length);
const mockCountChatTokens: ChatTokenCountFunction = vi.fn((messages: ModelMessage[]) => 
  messages.reduce((total, msg) => {
    if (typeof msg.content === 'string') {
      return total + msg.content.length;
    }
    return total + JSON.stringify(msg.content).length;
  }, 0)
);

describe('GenericPromptBuilder', () => {
  let builder: GenericPromptBuilder;

  beforeEach(() => {
    vi.clearAllMocks();
    builder = new GenericPromptBuilder({
      countTokens: mockCountTokens,
      countChatTokens: mockCountChatTokens
    });
  });

  describe('Constructor', () => {
    it('should create instance with required tokenizer functions', () => {
      expect(builder).toBeInstanceOf(GenericPromptBuilder);
    });

    it('should throw error if tokenizer functions are missing', () => {
      expect(() => new GenericPromptBuilder({} as any)).toThrow();
    });
  });

  describe('buildMessages - Basic functionality', () => {
    it('should handle empty components array', () => {
      const result = builder.buildMessages([]);
      
      expect(result.messages).toEqual([]);
      expect(result.buildMetadata.componentCount).toBe(0);
      expect(result.buildMetadata.processingTimeMs).toBeGreaterThan(0);
    });

    it('should validate input components array', () => {
      expect(() => builder.buildMessages(null as any)).toThrow('Components must be an array');
      expect(() => builder.buildMessages('invalid' as any)).toThrow('Components must be an array');
    });

    it('should return comprehensive metadata', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System message' },
        { type: 'user_message', content: 'User message' }
      ];

      const result = builder.buildMessages(components);

      expect(result.buildMetadata).toMatchObject({
        componentCount: 2,
        formatWarnings: expect.any(Array),
        normalizationWarnings: expect.any(Array),
        mergeStats: {
          originalCount: expect.any(Number),
          finalCount: expect.any(Number),
          mergedCount: expect.any(Number)
        },
        processingTimeMs: expect.any(Number),
        historyMessagesProcessed: 0
      });
    });
  });

  describe('Component Formatting Phase', () => {
    it('should format system instruction component', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'You are a helpful assistant' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0]).toMatchObject({
        role: 'system',
        content: 'You are a helpful assistant'
      });
    });

    it('should format user message component', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Hello, how are you?' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0]).toMatchObject({
        role: 'user',
        content: 'Hello, how are you?'
      });
    });

    it('should format assistant message component', () => {
      const components: PromptComponent[] = [
        { type: 'assistant_message', content: 'I am doing well, thank you!' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0]).toMatchObject({
        role: 'assistant',
        content: 'I am doing well, thank you!'
      });
    });

    it('should format assistant message with summary flag', () => {
      const components: PromptComponent[] = [
        { 
          type: 'assistant_message', 
          content: 'Previous conversation summary',
          isSummary: true
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages[0].content).toContain('[SUMMARY]');
      expect(result.messages[0].content).toContain('Previous conversation summary');
    });

    it('should format JSON schema instruction component', () => {
      const testSchema = z.object({
        name: z.string(),
        age: z.number()
      });

      const components: PromptComponent[] = [
        {
          type: 'json_schema_instruction',
          role: 'system',
          zodSchema: testSchema,
          instructionPrefix: 'Respond with JSON:'
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages[0].content).toContain('Respond with JSON:');
      expect(result.messages[0].content).toContain('```json');
      expect(result.messages[0].content).toContain('"type": "object"');
    });

    it('should format tagged data component', () => {
      const components: PromptComponent[] = [
        {
          type: 'tagged_data',
          role: 'user',
          tag: 'context',
          data: { key: 'value', number: 42 }
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages[0].content).toContain('<context>');
      expect(result.messages[0].content).toContain('</context>');
      expect(result.messages[0].content).toContain('"key": "value"');
    });

    it('should format tagged data with pre-formatted string', () => {
      const components: PromptComponent[] = [
        {
          type: 'tagged_data',
          role: 'user',
          tag: 'xml_data',
          data: '<item>Pre-formatted XML</item>',
          isPreformattedString: true
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages[0].content).toContain('<xml_data>');
      expect(result.messages[0].content).toContain('<item>Pre-formatted XML</item>');
      expect(result.messages[0].content).toContain('</xml_data>');
    });

    it('should format text block component', () => {
      const components: PromptComponent[] = [
        {
          type: 'text_block',
          role: 'user',
          content: 'This is a text block'
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages[0]).toMatchObject({
        role: 'user',
        content: 'This is a text block'
      });
    });
  });

  describe('History Block Processing', () => {
    it('should process history block messages', () => {
      const historyMessages: ModelMessage[] = [
        { role: 'user', content: 'Previous user message' },
        { role: 'assistant', content: 'Previous assistant response' }
      ];

      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'You are helpful' },
        { type: 'history_block', messages: historyMessages },
        { type: 'user_message', content: 'Current message' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(4); // system + 2 history + current
      expect(result.buildMetadata.historyMessagesProcessed).toBe(2);
      
      // Verify order: system, history1, history2, current
      expect(result.messages[0].role).toBe('system');
      expect(result.messages[1].role).toBe('user');
      expect(result.messages[1].content).toBe('Previous user message');
      expect(result.messages[2].role).toBe('assistant');
      expect(result.messages[2].content).toBe('Previous assistant response');
      expect(result.messages[3].role).toBe('user');
      expect(result.messages[3].content).toBe('Current message');
    });

    it('should handle empty history block', () => {
      const components: PromptComponent[] = [
        { type: 'history_block', messages: [] },
        { type: 'user_message', content: 'Current message' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0].content).toBe('Current message');
      expect(result.buildMetadata.historyMessagesProcessed).toBe(0);
    });

    it('should handle history block with complex content', () => {
      const historyMessages: ModelMessage[] = [
        { 
          role: 'assistant', 
          content: [
            { type: 'text', text: 'I can help with that.' },
            { type: 'tool-call', toolCallId: 'call_1', toolName: 'search', args: { query: 'test' } }
          ]
        }
      ];

      const components: PromptComponent[] = [
        { type: 'history_block', messages: historyMessages }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0].role).toBe('assistant');
      // Content should be stringified for processing
      expect(typeof result.messages[0].content).toBe('string');
    });
  });

  describe('Multiple Component Types Integration', () => {
    it('should handle mixed component types correctly', () => {
      const testSchema = z.object({ result: z.string() });
      
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System prompt' },
        { 
          type: 'json_schema_instruction',
          role: 'system',
          zodSchema: testSchema
        },
        { type: 'user_message', content: 'User question' },
        {
          type: 'tagged_data',
          role: 'user',
          tag: 'context',
          data: { info: 'additional context' }
        },
        { type: 'assistant_message', content: 'Assistant response' }
      ];

      const result = builder.buildMessages(components);

      // Should merge system messages, keep user and assistant separate
      expect(result.messages.length).toBeGreaterThanOrEqual(3);
      
      // Check that system content includes both system instruction and schema
      const systemMessage = result.messages.find(m => m.role === 'system');
      expect(systemMessage?.content).toContain('System prompt');
      expect(systemMessage?.content).toContain('```json');

      // Check user message includes tagged data
      const userMessages = result.messages.filter(m => m.role === 'user');
      expect(userMessages.length).toBeGreaterThanOrEqual(1);
      const userContent = userMessages.map(m => m.content).join(' ');
      expect(userContent).toContain('User question');
      expect(userContent).toContain('<context>');
    });
  });

  describe('Message Merging', () => {
    it('should merge compatible adjacent messages', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'First user message' },
        { type: 'user_message', content: 'Second user message' },
        { type: 'assistant_message', content: 'Assistant response' },
        { type: 'user_message', content: 'Third user message' }
      ];

      const result = builder.buildMessages(components);

      // Should merge first two user messages
      expect(result.buildMetadata.mergeStats.mergedCount).toBeGreaterThan(0);
      
      // Final result should have fewer messages than components
      expect(result.messages.length).toBeLessThan(components.length);
    });

    it('should respect merge options', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'First user' },
        { type: 'user_message', content: 'Second user' }
      ];

      // Without preserving boundaries (default: merge user messages)
      const result1 = builder.buildMessages(components);
      
      // With preserving boundaries (don't merge user messages)
      const result2 = builder.buildMessages(components, {
        mergeOptions: { preserveSemanticBoundaries: true }
      });

      // First result should have merged user messages, second should not
      expect(result1.buildMetadata.mergeStats.mergedCount).toBeGreaterThan(
        result2.buildMetadata.mergeStats.mergedCount
      );
    });

    it('should skip merging when requested', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'First message' },
        { type: 'user_message', content: 'Second message' }
      ];

      const result = builder.buildMessages(components, { skipMerging: true });

      expect(result.buildMetadata.mergeStats.mergedCount).toBe(0);
      expect(result.messages).toHaveLength(2);
    });
  });

  describe('Normalization Phase', () => {
    it('should normalize legacy message formats', () => {
      // This would be tested with history blocks containing legacy formats
      const legacyMessages: ModelMessage[] = [
        { role: 'human', content: 'User message' } as any, // Legacy role
        { role: 'ai', content: 'Assistant message' } as any // Legacy role
      ];

      const components: PromptComponent[] = [
        { type: 'history_block', messages: legacyMessages }
      ];

      const result = builder.buildMessages(components);

      // Should convert to standard roles
      expect(result.messages.every(m => ['system', 'user', 'assistant', 'tool'].includes(m.role))).toBe(true);
      expect(result.buildMetadata.normalizationWarnings.length).toBeGreaterThan(0);
    });

    it('should skip normalization when requested', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Test message' }
      ];

      const result = builder.buildMessages(components, { skipNormalization: true });

      expect(result.buildMetadata.normalizationWarnings).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle formatter errors gracefully', () => {
      const invalidComponent = {
        type: 'json_schema_instruction',
        role: 'system',
        zodSchema: null // Invalid schema
      } as any;

      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Valid message' },
        invalidComponent
      ];

      const result = builder.buildMessages(components);

      // Should continue processing despite error
      expect(result.messages).toHaveLength(1);
      expect(result.buildMetadata.formatWarnings.length).toBeGreaterThan(0);
    });

    it('should handle unknown component types', () => {
      const unknownComponent = {
        type: 'unknown_type',
        content: 'test'
      } as any;

      const components: PromptComponent[] = [
        unknownComponent,
        { type: 'user_message', content: 'Valid message' }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.buildMetadata.formatWarnings.some(w => w.includes('Unknown component type'))).toBe(true);
    });

    it('should throw error for critical failures', () => {
      // Create a builder with invalid formatter setup to trigger error
      const invalidComponents: PromptComponent[] = [
        { type: 'json_schema_instruction', role: 'system', zodSchema: null } as any
      ];

      // Test that the error handling works correctly  
      const result = builder.buildMessages(invalidComponents);
      expect(result.buildMetadata.formatWarnings.length).toBeGreaterThan(0);
    });
  });

  describe('Token Counting', () => {
    it('should calculate token count for final messages', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System message' },
        { type: 'user_message', content: 'User message' }
      ];

      const result = builder.buildMessages(components);

      expect(result.tokenCount).toBeDefined();
      expect(result.tokenCount).toBeGreaterThan(0);
      expect(mockCountChatTokens).toHaveBeenCalledWith(result.messages, undefined);
    });

    it('should use target model ID for token counting', () => {
      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Test' }
      ];

      const targetModelId = 'gpt-4';
      builder.buildMessages(components, { targetModelId });

      expect(mockCountChatTokens).toHaveBeenCalledWith(expect.any(Array), targetModelId);
    });

    it('should handle token counting errors gracefully', () => {
      const errorTokenizer = vi.fn(() => { throw new Error('Token counting failed'); });
      const builderWithErrorTokenizer = new GenericPromptBuilder({
        countTokens: mockCountTokens,
        countChatTokens: errorTokenizer
      });

      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Test' }
      ];

      const result = builderWithErrorTokenizer.buildMessages(components, { includeDebugInfo: true });

      expect(result.tokenCount).toBeUndefined();
      expect(result.buildMetadata.formatWarnings.some(w => w.includes('Token counting failed'))).toBe(true);
    });
  });

  describe('Legacy Compatibility', () => {
    it('should support legacy buildMessagesLegacy method', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System' },
        { type: 'user_message', content: 'User' }
      ];

      const legacyResult = builder.buildMessagesLegacy(components, 'gpt-3.5-turbo');

      expect(legacyResult).toMatchObject({
        messages: expect.any(Array),
        tokenCount: expect.any(Number)
      });
      expect(legacyResult.messages).toHaveLength(2);
    });

    it('should handle zero token count in legacy method', () => {
      const errorTokenizer = vi.fn(() => { throw new Error('Error'); });
      const builderWithErrorTokenizer = new GenericPromptBuilder({
        countTokens: mockCountTokens,
        countChatTokens: errorTokenizer
      });

      const components: PromptComponent[] = [
        { type: 'user_message', content: 'Test' }
      ];

      const result = builderWithErrorTokenizer.buildMessagesLegacy(components);

      expect(result.tokenCount).toBe(0);
    });
  });

  describe('Utility Methods', () => {
    it('should provide component statistics', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System' },
        { type: 'user_message', content: 'User1' },
        { type: 'user_message', content: 'User2' },
        { type: 'history_block', messages: [{ role: 'user', content: 'History1' }, { role: 'assistant', content: 'History2' }] }
      ];

      const stats = builder.getComponentStatistics(components);

      expect(stats).toMatchObject({
        totalComponents: 4,
        componentsByType: {
          'system_instruction': 1,
          'user_message': 2,
          'history_block': 1
        },
        historyMessageCount: 2
      });
    });

    it('should provide full debug information', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: 'System' },
        { type: 'user_message', content: 'User' }
      ];

      const debugResult = builder.buildMessagesWithFullDebug(components);

      expect(debugResult).toMatchObject({
        messages: expect.any(Array),
        tokenCount: expect.any(Number),
        buildMetadata: expect.any(Object),
        debugInfo: {
          componentStats: expect.any(Object),
          formattedMessages: expect.any(Array),
          preNormalizationMessages: expect.any(Array),
          preMergeMessages: expect.any(Array)
        }
      });

      expect(debugResult.debugInfo.formattedMessages).toHaveLength(2);
      expect(debugResult.debugInfo.componentStats.totalComponents).toBe(2);
    });
  });

  describe('Content Filtering', () => {
    it('should filter empty content appropriately', () => {
      const components: PromptComponent[] = [
        { type: 'system_instruction', content: '' }, // Empty system - should be filtered
        { type: 'text_block', role: 'user', content: '   ' }, // Whitespace only - should be filtered
        { type: 'user_message', content: 'Valid content' } // Valid - should be kept
      ];

      const result = builder.buildMessages(components);

      // Should keep valid content and filter empty/whitespace
      expect(result.messages.length).toBe(1);
      expect(result.messages[0].content).toBe('Valid content');
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large number of components efficiently', () => {
      const components: PromptComponent[] = Array.from({ length: 100 }, (_, i) => ({
        type: 'text_block',
        role: 'user',
        content: `Message ${i}`
      }));

      const startTime = Date.now();
      const result = builder.buildMessages(components);
      const endTime = Date.now();

      expect(result.messages.length).toBeLessThanOrEqual(100); // Should merge some
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in reasonable time
      expect(result.buildMetadata.processingTimeMs).toBeGreaterThan(0);
    });

    it('should handle deeply nested data in tagged components', () => {
      const deepData = {
        level1: {
          level2: {
            level3: {
              level4: {
                data: 'deep value',
                array: [1, 2, 3, { nested: true }]
              }
            }
          }
        }
      };

      const components: PromptComponent[] = [
        {
          type: 'tagged_data',
          role: 'user',
          tag: 'deep_context',
          data: deepData
        }
      ];

      const result = builder.buildMessages(components);

      expect(result.messages).toHaveLength(1);
      expect(result.messages[0].content).toContain('<deep_context>');
      expect(result.messages[0].content).toContain('deep value');
    });
  });
});