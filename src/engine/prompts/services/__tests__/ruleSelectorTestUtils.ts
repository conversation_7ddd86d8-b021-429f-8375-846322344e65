/**
 * Test Utilities for RuleSelectorService
 * 
 * Specialized utilities, mocks, and helpers for testing the RuleSelectorService.
 * Provides mock repositories, base rules loading, and contextual data.
 */

import { vi, expect, type MockedFunction } from 'vitest';
import type { CustomRulesRepository } from '../../persistence/repositories/CustomRulesRepository';
import { N8nRule, RuleType } from '../../types/rules.types';
import {
  RuleSelectionContext,
  SelectedRules,
  RuleSelectionOptions,
  KeywordExtractionOptions,
  KeywordExtractionResult,
  RuleScoringResult
} from '../types/ruleSelectorService.types';

/**
 * Create a mock CustomRulesRepository for RuleSelectorService testing
 */
export function createMockCustomRulesRepository(
  overrides: Partial<CustomRulesRepository> = {}
): MockedFunction<any> {
  const defaults = {
    getActiveRulesForAgent: vi.fn(),
    getAllRules: vi.fn(),
    getRulesByType: vi.fn(),
    addRule: vi.fn(),
    getRuleById: vi.fn(),
    updateRule: vi.fn(),
    deleteRule: vi.fn(),
    countRules: vi.fn(),
  };
  
  return { ...defaults, ...overrides } as any;
}

/**
 * Factory for creating test rule selection contexts
 */
export function createTestRuleSelectionContext(
  overrides: Partial<RuleSelectionContext> = {}
): RuleSelectionContext {
  const defaults: RuleSelectionContext = {
    agentSlug: 'test-agent',
    taskDescription: 'Create a webhook workflow with error handling and performance optimization',
    nodeTypes: ['Webhook', 'HTTP Request', 'Set'],
    workflowKeywords: ['webhook', 'api', 'error', 'performance'],
  };

  return { ...defaults, ...overrides };
}

/**
 * Factory for creating base rules for testing
 */
export function createTestBaseRules(): N8nRule[] {
  return [
    {
      id: 'base-rule-001',
      name: 'Use Global Error Workflow',
      description: 'All production workflows should have error handling',
      descriptionForLLM: 'CRITICAL: Always define and set a global Error Workflow for production.',
      type: 'ALWAYS_APPLY',
      priority: 'critical',
      category: 'error-handling',
      appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
      appliesToNodeTypes: ['Error Trigger', 'Set', 'HTTP Request'],
      triggerContext: {
        keywords: ['error', 'production', 'robust', 'failure'],
        nodeTypes: ['Error Trigger'],
      },
      recommendation: 'Create an Error Workflow with Error Trigger → Set → Notification',
      source: 'builtin',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      additionalData: {},
    },
    {
      id: 'base-rule-002',
      name: 'Validate Input Data',
      description: 'Always validate and sanitize input data from external sources',
      descriptionForLLM: 'When receiving data from webhooks or APIs, validate structure and sanitize values.',
      type: 'ALWAYS_APPLY',
      priority: 'high',
      category: 'data-flow',
      appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
      appliesToNodeTypes: ['Webhook', 'HTTP Request'],
      triggerContext: {
        keywords: ['webhook', 'api', 'input', 'external', 'validation'],
        nodeTypes: ['Webhook', 'HTTP Request'],
      },
      recommendation: 'Add validation after data input nodes',
      source: 'builtin',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      additionalData: {},
    },
    {
      id: 'base-rule-003',
      name: 'Optimize HTTP Request Batching',
      description: 'Batch HTTP requests when possible to improve performance',
      descriptionForLLM: 'Use Split in Batches node for multiple similar HTTP requests.',
      type: 'CONTEXTUAL_SUGGESTION',
      priority: 'medium',
      category: 'performance',
      appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
      appliesToNodeTypes: ['HTTP Request'],
      triggerContext: {
        keywords: ['multiple requests', 'batch', 'rate limit', 'performance'],
        nodeTypes: ['HTTP Request'],
      },
      recommendation: 'Use Split in Batches node for processing multiple items',
      source: 'builtin',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      additionalData: {},
    },
    {
      id: 'base-rule-004',
      name: 'Set Proper HTTP Request Timeouts',
      description: 'Configure appropriate timeouts for HTTP requests',
      descriptionForLLM: 'Set explicit timeout values based on expected API response time.',
      type: 'CONTEXTUAL_SUGGESTION',
      priority: 'medium',
      category: 'node-usage',
      appliesToAgents: ['n8n-node-composer-agent'],
      appliesToNodeTypes: ['HTTP Request'],
      triggerContext: {
        keywords: ['http request', 'timeout', 'hanging', 'slow api'],
        nodeTypes: ['HTTP Request'],
      },
      recommendation: 'Set timeout in HTTP Request node options',
      source: 'builtin',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      additionalData: {},
    },
    {
      id: 'base-rule-005',
      name: 'Use Webhook Response Node',
      description: 'Always respond to webhooks with appropriate HTTP status codes',
      descriptionForLLM: 'Include a Respond to Webhook node with proper status codes.',
      type: 'ALWAYS_APPLY',
      priority: 'high',
      category: 'node-usage',
      appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
      appliesToNodeTypes: ['Webhook'],
      triggerContext: {
        keywords: ['webhook', 'response', 'status code', 'http'],
        nodeTypes: ['Webhook'],
      },
      recommendation: 'Add Respond to Webhook node after webhook processing',
      source: 'builtin',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      additionalData: {},
    },
  ];
}

/**
 * Factory for creating custom user rules
 */
export function createTestCustomRules(agentSlug: string = 'test-agent'): N8nRule[] {
  return [
    {
      id: 'custom-rule-001',
      name: 'Custom Always Apply Rule',
      description: 'A custom rule that always applies',
      descriptionForLLM: 'This is a custom rule description for LLM.',
      type: 'ALWAYS_APPLY',
      priority: 'high',
      category: 'custom-category',
      appliesToAgents: [agentSlug],
      appliesToNodeTypes: [],
      triggerContext: {
        keywords: ['custom', 'always'],
      },
      recommendation: 'Follow this custom recommendation',
      source: 'user_defined',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-02T00:00:00Z'),
      updatedAt: new Date('2024-01-02T00:00:00Z'),
      additionalData: {},
    },
    {
      id: 'custom-rule-002',
      name: 'Custom Contextual Rule',
      description: 'A custom contextual suggestion rule',
      descriptionForLLM: 'This rule applies in specific contexts.',
      type: 'CONTEXTUAL_SUGGESTION',
      priority: 'medium',
      category: 'custom-workflow',
      appliesToAgents: [agentSlug],
      appliesToNodeTypes: ['HTTP Request'],
      triggerContext: {
        keywords: ['api', 'integration', 'custom'],
        nodeTypes: ['HTTP Request'],
      },
      recommendation: 'Use custom integration patterns',
      source: 'user_defined',
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      createdAt: new Date('2024-01-02T00:00:00Z'),
      updatedAt: new Date('2024-01-02T00:00:00Z'),
      additionalData: {},
    },
  ];
}

/**
 * Helper to create keyword extraction result for testing
 */
export function createTestKeywordExtractionResult(
  keywords: string[] = ['webhook', 'api', 'error', 'performance'],
  overrides: Partial<KeywordExtractionResult> = {}
): KeywordExtractionResult {
  const frequencies: Record<string, number> = {};
  keywords.forEach((keyword, index) => {
    frequencies[keyword] = keywords.length - index; // Higher frequency for earlier keywords
  });

  const defaults: KeywordExtractionResult = {
    keywords,
    frequencies,
    totalWordsProcessed: keywords.length * 2, // Simulate some processing
  };

  return { ...defaults, ...overrides };
}

/**
 * Helper to create rule scoring results for testing
 */
export function createTestRuleScoringResults(
  rules: N8nRule[],
  keywords: string[] = ['webhook', 'api', 'error']
): RuleScoringResult[] {
  return rules.map((rule, index) => {
    // Calculate mock matches based on rule's trigger context
    const matchedKeywords = keywords.filter(keyword =>
      rule.triggerContext?.keywords?.some(ruleKeyword =>
        ruleKeyword.toLowerCase().includes(keyword.toLowerCase())
      ) || false
    );

    const priorityWeights = {
      critical: 5,
      high: 4,
      medium: 3,
      low: 2,
      informational: 1,
    };

    const keywordMatches = matchedKeywords.length;
    const priorityWeight = priorityWeights[rule.priority];
    const categoryBonus = 0; // Can be extended for specific tests
    const totalScore = keywordMatches * 3 + priorityWeight * 0.5 + categoryBonus;

    return {
      rule,
      score: totalScore,
      matchedKeywords,
      scoringFactors: {
        keywordMatches,
        priorityWeight,
        categoryBonus,
        totalScore,
      },
    };
  });
}

/**
 * Test data for different task descriptions
 */
export const TestTaskDescriptions = {
  webhook: 'Create a webhook workflow that receives data and processes it',
  errorHandling: 'Build a robust workflow with comprehensive error handling',
  performance: 'Optimize workflow performance with batching and efficient processing',
  apiIntegration: 'Integrate with external API and handle rate limits',
  complex: 'Create a complex workflow with webhook, API calls, error handling, and performance optimization',
  simple: 'Build a simple data transformation workflow',
  empty: '',
  undefined: undefined,
} as const;

/**
 * Test data for different agent slugs
 */
export const TestAgentSlugs = {
  architect: 'n8n-architect-agent',
  nodeComposer: 'n8n-node-composer-agent',
  validator: 'n8n-best-practice-validator-agent',
  documenter: 'n8n-workflow-documenter-agent',
  unknown: 'unknown-agent',
} as const;

/**
 * Helper to setup mock repository responses
 */
export function setupMockRepositoryResponses(
  mockRepository: any,
  agentSlug: string = 'test-agent'
) {
  const customRules = createTestCustomRules(agentSlug);
  
  mockRepository.getActiveRulesForAgent.mockResolvedValue(customRules);
  mockRepository.getAllRules.mockResolvedValue(customRules);
  mockRepository.getRulesByType.mockImplementation((type: RuleType) =>
    Promise.resolve(customRules.filter(rule => rule.type === type))
  );
}

/**
 * Helper to verify rule selection results
 */
export function assertRuleSelectionResults(
  result: N8nRule[],
  expected: {
    totalCount?: number;
    hasAlwaysApplyRules?: boolean;
    hasContextualRules?: boolean;
    agentSlug?: string;
    containsRuleIds?: string[];
    excludesRuleIds?: string[];
  }
) {
  if (expected.totalCount !== undefined) {
    expect(result).toHaveLength(expected.totalCount);
  }

  if (expected.hasAlwaysApplyRules !== undefined) {
    const hasAlwaysApply = result.some(rule => rule.type === 'ALWAYS_APPLY');
    expect(hasAlwaysApply).toBe(expected.hasAlwaysApplyRules);
  }

  if (expected.hasContextualRules !== undefined) {
    const hasContextual = result.some(rule => rule.type === 'CONTEXTUAL_SUGGESTION');
    expect(hasContextual).toBe(expected.hasContextualRules);
  }

  if (expected.agentSlug) {
    result.forEach(rule => {
      expect(rule.appliesToAgents).toContain(expected.agentSlug);
    });
  }

  if (expected.containsRuleIds) {
    expected.containsRuleIds.forEach(ruleId => {
      expect(result.some(rule => rule.id === ruleId)).toBe(true);
    });
  }

  if (expected.excludesRuleIds) {
    expected.excludesRuleIds.forEach(ruleId => {
      expect(result.some(rule => rule.id === ruleId)).toBe(false);
    });
  }
} 