/**
 * Agent Prompt Builder Service Tests
 * 
 * Comprehensive test suite for the AgentPromptBuilder service,
 * covering all major functionality including service orchestration,
 * prompt component assembly, and error handling.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { z } from 'zod';
import { ModelMessage } from 'ai';
import { AgentPromptBuilder, AgentPromptOptions } from '../AgentPromptBuilder';
import { GenericPromptBuilder, AssembledPrompt } from '../GenericPromptBuilder';
import { ConversationHistoryService } from '../ConversationHistoryService';
import { RuleSelectorService } from '../RuleSelectorService';
import { TaskSession, TaskSessionStatusSchema } from '../../../../types/tasks.types';
import { GraphDefinition } from '../../../graph/types/graph.types';
import { N8nRule } from '../../../../types/rules.types';
import { PromptComponent } from '../types/genericPromptBuilder.types';

// Mock dependencies
const createMockGenericPromptBuilder = () => ({
  buildMessages: vi.fn(),
  getComponentStatistics: vi.fn()
});

const createMockConversationHistoryService = () => ({
  getHistoryForPrompt: vi.fn()
});

const createMockRuleSelectorService = () => ({
  getRulesForAgentPrompt: vi.fn(),
  selectRules: vi.fn()
});


// Test data factories
const createTestTaskSession = (overrides: Partial<TaskSession> = {}): TaskSession => ({
  id: 'test-task-123',
  originalUserInput: 'Create a workflow for processing webhooks',
  status: TaskSessionStatusSchema.Enum.processing,
  chatId: 'test-chat-456',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-01T10:05:00Z'),
  metadata: {
    totalStepsExecuted: 0,
    totalLLMTokensUsed: 0,
    estimatedCostUSD: 0,
    executionTimeMs: 0
  },
  priority: 'normal',
  tags: [],
  additionalData: {},
  executionHistory: [],
  ...overrides
});

const createTestGraphDefinition = (overrides: Partial<GraphDefinition> = {}): GraphDefinition => ({
  id: 'test-graph',
  description: 'Test workflow creation graph',
  version: '1.0.0',
  entryNodeId: 'start',
  nodes: [
    {
      id: 'start',
      type: 'start'
    },
    {
      id: 'architect',
      type: 'agentCall',
      agentSlug: 'n8n-architect-agent',
      description: 'Plans the workflow architecture'
    },
    {
      id: 'composer',
      type: 'agentCall', 
      agentSlug: 'n8n-node-composer-agent',
      description: 'Composes individual nodes'
    }
  ],
  edges: [],
  expectedInitialInputs: [],
  graphConstants: {},
  ...overrides
});

const createTestRule = (overrides: Partial<N8nRule> = {}): N8nRule => ({
  id: 'test-rule-1',
  name: 'Test Rule',
  description: 'This is a test rule for validation',
  descriptionForLLM: 'This is a test rule for validation (LLM optimized)',
  type: 'ALWAYS_APPLY',
  priority: 'high',
  category: 'best-practices',
  isActive: true,
  appliesToAgents: ['n8n-architect-agent'],
  appliesToNodeTypes: [],
  recommendation: 'Follow this test rule for better results',
  source: 'builtin',
  version: 1,
  usageCount: 0,
  relatedRules: [],
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  additionalData: {},
  ...overrides
});

describe('AgentPromptBuilder', () => {
  let agentPromptBuilder: AgentPromptBuilder;
  let mockGenericPromptBuilder: ReturnType<typeof createMockGenericPromptBuilder>;
  let mockConversationHistoryService: ReturnType<typeof createMockConversationHistoryService>;
  let mockRuleSelectorService: ReturnType<typeof createMockRuleSelectorService>;

  beforeEach(() => {
    mockGenericPromptBuilder = createMockGenericPromptBuilder();
    mockConversationHistoryService = createMockConversationHistoryService();
    mockRuleSelectorService = createMockRuleSelectorService();

    agentPromptBuilder = new AgentPromptBuilder(
      mockGenericPromptBuilder as any,
      mockConversationHistoryService as any,
      mockRuleSelectorService as any
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create instance with required dependencies', () => {
      expect(agentPromptBuilder).toBeInstanceOf(AgentPromptBuilder);
    });
  });

  describe('prepareAgentLlmMessages', () => {
    const defaultParams = {
      agentSlug: 'n8n-architect-agent',
      coreSystemInstruction: 'You are an expert n8n workflow architect.',
      subTaskUserInstruction: 'Plan a workflow for webhook processing',
      taskSession: createTestTaskSession(),
      graphDefinition: createTestGraphDefinition(),
      callSpecificData: { userRequirements: 'Process webhooks with error handling' }
    };

    beforeEach(() => {
      // Setup default mock responses
      mockConversationHistoryService.getHistoryForPrompt.mockResolvedValue({
        historyMessages: [
          { role: 'user', content: 'Previous message' },
          { role: 'assistant', content: 'Previous response' }
        ],
        summaryWasPerformedThisCall: false,
        updatedTaskSession: defaultParams.taskSession,
        finalHistoryTokenCount: 100,
        summarizerTokenUsage: null
      });

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([
        createTestRule(),
        createTestRule({ id: 'test-rule-2', type: 'CONTEXTUAL_SUGGESTION' })
      ]);


      mockGenericPromptBuilder.buildMessages.mockReturnValue({
        messages: [
          { role: 'system', content: 'System instruction' },
          { role: 'user', content: 'User instruction' }
        ],
        tokenCount: 250,
        buildMetadata: {
          componentCount: 5,
          formatWarnings: [],
          normalizationWarnings: [],
          mergeStats: { originalCount: 5, finalCount: 2, mergedCount: 3 },
          processingTimeMs: 45,
          historyMessagesProcessed: 2
        }
      });
    });

    it('should successfully prepare agent LLM messages with all components', async () => {
      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData
      );

      expect(result).toMatchObject({
        messages: expect.any(Array),
        tokenCount: 250,
        buildMetadata: expect.any(Object),
        agentMetadata: {
          agentSlug: 'n8n-architect-agent',
          summaryWasPerformed: false,
          rulesIncluded: 2,
          teamAwarenessIncluded: true,
          finalTokenCount: 250,
          processingTimeMs: expect.any(Number),
          warnings: expect.any(Array)
        }
      });

      expect(mockConversationHistoryService.getHistoryForPrompt).toHaveBeenCalledWith(
        'test-chat-456',
        'test-task-123',
        { 
          targetModelContextWindow: 8000,
          tokensAvailableForHistory: 4000, 
          forceSummarization: false 
        }
      );

      expect(mockRuleSelectorService.getRulesForAgentPrompt).toHaveBeenCalledWith(
        {
          agentSlug: 'n8n-architect-agent',
          taskDescription: 'Plan a workflow for webhook processing'
        },
        undefined
      );

      expect(mockGenericPromptBuilder.buildMessages).toHaveBeenCalledWith(
        expect.any(Array),
        {
          mergeOptions: { mergeSystemMessages: true },
          includeDebugInfo: undefined,
          targetModelId: undefined
        }
      );
    });

    it('should handle options parameter correctly', async () => {
      const options: AgentPromptOptions = {
        systemInstruction: 'Custom system instruction',
        outputSchema: z.object({ result: z.string() }),
        targetModel: 'gpt-4',
        maxHistoryTokens: 2000,
        includeDebugInfo: true,
        excludeTeamAwareness: true,
        customContextData: { customField: 'value' }
      };

      await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData,
        options
      );

      expect(mockConversationHistoryService.getHistoryForPrompt).toHaveBeenCalledWith(
        'test-chat-456',
        'test-task-123',
        { 
          targetModelContextWindow: 8000,
          tokensAvailableForHistory: 2000, 
          forceSummarization: false 
        }
      );

      expect(mockGenericPromptBuilder.buildMessages).toHaveBeenCalledWith(
        expect.any(Array),
        {
          mergeOptions: { mergeSystemMessages: true },
          includeDebugInfo: true,
          targetModelId: 'gpt-4'
        }
      );
    });

    it('should handle task session without chatId gracefully', async () => {
      const taskSessionWithoutChat = createTestTaskSession({ chatId: '' });

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        taskSessionWithoutChat,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData
      );

      expect(result.agentMetadata.warnings).toContain(
        'No chat ID found in task session, skipping conversation history'
      );
      expect(mockConversationHistoryService.getHistoryForPrompt).not.toHaveBeenCalled();
    });

    it('should handle conversation history service failure gracefully', async () => {
      mockConversationHistoryService.getHistoryForPrompt.mockRejectedValue(
        new Error('History service failed')
      );

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData
      );

      expect(result.agentMetadata.warnings).toContain(
        'Failed to retrieve conversation history: History service failed'
      );
      expect(result.agentMetadata.summaryWasPerformed).toBe(false);
    });

    it('should handle rule selector service failure gracefully', async () => {
      mockRuleSelectorService.getRulesForAgentPrompt.mockRejectedValue(
        new Error('Rule service failed')
      );

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData
      );

      expect(result.agentMetadata.warnings).toContain(
        'Failed to retrieve relevant rules: Rule service failed'
      );
      expect(result.agentMetadata.rulesIncluded).toBe(0);
    });

    it('should generate team awareness context from graph definition', async () => {
      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData
      );

      expect(result.agentMetadata.warnings).toContain(
        'Using default team awareness template (advanced template not yet available)'
      );
      expect(result.agentMetadata.teamAwarenessIncluded).toBe(true);
    });

    it('should exclude team awareness when option is set', async () => {
      const options: AgentPromptOptions = {
        excludeTeamAwareness: true
      };

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData,
        options
      );

      expect(result.agentMetadata.teamAwarenessIncluded).toBe(false);
    });

    it('should include output schema when provided', async () => {
      const validSchema = z.object({ result: z.string() });
      const options: AgentPromptOptions = {
        outputSchema: validSchema
      };

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData,
        options
      );

      // Should not have any output schema warnings
      expect(result.agentMetadata.warnings).not.toEqual(
        expect.arrayContaining([
          expect.stringMatching(/Failed to generate output schema instruction/)
        ])
      );
    });

    it('should handle priority rules correctly', async () => {
      const rules = [
        createTestRule({ id: 'rule-1', name: 'Rule 1' }),
        createTestRule({ id: 'rule-2', name: 'Rule 2' }),
        createTestRule({ id: 'rule-3', name: 'Rule 3' })
      ];
      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue(rules);

      const options: AgentPromptOptions = {
        priorityRules: ['rule-2', 'rule-1']
      };

      await agentPromptBuilder.prepareAgentLlmMessages(
        defaultParams.agentSlug,
        defaultParams.coreSystemInstruction,
        defaultParams.subTaskUserInstruction,
        defaultParams.taskSession,
        defaultParams.graphDefinition,
        defaultParams.callSpecificData,
        options
      );

      // Verify the component structure includes proper rule ordering
      const buildCallArgs = mockGenericPromptBuilder.buildMessages.mock.calls[0][0];
      expect(buildCallArgs).toEqual(expect.any(Array));
    });
  });

  describe('getPromptStatistics', () => {
    beforeEach(() => {
      mockConversationHistoryService.getHistoryForPrompt.mockResolvedValue({
        historyMessages: [
          { role: 'user', content: 'Message 1' },
          { role: 'assistant', content: 'Response 1' }
        ],
        summaryWasPerformedThisCall: false
      });

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([
        createTestRule(),
        createTestRule({ id: 'rule-2' }),
        createTestRule({ id: 'rule-3' })
      ]);
    });

    it('should return accurate statistics for prompt components', async () => {
      const taskSession = createTestTaskSession();
      const callSpecificData = { userInput: 'test data' };

      const stats = await agentPromptBuilder.getPromptStatistics(
        'n8n-architect-agent',
        taskSession,
        callSpecificData
      );

      expect(stats).toMatchObject({
        estimatedComponents: expect.any(Number),
        estimatedHistoryMessages: 2,
        estimatedRulesCount: 3
      });

      expect(stats.estimatedComponents).toBeGreaterThan(0);
    });

    it('should handle task session without chatId', async () => {
      const taskSession = createTestTaskSession({ chatId: '' });
      const callSpecificData = {};

      const stats = await agentPromptBuilder.getPromptStatistics(
        'n8n-architect-agent',
        taskSession,
        callSpecificData
      );

      expect(stats.estimatedHistoryMessages).toBe(0);
      expect(mockConversationHistoryService.getHistoryForPrompt).not.toHaveBeenCalled();
    });

    it('should handle service failures gracefully', async () => {
      mockRuleSelectorService.getRulesForAgentPrompt.mockRejectedValue(
        new Error('Service failure')
      );

      const taskSession = createTestTaskSession();
      const stats = await agentPromptBuilder.getPromptStatistics(
        'n8n-architect-agent',
        taskSession,
        {}
      );

      expect(stats).toMatchObject({
        estimatedComponents: 0,
        estimatedHistoryMessages: 0,
        estimatedRulesCount: 0
      });
    });
  });

  describe('Rule Formatting', () => {
    it('should format different rule types correctly', async () => {
      const rules = [
        createTestRule({ 
          id: 'always-rule',
          type: 'ALWAYS_APPLY',
          name: 'Always Apply Rule',
          description: 'This rule always applies'
        }),
        createTestRule({ 
          id: 'contextual-rule',
          type: 'CONTEXTUAL_SUGGESTION',
          name: 'Contextual Rule',
          description: 'This rule applies in certain contexts'
        }),
        createTestRule({ 
          id: 'anti-pattern-rule',
          type: 'ANTI_PATTERN_CHECK',
          name: 'Anti-Pattern Rule',
          description: 'This rule checks for anti-patterns'
        })
      ];

      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue(rules);
      
      // Mock proper response structure for this test
      mockConversationHistoryService.getHistoryForPrompt.mockResolvedValue({
        historyMessages: [],
        summaryWasPerformedThisCall: false,
        updatedTaskSession: createTestTaskSession(),
        finalHistoryTokenCount: 0,
        summarizerTokenUsage: null
      });
      
      mockGenericPromptBuilder.buildMessages.mockReturnValue({
        messages: [{ role: 'system', content: 'test' }],
        tokenCount: 100,
        buildMetadata: {
          componentCount: 1,
          formatWarnings: [],
          normalizationWarnings: [],
          mergeStats: { originalCount: 1, finalCount: 1, mergedCount: 0 },
          processingTimeMs: 10,
          historyMessagesProcessed: 0
        }
      });

      await agentPromptBuilder.prepareAgentLlmMessages(
        'n8n-architect-agent',
        'System instruction',
        'User instruction',
        createTestTaskSession(),
        createTestGraphDefinition(),
        {}
      );

      const buildCallArgs = mockGenericPromptBuilder.buildMessages.mock.calls[0][0];
      const textBlocks = buildCallArgs.filter((component: PromptComponent) => 
        component.type === 'text_block'
      );

      // Should have team awareness + rule sections
      expect(textBlocks.length).toBeGreaterThan(1);
      
      // Check that rule sections are properly formatted
      const ruleBlocks = textBlocks.filter((block: any) => 
        block.content.includes('Required Guidelines') ||
        block.content.includes('Contextual Recommendations') ||
        block.content.includes('Common Pitfalls')
      );
      
      expect(ruleBlocks.length).toBe(3); // One for each rule type
    });
  });

  describe('Error Handling', () => {
    it('should throw error if GenericPromptBuilder fails', async () => {
      // Reset mocks and set up for this specific test
      mockConversationHistoryService.getHistoryForPrompt.mockResolvedValue({
        historyMessages: [],
        summaryWasPerformedThisCall: false,
        updatedTaskSession: createTestTaskSession(),
        finalHistoryTokenCount: 0,
        summarizerTokenUsage: null
      });
      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      
      mockGenericPromptBuilder.buildMessages.mockImplementation(() => {
        throw new Error('GenericPromptBuilder failed');
      });

      await expect(
        agentPromptBuilder.prepareAgentLlmMessages(
          'n8n-architect-agent',
          'System instruction',
          'User instruction',
          createTestTaskSession(),
          createTestGraphDefinition(),
          {}
        )
      ).rejects.toThrow('Failed to prepare agent LLM messages: GenericPromptBuilder failed');
    });

    it('should handle undefined or null parameters gracefully', async () => {
      // Mock minimal responses
      mockConversationHistoryService.getHistoryForPrompt.mockResolvedValue({
        historyMessages: [],
        summaryWasPerformedThisCall: false
      });
      mockRuleSelectorService.getRulesForAgentPrompt.mockResolvedValue([]);
      mockGenericPromptBuilder.buildMessages.mockReturnValue({
        messages: [{ role: 'system', content: 'test' }],
        buildMetadata: {
          componentCount: 1,
          formatWarnings: [],
          normalizationWarnings: [],
          mergeStats: { originalCount: 1, finalCount: 1, mergedCount: 0 },
          processingTimeMs: 10,
          historyMessagesProcessed: 0
        }
      });

      const result = await agentPromptBuilder.prepareAgentLlmMessages(
        'test-agent',
        'system',
        'user',
        createTestTaskSession({ chatId: '' }),
        createTestGraphDefinition(),
        {}
      );

      expect(result).toBeDefined();
      expect(result.agentMetadata).toBeDefined();
    });
  });
});