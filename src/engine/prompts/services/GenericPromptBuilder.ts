/**
 * GenericPromptBuilder.ts
 * 
 * Rewritten to orchestrate the new micro-utilities architecture.
 * This service serves as the main orchestrator that transforms PromptComponent[]
 * into final ModelMessage[] through a clear pipeline of specialized utilities.
 * 
 * Architecture Overview:
 * PromptComponent[] → Formatters → FormattedMessage[] → Normalizers → ModelMessage[] → Mergers → Final ModelMessage[]
 */

import { ModelMessage } from 'ai';

// Import the new micro-utilities
import {
  formatJsonSchemaInstruction,
  formatTaggedData,
  formatSystemInstruction,
  formatUserMessage,
  formatAssistantMessage,
  formatTextBlock,
  type FormattedMessage
} from '../formatters';

import { normalizeMessages, type NormalizationResult } from '../normalizers/MessageNormalizer';
import { mergeMessages, type MergeResult, type MergeOptions } from '../mergers/MessageMerger';

// Import types
import type {
  PromptComponent,
  SystemInstructionComponent,
  UserMessageComponent,
  AssistantMessageComponent,
  HistoryBlockComponent,
  JsonSchemaInstructionComponent,
  TaggedDataComponent,
  TextBlockComponent,
  TokenCountFunction,
  ChatTokenCountFunction,
} from './types/genericPromptBuilder.types';

/**
 * Options for controlling the prompt building process
 */
export interface BuildOptions {
  /** Options for message merging behavior */
  mergeOptions?: MergeOptions;
  /** Target model ID for token counting */
  targetModelId?: string;
  /** Whether to include detailed debug information in the result */
  includeDebugInfo?: boolean;
  /** Whether to skip the merging phase entirely */
  skipMerging?: boolean;
  /** Whether to skip normalization (for already normalized messages) */
  skipNormalization?: boolean;
}

/**
 * Metadata about the build process
 */
export interface BuildMetadata {
  /** Number of original components processed */
  componentCount: number;
  /** Warnings collected during formatting */
  formatWarnings: string[];
  /** Warnings collected during normalization */
  normalizationWarnings: string[];
  /** Statistics from the merging process */
  mergeStats: {
    originalCount: number;
    finalCount: number;
    mergedCount: number;
  };
  /** Total time spent building the prompt in milliseconds */
  processingTimeMs: number;
  /** History messages processed during build */
  historyMessagesProcessed: number;
}

/**
 * Result of the prompt building process
 */
export interface AssembledPrompt {
  /** Final array of ModelMessage objects ready for LLM consumption */
  messages: ModelMessage[];
  /** Token count of the final messages (if tokenizer available) */
  tokenCount?: number;
  /** Detailed metadata about the build process */
  buildMetadata: BuildMetadata;
}

/**
 * Legacy interface for backward compatibility
 */
export interface AssembledPromptResult {
  messages: ModelMessage[];
  tokenCount: number;
}

/**
 * GenericPromptBuilder - Orchestrates the prompt building pipeline
 * 
 * This class has been completely rewritten to use the new micro-utilities architecture.
 * It serves as the main orchestrator that coordinates formatters, normalizers, and mergers
 * to transform PromptComponent[] into final ModelMessage[] arrays.
 */
export class GenericPromptBuilder {
  private readonly countTokens: TokenCountFunction;
  private readonly countChatTokens: ChatTokenCountFunction;

  constructor(tokenizers: {
    countTokens: TokenCountFunction;
    countChatTokens: ChatTokenCountFunction;
  }) {
    if (!tokenizers.countTokens || !tokenizers.countChatTokens) {
      throw new Error('Both countTokens and countChatTokens functions are required');
    }
    this.countTokens = tokenizers.countTokens;
    this.countChatTokens = tokenizers.countChatTokens;
  }

  /**
   * Main method to build messages from components using the new architecture
   * 
   * @param components - Array of PromptComponent objects to process
   * @param options - Build configuration options
   * @returns AssembledPrompt with messages and detailed metadata
   */
  public buildMessages(
    components: PromptComponent[],
    options: BuildOptions = {}
  ): AssembledPrompt {
    const startTime = Date.now();
    
    // Validate input
    if (!Array.isArray(components)) {
      throw new Error('Components must be an array');
    }

    const buildMetadata: BuildMetadata = {
      componentCount: components.length,
      formatWarnings: [],
      normalizationWarnings: [],
      mergeStats: { originalCount: 0, finalCount: 0, mergedCount: 0 },
      processingTimeMs: 0,
      historyMessagesProcessed: 0
    };

    try {
      // Phase 1: Format - Convert PromptComponent[] to FormattedMessage[]
      const formattedMessages = this.formatComponents(components, buildMetadata);

      // Phase 2: Convert - FormattedMessage[] to ModelMessage[]
      const modelMessages = this.convertToModelMessages(formattedMessages, buildMetadata);

      // Phase 3: Normalize - Ensure ModelMessage[] conformance to AI SDK v5
      let normalizedMessages = modelMessages;
      if (!options.skipNormalization) {
        normalizedMessages = this.normalizeMessages(modelMessages, buildMetadata);
      }

      // Phase 4: Merge - Combine compatible adjacent messages
      let finalMessages = normalizedMessages;
      if (!options.skipMerging) {
        finalMessages = this.mergeMessages(normalizedMessages, options.mergeOptions, buildMetadata);
      }

      // Phase 5: Validate & Assemble - Final validation and token counting
      const result = this.assembleResult(finalMessages, options, buildMetadata);

      buildMetadata.processingTimeMs = Math.max(1, Date.now() - startTime);
      result.buildMetadata = buildMetadata;

      return result;
    } catch (error) {
      buildMetadata.processingTimeMs = Math.max(1, Date.now() - startTime);
      throw new Error(`Failed to build messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Legacy method for backward compatibility
   * 
   * @param components - Array of PromptComponent objects to process
   * @param targetModelId - Model ID for token counting
   * @returns Legacy AssembledPromptResult format
   */
  public buildMessagesLegacy(
    components: PromptComponent[],
    targetModelId?: string
  ): AssembledPromptResult {
    const result = this.buildMessages(components, { targetModelId });
    return {
      messages: result.messages,
      tokenCount: result.tokenCount || 0
    };
  }

  /**
   * Phase 1: Format components using specialized formatters
   */
  private formatComponents(components: PromptComponent[], metadata: BuildMetadata): FormattedMessage[] {
    const formatted: FormattedMessage[] = [];

    for (let i = 0; i < components.length; i++) {
      try {
        const component = components[i];
        
        switch (component.type) {
          case 'system_instruction':
            formatted.push(formatSystemInstruction(component));
            break;
            
          case 'user_message':
            formatted.push(formatUserMessage(component));
            break;
            
          case 'assistant_message':
            formatted.push(formatAssistantMessage(component));
            break;
            
          case 'json_schema_instruction':
            formatted.push(formatJsonSchemaInstruction(component));
            break;
            
          case 'tagged_data':
            formatted.push(formatTaggedData(component));
            break;
            
          case 'text_block':
            formatted.push(formatTextBlock(component));
            break;
            
          case 'history_block':
            // History blocks are handled specially - we extract their messages directly
            // and will normalize them in the next phase
            const historyMessages = this.extractHistoryMessages(component);
            metadata.historyMessagesProcessed += historyMessages.length;
            
            // Convert history messages to FormattedMessage format for consistency
            for (const histMsg of historyMessages) {
              formatted.push({
                role: histMsg.role,
                contentString: typeof histMsg.content === 'string' 
                  ? histMsg.content 
                  : JSON.stringify(histMsg.content)
              });
            }
            break;
            
          default:
            const exhaustiveCheck: never = component;
            metadata.formatWarnings.push(`Unknown component type at index ${i}: ${(exhaustiveCheck as any)?.type}`);
            break;
        }
      } catch (error) {
        metadata.formatWarnings.push(`Failed to format component ${i}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return formatted;
  }

  /**
   * Phase 2: Convert FormattedMessage[] to ModelMessage[]
   */
  private convertToModelMessages(formatted: FormattedMessage[], metadata: BuildMetadata): ModelMessage[] {
    const modelMessages: ModelMessage[] = [];

    for (const formattedMsg of formatted) {
      // Filter out empty content unless it's a user or assistant message
      const trimmedContent = formattedMsg.contentString.trim();
      const shouldInclude = trimmedContent !== '' || 
                           formattedMsg.role === 'user' || 
                           formattedMsg.role === 'assistant';

      if (shouldInclude) {
        modelMessages.push({
          role: formattedMsg.role as any, // Type assertion since we validate roles in formatters
          content: formattedMsg.contentString
        });
      }
    }

    metadata.mergeStats.originalCount = modelMessages.length;
    return modelMessages;
  }

  /**
   * Phase 3: Normalize messages using MessageNormalizer
   */
  private normalizeMessages(messages: ModelMessage[], metadata: BuildMetadata): ModelMessage[] {
    try {
      const normalizationResult: NormalizationResult = normalizeMessages(messages);
      
      if (normalizationResult.warnings) {
        metadata.normalizationWarnings.push(...normalizationResult.warnings);
      }

      return normalizationResult.messages;
    } catch (error) {
      throw new Error(`Normalization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Phase 4: Merge messages using MessageMerger
   */
  private mergeMessages(
    messages: ModelMessage[], 
    mergeOptions: MergeOptions = {}, 
    metadata: BuildMetadata
  ): ModelMessage[] {
    try {
      // Default merge options for GenericPromptBuilder: merge system messages by default
      const defaultedOptions: MergeOptions = {
        mergeSystemMessages: true,
        ...mergeOptions
      };
      
      const mergeResult: MergeResult = mergeMessages(messages, defaultedOptions);
      
      metadata.mergeStats.finalCount = mergeResult.messages.length;
      metadata.mergeStats.mergedCount = mergeResult.mergeCount;

      if (mergeResult.warnings) {
        // Add merge warnings to format warnings (since we don't have a separate category)
        metadata.formatWarnings.push(...mergeResult.warnings.map(w => `Merge: ${w}`));
      }

      return mergeResult.messages;
    } catch (error) {
      throw new Error(`Message merging failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Phase 5: Assemble final result with validation and token counting
   */
  private assembleResult(
    messages: ModelMessage[], 
    options: BuildOptions, 
    metadata: BuildMetadata
  ): AssembledPrompt {
    // Final validation
    this.validateFinalMessages(messages);

    // Calculate token count if requested
    let tokenCount: number | undefined;
    try {
      tokenCount = this.countChatTokens(messages, options.targetModelId);
    } catch (error) {
      // Token counting is non-critical - continue without it
      if (options.includeDebugInfo) {
        metadata.formatWarnings.push(`Token counting failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      messages,
      tokenCount,
      buildMetadata: metadata // Will be filled in by caller
    };
  }

  /**
   * Extract ModelMessage[] from HistoryBlockComponent
   */
  private extractHistoryMessages(component: HistoryBlockComponent): ModelMessage[] {
    if (!Array.isArray(component.messages)) {
      return [];
    }

    // Return a copy to avoid mutation of original history
    return component.messages.map(msg => ({ ...msg }));
  }

  /**
   * Validate the final messages array
   */
  private validateFinalMessages(messages: ModelMessage[]): void {
    if (!Array.isArray(messages)) {
      throw new Error('Final messages must be an array');
    }

    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      if (!msg || typeof msg !== 'object') {
        throw new Error(`Message ${i}: Must be an object`);
      }
      if (!msg.role || typeof msg.role !== 'string') {
        throw new Error(`Message ${i}: Must have a valid role`);
      }
      if (msg.content === undefined || msg.content === null) {
        throw new Error(`Message ${i}: Must have content`);
      }
    }
  }

  /**
   * Utility method to get component statistics
   */
  public getComponentStatistics(components: PromptComponent[]): {
    totalComponents: number;
    componentsByType: Record<string, number>;
    historyMessageCount: number;
  } {
    const stats = {
      totalComponents: components.length,
      componentsByType: {} as Record<string, number>,
      historyMessageCount: 0
    };

    for (const component of components) {
      stats.componentsByType[component.type] = (stats.componentsByType[component.type] || 0) + 1;
      
      if (component.type === 'history_block') {
        stats.historyMessageCount += component.messages.length;
      }
    }

    return stats;
  }

  /**
   * Utility method for debugging - returns detailed information about the build process
   */
  public buildMessagesWithFullDebug(
    components: PromptComponent[],
    options: BuildOptions = {}
  ): AssembledPrompt & {
    debugInfo: {
      componentStats: ReturnType<GenericPromptBuilder['getComponentStatistics']>;
      formattedMessages: FormattedMessage[];
      preNormalizationMessages: ModelMessage[];
      preMergeMessages: ModelMessage[];
    };
  } {
    const componentStats = this.getComponentStatistics(components);
    
    // Build with debug enabled
    const debugOptions = { ...options, includeDebugInfo: true };
    const startTime = Date.now();
    
    const buildMetadata: BuildMetadata = {
      componentCount: components.length,
      formatWarnings: [],
      normalizationWarnings: [],
      mergeStats: { originalCount: 0, finalCount: 0, mergedCount: 0 },
      processingTimeMs: 0,
      historyMessagesProcessed: 0
    };

    // Phase 1: Format
    const formattedMessages = this.formatComponents(components, buildMetadata);
    
    // Phase 2: Convert
    const preNormalizationMessages = this.convertToModelMessages(formattedMessages, buildMetadata);
    
    // Phase 3: Normalize
    const preMergeMessages = debugOptions.skipNormalization 
      ? preNormalizationMessages
      : this.normalizeMessages(preNormalizationMessages, buildMetadata);
    
    // Phase 4: Merge
    const finalMessages = debugOptions.skipMerging
      ? preMergeMessages
      : this.mergeMessages(preMergeMessages, debugOptions.mergeOptions, buildMetadata);
    
    // Phase 5: Assemble
    const result = this.assembleResult(finalMessages, debugOptions, buildMetadata);
    
    buildMetadata.processingTimeMs = Math.max(1, Date.now() - startTime);
    result.buildMetadata = buildMetadata;

    return {
      ...result,
      debugInfo: {
        componentStats,
        formattedMessages,
        preNormalizationMessages,
        preMergeMessages
      }
    };
  }
}