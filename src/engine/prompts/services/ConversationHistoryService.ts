import { ModelMessage } from 'ai';
import { TaskSessionRepository } from '../../../persistence/repositories/TaskSessionRepository';
import { ChatRepository } from '../../../persistence/repositories/ChatRepository';
import { LLMService } from '../../llm/LLMService';
import { LLMInvocationResult, LLMTokenUsage as ServiceLLMTokenUsage } from '../../llm/types/llmService.types';
import { GenericPromptBuilder } from './GenericPromptBuilder';
import { AgentRegistryEntry, getAgentConfig } from '../../agents/agentRegistry';
import { N8N_CONVERSATION_SUMMARIZER_SYSTEM, N8N_CONVERSATION_SUMMARIZER_USER } from '../parts/summarizer.parts';
import { TaskSession, ChatMessage, ChatMessageRoleSchema, ContentPart, TextContentPart, ChatMessageRole } from '../../../types';
import { GetHistoryOptions, HistoryForPromptResult, InternalSummarizationOutcome, LLMTokenUsage } from './types/conversationHistoryService.types';
import {
  ChatTokenCountFunction,
  ContextWindowConfig,
  DEFAULT_CONTEXT_WINDOW_CONFIG
} from './types/promptBuilder.types';
import { LLMTaskCategory } from '../../graph/types/graph.types';

export class TaskSessionNotFoundError extends Error {
  constructor(taskId: string) {
    super(`TaskSession with ID "${taskId}" not found.`);
    this.name = 'TaskSessionNotFoundError';
  }
}

/**
 * ConversationHistoryService is responsible for retrieving, managing, and preparing
 * conversation history (including a persisted summary and recent messages) to be
 * included in LLM prompts. It orchestrates summarization if the context becomes too large.
 *
 * Key Responsibilities:
 * - Fetches persisted summaries and recent messages.
 * - Assembles candidate history and calculates token counts.
 * - Triggers summarization via LLMService if token budget is exceeded.
 * - Updates TaskSession with new summaries.
 * - Formats final history for prompt inclusion.
 *
 * Dependencies:
 * - TaskSessionRepository: For TaskSession state.
 * - ChatRepository: For raw ChatMessages.
 * - LLMService: For invoking summarizer agent.
 * - GenericPromptBuilder: For constructing summarizer prompts.
 * - AgentConfigRegistry: For summarizer agent configuration.
 * - Tokenizer functions: For token counting.
 * - ContextWindowConfig: For summarization thresholds and limits.
 *
 * Usage Example:
 * ```typescript
 * // Assuming all dependencies are instantiated
 * const historyService = new ConversationHistoryService(...);
 * const historyResult = await historyService.getHistoryForPrompt(
 *   'chat-123',
 *   'task-456',
 *   {
 *     targetModelContextWindow: 8000,
 *     tokensAvailableForHistory: 4000,
 *   }
 * );
 * // historyResult.historyMessages can now be used in a prompt
 * ```
 */
export class ConversationHistoryService {
  private readonly taskSessionRepo: TaskSessionRepository;
  private readonly chatRepo: ChatRepository;
  private readonly llmService: LLMService;
  private readonly genericPromptBuilder: GenericPromptBuilder;
  private readonly countChatTokens: ChatTokenCountFunction;
  private readonly contextWindowConfig: ContextWindowConfig;
  private readonly defaultSummarizerSlug: string;
  private readonly defaultSummarizerCategory: LLMTaskCategory;

  constructor(
    taskSessionRepo: TaskSessionRepository,
    chatRepo: ChatRepository,
    llmService: LLMService,
    genericPromptBuilder: GenericPromptBuilder,
    tokenizer: { countChatTokens: ChatTokenCountFunction },
    contextWindowConfig?: ContextWindowConfig
  ) {
    this.taskSessionRepo = taskSessionRepo;
    this.chatRepo = chatRepo;
    this.llmService = llmService;
    this.genericPromptBuilder = genericPromptBuilder;
    this.countChatTokens = tokenizer.countChatTokens;
    this.contextWindowConfig = contextWindowConfig || DEFAULT_CONTEXT_WINDOW_CONFIG;
    this.defaultSummarizerSlug = this.contextWindowConfig.defaultSummarizerAgentSlug;
    this.defaultSummarizerCategory = this.contextWindowConfig.defaultSummarizerTaskCategory as LLMTaskCategory;
  }

  public async getHistoryForPrompt(
    chatId: string,
    taskId: string,
    options: GetHistoryOptions
  ): Promise<HistoryForPromptResult> {
    const {
      tokensAvailableForHistory,
      maxMessagesToKeepAfterSummary = this.contextWindowConfig.maxMessagesToKeepAfterSummary,
      maxRecentMessagesForInitialSummaryInput = this.contextWindowConfig.maxRecentMessagesForInitialSummaryInput,
      forceSummarization = false,
      summarizerAgentSlug = this.defaultSummarizerSlug,
      summarizerTaskCategory = this.defaultSummarizerCategory,
    } = options;

    const currentTaskSession = await this.taskSessionRepo.getSessionById(taskId);
    if (!currentTaskSession) {
      throw new TaskSessionNotFoundError(taskId);
    }

    let currentSummary = currentTaskSession.llmContextSummary || '';
    const summaryLastMsgId = currentTaskSession.summaryLastMessageId;

    const recentRawMessages = await this._fetchRelevantRawMessages(
      chatId,
      summaryLastMsgId || undefined,
      maxRecentMessagesForInitialSummaryInput
    );

    let candidateHistoryMessages: ModelMessage[] = [];
    if (currentSummary) {
      candidateHistoryMessages.push({ role: 'assistant', content: `## Previous Conversation Summary\n${currentSummary}` });
    }

    let tempHistoryForPrompt: ModelMessage[] = [...candidateHistoryMessages];
    for (const msg of recentRawMessages) {
      const modelMsg = this._chatMessageToModelMessage(msg);
      if (modelMsg) {
        const potentialHistory = [...tempHistoryForPrompt, modelMsg];
        if (this.countChatTokens(potentialHistory) <= tokensAvailableForHistory) {
          tempHistoryForPrompt.push(modelMsg);
        } else {
          break;
        }
      }
    }
    
    let finalHistoryForPrompt: ModelMessage[] = tempHistoryForPrompt;
    let currentHistoryTokenCount = this.countChatTokens(finalHistoryForPrompt);
    let summaryWasPerformedThisCall = false;
    let sessionToReturn = currentTaskSession;
    let summarizerTokenUsageThisCall: LLMTokenUsage | null = null;

    const hasNewMessagesToPotentiallySummarize = recentRawMessages.length > 0;
    let fullPotentialHistory: ModelMessage[] = [];
    if (currentSummary) {
      fullPotentialHistory.push({ role: 'assistant', content: `## Previous Conversation Summary\n${currentSummary}` });
    }
    recentRawMessages.forEach(msg => {
      const modelMsg = this._chatMessageToModelMessage(msg);
      if (modelMsg) {
        fullPotentialHistory.push(modelMsg);
      }
    });
    const fullPotentialTokenCount = this.countChatTokens(fullPotentialHistory);

    const needsSummarization = forceSummarization || (fullPotentialTokenCount > tokensAvailableForHistory && hasNewMessagesToPotentiallySummarize);

    if (needsSummarization) {
      const { messagesForSummarizerInput, lastMessageIdInSummarizerInput: selectedLastMsgIdForSummary } = this._selectMessagesToSummarize(
        recentRawMessages
      );

      if (messagesForSummarizerInput.length > 0 && selectedLastMsgIdForSummary) {
        const summarizationOutcome = await this._performSummarization(
          currentTaskSession,
          messagesForSummarizerInput,
          selectedLastMsgIdForSummary,
          currentSummary,
          summarizerAgentSlug,
          summarizerTaskCategory
        );

        if (summarizationOutcome.success && summarizationOutcome.newSummaryText) {
          summaryWasPerformedThisCall = true;
          currentSummary = summarizationOutcome.newSummaryText;
          summarizerTokenUsageThisCall = summarizationOutcome.summarizerTokenUsage || null;

          const updatedSessionData: Partial<TaskSession> = {
            llmContextSummary: currentSummary,
            summaryLastMessageId: summarizationOutcome.lastMessageIdInSummarizerInput || summaryLastMsgId,
            updatedAt: new Date(),
          };
          Object.assign(currentTaskSession, updatedSessionData);
          sessionToReturn = await this.taskSessionRepo.saveSession(currentTaskSession);

          finalHistoryForPrompt = [];
          if (currentSummary) {
            finalHistoryForPrompt.push({ role: 'assistant', content: `## Previous Conversation Summary\n${currentSummary}` });
          }

          const messagesPostSummary = recentRawMessages.filter(msg => {
            if (!msg.id || !summarizationOutcome.lastMessageIdInSummarizerInput) return false;
            const lastSummarizedMsgOriginal = recentRawMessages.find(rm => rm.id === summarizationOutcome.lastMessageIdInSummarizerInput);
            return lastSummarizedMsgOriginal ? msg.timestamp > lastSummarizedMsgOriginal.timestamp : false;
          }).slice(0, maxMessagesToKeepAfterSummary);

          for (const msg of messagesPostSummary) {
            const modelMsg = this._chatMessageToModelMessage(msg);
            if (modelMsg) {
              const potentialHistory = [...finalHistoryForPrompt, modelMsg];
              if (this.countChatTokens(potentialHistory) <= tokensAvailableForHistory) {
                finalHistoryForPrompt.push(modelMsg);
              } else {
                break;
              }
            }
          }
          currentHistoryTokenCount = this.countChatTokens(finalHistoryForPrompt);
        } else {
          console.error('ConversationHistoryService: Summarization failed.', summarizationOutcome.error);
        }
      }
    }

    return {
      historyMessages: finalHistoryForPrompt,
      summaryWasPerformedThisCall,
      updatedTaskSession: sessionToReturn,
      finalHistoryTokenCount: currentHistoryTokenCount,
      summarizerTokenUsage: summarizerTokenUsageThisCall,
    };
  }

  private _chatMessageToModelMessage(chatMsg: ChatMessage): ModelMessage | null {
    let role: ChatMessageRole | 'system' | 'user' | 'assistant' = chatMsg.role as ChatMessageRole;
    if (chatMsg.role === ChatMessageRoleSchema.Enum.system_internal) {
        role = 'system';
    }

    let contentString = '';
    if (typeof chatMsg.content === 'string') {
      contentString = chatMsg.content;
    } else if (Array.isArray(chatMsg.content)) {
      const textPart = chatMsg.content.find(
        (part: ContentPart): part is TextContentPart => part.type === 'text'
      );
      if (textPart) {
        contentString = textPart.text;
      }
    }
    
    if (role === 'user') {
        return { role: 'user', content: contentString };
    } else if (role === 'assistant') {
        return { role: 'assistant', content: contentString };
    } else if (role === 'system') {
        return { role: 'system', content: contentString };
    } else {
        console.warn(`_chatMessageToModelMessage: Unsupported role "${role}" for direct conversion. Skipping.`);
        return null;
    }
  }

  private async _fetchRelevantRawMessages(
    chatId: string,
    sinceMessageId?: string,
    limit: number = this.contextWindowConfig.maxRecentMessagesForInitialSummaryInput
  ): Promise<ChatMessage[]> {
    const effectiveLimit = Math.max(1, limit);
    return this.chatRepo.getMessagesForChatSince(chatId, sinceMessageId, { limit: effectiveLimit, sortDirection: 'asc' });
  }

  private _selectMessagesToSummarize(
    rawMessagesSinceLastSummary: ChatMessage[]
  ): { messagesForSummarizerInput: ModelMessage[]; lastMessageIdInSummarizerInput?: string } {
    if (!rawMessagesSinceLastSummary || rawMessagesSinceLastSummary.length === 0) {
      return { messagesForSummarizerInput: [], lastMessageIdInSummarizerInput: undefined };
    }
    const messagesForSummarizerInput: ModelMessage[] = rawMessagesSinceLastSummary
      .map(msg => this._chatMessageToModelMessage(msg))
      .filter(modelMsg => modelMsg !== null) as ModelMessage[];
      
    const lastMessageInBatch = rawMessagesSinceLastSummary[rawMessagesSinceLastSummary.length - 1];
    return {
      messagesForSummarizerInput,
      lastMessageIdInSummarizerInput: lastMessageInBatch?.id,
    };
  }

  private async _performSummarization(
    _taskSession: TaskSession,
    messagesForSummarizerInput: ModelMessage[],
    lastMessageIdFromNewMessages: string,
    currentSummaryToBuildOn?: string,
    summarizerSlug: string = this.defaultSummarizerSlug,
    summarizerCategory: LLMTaskCategory = this.defaultSummarizerCategory
  ): Promise<InternalSummarizationOutcome> {
    let agentConfig: AgentRegistryEntry | undefined;
    try {
      agentConfig = getAgentConfig(summarizerSlug);
    } catch (e: any) {
        console.error(`Error fetching agent config for ${summarizerSlug}:`, e);
        return { success: false, error: e.message || `Summarizer agent config "${summarizerSlug}" not found.` };
    }
    if (!agentConfig) {
      return { success: false, error: `Summarizer agent config "${summarizerSlug}" not found.` };
    }

    const systemInstruction = N8N_CONVERSATION_SUMMARIZER_SYSTEM;
    const userTaskTemplate = N8N_CONVERSATION_SUMMARIZER_USER;

    const newMessagesString = messagesForSummarizerInput
      .map(m => `[${m.role.toUpperCase()}]: ${m.content}`)
      .join('\n\n');

    const filledUserTask = userTaskTemplate
      .replace('{{previousSummarySection}}', currentSummaryToBuildOn ? `PREVIOUS SUMMARY:\n${currentSummaryToBuildOn}\n\n` : '')
      .replace('{{conversationHistory}}', newMessagesString);

    const summarizerMessages: ModelMessage[] = this.genericPromptBuilder.buildMessages([
      {
        type: 'system_instruction',
        content: systemInstruction
      },
      {
        type: 'user_message',
        content: filledUserTask
      }
    ]).messages;

    try {
      const llmResult: LLMInvocationResult = await this.llmService.invokeModel(
        summarizerCategory,
        summarizerMessages,
        {
          temperature: agentConfig.defaultModelConfig?.temperature || 0.2,
          maxOutputTokens: agentConfig.defaultModelConfig?.maxOutputTokens || this.contextWindowConfig.maxSummaryTokens,
        }
      );

      if (llmResult.status === 'success' && llmResult.text) {
        let mappedTokenUsage: LLMTokenUsage | undefined = undefined;
        if (llmResult.details.tokenUsage) {
            const serviceUsage = llmResult.details.tokenUsage as ServiceLLMTokenUsage;
            mappedTokenUsage = {
                promptTokens: serviceUsage.promptTokens,
                completionTokens: serviceUsage.completionTokens,
                totalTokens: serviceUsage.totalTokens,
                cost: llmResult.details.cost?.totalCost
            };
        }

        return {
          success: true,
          newSummaryText: llmResult.text.trim(),
          summarizerTokenUsage: mappedTokenUsage,
          lastMessageIdInSummarizerInput: lastMessageIdFromNewMessages
        };
      } else {
        const errorMsg = llmResult.error?.message || 'Summarization LLM call failed without specific error.';
        return { success: false, error: errorMsg };
      }
    } catch (error: any) {
      console.error('ConversationHistoryService: Error during _performSummarization LLM call:', error);
      return { success: false, error: error.message || 'Unknown error during summarization LLM call.' };
    }
  }
} 