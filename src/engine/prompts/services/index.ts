/**
 * Prompt Engineering Services
 * 
 * This module provides orchestrating services for the prompt engineering subsystem,
 * including prompt building, context management, and rule selection.
 */

export { GenericPromptBuilder } from './GenericPromptBuilder';
export type {
  PromptComponent,
  PromptComponentRole,
  SystemInstructionComponent,
  UserMessageComponent,
  JsonSchemaInstructionComponent,
  TaggedDataComponent,
  TokenCountFunction,
  ChatTokenCountFunction
} from './types/genericPromptBuilder.types';

export { PromptBuilderService } from './PromptBuilderService';
export type {
  AgentCallSpecificData,
  PromptBuilderOptions,
  AgentConfigEntry,
  AgentConfigRegistry,
  TaskSession,
  N8nRule,
  KnowledgeChunkWithScore,
  KnowledgeRepository,
  TaskSessionManager,
  ContextWindowManagementResult,
  StandardPromptSections,
  PromptTemplate,
  PromptBuilderError,
  AgentConfigNotFoundError,
  TemplateNotFoundError,
  ContextWindowOverflowError,
  SummarizationFailedError,
  ContextWindowConfig,
  DEFAULT_CONTEXT_WINDOW_CONFIG
} from './types/promptBuilder.types';

export { RuleSelectorService } from './RuleSelectorService';
export type {
  RuleSelectionContext,
  SelectedRules,
  RuleSelectionOptions,
  KeywordExtractionOptions,
  KeywordExtractionResult,
  RuleScoringResult
} from './types/ruleSelectorService.types';

export { ConversationHistoryService } from './ConversationHistoryService';
export type {
  GetHistoryOptions,
  HistoryForPromptResult,
  InternalSummarizationOutcome,
  LLMTokenUsage
} from './types/conversationHistoryService.types';

export { AgentPromptBuilder } from './AgentPromptBuilder';
export type {
  AgentPromptOptions,
  AgentPromptMetadata,
  AgentAssembledPrompt
} from './AgentPromptBuilder';