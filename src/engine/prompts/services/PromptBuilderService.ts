/**
 * Prompt Builder Service
 * 
 * @deprecated This service is being replaced by AgentPromptBuilder as part of V3.3.1 architecture.
 * 
 * TODO: Replace this entire service with AgentPromptBuilder that:
 * - Uses static agent CORE_SYSTEM_INSTRUCTION instead of TemplateRegistry
 * - Orchestrates GenericPromptBuilder, ConversationHistoryService, and RuleSelectorService
 * - Provides prepareAgentLlmMessages() method for BaseAgent.prepareLlmMessages()
 * 
 * This service is responsible for dynamically constructing complete ModelMessage[] arrays
 * that AI agents pass to the LLMService for their LLM interactions. It ensures prompts are
 * well-structured, contextually relevant, include necessary instructions and schemas, and
 * respect token limits.
 * 
 * Key Responsibilities:
 * - Inject standardized sections (Team Awareness, Input Context, Output Format)
 * - Manage conversation history and context window limits
 * - Integrate with RuleSelectorService and KnowledgeRepository for contextual information
 * - Handle proactive summarization when context exceeds limits
 * - Format dynamic data with consistent tagging
 * 
 * Dependencies:
 * - LLMService (for invoking n8n-summarizer-agent and getting model capabilities)
 * - RuleSelectorService (for relevant rules)
 * - KnowledgeRepository (for RAG knowledge snippets)
 * - TaskSessionManager (for conversation history)
 * - AgentConfigRegistry (for agent configurations)
 * 
 * Usage Example:
 * ```typescript
 * const promptBuilder = new PromptBuilderService(dependencies);
 * const assembledPrompt = await promptBuilder.buildPromptMessagesForAgentCall(
 *   'n8n-architect-agent',
 *   currentGraphDefinition,
 *   taskSession,
 *   { userRequest: 'Create a workflow for email notifications' }
 * );
 * ```
 */

import { ModelMessage } from 'ai';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { encode as gptEncode, encodeChat as gptEncodeChat } from 'gpt-tokenizer';

import { LLMTaskCategory } from '../../graph/types/graph.types';
import { GraphDefinition } from '../../graph/types/graph.types';
import { LLMService } from '../../llm/LLMService';
import { 
  LLMInvocationResult
} from '../../llm/types/llmService.types';

// TODO: Remove these imports when replacing with AgentPromptBuilder
// Import prompt template system
// import { 
//   TemplateRegistry, 
//   defaultTemplateRegistry,
//   TemplateLoadError 
// } from '../../../prompts/TemplateRegistry';
// import { TemplateKey } from '../../../prompts/types/templates.types';

// Temporary stubs for deprecated TemplateRegistry system
interface TemplateRegistry {
  loadCommonTemplate(key: string): string;
  fillTemplate(template: string, variables: Record<string, any>): string;
  // TODO: Remove this stub method when replacing with AgentPromptBuilder
  loadAgentTemplate(agentSlug: string, templateType: string): Promise<string>;
}

const defaultTemplateRegistry: TemplateRegistry = {
  loadCommonTemplate: () => 'DEPRECATED TEMPLATE',
  fillTemplate: (template, vars) => template,
  // TODO: Remove this stub method when replacing with AgentPromptBuilder
  loadAgentTemplate: async () => 'DEPRECATED AGENT TEMPLATE'
};

import { RuleSelectorService } from './RuleSelectorService';

import {
  AgentCallSpecificData,
  PromptBuilderOptions,
  AssembledPrompt,
  AgentConfigEntry,
  AgentConfigRegistry,
  TaskSession,
  N8nRule,
  KnowledgeChunkWithScore,
  KnowledgeRepository,
  TaskSessionManager,
  RuleSelectionContext,
  ContextWindowManagementResult,
  TokenCountFunction,
  ChatTokenCountFunction,
  StandardPromptSections,
  // PromptTemplate, // TODO: Remove when replacing with AgentPromptBuilder
  PromptBuilderError,
  AgentConfigNotFoundError,
  // TemplateNotFoundError, // TODO: Remove when replacing with AgentPromptBuilder
  ContextWindowOverflowError,
  SummarizationFailedError,
  ContextWindowConfig,
  DEFAULT_CONTEXT_WINDOW_CONFIG
} from './types/promptBuilder.types';

/**
 * Main PromptBuilderService class
 */
export class PromptBuilderService {
  private readonly llmService: LLMService;
  private readonly ruleSelectorService: RuleSelectorService;
  private readonly knowledgeRepository: KnowledgeRepository;
  private readonly taskSessionManager: TaskSessionManager;
  private readonly agentConfigs: AgentConfigRegistry;
  private readonly contextWindowConfig: ContextWindowConfig;
  private readonly templateRegistry: TemplateRegistry;
  private readonly countTokens: TokenCountFunction;
  private readonly countChatTokens: ChatTokenCountFunction;

  constructor(
    llmService: LLMService,
    ruleSelectorService: RuleSelectorService,
    knowledgeRepository: KnowledgeRepository,
    taskSessionManager: TaskSessionManager,
    agentConfigs: AgentConfigRegistry,
    options: {
      contextWindowConfig?: ContextWindowConfig;
      templateRegistry?: TemplateRegistry;
      countTokens?: TokenCountFunction;
      countChatTokens?: ChatTokenCountFunction;
    } = {}
  ) {
    this.llmService = llmService;
    this.ruleSelectorService = ruleSelectorService;
    this.knowledgeRepository = knowledgeRepository;
    this.taskSessionManager = taskSessionManager;
    this.agentConfigs = agentConfigs;
    this.contextWindowConfig = options.contextWindowConfig || DEFAULT_CONTEXT_WINDOW_CONFIG;
    this.templateRegistry = options.templateRegistry || defaultTemplateRegistry;
    
    // Inject tokenizer functions or use defaults with direct gpt-tokenizer imports
    this.countTokens = options.countTokens || this.defaultCountTokensInternal.bind(this);
    this.countChatTokens = options.countChatTokens || this.defaultCountChatTokensInternal.bind(this);
  }

  /**
   * Main public method to build prompt messages for an agent call
   */
  public async buildPromptMessagesForAgentCall(
    agentSlug: string,
    currentGraphDefinition: GraphDefinition | null,
    taskSession: TaskSession,
    specificDataForThisLLMCall: AgentCallSpecificData,
    options?: PromptBuilderOptions
  ): Promise<AssembledPrompt> {
    try {
      // 1. Get agent configuration
      const agentConfig = this.getAgentConfig(agentSlug);

      // 2. Get target model capabilities from LLMService
      const modelCapabilities = await this.llmService.getModelCapabilitiesForCategory(
        agentConfig.defaultLlmTaskCategory
      );

      // 3. Prepare standard sections
      const standardSections = await this.prepareStandardSections(
        agentSlug,
        currentGraphDefinition,
        specificDataForThisLLMCall,
        options
      );

      // 4. Fetch contextual data
      const rulesText = await this.fetchRelevantRules(agentSlug, specificDataForThisLLMCall);
      const knowledgeText = await this.fetchRelevantKnowledge(specificDataForThisLLMCall, options);

      // 5. Format specific call data
      const formattedSpecificData = this.formatDynamicData(specificDataForThisLLMCall);

      // 6. Create preliminary system message
      const preliminarySystemMessage = await this.createSystemMessage(
        agentConfig,
        standardSections,
        rulesText,
        knowledgeText,
        formattedSpecificData
      );

      // 7. Create core task message
      const coreTaskMessage = this.extractCoreTaskMessage(specificDataForThisLLMCall);

      // 8. Manage context window and get history
      const preliminaryMessages: ModelMessage[] = [
        preliminarySystemMessage,
        { role: 'user', content: coreTaskMessage }
      ];

      const contextResult = await this.manageContextWindowAndSummarizeIfNeeded(
        taskSession,
        preliminaryMessages,
        modelCapabilities.modelId,
        modelCapabilities.contextWindow,
        options?.maxContextTokensForThisCall
      );

      // 9. Assemble final messages
      const finalMessages = this.assembleFinalMessages(
        preliminarySystemMessage,
        contextResult.historyMessagesForPrompt,
        coreTaskMessage
      );

      // 10. Calculate final token count
      const promptTokenCount = this.countChatTokens(finalMessages);

      return {
        messages: finalMessages,
        promptTokenCount,
        contextWasSummarized: contextResult.summarized
      };

    } catch (error: any) {
      if (error instanceof PromptBuilderError) {
        throw error;
      }
      throw new PromptBuilderError(
        `Failed to build prompt for agent ${agentSlug}: ${error.message}`,
        'PromptBuildingError',
        error
      );
    }
  }

  // === Private Helper Methods ===

  /**
   * Get agent configuration
   */
  private getAgentConfig(agentSlug: string): AgentConfigEntry {
    const config = this.agentConfigs[agentSlug];
    if (!config) {
      throw new AgentConfigNotFoundError(agentSlug);
    }
    return config;
  }

  /**
   * Prepare standard prompt sections
   */
  private async prepareStandardSections(
    agentSlug: string,
    graphDefinition: GraphDefinition | null,
    specificData: AgentCallSpecificData,
    options?: PromptBuilderOptions
  ): Promise<StandardPromptSections> {
    const teamAwareness = this.generateTeamAwarenessBlock(agentSlug, graphDefinition);
    const inputContextDefinition = this.generateInputContextDefinitionBlock(
      Object.keys(specificData)
    );
    const outputFormatInstruction = this.generateOutputFormatInstructionBlock(
      agentSlug,
      options?.overrideOutputJsonSchemaString
    );

    return {
      teamAwareness,
      inputContextDefinition,
      outputFormatInstruction
    };
  }

  /**
   * Generate team awareness block using template system
   */
  private generateTeamAwarenessBlock(
    currentAgentSlug: string,
    graphDefinition: GraphDefinition | null
  ): string {
    const agentConfig = this.agentConfigs[currentAgentSlug];
    const agentRole = agentConfig?.roleDescription || `${currentAgentSlug} agent`;

    if (!graphDefinition) {
      return this.templateRegistry.fillTemplate(
        this.templateRegistry.loadCommonTemplate('TEAM_AWARENESS'),
        {
          agentRole,
          graphDescription: 'You are working independently on this task.',
          otherAgents: ''
        }
      );
    }

    const graphDescription = graphDefinition.description || 'Current workflow';
    
    // Find other agents in the graph
    const otherAgentNodes = graphDefinition.nodes?.filter(
      (node): node is any => node.type === 'agentCall' && 
                             (node as any).agentSlug && 
                             (node as any).agentSlug !== currentAgentSlug
    ) || [];

    let otherAgentsText = '';
    if (otherAgentNodes.length > 0) {
      const otherAgentDescriptions = otherAgentNodes.map(node => {
        const agentSlug = (node as any).agentSlug;
        const otherAgentConfig = this.agentConfigs[agentSlug];
        const otherAgentRole = otherAgentConfig?.roleDescription || `${agentSlug} agent`;
        return `- ${otherAgentRole} (${agentSlug})`;
      });
      
      otherAgentsText = `\n\nOther agents in this workflow:\n${otherAgentDescriptions.join('\n')}`;
    }

    return this.templateRegistry.fillTemplate(
      this.templateRegistry.loadCommonTemplate('TEAM_AWARENESS'),
      {
        agentRole,
        graphDescription,
        otherAgents: otherAgentsText
      }
    );
  }

  /**
   * Generate input context definition block using template system
   */
  private generateInputContextDefinitionBlock(inputKeys: string[]): string {
    if (inputKeys.length === 0) {
      return this.templateRegistry.fillTemplate(
        this.templateRegistry.loadCommonTemplate('INPUT_CONTEXT_DEFINITION'),
        {
          inputContext: 'No specific input data provided for this call.'
        }
      );
    }

    const inputList = inputKeys.map(key => `- ${key}`).join('\n');
    
    return this.templateRegistry.fillTemplate(
      this.templateRegistry.loadCommonTemplate('INPUT_CONTEXT_DEFINITION'),
      {
        inputContext: `Available input data fields:\n${inputList}`
      }
    );
  }

  /**
   * Generate output format instruction block using template system
   */
  private generateOutputFormatInstructionBlock(
    agentSlug: string,
    overrideSchemaString?: string
  ): string {
    let jsonSchemaString = overrideSchemaString;

    if (!jsonSchemaString) {
      const agentConfig = this.agentConfigs[agentSlug];
      if (agentConfig?.outputSchema) {
        // Convert Zod schema to JSON schema
        jsonSchemaString = JSON.stringify(
          zodToJsonSchema(agentConfig.outputSchema, "AgentOutputSchema"), 
          null, 
          2
        );
      }
    }

    if (!jsonSchemaString) {
      // Default fallback schema
      jsonSchemaString = JSON.stringify({
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['success', 'failure'] },
          result: { type: 'object' }
        },
        required: ['status', 'result']
      }, null, 2);
    }

    return this.templateRegistry.fillTemplate(
      this.templateRegistry.loadCommonTemplate('OUTPUT_FORMAT_INSTRUCTION'),
      {
        jsonSchema: jsonSchemaString
      }
    );
  }

  /**
   * Fetch relevant rules for the agent (updated interface)
   */
  private async fetchRelevantRules(
    agentSlug: string, 
    specificData: AgentCallSpecificData
  ): Promise<string> {
    try {
      const context: RuleSelectionContext = {
        agentSlug,
        taskCategory: this.agentConfigs[agentSlug]?.defaultLlmTaskCategory,
        contextData: specificData,
        userRequest: this.extractUserRequest(specificData)
      };

      const rules = await this.ruleSelectorService.getRulesForAgentPrompt(context);
      
      if (rules.length === 0) {
        return '';
      }

      let rulesText = '# Relevant Rules and Best Practices\n';
      for (const rule of rules) {
        rulesText += `- ${rule.name}: ${rule.descriptionForLLM}\n`;
      }

      return rulesText;
    } catch (error: any) {
      // Non-critical error, continue without rules
      console.warn(`Failed to fetch rules for agent ${agentSlug}:`, error);
      return '';
    }
  }

  /**
   * Extract user request from specific data for rule context
   */
  private extractUserRequest(specificData: AgentCallSpecificData): string | undefined {
    const userRequestKeys = [
      'userRequest', 'userInput', '_USER_FULL_REQUEST', 'request', 'input'
    ];

    for (const key of userRequestKeys) {
      if (specificData[key] && typeof specificData[key] === 'string') {
        return specificData[key];
      }
    }

    return undefined;
  }

  /**
   * Fetch relevant knowledge snippets (updated interface)
   */
  private async fetchRelevantKnowledge(
    specificData: AgentCallSpecificData,
    options?: PromptBuilderOptions
  ): Promise<string> {
    try {
      // Skip if disabled in options
      if (options?.includeKnowledgeSnippets === false) {
        return '';
      }

      // Extract key terms for knowledge search
      const searchQuery = this.extractSearchQuery(specificData);
      
      if (!searchQuery) {
        return '';
      }

      const knowledgeChunks = await this.knowledgeRepository.retrieveSnippets(
        searchQuery,
        3 // Limit to top 3 most relevant chunks
      );

      if (knowledgeChunks.length === 0) {
        return '';
      }

      let knowledgeText = '# Relevant Knowledge\n';
      for (const chunk of knowledgeChunks) {
        knowledgeText += `${chunk.chunkText}\n\n`;
      }

      return knowledgeText;
    } catch (error: any) {
      // Non-critical error, continue without knowledge
      console.warn(`Failed to fetch knowledge snippets:`, error);
      return '';
    }
  }

  /**
   * Extract search query from specific data
   */
  private extractSearchQuery(specificData: AgentCallSpecificData): string {
    // Look for common keys that might contain search-worthy content
    const searchKeys = [
      'userRequest', 'userInput', 'requirement', 'task', 'description',
      '_USER_FULL_REQUEST', '_CORE_AGENT_TASK_DESCRIPTION'
    ];

    for (const key of searchKeys) {
      if (specificData[key] && typeof specificData[key] === 'string') {
        return specificData[key].substring(0, 200); // Limit query length
      }
    }

    // Fallback: concatenate all string values
    const allStrings = Object.values(specificData)
      .filter(value => typeof value === 'string')
      .join(' ');

    return allStrings.substring(0, 200);
  }

  /**
   * Format dynamic data with XML-like tags
   */
  private formatDynamicData(data: AgentCallSpecificData): string {
    if (Object.keys(data).length === 0) {
      return '';
    }

    let formatted = '# Context Data\n';
    
    for (const [key, value] of Object.entries(data)) {
      formatted += `<${key.toLowerCase()}>\n`;
      
      if (typeof value === 'object') {
        formatted += JSON.stringify(value, null, 2);
      } else {
        formatted += String(value);
      }
      
      formatted += `\n</${key.toLowerCase()}>\n\n`;
    }

    return formatted;
  }

  /**
   * Create system message using template system
   */
  private async createSystemMessage(
    agentConfig: AgentConfigEntry,
    standardSections: StandardPromptSections,
    rulesText: string,
    knowledgeText: string,
    formattedSpecificData: string
  ): Promise<ModelMessage> {
    const templateData: Record<string, string> = {
      agentRole: agentConfig.roleDescription || `${agentConfig.slug} agent`,
      teamAwareness: standardSections.teamAwareness,
      inputContextDefinition: standardSections.inputContextDefinition,
      outputFormatInstruction: standardSections.outputFormatInstruction,
      rulesSection: rulesText ? `\n${rulesText}` : '',
      knowledgeSection: knowledgeText ? `\n${knowledgeText}` : '',
      specificDataSection: formattedSpecificData ? `\n${formattedSpecificData}` : ''
    };

    // Load agent-specific system template with automatic fallback to SYSTEM_PROMPT_BASE
    const systemTemplateContent = await this.templateRegistry.loadAgentTemplate(agentConfig.slug, 'system');
    const content = this.templateRegistry.fillTemplate(systemTemplateContent, templateData);

    return {
      role: 'system',
      content: content.trim()
    };
  }

  /**
   * Extract core task message
   */
  private extractCoreTaskMessage(specificData: AgentCallSpecificData): string {
    // Look for the main task description
    const taskKeys = [
      '_CORE_AGENT_TASK_DESCRIPTION',
      'task',
      'userRequest',
      'instruction'
    ];

    for (const key of taskKeys) {
      if (specificData[key] && typeof specificData[key] === 'string') {
        return specificData[key];
      }
    }

    // Fallback to a generic message
    return 'Please process the provided context data and complete your assigned task.';
  }

  /**
   * Manage context window and summarize if needed (improved logic)
   */
  private async manageContextWindowAndSummarizeIfNeeded(
    taskSession: TaskSession,
    currentMessagesWithoutHistory: ModelMessage[],
    targetModelId: string,
    contextWindowSize: number,
    maxContextTokensForThisCall?: number
  ): Promise<ContextWindowManagementResult> {
    const maxContextTokens = maxContextTokensForThisCall || 
      Math.floor(contextWindowSize * this.contextWindowConfig.summarizationThreshold);

    // Count tokens for current messages
    const currentTokenCount = this.countChatTokens(currentMessagesWithoutHistory);

    // Get conversation history
    const summary = taskSession.llmContextSummary || '';
    const recentHistory = taskSession.llmContextRelevantHistory || [];

    // Start with summary
    let historyMessages: ModelMessage[] = [];
    if (summary) {
      historyMessages.push({
        role: 'assistant',
        content: `## Previous Conversation Summary\n${summary}`
      });
    }

    // Add recent history if there's room
    const availableTokens = maxContextTokens - currentTokenCount;
    let usedTokens = this.countChatTokens(historyMessages);

    for (const message of recentHistory) {
      const messageTokens = this.countChatTokens([message]);
      
      if (usedTokens + messageTokens <= availableTokens) {
        historyMessages.push(message);
        usedTokens += messageTokens;
      } else {
        break; // No more room
      }
    }

    // Check if we need to summarize
    const totalTokens = currentTokenCount + usedTokens;
    
    if (totalTokens > maxContextTokens && recentHistory.length > 0) {
      try {
        // Trigger summarization
        const newSummary = await this.triggerSummarization(taskSession);
        
        // Calculate how many recent messages to keep after summarization
        const summaryTokens = this.countTokens(newSummary);
        const remainingTokensForHistory = availableTokens - summaryTokens;
        
        // Keep only the most recent messages that fit
        const keptMessages: ModelMessage[] = [];
        let historyTokenCount = 0;
        
        for (let i = recentHistory.length - 1; i >= 0 && keptMessages.length < this.contextWindowConfig.maxHistoryMessagesAfterSummarization; i--) {
          const message = recentHistory[i];
          const messageTokens = this.countChatTokens([message]);
          
          if (historyTokenCount + messageTokens <= remainingTokensForHistory) {
            keptMessages.unshift(message); // Add to beginning to maintain order
            historyTokenCount += messageTokens;
          } else {
            break;
          }
        }
        
        // Update task session
        const updatedTaskSession = {
          ...taskSession,
          llmContextSummary: newSummary,
          llmContextRelevantHistory: keptMessages,
          updatedAt: new Date()
        };

        await this.taskSessionManager.updateTaskSession(updatedTaskSession);

        // Create new history with summary and kept messages
        const newHistoryMessages: ModelMessage[] = [
          {
            role: 'assistant',
            content: `## Previous Conversation Summary\n${newSummary}`
          },
          ...keptMessages
        ];

        return {
          updatedTaskSession,
          historyMessagesForPrompt: newHistoryMessages,
          summarized: true
        };

      } catch (error: any) {
        throw new SummarizationFailedError(
          `Failed to summarize conversation history: ${error.message}`,
          error
        );
      }
    }

    return {
      updatedTaskSession: taskSession,
      historyMessagesForPrompt: historyMessages,
      summarized: false
    };
  }

  /**
   * Trigger summarization using the n8n-summarizer-agent
   */
  private async triggerSummarization(taskSession: TaskSession): Promise<string> {
    const summaryContent = [
      taskSession.llmContextSummary || '',
      ...((taskSession.llmContextRelevantHistory || []).map(msg => 
        `${msg.role}: ${msg.content}`
      ))
    ].filter(Boolean).join('\n\n');

    // Prepare messages for summarizer agent
    const messages: ModelMessage[] = [
      {
        role: 'system',
        content: 'You are a conversation summarizer. Create a concise summary of the conversation history that preserves key context and decisions.'
      },
      {
        role: 'user',
        content: `Please summarize the following conversation history, focusing on key decisions, progress made, and important context:\n\n${summaryContent}`
      }
    ];

    // Call LLMService to invoke summarizer agent
    const result = await this.llmService.invokeModel(
      LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
      messages,
      {
        temperature: 0.1,
        maxOutputTokens: this.contextWindowConfig.maxSummaryTokens
      }
    );

    if (result.status !== 'success' || !result.content) {
      const errorMessage = result.status === 'failure' && result.error 
        ? result.error.message 
        : 'No content returned';
      throw new SummarizationFailedError(`Summarization failed: ${errorMessage}`);
    }

    return typeof result.content === 'string' ? result.content : JSON.stringify(result.content);
  }

  /**
   * Assemble final messages
   */
  private assembleFinalMessages(
    systemMessage: ModelMessage,
    historyMessages: ModelMessage[],
    coreTaskMessage: string
  ): ModelMessage[] {
    const messages: ModelMessage[] = [systemMessage];
    
    // Add history if present
    if (historyMessages.length > 0) {
      messages.push(...historyMessages);
    }
    
    // Add the current task
    messages.push({
      role: 'user',
      content: coreTaskMessage
    });

    return messages;
  }

  /**
   * Default token counting function using gpt-tokenizer.
   * Marked private as it's the internal default; public access is via this.countTokens.
   */
  private defaultCountTokensInternal(text: string, modelId?: string): number {
    try {
      return gptEncode(text).length;
    } catch (error) {
      console.warn(`PromptBuilderService: gpt-tokenizer 'encode' failed for text. Falling back to char approximation. Error: ${error instanceof Error ? error.message : String(error)}`);
      return Math.ceil(text.length / 4);
    }
  }

  /**
   * Default chat token counting function using gpt-tokenizer.
   * Marked private as it's the internal default; public access is via this.countChatTokens.
   */
  private defaultCountChatTokensInternal(messages: ModelMessage[], modelId?: string): number {
    try {
      const chatMessages = messages.map(msg => ({
        role: msg.role === 'system' || msg.role === 'user' || msg.role === 'assistant' 
          ? msg.role 
          : 'assistant', // Default to assistant for tool/function roles
        content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
      }));
      // encodeChat might expect a specific model family hint for best results,
      // but gpt-tokenizer often defaults to a common GPT model.
      // We use a simple check for 'gpt-' prefix as a hint.
      const tokenizerModelHint = modelId?.startsWith('gpt-') ? modelId as any : 'gpt-4';
      return gptEncodeChat(chatMessages, tokenizerModelHint).length;
    } catch (error) {
      console.warn(`PromptBuilderService: gpt-tokenizer 'encodeChat' failed. Falling back to summing individual message tokens. Error: ${error instanceof Error ? error.message : String(error)}`);
      return messages.reduce((total, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        // Use the already defined this.countTokens which includes its own fallback for the sum
        return total + this.countTokens(content);
      }, 0);
    }
  }
} 