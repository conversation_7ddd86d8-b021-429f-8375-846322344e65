/**
 * Agent Prompt Builder Service
 * 
 * The primary service used by BaseAgent to construct complete, context-aware ModelMessage[]
 * arrays for agent LLM calls. This service orchestrates multiple services to build 
 * comprehensive prompts with history, rules, team awareness, and structured output instructions.
 * 
 * Key Responsibilities:
 * - Orchestrate ConversationHistoryService for managed history (with auto-summarization)
 * - Coordinate RuleSelectorService for relevant rules injection
 * - Generate team awareness context from graph definitions
 * - Format input context and call-specific data
 * - Use GenericPromptBuilder for final message assembly
 * - Support structured output schema instructions
 * 
 * Dependencies:
 * - GenericPromptBuilder: For final message assembly
 * - ConversationHistoryService: For conversation history management and summarization
 * - RuleSelectorService: For selecting relevant rules
 * 
 * Usage Example:
 * ```typescript
 * const promptBuilder = new AgentPromptBuilder(genericBuilder, historyService, ruleSelector);
 * const assembledPrompt = await promptBuilder.prepareAgentLlmMessages(
 *   'n8n-architect-agent',
 *   coreSystemInstruction,
 *   'Plan a workflow for user requirements',
 *   taskSession,
 *   graphDefinition,
 *   { userRequirements: '...' },
 *   { outputSchema: ArchitectOutputSchema }
 * );
 * ```
 */

import { ZodTypeAny } from 'zod';
import { ModelMessage } from 'ai';
import { GenericPromptBuilder, AssembledPrompt } from './GenericPromptBuilder';
import { ConversationHistoryService } from './ConversationHistoryService';
import { RuleSelectorService } from './RuleSelectorService';
import {
  PromptComponent,
  SystemInstructionComponent,
  UserMessageComponent,
  TextBlockComponent,
  TaggedDataComponent,
  JsonSchemaInstructionComponent,
  HistoryBlockComponent
} from './types/genericPromptBuilder.types';
import { TaskSession } from '../../../types/tasks.types';
import { GraphDefinition } from '../../graph/types/graph.types';
import { N8nRule } from '../../../types/rules.types';
import { RuleSelectionOptions } from './types/ruleSelectorService.types';

/**
 * Options for configuring agent prompt building
 */
export interface AgentPromptOptions {
  /** Override default system instruction */
  systemInstruction?: string;
  
  /** Output schema for structured responses */
  outputSchema?: ZodTypeAny;
  
  /** Target model for context sizing (used by ConversationHistoryService) */
  targetModel?: string;
  
  /** Rule selection options */
  ruleSelectionOptions?: RuleSelectionOptions;
  
  /** Context management options */
  maxHistoryTokens?: number;
  forceNewSummarization?: boolean;
  
  /** Debug options */
  includeDebugInfo?: boolean;
  
  /** Advanced options */
  excludeTeamAwareness?: boolean;
  customContextData?: Record<string, any>;
  priorityRules?: string[];
}

/**
 * Metadata about the agent prompt building process
 */
export interface AgentPromptMetadata {
  /** Agent slug that the prompt was built for */
  agentSlug: string;
  
  /** Whether summarization was triggered during history retrieval */
  summaryWasPerformed: boolean;
  
  /** Number of rules included in the prompt */
  rulesIncluded: number;
  
  /** Team awareness was included */
  teamAwarenessIncluded: boolean;
  
  /** Final token count if available */
  finalTokenCount?: number;
  
  /** Processing time in milliseconds */
  processingTimeMs: number;
  
  /** Warnings or issues during prompt building */
  warnings: string[];
}

/**
 * Extended assembled prompt with agent-specific metadata
 */
export interface AgentAssembledPrompt extends AssembledPrompt {
  /** Agent-specific metadata */
  agentMetadata: AgentPromptMetadata;
}

/**
 * AgentPromptBuilder - Orchestrates the complete prompt building process for agents
 */
export class AgentPromptBuilder {
  private readonly genericPromptBuilder: GenericPromptBuilder;
  private readonly conversationHistoryService: ConversationHistoryService;
  private readonly ruleSelectorService: RuleSelectorService;

  constructor(
    genericPromptBuilder: GenericPromptBuilder,
    conversationHistoryService: ConversationHistoryService,
    ruleSelectorService: RuleSelectorService
  ) {
    this.genericPromptBuilder = genericPromptBuilder;
    this.conversationHistoryService = conversationHistoryService;
    this.ruleSelectorService = ruleSelectorService;
  }

  /**
   * Main method to prepare complete LLM messages for an agent call
   * 
   * @param agentSlug - Unique identifier for the agent
   * @param coreSystemInstruction - The agent's core system instruction
   * @param subTaskUserInstruction - Specific instruction for this LLM call
   * @param taskSession - Current task session context
   * @param graphDefinition - Current graph definition for team awareness
   * @param callSpecificData - Data specific to this agent call
   * @param options - Optional configuration for prompt building
   * @returns Promise resolving to assembled prompt with metadata
   */
  public async prepareAgentLlmMessages(
    agentSlug: string,
    coreSystemInstruction: string,
    subTaskUserInstruction: string,
    taskSession: TaskSession,
    graphDefinition: GraphDefinition,
    callSpecificData: Record<string, any>,
    options: AgentPromptOptions = {}
  ): Promise<AgentAssembledPrompt> {
    const startTime = Date.now();
    const warnings: string[] = [];
    
    try {
      // Step 1: Gather context components
      const [historyResult, relevantRules, teamAwarenessContext] = await Promise.all([
        this.getConversationHistory(taskSession, options, warnings),
        this.getRelevantRules(agentSlug, subTaskUserInstruction, options, warnings),
        this.buildTeamAwarenessContext(graphDefinition, options, warnings)
      ]);

      // Ensure we have default values for missing components
      const safeHistoryResult = historyResult || { historyMessages: [], summaryWasPerformedThisCall: false };
      const safeRelevantRules = relevantRules || [];
      const safeTeamAwarenessContext = teamAwarenessContext || '';

      // Step 2: Assemble prompt components
      const promptComponents = this.assemblePromptComponents(
        coreSystemInstruction,
        subTaskUserInstruction,
        callSpecificData,
        safeHistoryResult.historyMessages,
        safeRelevantRules,
        safeTeamAwarenessContext,
        options,
        warnings
      );

      // Step 3: Build final messages using GenericPromptBuilder
      const assembledPrompt = this.genericPromptBuilder.buildMessages(promptComponents, {
        mergeOptions: { mergeSystemMessages: true },
        includeDebugInfo: options.includeDebugInfo,
        targetModelId: options.targetModel
      });

      // Step 4: Create agent-specific metadata
      const agentMetadata: AgentPromptMetadata = {
        agentSlug,
        summaryWasPerformed: safeHistoryResult.summaryWasPerformedThisCall || false,
        rulesIncluded: safeRelevantRules.length,
        teamAwarenessIncluded: !options.excludeTeamAwareness && safeTeamAwarenessContext.length > 0,
        finalTokenCount: assembledPrompt.tokenCount,
        processingTimeMs: Date.now() - startTime,
        warnings
      };

      return {
        ...assembledPrompt,
        agentMetadata
      };

    } catch (error) {
      const errorMessage = `Failed to prepare agent LLM messages: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(`AgentPromptBuilder: ${errorMessage}`, error);
      throw new Error(errorMessage);
    }
  }

  /**
   * Get managed conversation history with auto-summarization
   * @private
   */
  private async getConversationHistory(
    taskSession: TaskSession,
    options: AgentPromptOptions,
    warnings: string[]
  ): Promise<{ historyMessages: ModelMessage[]; summaryWasPerformedThisCall: boolean }> {
    try {
      if (!taskSession.chatId) {
        warnings.push('No chat ID found in task session, skipping conversation history');
        return { historyMessages: [], summaryWasPerformedThisCall: false };
      }

      const historyOptions = {
        targetModelContextWindow: 8000, // Default context window
        tokensAvailableForHistory: options.maxHistoryTokens || 4000,
        forceSummarization: options.forceNewSummarization || false,
      };

      const historyResult = await this.conversationHistoryService.getHistoryForPrompt(
        taskSession.chatId,
        taskSession.id,
        historyOptions
      );

      return {
        historyMessages: historyResult.historyMessages,
        summaryWasPerformedThisCall: historyResult.summaryWasPerformedThisCall
      };

    } catch (error) {
      warnings.push(`Failed to retrieve conversation history: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { historyMessages: [], summaryWasPerformedThisCall: false };
    }
  }

  /**
   * Get relevant rules for the agent and context
   * @private
   */
  private async getRelevantRules(
    agentSlug: string,
    subTaskUserInstruction: string,
    options: AgentPromptOptions,
    warnings: string[]
  ): Promise<N8nRule[]> {
    try {
      const ruleSelectionContext = {
        agentSlug,
        taskDescription: subTaskUserInstruction,
        // Add nodeTypes from callSpecificData if available
        // nodeTypes: extractNodeTypesFromData(callSpecificData)
      };

      const rules = await this.ruleSelectorService.getRulesForAgentPrompt(
        ruleSelectionContext,
        options.ruleSelectionOptions
      );

      // Filter priority rules if specified
      if (options.priorityRules && options.priorityRules.length > 0) {
        const priorityRuleSet = new Set(options.priorityRules);
        const priorityRules = rules.filter(rule => priorityRuleSet.has(rule.id));
        const remainingRules = rules.filter(rule => !priorityRuleSet.has(rule.id));
        return [...priorityRules, ...remainingRules];
      }

      return rules;

    } catch (error) {
      warnings.push(`Failed to retrieve relevant rules: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Build team awareness context from graph definition
   * @private
   */
  private async buildTeamAwarenessContext(
    graphDefinition: GraphDefinition,
    options: AgentPromptOptions,
    warnings: string[]
  ): Promise<string> {
    try {
      if (options.excludeTeamAwareness) {
        return '';
      }

      // Try to load team awareness template
      let template: string;
      try {
        // For now, use a known template key or fallback to default
        // TODO: Add TEAM_AWARENESS_TEMPLATE_V1 to PromptPartKey type when available
        template = this.getDefaultTeamAwarenessTemplate();
        warnings.push('Using default team awareness template (advanced template not yet available)');
      } catch (error) {
        // Fallback to basic template if not found
        template = this.getDefaultTeamAwarenessTemplate();
        warnings.push('Using default team awareness template (advanced template not found)');
      }

      // Extract agent information from graph
      const agentNodes = graphDefinition.nodes.filter(node => 
        node.type === 'agentCall'
      );

      const agentDescriptions = agentNodes.map(node => {
        const agentConfig = node as any; // Type assertion for agentCall node
        return `- ${agentConfig.agentSlug || 'Unknown Agent'}: ${agentConfig.description || 'No description available'}`;
      }).join('\n');

      // Fill template with actual data
      const context = template
        .replace('{graphId}', graphDefinition.id || 'Unknown Graph')
        .replace('{graphDescription}', graphDefinition.description || 'No description available')
        .replace('{agentDescriptions}', agentDescriptions || 'No other agents in this graph')
        .replace('{totalAgents}', agentNodes.length.toString());

      return context;

    } catch (error) {
      warnings.push(`Failed to build team awareness context: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return '';
    }
  }

  /**
   * Get default team awareness template as fallback
   * @private
   */
  private getDefaultTeamAwarenessTemplate(): string {
    return `## Team Awareness Context

You are part of an AI agent team working on graph "{graphId}".

**Graph Purpose:** {graphDescription}

**Your Team Members:**
{agentDescriptions}

**Total Agents in Graph:** {totalAgents}

**Collaboration Guidelines:**
- Work collaboratively with other agents in the graph
- Your output will be used by subsequent agents
- Focus on your specific role while considering the overall workflow
- Provide clear, structured outputs that other agents can consume`;
  }

  /**
   * Assemble all prompt components in the correct order
   * @private
   */
  private assemblePromptComponents(
    coreSystemInstruction: string,
    subTaskUserInstruction: string,
    callSpecificData: Record<string, any>,
    historyMessages: ModelMessage[],
    relevantRules: N8nRule[],
    teamAwarenessContext: string,
    options: AgentPromptOptions,
    warnings: string[]
  ): PromptComponent[] {
    const components: PromptComponent[] = [];

    // 1. Core system instruction (potentially overridden)
    const systemInstruction = options.systemInstruction || coreSystemInstruction;
    components.push({
      type: 'system_instruction',
      content: systemInstruction
    } as SystemInstructionComponent);

    // 2. Team awareness block (if available and not excluded)
    if (teamAwarenessContext && !options.excludeTeamAwareness) {
      components.push({
        type: 'text_block',
        role: 'system',
        content: teamAwarenessContext
      } as TextBlockComponent);
    }

    // 3. Input context definition
    if (Object.keys(callSpecificData).length > 0) {
      components.push({
        type: 'tagged_data',
        role: 'system',
        tag: 'CURRENT_INPUTS',
        data: callSpecificData
      } as TaggedDataComponent);
    }

    // 4. Custom context data (if provided)
    if (options.customContextData && Object.keys(options.customContextData).length > 0) {
      components.push({
        type: 'tagged_data',
        role: 'system',
        tag: 'ADDITIONAL_CONTEXT',
        data: options.customContextData
      } as TaggedDataComponent);
    }

    // 5. Relevant rules
    if (relevantRules.length > 0) {
      const rulesComponents = this.formatRulesAsComponents(relevantRules);
      components.push(...rulesComponents);
    }

    // 6. Conversation history
    if (historyMessages.length > 0) {
      components.push({
        type: 'history_block',
        messages: historyMessages
      } as HistoryBlockComponent);
    }

    // 7. Current task instruction
    components.push({
      type: 'user_message',
      content: subTaskUserInstruction
    } as UserMessageComponent);

    // 8. Output schema instruction (if provided)
    if (options.outputSchema) {
      try {
        const outputSchemaComponent = this.buildOutputSchemaComponent(options.outputSchema);
        components.push(outputSchemaComponent);
      } catch (error) {
        warnings.push(`Failed to generate output schema instruction: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return components;
  }

  /**
   * Convert N8nRule objects to prompt components
   * @private
   */
  private formatRulesAsComponents(rules: N8nRule[]): PromptComponent[] {
    const components: PromptComponent[] = [];

    // Group rules by type
    const alwaysApplyRules = rules.filter(rule => rule.type === 'ALWAYS_APPLY');
    const contextualRules = rules.filter(rule => rule.type === 'CONTEXTUAL_SUGGESTION');
    const antiPatternRules = rules.filter(rule => rule.type === 'ANTI_PATTERN_CHECK');

    // Add always-apply rules
    if (alwaysApplyRules.length > 0) {
      const rulesContent = alwaysApplyRules.map(rule => 
        `**${rule.name}** (${rule.priority}): ${rule.description}`
      ).join('\n\n');

      components.push({
        type: 'text_block',
        role: 'system',
        content: `## Required Guidelines\n\n${rulesContent}`
      } as TextBlockComponent);
    }

    // Add contextual rules
    if (contextualRules.length > 0) {
      const rulesContent = contextualRules.map(rule => 
        `**${rule.name}** (${rule.priority}): ${rule.description}`
      ).join('\n\n');

      components.push({
        type: 'text_block',
        role: 'system',
        content: `## Contextual Recommendations\n\n${rulesContent}`
      } as TextBlockComponent);
    }

    // Add anti-pattern rules
    if (antiPatternRules.length > 0) {
      const rulesContent = antiPatternRules.map(rule => 
        `**${rule.name}** (${rule.priority}): ${rule.description}`
      ).join('\n\n');

      components.push({
        type: 'text_block',
        role: 'system',
        content: `## Common Pitfalls to Avoid\n\n${rulesContent}`
      } as TextBlockComponent);
    }

    return components;
  }

  /**
   * Build output schema instruction component
   * @private
   */
  private buildOutputSchemaComponent(schema: ZodTypeAny): JsonSchemaInstructionComponent {
    try {
      return {
        type: 'json_schema_instruction',
        role: 'system',
        instructionPrefix: 'Your response MUST conform to the following JSON schema:',
        zodSchema: schema,
        schemaName: 'AgentOutput'
      } as JsonSchemaInstructionComponent;

    } catch (error) {
      throw new Error(`Failed to convert Zod schema to JSON schema: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Utility method to get prompt statistics
   */
  public async getPromptStatistics(
    agentSlug: string,
    taskSession: TaskSession,
    _callSpecificData: Record<string, any>,
    options: AgentPromptOptions = {}
  ): Promise<{
    estimatedComponents: number;
    estimatedHistoryMessages: number;
    estimatedRulesCount: number;
    estimatedTokens?: number;
  }> {
    try {
      // Get component counts without building full prompt
      const [historyResult, relevantRules] = await Promise.all([
        taskSession.chatId ? this.conversationHistoryService.getHistoryForPrompt(
          taskSession.chatId,
          taskSession.id,
          { 
            targetModelContextWindow: 8000, // Default context window
            tokensAvailableForHistory: options.maxHistoryTokens || 4000 
          }
        ) : Promise.resolve({ historyMessages: [], summaryWasPerformedThisCall: false }),
        this.ruleSelectorService.getRulesForAgentPrompt({
          agentSlug,
          taskDescription: ''
        }, options.ruleSelectionOptions)
      ]);

      // Estimate component count
      let componentCount = 3; // System instruction, user message, input context
      if (historyResult.historyMessages.length > 0) componentCount++;
      if (relevantRules.length > 0) componentCount += Math.ceil(relevantRules.length / 10); // Group rules
      if (!options.excludeTeamAwareness) componentCount++;
      if (options.outputSchema) componentCount++;
      if (options.customContextData) componentCount++;

      return {
        estimatedComponents: componentCount,
        estimatedHistoryMessages: historyResult.historyMessages.length,
        estimatedRulesCount: relevantRules.length
      };

    } catch (error) {
      console.error('AgentPromptBuilder: Failed to get prompt statistics:', error);
      return {
        estimatedComponents: 0,
        estimatedHistoryMessages: 0,
        estimatedRulesCount: 0
      };
    }
  }
}