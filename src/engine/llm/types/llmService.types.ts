/**
 * LLM Service Types
 * 
 * This file defines all TypeScript interfaces and types for the LLMService component.
 * These types provide comprehensive coverage for LLM interactions, token usage tracking,
 * cost estimation, error handling, and streaming responses.
 */

import { 
  LanguageModel,
  ModelMessage,
  ToolCall,
  ToolResult,
  Tool
} from 'ai';
import { z, ZodTypeAny } from 'zod';
import { LLMTaskCategory } from '../../graph/types/graph.types';

/**
 * Token usage tracking for LLM calls
 */
export interface LLMTokenUsage {
  promptTokens: number;    // Our internal interface still uses promptTokens for consistency
  completionTokens: number; // Our internal interface still uses completionTokens for consistency
  totalTokens: number;
}

/**
 * Cost breakdown for LLM calls
 */
export interface LLMCallCost {
  inputTokenCost: number;   // Cost for input tokens
  outputTokenCost: number;  // Cost for output tokens  
  totalCost: number;        // Total cost in USD
}

/**
 * Detailed information about an LLM call execution
 */
export interface LLMOutputDetails {
  modelProvider: string;    // e.g., 'openai', 'anthropic', 'google', 'chrome-ai'
  modelName: string;        // e.g., 'gpt-4-turbo', 'claude-3-opus', 'gemini-nano'
  tokenUsage: LLMTokenUsage;
  cost?: LLMCallCost;
  finishReason?: string;    // e.g., 'stop', 'length', 'tool_calls'
  latencyMs?: number;       // Execution time in milliseconds
}

/**
 * Standardized wrapper for LLM service call results
 */
export interface LLMInvocationResult<TContent = string | object> {
  status: 'success' | 'failure' | 'error_max_retries';
  content?: TContent;       // Parsed content (string, or JSON object if structured output)
  toolCalls?: ToolCall<string, any>[];   // If model requests tool calls
  text?: string;            // Raw text response if applicable
  error?: {
    message: string;
    type: string;           // e.g., 'APIError', 'RateLimitError', 'OutputValidationError'
    originalError?: any;
  };
  details: LLMOutputDetails; // Always present, even on failure
}

/**
 * Options for configuring LLM model invocation
 */
export interface InvokeModelOptions {
  systemPrompt?: string;    // System prompt (usually added by PromptBuilderService)
  tools?: Record<string, Tool<any, any>>;       // Available tools for the model
  toolChoice?: 'auto' | 'none' | { type: 'tool'; toolName: string };
  responseFormat?: { 
    type: 'json_object'; 
    schema?: any 
  } | { 
    type: 'text' 
  };                        // For structured output
  temperature?: number;     // Sampling temperature (0.0 to 2.0)
  topP?: number;           // Nucleus sampling parameter
  maxRetries?: number;     // Override default retry count
  abortSignal?: AbortSignal; // For request cancellation
  maxOutputTokens?: number; // Maximum tokens to generate (changed from maxTokens)
}

/**
 * Custom UI message data parts for streaming
 */
export interface CustomUIMessageDataPart {
  type: 'agent_thought_chunk' | 
        'tool_call_initiated' | 
        'tool_result_received' | 
        'agent_step_completed' | 
        'custom_log' |
        'agent_invocation_start' |
        'partial_object_update';
  data: any;
}

/**
 * Model selection result from getModelAndProvider
 */
export interface ModelSelectionResult {
  providerName: string;
  modelId: string;
  modelInstance?: LanguageModel;
  isChromeAI: boolean;
  effectiveTemperature?: number;
}

/**
 * User settings for model preferences per task category
 */
export interface UserModelPreference {
  provider: string;         // e.g., 'openai', 'anthropic', 'google'
  modelId: string;          // e.g., 'gpt-4-turbo', 'claude-3-opus'
  temperature?: number;     // Default temperature for this preference
}

/**
 * Configuration for model pricing (loaded from external source)
 */
export interface ModelPricingConfig {
  [provider: string]: {
    [modelId: string]: {
      inputCostPerToken: number;    // Cost per input token in USD
      outputCostPerToken: number;   // Cost per output token in USD
      capabilities?: {
        maxTokens: number;
        contextWindow: number;
        supportsImages: boolean;
        supportsComputerUse?: boolean;
        supportsPromptCache: boolean;
        supportsReasoningBudget?: boolean;
        reasoningEffort?: 'low' | 'medium' | 'high';
      };
    };
  };
}

/**
 * Chrome AI service interface (conceptual)
 */
export interface ChromeAIService {
  isAvailable(): Promise<boolean>;
  generateText(options: {
    prompt: ModelMessage[] | string;
    temperature?: number;
    signal?: AbortSignal;
  }): Promise<string>;
  streamText?(options: {
    prompt: ModelMessage[] | string;
    temperature?: number;
    signal?: AbortSignal;
  }): ReadableStream<string>;
}

/**
 * User settings service interface
 */
export interface UserSettingsService {
  getApiKeyForProvider(provider: string): Promise<string | undefined>;
  getModelPreference(taskCategory: LLMTaskCategory): Promise<UserModelPreference | undefined>;
  getSetting(key: string): Promise<any>;
  isFeatureEnabled(feature: string): Promise<boolean>;
}

/**
 * Error types for LLM service operations
 */
export class LLMServiceError extends Error {
  constructor(
    message: string,
    public readonly type: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'LLMServiceError';
  }
}

export class MissingApiKeyError extends LLMServiceError {
  constructor(provider: string) {
    super(`Missing API key for provider: ${provider}`, 'MissingApiKeyError');
  }
}

export class ModelNotConfiguredError extends LLMServiceError {
  constructor(taskCategory: LLMTaskCategory) {
    super(`No model configured for task category: ${taskCategory}`, 'ModelNotConfiguredError');
  }
}

export class ContextWindowExceededError extends LLMServiceError {
  constructor(tokenCount: number, maxTokens: number) {
    super(`Context window exceeded: ${tokenCount} tokens > ${maxTokens} max`, 'ContextWindowExceededError');
  }
}

export class OutputValidationError extends LLMServiceError {
  constructor(message: string, originalError?: any) {
    super(`Output validation failed: ${message}`, 'OutputValidationError', originalError);
  }
}

/**
 * Structured output options with Zod schema
 */
export interface StructuredOutputOptions<T extends ZodTypeAny> extends Omit<InvokeModelOptions, 'responseFormat'> {
  schema: T;
  schemaName?: string;
  schemaDescription?: string;
} 