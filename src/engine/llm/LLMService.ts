/**
 * LLM Service
 * 
 * This is the centralized service responsible for all interactions with Large Language Models (LLMs)
 * via the Vercel AI SDK Core (v5 alpha). It abstracts the complexities of LLM provider integration,
 * model selection, token counting, cost estimation, error handling, and streaming standardized responses.
 */

import {
  generateText,
  streamText,
  generateObject,
  streamObject,
  LanguageModel,
  ModelMessage,
  generateId,
  createUIMessageStream,
  createUIMessageStreamResponse,
  pipeUIMessageStreamToResponse
} from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { z, ZodTypeAny } from 'zod';
import { encode as gptEncode, encodeChat } from 'gpt-tokenizer';

import { LLMTaskCategory } from '../graph/types/graph.types';
import {
  LLMTokenUsage,
  LLMCallCost,
  LLMOutputDetails,
  LLMInvocationResult,
  InvokeModelOptions,
  ModelSelectionResult,
  UserModelPreference,
  ModelPricingConfig,
  ChromeAIService,
  UserSettingsService,
  LLMServiceError,
  MissingApiKeyError,
  ModelNotConfiguredError,
  ContextWindowExceededError,
  OutputValidationError,
  StructuredOutputOptions
} from './types/llmService.types';
import {
  createEnhancedModelPricingConfig,
  getModelInfo,
  anthropicDefaultModelId,
  openAiDefaultModelId,
  googleDefaultModelId,
  REASONING_MODELS,
  COMPUTER_USE_MODELS,
  PROMPT_CACHING_MODELS
} from '../../config/llmModels.config';

/**
 * Default fallback models per task category - updated with latest models
 */
const DEFAULT_FALLBACK_MODELS: Record<LLMTaskCategory, { provider: string; modelId: string }> = {
  [LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING]: { provider: 'anthropic', modelId: anthropicDefaultModelId },
  [LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION]: { provider: 'anthropic', modelId: anthropicDefaultModelId },
  [LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION]: { provider: 'anthropic', modelId: 'claude-3-5-sonnet-********' },
  [LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE]: { provider: 'anthropic', modelId: 'claude-3-5-sonnet-********' },
  [LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING]: { provider: 'openai', modelId: 'gpt-4o-mini' },
  [LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION]: { provider: 'openai', modelId: 'gpt-4o-mini' }
};

/**
 * Task categories that are suitable for Chrome AI (on-device processing)
 */
const CHROME_AI_SUITABLE_CATEGORIES: Set<LLMTaskCategory> = new Set([
  LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
  LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION
]);

/**
 * Platform detection utility
 */
function isBrowser(): boolean {
  return typeof window !== 'undefined' && typeof window.document !== 'undefined';
}

/**
 * Proper token counting using gpt-tokenizer
 * Supports accurate counting for GPT models and reasonable approximation for others
 */
function countTokens(text: string, modelId?: string): number {
  try {
    // Use gpt-tokenizer for accurate counting
    // For non-GPT models, this still provides a good approximation
    return gptEncode(text).length;
  } catch (error) {
    console.warn('Token counting failed, falling back to approximation:', error);
    // Fallback to character-based approximation
    return Math.ceil(text.length / 4);
  }
}

/**
 * Count tokens in chat messages using proper tokenizer
 * Accounts for message structure and special tokens
 */
function countChatTokens(messages: ModelMessage[], modelId?: string): number {
  try {
    // Convert to format expected by encodeChat
    const chatMessages = messages.map(msg => ({
      role: msg.role,
      content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
    }));
    
    // Use a safe default model for token counting if the specific model isn't supported
    const supportedModel = modelId?.startsWith('gpt-') ? modelId as any : 'gpt-4';
    return encodeChat(chatMessages as any, supportedModel).length;
  } catch (error) {
    console.warn('Chat token counting failed, falling back to text counting:', error);
    // Fallback to counting as concatenated text
    const text = messagesToText(messages);
    return countTokens(text, modelId);
  }
}

/**
 * Convert messages to text for fallback token counting
 */
function messagesToText(messages: ModelMessage[]): string {
  return messages.map(msg => {
    if (typeof msg.content === 'string') {
      return msg.content;
    }
    // Handle array content (multimodal)
    if (Array.isArray(msg.content)) {
      return msg.content
        .filter(part => part.type === 'text')
        .map(part => (part as any).text)
        .join(' ');
    }
    return '';
  }).join(' ');
}

/**
 * Sleep utility for retry delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Main LLM Service Implementation
 */
export class LLMService {
  private userSettingsService: UserSettingsService;
  private chromeAIService: ChromeAIService;
  private modelPricingData: ModelPricingConfig;
  private defaultMaxRetries: number;
  private activeLLMClients: Map<string, LanguageModel> = new Map();

  constructor(
    userSettingsService: UserSettingsService,
    chromeAIService: ChromeAIService,
    modelPricingData?: ModelPricingConfig
  ) {
    this.userSettingsService = userSettingsService;
    this.chromeAIService = chromeAIService;
    // Use enhanced model configuration if no custom pricing provided
    this.modelPricingData = modelPricingData || createEnhancedModelPricingConfig();
    this.defaultMaxRetries = 3; // TODO: Make configurable via userSettingsService.getSetting('defaultLlmServiceMaxRetries') || 3
  }

  /**
   * Main method to invoke an LLM with retry logic and comprehensive error handling
   */
  public async invokeModel(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    options?: InvokeModelOptions
  ): Promise<LLMInvocationResult> {
    const maxRetries = options?.maxRetries ?? this.defaultMaxRetries;
    let lastError: any = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const startTime = Date.now();
        
        // 1. Get model and provider configuration
        const modelConfig = await this.getModelAndProvider(taskCategory);
        
        // 2. Count tokens before making the call
        const promptTokens = this.countPromptTokens(messages);
        
        // 3. Execute the appropriate path (Chrome AI vs Cloud LLM)
        if (modelConfig.isChromeAI) {
          return await this.executeChromeAICall(modelConfig, messages, options, promptTokens, startTime);
        } else {
          return await this.executeCloudLLMCall(modelConfig, messages, options, promptTokens, startTime);
        }
        
      } catch (error: any) {
        lastError = error;
        
        // Determine if this error is retryable
        if (this.isRetryableError(error) && attempt < maxRetries - 1) {
          // Exponential backoff: 1s, 2s, 4s, etc.
          const delayMs = Math.pow(2, attempt) * 1000;
          await sleep(delayMs);
          continue;
        }
        
        // If not retryable or we've exhausted retries, fall through to error handling
        break;
      }
    }

    // If we get here, all retries failed
    return this.createErrorResult(lastError, maxRetries);
  }

  /**
   * Get model capabilities for a given task category
   * Used by PromptBuilderService for context window management
   */
  public async getModelCapabilitiesForCategory(taskCategory: LLMTaskCategory): Promise<{
    provider: string;
    modelId: string;
    contextWindow: number;
    pricing?: ModelPricingConfig[string][string];
  }> {
    const modelConfig = await this.getModelAndProvider(taskCategory);
    const capabilities = this.getModelCapabilities(modelConfig.providerName, modelConfig.modelId);
    
    return {
      provider: modelConfig.providerName,
      modelId: modelConfig.modelId,
      contextWindow: capabilities.modelInfo?.contextWindow || 8192,
      pricing: this.modelPricingData[modelConfig.providerName]?.[modelConfig.modelId]
    };
  }

  /**
   * Stream model response with real-time UI updates and custom metadata
   * Returns a proper UIMessage stream with LLMOutputDetails and custom data parts
   */
  public async streamModelResponse(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    options?: InvokeModelOptions
  ): Promise<ReadableStream> {
    try {
      const modelConfig = await this.getModelAndProvider(taskCategory);
      const promptTokens = this.countPromptTokens(messages);
      const startTime = Date.now();
      
      // Create UIMessage stream for rich UI integration
      const stream = createUIMessageStream({
        execute: async (writer) => {
          // Start with invocation metadata
          await writer.write({
            type: 'data-agent-invocation',
            id: generateId(),
            data: {
              type: 'agent_invocation_start',
              taskCategory,
              modelProvider: modelConfig.providerName,
              modelName: modelConfig.modelId,
              isChromeAI: modelConfig.isChromeAI,
              startTime,
              promptTokens
            }
          });

          try {
            if (modelConfig.isChromeAI) {
              await this.streamChromeAIToUIWriter(modelConfig, messages, options, writer, promptTokens, startTime);
            } else {
              await this.streamCloudLLMToUIWriter(modelConfig, messages, options, writer, promptTokens, startTime);
            }
          } catch (error: any) {
            await writer.write({
              type: 'data-agent-error',
              id: generateId(),
              data: {
                type: 'agent_error',
                message: error.message,
                errorType: error.constructor.name,
                timestamp: Date.now()
              }
            });
            throw error;
          }
        }
      });

      return stream;
      
    } catch (error: any) {
      throw new LLMServiceError(`Failed to initiate streaming: ${error.message}`, 'StreamInitializationError', error);
    }
  }

  /**
   * Stream structured object generation with real-time updates
   * Returns a UIMessage stream with partial object updates and metadata
   */
  public async streamStructuredObject<T extends ZodTypeAny>(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    outputSchema: T,
    options?: StructuredOutputOptions<T>
  ): Promise<ReadableStream> {
    try {
      const modelConfig = await this.getModelAndProvider(taskCategory);
      
      if (modelConfig.isChromeAI) {
        // Chrome AI doesn't support structured streaming, fallback to regular generation
        const result = await this.generateStructuredObject(taskCategory, messages, outputSchema, options);
        
        const stream = createUIMessageStream({
          execute: async (writer) => {
            await writer.write({
              type: 'data-structured-object',
              id: generateId(),
              data: {
                type: 'structured_object_complete',
                content: result.content,
                details: result.details
              }
            });
          }
        });
        
        return stream;
      }

      const client = await this.getModelInstance(modelConfig.providerName, modelConfig.modelId);
      const promptTokens = this.countPromptTokens(messages);
      const startTime = Date.now();

      const stream = createUIMessageStream({
        execute: async (writer) => {
          // Start with metadata
          await writer.write({
            type: 'data-structured-generation',
            id: generateId(),
            data: {
              type: 'structured_generation_start',
              schema: outputSchema.description || 'Structured Output',
              modelProvider: modelConfig.providerName,
              modelName: modelConfig.modelId,
              startTime
            }
          });

          const streamResult = streamObject({
            model: client,
            messages,
            schema: outputSchema,
            system: options?.systemPrompt,
            temperature: modelConfig.effectiveTemperature ?? options?.temperature,
            maxOutputTokens: options?.maxOutputTokens,
            abortSignal: options?.abortSignal,
          });

          // Stream the object parts using the correct API
          for await (const partialObject of streamResult.partialObjectStream) {
            await writer.write({
              type: 'data-object-delta',
              id: generateId(),
              data: {
                type: 'partial_object_update',
                delta: partialObject,
                timestamp: Date.now()
              }
            });
          }
          
          // Get final result and metadata
          const finalResult = await streamResult;
          const latencyMs = Date.now() - startTime;
          const usage = await finalResult.usage;
          const completionTokens = usage.outputTokens ?? countTokens(JSON.stringify(finalResult.object), modelConfig.modelId);
          const cost = this.calculateCost(modelConfig.providerName, modelConfig.modelId, promptTokens, completionTokens);

          await writer.write({
            type: 'data-structured-complete',
            id: generateId(),
            data: {
              type: 'structured_generation_complete',
              finalObject: finalResult.object,
              details: {
                modelProvider: modelConfig.providerName,
                modelName: modelConfig.modelId,
                tokenUsage: {
                  promptTokens,
                  completionTokens,
                  totalTokens: promptTokens + completionTokens
                },
                cost,
                latencyMs
              }
            }
          });
        }
      });

      return stream;

    } catch (error: any) {
      throw new LLMServiceError(`Failed to stream structured object: ${error.message}`, 'StructuredStreamError', error);
    }
  }

  /**
   * Generate structured object with Zod schema validation
   */
  public async generateStructuredObject<T extends ZodTypeAny>(
    taskCategory: LLMTaskCategory,
    messages: ModelMessage[],
    outputSchema: T,
    options?: StructuredOutputOptions<T>
  ): Promise<LLMInvocationResult<z.infer<T>>> {
    try {
      const modelConfig = await this.getModelAndProvider(taskCategory);
      
      if (modelConfig.isChromeAI) {
        // Chrome AI doesn't support structured output, fallback to text parsing
        return await this.generateStructuredObjectFallback(modelConfig, messages, outputSchema, options);
      }

      const client = await this.getModelInstance(modelConfig.providerName, modelConfig.modelId);
      const startTime = Date.now();
      
      const result = await generateObject({
        model: client,
        messages,
        schema: outputSchema,
        system: options?.systemPrompt,
        temperature: modelConfig.effectiveTemperature ?? options?.temperature,
        maxOutputTokens: options?.maxOutputTokens,
        abortSignal: options?.abortSignal,
      });

      const latencyMs = Date.now() - startTime;
      const promptTokens = this.countPromptTokens(messages);
      const completionTokens = result.usage?.outputTokens ?? countTokens(JSON.stringify(result.object), modelConfig.modelId);
      const cost = this.calculateCost(modelConfig.providerName, modelConfig.modelId, promptTokens, completionTokens);

      const details: LLMOutputDetails = {
        modelProvider: modelConfig.providerName,
        modelName: modelConfig.modelId,
        tokenUsage: {
          promptTokens,
          completionTokens,
          totalTokens: promptTokens + completionTokens
        },
        cost,
        finishReason: result.finishReason,
        latencyMs
      };

      return {
        status: 'success',
        content: result.object,
        details
      };

    } catch (error: any) {
      return this.createErrorResult(error, 1);
    }
  }

  // === Private Helper Methods ===

  /**
   * Get model and provider configuration for a task category
   */
  private async getModelAndProvider(taskCategory: LLMTaskCategory): Promise<ModelSelectionResult> {
    try {
      // Check if Chrome AI is enabled and suitable for this task
      const chromeAIEnabled = await this.userSettingsService.isFeatureEnabled('chromeAI');
      if (chromeAIEnabled && 
          CHROME_AI_SUITABLE_CATEGORIES.has(taskCategory) && 
          await this.chromeAIService.isAvailable()) {
        
        const preference = await this.userSettingsService.getModelPreference(taskCategory);
        return {
          providerName: 'chrome-ai',
          modelId: 'gemini-nano',
          isChromeAI: true,
          effectiveTemperature: preference?.temperature
        };
      }

      // Get user preference for this task category
      const preference = await this.userSettingsService.getModelPreference(taskCategory);
      
      if (preference) {
        return {
          providerName: preference.provider,
          modelId: preference.modelId,
          isChromeAI: false,
          effectiveTemperature: preference.temperature
        };
      }

      // Fallback to task category default
      const fallback = DEFAULT_FALLBACK_MODELS[taskCategory];
      if (fallback) {
        return {
          providerName: fallback.provider,
          modelId: fallback.modelId,
          isChromeAI: false
        };
      }

      // Final fallback to core orchestration default
      const coreFallback = DEFAULT_FALLBACK_MODELS[LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING];
      return {
        providerName: coreFallback.provider,
        modelId: coreFallback.modelId,
        isChromeAI: false
      };

    } catch (error: any) {
      throw new ModelNotConfiguredError(taskCategory);
    }
  }

  /**
   * Get or create a client for the specified provider
   */
  private async getClientForProvider(providerName: string): Promise<any> {
    const apiKey = await this.userSettingsService.getApiKeyForProvider(providerName);
    if (!apiKey) {
      throw new MissingApiKeyError(providerName);
    }

    const clientKey = `${providerName}_${apiKey.slice(-8)}`;
    
    if (this.activeLLMClients.has(clientKey)) {
      return this.activeLLMClients.get(clientKey)!;
    }

    let providerClient: any;

    switch (providerName) {
      case 'openai':
        providerClient = createOpenAI({ apiKey });
        break;
      
      case 'anthropic':
        providerClient = createAnthropic({ apiKey });
        break;
      
      case 'google':
        providerClient = createGoogleGenerativeAI({ apiKey });
        break;
      
      default:
        throw new LLMServiceError(`Unsupported provider: ${providerName}`, 'UnsupportedProviderError');
    }

    this.activeLLMClients.set(clientKey, providerClient);
    return providerClient;
  }

  /**
   * Get specific model instance for the provider and model ID
   */
  private async getModelInstance(providerName: string, modelId: string): Promise<LanguageModel> {
    const providerClient = await this.getClientForProvider(providerName);
    
    // Create specific model instance using the provider client
    return providerClient(modelId);
  }

  /**
   * Calculate cost based on token usage and model pricing
   */
  private calculateCost(
    providerName: string, 
    modelId: string, 
    promptTokens: number, 
    completionTokens: number
  ): LLMCallCost | undefined {
    const providerPricing = this.modelPricingData[providerName];
    if (!providerPricing) return undefined;

    const modelPricing = providerPricing[modelId];
    if (!modelPricing) return undefined;

    const inputTokenCost = promptTokens * modelPricing.inputCostPerToken;
    const outputTokenCost = completionTokens * modelPricing.outputCostPerToken;
    const totalCost = inputTokenCost + outputTokenCost;

    return {
      inputTokenCost,
      outputTokenCost,
      totalCost
    };
  }

  /**
   * Get model capabilities and constraints
   */
  private getModelCapabilities(providerName: string, modelId: string) {
    const modelInfo = getModelInfo(providerName, modelId);
    const pricingInfo = this.modelPricingData[providerName]?.[modelId];
    
    return {
      modelInfo,
      capabilities: pricingInfo?.capabilities,
      hasReasoningSupport: REASONING_MODELS.has(modelId),
      hasComputerUse: COMPUTER_USE_MODELS.has(modelId),
      hasPromptCaching: PROMPT_CACHING_MODELS.has(modelId)
    };
  }

  /**
   * Count tokens in the prompt messages
   */
  private countPromptTokens(messages: ModelMessage[]): number {
    // Use chat-specific token counting for conversation messages
    if (messages.length > 1 || (messages.length === 1 && messages[0].role !== 'user')) {
      // This looks like a conversation, use chat-specific counting
      return countChatTokens(messages);
    }
    
    // For single user messages, use simple text counting
    const text = messagesToText(messages);
    return countTokens(text);
  }

  /**
   * Execute Chrome AI call
   */
  private async executeChromeAICall(
    modelConfig: ModelSelectionResult,
    messages: ModelMessage[],
    options: InvokeModelOptions | undefined,
    promptTokens: number,
    startTime: number
  ): Promise<LLMInvocationResult> {
    try {
      const response = await this.chromeAIService.generateText({
        prompt: messages,
        temperature: modelConfig.effectiveTemperature,
        signal: options?.abortSignal
      });

      const latencyMs = Date.now() - startTime;
      const completionTokens = countTokens(response);

      const details: LLMOutputDetails = {
        modelProvider: 'chrome-ai',
        modelName: 'gemini-nano',
        tokenUsage: {
          promptTokens,
          completionTokens,
          totalTokens: promptTokens + completionTokens
        },
        latencyMs
      };

      return {
        status: 'success',
        content: response,
        text: response,
        details
      };
    } catch (error: any) {
      throw new LLMServiceError(`Chrome AI error: ${error.message}`, 'ChromeAIError', error);
    }
  }

  /**
   * Execute cloud LLM call
   */
  private async executeCloudLLMCall(
    modelConfig: ModelSelectionResult,
    messages: ModelMessage[],
    options: InvokeModelOptions | undefined,
    promptTokens: number,
    startTime: number
  ): Promise<LLMInvocationResult> {
    const client = await this.getModelInstance(modelConfig.providerName, modelConfig.modelId);

    const result = await generateText({
      model: client,
      messages,
      system: options?.systemPrompt,
      tools: options?.tools,
      toolChoice: options?.toolChoice,
      temperature: modelConfig.effectiveTemperature ?? options?.temperature,
      topP: options?.topP,
      maxOutputTokens: options?.maxOutputTokens,
      abortSignal: options?.abortSignal,
    });

    const latencyMs = Date.now() - startTime;
    const completionTokens = result.usage?.outputTokens ?? countTokens(result.text, modelConfig.modelId);
    const actualPromptTokens = result.usage?.inputTokens ?? promptTokens;
    const cost = this.calculateCost(modelConfig.providerName, modelConfig.modelId, actualPromptTokens, completionTokens);

    const details: LLMOutputDetails = {
      modelProvider: modelConfig.providerName,
      modelName: modelConfig.modelId,
      tokenUsage: {
        promptTokens: actualPromptTokens,
        completionTokens,
        totalTokens: actualPromptTokens + completionTokens
      },
      cost,
      finishReason: result.finishReason,
      latencyMs
    };

    return {
      status: 'success',
      content: result.text,
      text: result.text,
      toolCalls: result.toolCalls,
      details
    };
  }

  /**
   * Stream Chrome AI response to UIMessage writer
   */
  private async streamChromeAIToUIWriter(
    modelConfig: ModelSelectionResult,
    messages: ModelMessage[],
    options: InvokeModelOptions | undefined,
    writer: any, // WritableStreamDefaultWriter from AI SDK
    promptTokens: number,
    startTime: number
  ): Promise<void> {
    if (this.chromeAIService.streamText) {
      const responseStream = this.chromeAIService.streamText({
        prompt: messages,
        temperature: modelConfig.effectiveTemperature,
        signal: options?.abortSignal
      });
      
      const reader = responseStream.getReader();
      let fullResponse = '';
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          fullResponse += value;
          
          // Stream text delta
          await writer.write({
            type: 'text-delta',
            textDelta: value
          });
        }
        
        // Final metadata
        const latencyMs = Date.now() - startTime;
        const completionTokens = countTokens(fullResponse);
        
        await writer.write({
          type: 'data-completion-metadata',
          id: generateId(),
          data: {
            type: 'completion_metadata',
            details: {
              modelProvider: 'chrome-ai',
              modelName: 'gemini-nano',
              tokenUsage: {
                promptTokens,
                completionTokens,
                totalTokens: promptTokens + completionTokens
              },
              latencyMs
            }
          }
        });
      } finally {
        reader.releaseLock();
      }
    } else {
      // Fallback to non-streaming for Chrome AI
      const response = await this.chromeAIService.generateText({
        prompt: messages,
        temperature: modelConfig.effectiveTemperature,
        signal: options?.abortSignal
      });
      
      await writer.write({
        type: 'text-delta',
        textDelta: response
      });
      
      const latencyMs = Date.now() - startTime;
      const completionTokens = countTokens(response);
      
      await writer.write({
        type: 'data-completion-metadata',
        id: generateId(),
        data: {
          type: 'completion_metadata',
          details: {
            modelProvider: 'chrome-ai',
            modelName: 'gemini-nano',
            tokenUsage: {
              promptTokens,
              completionTokens,
              totalTokens: promptTokens + completionTokens
            },
            latencyMs
          }
        }
      });
    }
  }

  /**
   * Stream Cloud LLM response to UIMessage writer
   */
  private async streamCloudLLMToUIWriter(
    modelConfig: ModelSelectionResult,
    messages: ModelMessage[],
    options: InvokeModelOptions | undefined,
    writer: any, // WritableStreamDefaultWriter from AI SDK
    promptTokens: number,
    startTime: number
  ): Promise<void> {
    const client = await this.getModelInstance(modelConfig.providerName, modelConfig.modelId);

    const streamResult = streamText({
      model: client,
      messages,
      system: options?.systemPrompt,
      tools: options?.tools,
      toolChoice: options?.toolChoice,
      temperature: modelConfig.effectiveTemperature ?? options?.temperature,
      topP: options?.topP,
      maxOutputTokens: options?.maxOutputTokens,
      abortSignal: options?.abortSignal,
    });

    // Stream the text parts
    for await (const textPart of streamResult.textStream) {
      await writer.write({
        type: 'text-delta',
        textDelta: textPart
      });
    }
    
    // Get final usage and write metadata
    const usage = await streamResult.usage;
    const finishReason = await streamResult.finishReason;
    const text = await streamResult.text;
    const latencyMs = Date.now() - startTime;
    const completionTokens = usage.outputTokens ?? countTokens(text, modelConfig.modelId);
    const actualPromptTokens = usage.inputTokens ?? promptTokens;
    const cost = this.calculateCost(modelConfig.providerName, modelConfig.modelId, actualPromptTokens, completionTokens);

    await writer.write({
      type: 'data-completion-metadata',
      id: generateId(),
      data: {
        type: 'completion_metadata',
        details: {
          modelProvider: modelConfig.providerName,
          modelName: modelConfig.modelId,
          tokenUsage: {
            promptTokens: actualPromptTokens,
            completionTokens,
            totalTokens: actualPromptTokens + completionTokens
          },
          cost,
          finishReason,
          latencyMs
        }
      }
    });
  }

  /**
   * Generate structured object with fallback for unsupported providers
   */
  private async generateStructuredObjectFallback<T extends ZodTypeAny>(
    modelConfig: ModelSelectionResult,
    messages: ModelMessage[],
    outputSchema: T,
    options: StructuredOutputOptions<T> | undefined
  ): Promise<LLMInvocationResult<z.infer<T>>> {
    // Add JSON schema instruction to the prompt
    const schemaInstruction = `Please respond with valid JSON that matches this schema: ${JSON.stringify(outputSchema)}`;
    const enhancedMessages: ModelMessage[] = [
      ...messages,
      { role: 'user', content: schemaInstruction }
    ];

    const textResult = await this.executeChromeAICall(
      modelConfig,
      enhancedMessages,
      options,
      this.countPromptTokens(enhancedMessages),
      Date.now()
    );

    if (textResult.status !== 'success' || !textResult.text) {
      return textResult as any;
    }

    try {
      const parsed = JSON.parse(textResult.text);
      const validated = outputSchema.parse(parsed);
      
      return {
        ...textResult,
        content: validated
      };
    } catch (error: any) {
      throw new OutputValidationError(`Failed to parse or validate JSON response: ${error.message}`, error);
    }
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    // These are configuration errors, not transient - never retry
    if (error instanceof MissingApiKeyError || error instanceof ModelNotConfiguredError) {
      return false;
    }

    // AbortError (from AbortSignal) should not be retried
    if (error.name === 'AbortError' || error.constructor.name === 'AbortError' || 
        (error instanceof DOMException && error.name === 'AbortError')) {
      return false;
    }

    // LLMServiceError for unsupported providers should not be retried
    if (error instanceof LLMServiceError && error.message?.includes('Unsupported provider')) {
      return false;
    }

    // Check for common retryable error patterns
    const errorMessage = error.message?.toLowerCase() || '';
    const retryablePatterns = [
      'rate limit',
      'timeout',
      'network',
      'connection',
      'server error',
      '500',
      '502',
      '503',
      '504',
      'temporary'
    ];

    return retryablePatterns.some(pattern => errorMessage.includes(pattern));
  }

  /**
   * Create standardized error result
   */
  private createErrorResult(error: any, maxRetriesConfigured: number): LLMInvocationResult {
    // If the error is non-retryable, we only made 1 attempt, so status should be 'failure'
    // If the error is retryable but we exhausted all retries, status should be 'error_max_retries'
    const isNonRetryable = !this.isRetryableError(error);
    
    // Create minimal details for error cases
    const details: LLMOutputDetails = {
      modelProvider: 'unknown',
      modelName: 'unknown',
      tokenUsage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      }
    };

    return {
      status: isNonRetryable ? 'failure' : 'error_max_retries',
      error: {
        message: error.message || 'Unknown error occurred',
        type: error.constructor.name || 'LLMServiceError',
        originalError: error
      },
      details
    };
  }
} 