import { describe, it, expect, beforeEach, afterEach, vi, type MockedFunction } from 'vitest';
import { LLMService } from '../LLMService';
import { LLMTaskCategory } from '../../graph/types/graph.types';
import type {
  LLMInvocationResult,
  InvokeModelOptions,
  UserSettingsService,
  ChromeAIService,
  ModelPricingConfig,
  LLMTokenUsage,
  LLMCallCost,
  LLMOutputDetails,
  MissingApiKeyError,
  LLMServiceError
} from '../types/llmService.types';
import { z } from 'zod';

// Mock external dependencies
vi.mock('ai', () => ({
  generateText: vi.fn(),
  streamText: vi.fn(),
  generateObject: vi.fn(),
  streamObject: vi.fn(),
  generateId: vi.fn(() => 'test-id-123'),
  createUIMessageStream: vi.fn(),
  createUIMessageStreamResponse: vi.fn(),
  pipeUIMessageStreamToResponse: vi.fn()
}));

vi.mock('@ai-sdk/openai', () => ({
  createOpenAI: vi.fn()
}));

vi.mock('@ai-sdk/anthropic', () => ({
  createAnthropic: vi.fn()
}));

vi.mock('@ai-sdk/google', () => ({
  createGoogleGenerativeAI: vi.fn()
}));

vi.mock('gpt-tokenizer', () => ({
  encode: vi.fn(),
  encodeChat: vi.fn()
}));

vi.mock('../../../config/models.config', () => ({
  createEnhancedModelPricingConfig: vi.fn(),
  getModelInfo: vi.fn(),
  anthropicDefaultModelId: 'claude-3-5-sonnet-********',
  openAiDefaultModelId: 'gpt-4o',
  googleDefaultModelId: 'gemini-1.5-pro',
  REASONING_MODELS: new Set(['o1-preview', 'o1-mini']),
  COMPUTER_USE_MODELS: new Set(['claude-3-5-sonnet-********']),
  PROMPT_CACHING_MODELS: new Set(['claude-3-5-sonnet-********'])
}));

// Import mocked functions
import {
  generateText,
  streamText,
  generateObject,
  streamObject,
  generateId,
  createUIMessageStream
} from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { encode as gptEncode, encodeChat } from 'gpt-tokenizer';
import { createEnhancedModelPricingConfig, getModelInfo } from '../../../config/models.config';

// Test utilities
async function collectStreamParts(stream: ReadableStream): Promise<any[]> {
  const reader = stream.getReader();
  const parts: any[] = [];
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      parts.push(value);
    }
  } finally {
    reader.releaseLock();
  }
  
  return parts;
}

function createMockModelInstance(responses: any[] = []) {
  let callCount = 0;
  return vi.fn(() => ({
    generateText: vi.fn().mockImplementation(() => {
      const response = responses[callCount] || responses[responses.length - 1];
      callCount++;
      return Promise.resolve(response);
    }),
    streamText: vi.fn(),
    generateObject: vi.fn(),
    streamObject: vi.fn()
  }));
}

function createMockUserSettingsService(overrides: Partial<any> = {}): UserSettingsService {
  const defaults = {
    isFeatureEnabled: vi.fn().mockResolvedValue(false),
    getModelPreference: vi.fn().mockResolvedValue(null),
    getApiKeyForProvider: vi.fn().mockResolvedValue('test-api-key'),
    getSetting: vi.fn().mockResolvedValue(3)
  };
  
  return { ...defaults, ...overrides } as UserSettingsService;
}

function createMockChromeAIService(overrides: Partial<any> = {}): ChromeAIService {
  const defaults = {
    isAvailable: vi.fn().mockResolvedValue(false),
    generateText: vi.fn().mockResolvedValue('Chrome AI response'),
    streamText: vi.fn()
  };
  
  return { ...defaults, ...overrides } as ChromeAIService;
}

function createMockPricingConfig(): ModelPricingConfig {
  return {
    openai: {
      'gpt-4o': {
        inputCostPerToken: 0.000005,
        outputCostPerToken: 0.000015,
        capabilities: {
          maxTokens: 4096,
          contextWindow: 128000,
          supportsImages: true,
          supportsPromptCache: false
        }
      },
      'gpt-4o-mini': {
        inputCostPerToken: 0.00000015,
        outputCostPerToken: 0.0000006,
        capabilities: {
          maxTokens: 16384,
          contextWindow: 128000,
          supportsImages: true,
          supportsPromptCache: false
        }
      }
    },
    anthropic: {
      'claude-3-5-sonnet-********': {
        inputCostPerToken: 0.000003,
        outputCostPerToken: 0.000015,
        capabilities: {
          maxTokens: 8192,
          contextWindow: 200000,
          supportsImages: true,
          supportsComputerUse: true,
          supportsPromptCache: true
        }
      }
    },
    google: {
      'gemini-1.5-pro': {
        inputCostPerToken: 0.00000125,
        outputCostPerToken: 0.000005,
        capabilities: {
          maxTokens: 8192,
          contextWindow: 2000000,
          supportsImages: true,
          supportsPromptCache: false
        }
      }
    }
  };
}

describe('LLMService', () => {
  let llmService: LLMService;
  let mockUserSettingsService: UserSettingsService;
  let mockChromeAIService: ChromeAIService;
  let mockPricingConfig: ModelPricingConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUserSettingsService = createMockUserSettingsService();
    mockChromeAIService = createMockChromeAIService();
    mockPricingConfig = createMockPricingConfig();
    
    // Setup default mocks
    (createEnhancedModelPricingConfig as MockedFunction<any>).mockReturnValue(mockPricingConfig);
    (gptEncode as MockedFunction<any>).mockReturnValue([1, 2, 3, 4]); // 4 tokens
    (encodeChat as MockedFunction<any>).mockReturnValue([1, 2, 3, 4, 5]); // 5 tokens
    (generateId as MockedFunction<any>).mockReturnValue('test-id-123');
    
    // Setup provider client mocks to return functions that create model instances
    const mockModelInstance = vi.fn();
    (createOpenAI as MockedFunction<any>).mockReturnValue(mockModelInstance);
    (createAnthropic as MockedFunction<any>).mockReturnValue(mockModelInstance);
    (createGoogleGenerativeAI as MockedFunction<any>).mockReturnValue(mockModelInstance);
    
    // Setup default successful responses for AI SDK functions
    (generateText as MockedFunction<any>).mockResolvedValue({
      text: 'Default mock response',
      usage: { inputTokens: 4, outputTokens: 10 },
      finishReason: 'stop'
    });
    
    (generateObject as MockedFunction<any>).mockResolvedValue({
      object: { test: 'object' },
      usage: { inputTokens: 4, outputTokens: 10 },
      finishReason: 'stop'
    });
    
    (streamText as MockedFunction<any>).mockReturnValue({
      textStream: (async function* () {
        yield 'chunk1';
        yield 'chunk2';
      })(),
      usage: Promise.resolve({ inputTokens: 4, outputTokens: 10 }),
      finishReason: Promise.resolve('stop'),
      text: Promise.resolve('chunk1chunk2')
    });
    
    (streamObject as MockedFunction<any>).mockReturnValue({
      partialObjectStream: (async function* () {
        yield { partial: 'data' };
        yield { complete: 'data' };
      })(),
      object: Promise.resolve({ complete: 'data' }),
      usage: Promise.resolve({ inputTokens: 4, outputTokens: 10 })
    });
    
    (createUIMessageStream as MockedFunction<any>).mockImplementation((options: any) => {
      const mockWriter = {
        write: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined)
      };
      if (options?.execute) {
        return options.execute(mockWriter);
      }
      return new ReadableStream();
    });
    
    llmService = new LLMService(mockUserSettingsService, mockChromeAIService, mockPricingConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor & Initialization', () => {
    it('should initialize correctly with provided services', () => {
      expect(llmService).toBeInstanceOf(LLMService);
    });

    it('should use default pricing config when none provided', () => {
      const serviceWithoutConfig = new LLMService(mockUserSettingsService, mockChromeAIService);
      expect(createEnhancedModelPricingConfig).toHaveBeenCalled();
    });

    it('should set default max retries to 3', () => {
      // The LLMService sets defaultMaxRetries to 3 by default
      // We can verify this through the retry behavior in other tests
      expect(llmService).toBeInstanceOf(LLMService);
    });
  });

  describe('Model & Provider Selection', () => {
    it('should select Chrome AI for suitable tasks when enabled and available', async () => {
      // Setup Chrome AI enabled and available
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);
      
      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Test message' }]
      );

      expect(mockChromeAIService.generateText).toHaveBeenCalled();
      expect(result.details.modelProvider).toBe('chrome-ai');
      expect(result.details.modelName).toBe('gemini-nano');
    });

    it('should fallback to cloud LLM when Chrome AI disabled', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(false);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o-mini',
        temperature: 0.7
      });

      const mockOpenAIClient = createMockModelInstance([{
        text: 'OpenAI response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      }]);
      (createOpenAI as MockedFunction<any>).mockReturnValue(mockOpenAIClient);
      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'OpenAI response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Test message' }]
      );

      expect(result.details.modelProvider).toBe('openai');
      expect(result.details.modelName).toBe('gpt-4o-mini');
    });

    it('should use user preference when available', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-********',
        temperature: 0.5
      });

      const mockAnthropicClient = createMockModelInstance([{
        text: 'Anthropic response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      }]);
      (createAnthropic as MockedFunction<any>).mockReturnValue(mockAnthropicClient);
      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Anthropic response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test message' }]
      );

      expect(result.details.modelProvider).toBe('anthropic');
      expect(result.details.modelName).toBe('claude-3-5-sonnet-********');
    });

    it('should fallback to category default when no user preference', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue(null);

      const mockAnthropicClient = createMockModelInstance([{
        text: 'Default response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      }]);
      (createAnthropic as MockedFunction<any>).mockReturnValue(mockAnthropicClient);
      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Default response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION,
        [{ role: 'user', content: 'Test message' }]
      );

      // Should use the default for this category (Anthropic)
      expect(result.details.modelProvider).toBe('anthropic');
    });

    it('should pass through temperature from user preference', async () => {
      const customTemp = 0.9;
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o',
        temperature: customTemp
      });

      const mockOpenAIClient = createMockModelInstance([{
        text: 'Response with custom temp',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      }]);
      (createOpenAI as MockedFunction<any>).mockReturnValue(mockOpenAIClient);
      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Response with custom temp',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test message' }]
      );

      expect(generateText).toHaveBeenCalledWith(
        expect.objectContaining({
          temperature: customTemp
        })
      );
    });
  });

  describe('API Key & Client Management', () => {
    it('should throw MissingApiKeyError when API key not found', async () => {
      (mockUserSettingsService.getApiKeyForProvider as MockedFunction<any>).mockResolvedValue(null);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      // MissingApiKeyError is now properly non-retryable, so we expect 'failure' status
      expect(result.status).toBe('failure');
      expect(result.error?.type).toBe('MissingApiKeyError');
    });

    it('should cache LLM clients for the same provider', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const mockClient = createMockModelInstance([
        { text: 'First response', usage: { inputTokens: 5, outputTokens: 10 }, finishReason: 'stop' },
        { text: 'Second response', usage: { inputTokens: 5, outputTokens: 10 }, finishReason: 'stop' }
      ]);
      (createOpenAI as MockedFunction<any>).mockReturnValue(mockClient);
      (generateText as MockedFunction<any>)
        .mockResolvedValueOnce({ text: 'First response', usage: { inputTokens: 5, outputTokens: 10 }, finishReason: 'stop' })
        .mockResolvedValueOnce({ text: 'Second response', usage: { inputTokens: 5, outputTokens: 10 }, finishReason: 'stop' });

      // Make two calls
      await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'First call' }]
      );
      
      await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Second call' }]
      );

      // Should only create client once
      expect(createOpenAI).toHaveBeenCalledTimes(1);
    });

    it('should handle unsupported provider gracefully', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'unsupported-provider',
        modelId: 'some-model'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      // LLMServiceError for unsupported provider is now non-retryable, so expect 'failure'
      expect(result.status).toBe('failure');
      expect(result.error?.type).toBe('LLMServiceError');
      expect(result.error?.message).toContain('Unsupported provider');
    });
  });

  describe('Token Counting', () => {
    it('should count tokens using gpt-tokenizer', async () => {
      (gptEncode as MockedFunction<any>).mockReturnValue([1, 2, 3, 4, 5, 6]); // 6 tokens
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Test message for token counting' }]
      );

      expect(result.details.tokenUsage.promptTokens).toBeGreaterThan(0);
    });

    it('should fall back to character approximation when tokenizer fails', async () => {
      (gptEncode as MockedFunction<any>).mockImplementation(() => {
        throw new Error('Tokenizer error');
      });
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Test message' }]
      );

      // Should still provide token count via fallback
      expect(result.details.tokenUsage.promptTokens).toBeGreaterThan(0);
    });

    it('should use chat-specific token counting for conversation messages', async () => {
      // Force cloud LLM usage to trigger token counting
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(false);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });
      
      const messages = [
        { role: 'system', content: 'You are a helpful assistant' },
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];

      await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        messages as any
      );

      // The updated implementation should use encodeChat for conversation messages
      expect(encodeChat).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: 'system' }),
          expect.objectContaining({ role: 'user' }),
          expect.objectContaining({ role: 'assistant' })
        ]),
        expect.any(String)
      );
    });
  });

  describe('Cost Calculation', () => {
    it('should calculate cost correctly for OpenAI models', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const mockUsage = { inputTokens: 100, outputTokens: 50 };
      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Test response',
        usage: mockUsage,
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      const expectedInputCost = 100 * 0.000005; // 100 tokens * $0.000005
      const expectedOutputCost = 50 * 0.000015; // 50 tokens * $0.000015
      const expectedTotalCost = expectedInputCost + expectedOutputCost;

      expect(result.details.cost).toEqual({
        inputTokenCost: expectedInputCost,
        outputTokenCost: expectedOutputCost,
        totalCost: expectedTotalCost
      });
    });

    it('should return undefined cost for unknown models', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'unknown-model'
      });

      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Test response',
        usage: { inputTokens: 100, outputTokens: 50 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      expect(result.details.cost).toBeUndefined();
    });

    it('should handle zero token costs correctly', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      (generateText as MockedFunction<any>).mockResolvedValue({
        text: '',
        usage: { inputTokens: 0, outputTokens: 0 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      expect(result.details.cost).toEqual({
        inputTokenCost: 0,
        outputTokenCost: 0,
        totalCost: 0
      });
    });
  });

  describe('invokeModel - Success Cases', () => {
    it('should handle successful cloud LLM text generation', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const mockResponse = {
        text: 'This is a successful response',
        usage: { inputTokens: 10, outputTokens: 20 },
        finishReason: 'stop'
      };
      (generateText as MockedFunction<any>).mockResolvedValue(mockResponse);

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test prompt' }]
      );

      expect(result).toMatchObject({
        status: 'success',
        content: 'This is a successful response',
        text: 'This is a successful response',
        details: expect.objectContaining({
          modelProvider: 'openai',
          modelName: 'gpt-4o',
          tokenUsage: expect.objectContaining({
            promptTokens: expect.any(Number),
            completionTokens: 20,
            totalTokens: expect.any(Number)
          }),
          cost: expect.any(Object),
          finishReason: 'stop',
          latencyMs: expect.any(Number)
        })
      });
    });

    it('should handle successful cloud LLM with tool calls', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const mockToolCalls = [
        {
          toolCallId: 'call_123',
          toolName: 'test_tool',
          args: { param: 'value' }
        }
      ];

      const mockResponse = {
        text: '',
        toolCalls: mockToolCalls,
        usage: { inputTokens: 15, outputTokens: 25 },
        finishReason: 'tool_calls'
      };
      (generateText as MockedFunction<any>).mockResolvedValue(mockResponse);

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Use a tool to help me' }],
        {
          tools: {
            test_tool: {
              description: 'A test tool',
              parameters: z.object({ param: z.string() })
            }
          }
        }
      );

      expect(result).toMatchObject({
        status: 'success',
        toolCalls: mockToolCalls,
        details: expect.objectContaining({
          finishReason: 'tool_calls'
        })
      });
    });

    it('should handle successful Chrome AI call', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.generateText as MockedFunction<any>).mockResolvedValue('Chrome AI response');

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Simple classification task' }]
      );

      expect(result).toMatchObject({
        status: 'success',
        content: 'Chrome AI response',
        text: 'Chrome AI response',
        details: expect.objectContaining({
          modelProvider: 'chrome-ai',
          modelName: 'gemini-nano',
          tokenUsage: expect.any(Object),
          latencyMs: expect.any(Number)
        })
      });
    });
  });

  describe('invokeModel - Error Handling & Retry Logic', () => {
    it('should retry on retryable errors and eventually succeed', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock to fail first time with retryable error, then succeed
      (generateText as MockedFunction<any>)
        .mockRejectedValueOnce(new Error('rate limit exceeded'))
        .mockResolvedValueOnce({
          text: 'Success after retry',
          usage: { inputTokens: 10, outputTokens: 20 },
          finishReason: 'stop'
        });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test with retry' }],
        { maxRetries: 2 }
      );

      expect(generateText).toHaveBeenCalledTimes(2);
      expect(result.status).toBe('success');
      expect(result.content).toBe('Success after retry');
    });

    it('should return error_max_retries when all retries exhausted', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock to always fail with retryable error
      (generateText as MockedFunction<any>).mockRejectedValue(new Error('server error 500'));

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test max retries' }],
        { maxRetries: 3 }
      );

      expect(generateText).toHaveBeenCalledTimes(3);
      expect(result.status).toBe('error_max_retries');
      expect(result.error).toMatchObject({
        message: expect.stringContaining('server error 500'),
        type: 'Error'
      });
    });

    it('should not retry on non-retryable errors', async () => {
      // Set up no API key to trigger MissingApiKeyError (non-retryable)
      (mockUserSettingsService.getApiKeyForProvider as MockedFunction<any>).mockResolvedValue(null);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test non-retryable error' }]
      );

      // MissingApiKeyError should not be retried - expect 'failure' status
      expect(result.status).toBe('failure');
      expect(result.error?.type).toBe('MissingApiKeyError');
    });

    it('should handle AbortSignal cancellation', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const abortController = new AbortController();
      
      // Mock to simulate proper AbortError
      const abortError = new DOMException('Request aborted', 'AbortError');
      (generateText as MockedFunction<any>).mockRejectedValue(abortError);

      // Start the request and abort it immediately
      abortController.abort();
      
      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test abort' }],
        { abortSignal: abortController.signal }
      );

      // AbortError should not be retried - expect 'failure' status
      expect(result.status).toBe('failure');
      expect(result.error?.message).toContain('aborted');
    });
  });

  describe('generateStructuredObject', () => {
    const TestSchema = z.object({
      name: z.string(),
      age: z.number(),
      active: z.boolean()
    });

    it('should generate valid structured object for cloud LLM', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      const mockObject = { name: 'John', age: 30, active: true };
      (generateObject as MockedFunction<any>).mockResolvedValue({
        object: mockObject,
        usage: { inputTokens: 15, outputTokens: 25 },
        finishReason: 'stop'
      });

      const result = await llmService.generateStructuredObject(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Generate a person object' }],
        TestSchema
      );

      expect(result).toMatchObject({
        status: 'success',
        content: mockObject,
        details: expect.objectContaining({
          modelProvider: 'openai',
          modelName: 'gpt-4o'
        })
      });
    });

    it('should handle Chrome AI fallback for structured objects', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      const validJson = '{"name": "Jane", "age": 25, "active": false}';
      (mockChromeAIService.generateText as MockedFunction<any>).mockResolvedValue(validJson);

      const result = await llmService.generateStructuredObject(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Generate a person object' }],
        TestSchema
      );

      expect(result).toMatchObject({
        status: 'success',
        content: { name: 'Jane', age: 25, active: false },
        details: expect.objectContaining({
          modelProvider: 'chrome-ai',
          modelName: 'gemini-nano'
        })
      });
    });

    it('should handle JSON validation errors', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      // Invalid JSON for schema
      const invalidJson = '{"name": "John", "age": "not-a-number", "active": true}';
      (mockChromeAIService.generateText as MockedFunction<any>).mockResolvedValue(invalidJson);

      const result = await llmService.generateStructuredObject(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Generate a person object' }],
        TestSchema
      );

      expect(result.status).toBe('failure');
      expect(result.error?.type).toBe('OutputValidationError');
    });

    it('should handle malformed JSON', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      // Malformed JSON
      const malformedJson = '{"name": "John", "age": 30, "active":}';
      (mockChromeAIService.generateText as MockedFunction<any>).mockResolvedValue(malformedJson);

      const result = await llmService.generateStructuredObject(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Generate a person object' }],
        TestSchema
      );

      expect(result.status).toBe('failure');
      expect(result.error?.type).toBe('OutputValidationError');
    });
  });

  describe('streamModelResponse', () => {
    it('should stream text successfully from cloud LLM', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock the streaming response
      const mockStreamParts = [
        { type: 'text-delta', textDelta: 'Hello' },
        { type: 'text-delta', textDelta: ' there!' },
        { type: 'finish', finishReason: 'stop', usage: { inputTokens: 5, outputTokens: 10 } }
      ];

      const mockUIStream = {
        execute: vi.fn((callback) => {
          const mockWriter = {
            write: vi.fn(),
            close: vi.fn()
          };
          return callback(mockWriter);
        })
      };

      (createUIMessageStream as MockedFunction<any>).mockReturnValue(mockUIStream);

      // Mock streamText to return an async iterator
      const mockTextStream = {
        textStream: (async function* () {
          yield 'Hello';
          yield ' there!';
        })(),
        usage: Promise.resolve({ inputTokens: 5, outputTokens: 10 }),
        finishReason: Promise.resolve('stop'),
        text: Promise.resolve('Hello there!')
      };
      
      (streamText as MockedFunction<any>).mockReturnValue(mockTextStream);

      const stream = await llmService.streamModelResponse(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Say hello' }]
      );

      expect(stream).toBeDefined();
      expect(createUIMessageStream).toHaveBeenCalled();
    });

    it('should handle Chrome AI streaming', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      // Mock Chrome AI streaming
      const mockChromeStream = new ReadableStream({
        start(controller) {
          controller.enqueue('Hello');
          controller.enqueue(' from');
          controller.enqueue(' Chrome AI');
          controller.close();
        }
      });

      (mockChromeAIService.streamText as MockedFunction<any>).mockReturnValue(mockChromeStream);

      // Mock createUIMessageStream to return a proper stream
      const mockUIStream = new ReadableStream();
      (createUIMessageStream as MockedFunction<any>).mockReturnValue(mockUIStream);

      const stream = await llmService.streamModelResponse(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Say hello' }]
      );

      expect(stream).toBeDefined();
      expect(createUIMessageStream).toHaveBeenCalled();
    });

    it('should handle streaming errors gracefully', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock createUIMessageStream to throw an error
      (createUIMessageStream as MockedFunction<any>).mockImplementation(() => {
        throw new Error('Streaming failed');
      });

      await expect(
        llmService.streamModelResponse(
          LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
          [{ role: 'user', content: 'Test stream error' }]
        )
      ).rejects.toThrow('Failed to initiate streaming');
    });
  });

  describe('streamStructuredObject', () => {
    const TestSchema = z.object({
      items: z.array(z.string()),
      count: z.number()
    });

    it('should stream structured object from cloud LLM', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock createUIMessageStream to return a proper stream
      const mockUIStream = new ReadableStream();
      (createUIMessageStream as MockedFunction<any>).mockReturnValue(mockUIStream);

      const stream = await llmService.streamStructuredObject(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Generate a list' }],
        TestSchema
      );

      expect(stream).toBeDefined();
      expect(createUIMessageStream).toHaveBeenCalled();
    });

    it('should fallback to regular generation for Chrome AI', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.generateText as MockedFunction<any>).mockResolvedValue(
        '{"items": ["chrome1", "chrome2"], "count": 2}'
      );

      const mockUIStream = {
        execute: vi.fn((callback) => {
          const mockWriter = {
            write: vi.fn(),
            close: vi.fn()
          };
          return callback(mockWriter);
        })
      };

      (createUIMessageStream as MockedFunction<any>).mockReturnValue(mockUIStream);

      const stream = await llmService.streamStructuredObject(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Generate a list' }],
        TestSchema
      );

      expect(stream).toBeDefined();
      expect(mockChromeAIService.generateText).toHaveBeenCalled();
    });
  });

  describe('Security & Privacy', () => {
    it('should not expose API keys in error messages', async () => {
      const sensitiveApiKey = 'sk-very-secret-key-12345';
      (mockUserSettingsService.getApiKeyForProvider as MockedFunction<any>).mockResolvedValue(sensitiveApiKey);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      // Mock generateText to fail, but not include the API key in the error
      (generateText as MockedFunction<any>).mockRejectedValue(new Error('API call failed'));

      const result = await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      // Error message should not contain the API key
      expect(result.error?.message).not.toContain(sensitiveApiKey);
      expect(JSON.stringify(result)).not.toContain(sensitiveApiKey);
    });

    it('should pass API keys correctly to provider functions', async () => {
      const testApiKey = 'test-api-key-123';
      (mockUserSettingsService.getApiKeyForProvider as MockedFunction<any>).mockResolvedValue(testApiKey);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Success',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      await llmService.invokeModel(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        [{ role: 'user', content: 'Test' }]
      );

      expect(createOpenAI).toHaveBeenCalledWith({ apiKey: testApiKey });
    });
  });

  describe('Platform Compatibility', () => {
    it('should handle browser environment correctly', async () => {
      // Mock browser environment
      const originalWindow = global.window;
      global.window = { document: {} } as any;

      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Browser test' }]
      );

      expect(result.status).toBe('success');

      // Restore
      global.window = originalWindow;
    });

    it('should handle Node.js environment correctly', async () => {
      // Mock Node.js environment (Chrome AI unavailable)
      const originalWindow = global.window;
      delete (global as any).window;

      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(false);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'gpt-4o'
      });

      (generateText as MockedFunction<any>).mockResolvedValue({
        text: 'Node.js response',
        usage: { inputTokens: 5, outputTokens: 10 },
        finishReason: 'stop'
      });

      const result = await llmService.invokeModel(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING,
        [{ role: 'user', content: 'Node.js test' }]
      );

      expect(result.status).toBe('success');
      expect(result.details.modelProvider).toBe('openai');

      // Restore
      global.window = originalWindow;
    });
  });

  describe('getModelCapabilitiesForCategory', () => {
    it('should return model capabilities for a given task category', async () => {
      // Mock model preference
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-********'
      });

      // Mock getModelInfo
      (getModelInfo as MockedFunction<any>).mockReturnValue({
        contextWindow: 200000,
        maxTokens: 8192,
        supportsImages: true,
        supportsComputerUse: true
      });

      const capabilities = await llmService.getModelCapabilitiesForCategory(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      );

      expect(capabilities).toEqual({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-********',
        contextWindow: 200000,
        pricing: mockPricingConfig.anthropic['claude-3-5-sonnet-********']
      });
    });

    it('should return capabilities for Chrome AI when suitable and enabled', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(true);
      (mockChromeAIService.isAvailable as MockedFunction<any>).mockResolvedValue(true);

      // Mock getModelInfo for Chrome AI
      (getModelInfo as MockedFunction<any>).mockReturnValue({
        contextWindow: 8192,
        maxTokens: 2048,
        supportsImages: false
      });

      const capabilities = await llmService.getModelCapabilitiesForCategory(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING
      );

      expect(capabilities).toEqual({
        provider: 'chrome-ai',
        modelId: 'gemini-nano',
        contextWindow: 8192,
        pricing: undefined // Chrome AI doesn't have pricing
      });
    });

    it('should fallback to default model when no preference is set', async () => {
      (mockUserSettingsService.isFeatureEnabled as MockedFunction<any>).mockResolvedValue(false);
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue(null);

      // Mock getModelInfo for default model
      (getModelInfo as MockedFunction<any>).mockReturnValue({
        contextWindow: 200000,
        maxTokens: 8192
      });

      const capabilities = await llmService.getModelCapabilitiesForCategory(
        LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE
      );

      expect(capabilities.provider).toBe('anthropic');
      expect(capabilities.modelId).toBe('claude-3-5-sonnet-********');
      expect(capabilities.contextWindow).toBe(200000);
    });

    it('should handle model info not found gracefully', async () => {
      (mockUserSettingsService.getModelPreference as MockedFunction<any>).mockResolvedValue({
        provider: 'openai',
        modelId: 'unknown-model'
      });

      // Mock getModelInfo returning undefined/null
      (getModelInfo as MockedFunction<any>).mockReturnValue(null);

      const capabilities = await llmService.getModelCapabilitiesForCategory(
        LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING
      );

      expect(capabilities.provider).toBe('openai');
      expect(capabilities.modelId).toBe('unknown-model');
      expect(capabilities.contextWindow).toBe(8192); // Default fallback
    });
  });
}); 