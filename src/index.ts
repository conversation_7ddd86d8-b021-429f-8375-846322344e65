/**
 * n8n AI Assistant Library
 * Main entry point for the n8n Agentic AI "Brain" Library
 * 
 * Cross-environment module that works in both Node.js and browsers
 */

// Environment detection utilities
export { isNodeEnvironment, isBrowserEnvironment, getEnvironment } from './utils/environment';

// Legacy compatibility
export const isNode = typeof window === 'undefined';
export const isBrowser = !isNode;

// Export global domain types
export * from './types';

// Export engine graph components (specific exports to avoid conflicts)
export type {
  GraphDefinition,
  GraphNodeDefinition,
  AgentCallNodeDef,
  ToolCallNodeDef,
  ConditionalBranchNodeDef,
  DataTransformNodeDef,
  StartNodeDef,
  EndNodeDef,
  GraphEdgeDefinition,
  GraphInputValueSource,
  LLMTaskCategory
} from './engine/graph/types/graph.types';

export type {
  FunctionRegistry
} from './engine/graph/types/builder.types';

export type {
  CompiledGraph
} from './engine/graph/GraphBuilder';

export type {
  GraphExecutionContext,
  GraphExecutionResult,
  NodeOutputVersion,
  GraphExecutionStatus
} from './engine/graph/types/execution.types';

export { GraphBuilder } from './engine/graph/GraphBuilder';
export { GraphExecutor } from './engine/graph/GraphExecutor';

// Export engine services (specific exports to avoid conflicts)
export { LLMService } from './engine/llm/LLMService';
export type { LLMInvocationResult } from './engine/llm/types/llmService.types';

export { BaseAgent } from './engine/agents/base/BaseAgent';
export { agentRegistryMap } from './engine/agents/agentRegistry';

// Export persistence layer
export { initializeDb, getDb, destroyDb, isDbReady, getDbStatus } from './persistence/rxdbSetup';
export { UserSettingsRepository, CustomRulesRepository, TaskSessionRepository } from './persistence';
export type { N8nAidDatabase } from './persistence';

// Export application services layer (specific exports to avoid conflicts)
export { NodeSpecService } from './services/NodeSpecService';
export { UserSettingsService } from './services/UserSettingsService';
// Note: TaskSessionManager is deprecated, not exporting for now

// Export configuration
export * from './config';

// Export utilities
export * from './utils';

// Future exports (shell directories)
// export * from './app';
// export * from './agents';
// export * from './knowledge';
// export * from './tools';
// export * from './graphs';

