/**
 * RxDB Setup and Initialization
 * 
 * This module is responsible for setting up and initializing our RxDB database instance.
 * It detects the environment (Node.js vs Browser), configures appropriate storage adapters,
 * defines collection schemas, and provides a standardized way to access the database.
 * 
 * Key Features:
 * - Environment-specific storage adapter selection
 * - Zod schema to RxDB JSON schema conversion
 * - Singleton pattern for database instance
 * - Comprehensive error handling and recovery
 * - Multi-platform compatibility
 * - Schema validation with AJV
 * - Filesystem persistence for Node.js
 * 
 * Usage Example:
 * ```typescript
 * // Initialize the database
 * await initializeDb();
 * 
 * // Get the database instance
 * const db = await getDb();
 * const userSettings = await db.user_settings.findOne().exec();
 * ```
 */

import { createRxDatabase, addRxPlugin, RxDatabase, RxJsonSchema } from 'rxdb';
import { RxDBDevModePlugin } from 'rxdb/plugins/dev-mode';
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
import { RxDBLeaderElectionPlugin } from 'rxdb/plugins/leader-election';
import { wrappedValidateAjvStorage } from 'rxdb/plugins/validate-ajv';
import { zodToJsonSchema } from 'zod-to-json-schema';

// Import environment detection utility
import { isNodeEnvironment } from '../utils/environment';

// Import all Zod schemas for collections
import { UserSettingsSchema } from '../types/userSettings.types';
import { TaskSessionSchema } from '../types/tasks.types';
import { ChatThreadSchema, ChatMessageSchema } from '../types/chat.types';
import { N8nRuleSchema } from '../types/rules.types';
import { KnowledgeChunkSchema } from '../types/knowledge.types';

// Import database types
import type { N8nAidDatabase, N8nAidDbCollections, DatabaseInitializationOptions, DatabaseStatus } from './types/database.types';

/**
 * Storage adapter imports - conditionally imported based on environment
 */
let getRxStorageDexie: any;
let getRxStorageLocalstorage: any;

// Conditional imports for platform-specific adapters
async function loadStorageAdapters() {
  if (isNodeEnvironment()) {
    // Node.js environment - use localStorage storage which provides persistence via filesystem
    // This gives us actual file-based persistence unlike the memory storage
    const localstorageModule = await import('rxdb/plugins/storage-localstorage');
    getRxStorageLocalstorage = localstorageModule.getRxStorageLocalstorage;
  } else {
    // Browser environment - use Dexie (IndexedDB)
    const dexieModule = await import('rxdb/plugins/storage-dexie');
    getRxStorageDexie = dexieModule.getRxStorageDexie;
  }
}

/**
 * Global database instance variable for singleton pattern
 */
let n8nAidDbPromise: Promise<N8nAidDatabase> | null = null;
let dbStatus: DatabaseStatus = {
  isInitialized: false,
  name: '',
  environment: isNodeEnvironment() ? 'node' : 'browser',
  storageAdapter: '',
  collections: [],
};

/**
 * Plugin registration - ensure plugins are added only once
 */
let pluginsRegistered = false;

function registerPlugins(): void {
  if (pluginsRegistered) return;

  try {
    // Development mode plugin for debugging
    if (process.env.NODE_ENV === 'development') {
      addRxPlugin(RxDBDevModePlugin);
    }

    // Core plugins
    addRxPlugin(RxDBQueryBuilderPlugin);

    // Leader election only for browser environments (multi-tab coordination)
    if (!isNodeEnvironment()) {
      addRxPlugin(RxDBLeaderElectionPlugin);
    }

    pluginsRegistered = true;
    console.log('RxDB plugins registered successfully');
  } catch (error) {
    console.error('Failed to register RxDB plugins:', error);
    throw new Error(`Plugin registration failed: ${error}`);
  }
}

/**
 * Reset plugin registration state - for testing purposes only
 * @internal
 */
export function resetPluginRegistration(): void {
  pluginsRegistered = false;
}

/**
 * Reset database status state - for testing purposes only
 * @internal
 */
export function resetDbStatus(): void {
  dbStatus = {
    isInitialized: false,
    name: '',
    environment: isNodeEnvironment() ? 'node' : 'browser',
    storageAdapter: '',
    collections: [],
  };
}

/**
 * Converts a Zod schema to RxDB-compatible JSON schema
 */
function zodSchemaToRxDBSchema(
  zodSchema: any,
  schemaName: string,
  title: string,
  primaryKey: string,
  indexes?: (string | string[])[]
): RxJsonSchema<any> {
  try {
    const baseSchema = zodToJsonSchema(zodSchema, schemaName);
    
    // Ensure the schema has the correct structure for RxDB
    const rxdbSchema: RxJsonSchema<any> = {
      title,
      version: 0,
      primaryKey,
      type: 'object',
      properties: (baseSchema as any).properties || {},
      required: (baseSchema as any).required || [],
      indexes: indexes || [],
      additionalProperties: false, // RxDB requires this to be false
    };

    return rxdbSchema;
  } catch (error) {
    console.error(`Failed to convert Zod schema ${schemaName} to RxDB schema:`, error);
    throw new Error(`Schema conversion failed for ${schemaName}: ${error}`);
  }
}

/**
 * Creates RxDB JSON schemas for all collections
 */
function createCollectionSchemas(): Record<string, RxJsonSchema<any>> {
  try {
    return {
      user_settings: zodSchemaToRxDBSchema(
        UserSettingsSchema,
        'UserSettingsSchema',
        'User Settings',
        'id'
      ),
      
      task_sessions: zodSchemaToRxDBSchema(
        TaskSessionSchema,
        'TaskSessionSchema',
        'Task Sessions',
        'id',
        ['status', 'chatId', 'updatedAt', ['status', 'chatId']]
      ),
      
      chat_threads: zodSchemaToRxDBSchema(
        ChatThreadSchema,
        'ChatThreadSchema',
        'Chat Threads',
        'id',
        ['status', 'lastUpdatedAt', 'isArchived']
      ),
      
      conversation_messages: zodSchemaToRxDBSchema(
        ChatMessageSchema,
        'ChatMessageSchema',
        'Conversation Messages',
        'id',
        ['chatId', 'timestamp', 'role', ['chatId', 'timestamp'], ['chatId', 'sequenceNumber']]
      ),
      
      custom_rules: zodSchemaToRxDBSchema(
        N8nRuleSchema,
        'N8nRuleSchema',
        'Custom Rules',
        'id',
        ['type', 'appliesToAgents', 'isActive', ['type', 'isActive']]
      ),
      
      knowledge_chunks: zodSchemaToRxDBSchema(
        KnowledgeChunkSchema,
        'KnowledgeChunkSchema',
        'Knowledge Chunks',
        'id',
        ['sourceType', 'isActive', 'idx0', 'idx1', 'idx2', 'idx3', 'idx4', ['sourceType', 'isActive']]
      ),
    };
  } catch (error) {
    console.error('Failed to create collection schemas:', error);
    throw new Error(`Collection schema creation failed: ${error}`);
  }
}

/**
 * Get the appropriate storage adapter for the current environment
 */
async function getStorageAdapter() {
  await loadStorageAdapters();
  
  if (isNodeEnvironment()) {
    // Node.js: Use localStorage storage with filesystem persistence
    // This provides actual file-based persistence in Node.js environments
    const baseStorage = getRxStorageLocalstorage();
    
    // Wrap with AJV validation for schema checking
    return wrappedValidateAjvStorage({
      storage: baseStorage
    });
  } else {
    // Browser: Use Dexie (IndexedDB) storage
    const baseStorage = getRxStorageDexie();
    
    // Wrap with AJV validation for schema checking
    return wrappedValidateAjvStorage({
      storage: baseStorage
    });
  }
}

/**
 * Initialize the RxDB database instance
 */
export async function initializeDb(options: DatabaseInitializationOptions = {}): Promise<N8nAidDatabase> {
  // Return existing promise if already initialized
  if (n8nAidDbPromise) {
    return n8nAidDbPromise;
  }

  n8nAidDbPromise = (async (): Promise<N8nAidDatabase> => {
    try {
      console.log('Initializing n8n AI Assistant database...');
      
      // Set default options
      const {
        databaseName = 'n8n-ai-assistant',
        multiInstance = !isNodeEnvironment(), // Enable multi-instance for browser (multi-tab), disable for Node.js
        ignoreDuplicate = true,
        cleanup = true,
        password,
        ...otherOptions
      } = options;

      // Register plugins first
      registerPlugins();
      
      // Get appropriate storage adapter
      const storageAdapter = await getStorageAdapter();
      const adapterName = isNodeEnvironment() ? 'localStorage-node' : 'dexie-browser';

      // Update status
      dbStatus.name = databaseName;
      dbStatus.storageAdapter = adapterName;

      // Create database instance
      const database = await createRxDatabase<N8nAidDbCollections>({
        name: databaseName,
        storage: storageAdapter,
        multiInstance,
        ignoreDuplicate,
        ...(password && { password }),
        ...otherOptions,
        cleanupPolicy: cleanup ? {
          /**
           * The minimum time in milliseconds of how long a document must have been deleted
           * until it is purged by the cleanup.
           */
          minimumDeletedTime: 1000 * 60 * 60 * 24 * 30, // 30 days
          /**
           * The minimum amount of that that the RxCollection must have existed.
           * This ensures that at the initial page load, more important
           * tasks are not slowed down because a cleanup process is running.
           */
          minimumCollectionAge: 1000 * 60, // 1 minute
          /**
           * After the initial cleanup is done, a new cleanup is started after [runEach] milliseconds
           */
          runEach: 1000 * 60 * 5, // 5 minutes
          /**
           * If set to true, RxDB will await all running cleanups before the database is destroyed.
           * This ensures that no orphan cleanup process is running when the database is closed.
           */
          awaitReplicationsInSync: true,
          /**
           * If true, it will only start the cleanup when the current instance is also the leader.
           * This ensures that when RxDB is used in multiInstance mode, only one instance will start the cleanup.
           */
          waitForLeadership: !isNodeEnvironment(), // Only for browser with multi-instance
        } : undefined,
      });

      // Create collection schemas
      const collectionSchemas = createCollectionSchemas();

      // Add collections to database
      await database.addCollections({
        user_settings: { schema: collectionSchemas.user_settings },
        task_sessions: { schema: collectionSchemas.task_sessions },
        chat_threads: { schema: collectionSchemas.chat_threads },
        conversation_messages: { schema: collectionSchemas.conversation_messages },
        custom_rules: { schema: collectionSchemas.custom_rules },
        knowledge_chunks: { schema: collectionSchemas.knowledge_chunks },
      });

      // Update status
      dbStatus.isInitialized = true;
      dbStatus.collections = Object.keys(collectionSchemas);

      console.log(`n8n AI Assistant database initialized successfully with ${adapterName} storage`);
      console.log(`Collections created: ${dbStatus.collections.join(', ')}`);

      return database as N8nAidDatabase;

    } catch (error) {
      console.error('Failed to initialize database:', error);
      
      // Reset promise to allow retry
      n8nAidDbPromise = null;
      dbStatus.isInitialized = false;
      
      // Throw a more descriptive error
      throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  })();

  return n8nAidDbPromise;
}

/**
 * Get the database instance
 * 
 * @throws {Error} If database has not been initialized yet
 */
export async function getDb(): Promise<N8nAidDatabase> {
  if (!n8nAidDbPromise) {
    throw new Error('Database not initialized. Call initializeDb() first.');
  }
  
  try {
    return await n8nAidDbPromise;
  } catch (error) {
    // Reset promise on error to allow retry
    n8nAidDbPromise = null;
    throw error;
  }
}

/**
 * Get the current database status
 */
export function getDbStatus(): DatabaseStatus {
  return { ...dbStatus };
}

/**
 * Destroy the database instance and clean up resources
 */
export async function destroyDb(): Promise<void> {
  if (!n8nAidDbPromise) {
    console.log('Database not initialized, nothing to destroy');
    return;
  }

  try {
    const db = await n8nAidDbPromise;
    await db.remove();
    
    console.log('Database destroyed successfully');
  } catch (error) {
    console.error('Error destroying database:', error);
    throw error;
  } finally {
    // Reset state regardless of success/failure
    n8nAidDbPromise = null;
    dbStatus.isInitialized = false;
    dbStatus.collections = [];
  }
}

/**
 * Check if the database is ready for use
 */
export function isDbReady(): boolean {
  return dbStatus.isInitialized && n8nAidDbPromise !== null;
}

// Export types for external use
export type { N8nAidDatabase, N8nAidDbCollections, DatabaseInitializationOptions, DatabaseStatus } from './types/database.types'; 