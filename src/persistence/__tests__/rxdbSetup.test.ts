/**
 * RxDB Setup Tests
 * 
 * Comprehensive test suite for the RxDB setup and initialization module.
 * Tests environment detection, database initialization, schema conversion,
 * singleton pattern, error handling, and AJV validation.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Import the modules to test
import {
  initializeDb,
  getDb,
  getDbStatus,
  destroyDb,
  isDbReady,
  resetPluginRegistration,
  resetDbStatus,
} from '../rxdbSetup';

// Mock environment detection
const mockIsNodeEnvironment = vi.hoisted(() => vi.fn());
vi.mock('../../utils/environment', () => ({
  isNodeEnvironment: mockIsNodeEnvironment,
}));

// Mock zod-to-json-schema
const mockZodToJsonSchema = vi.hoisted(() => vi.fn());
vi.mock('zod-to-json-schema', () => ({
  zodToJsonSchema: mockZodToJsonSchema,
}));

// Mock RxDB modules
const mockCreateRxDatabase = vi.hoisted(() => vi.fn());
const mockAddRxPlugin = vi.hoisted(() => vi.fn());
const mockGetRxStorageDexie = vi.hoisted(() => vi.fn());
const mockGetRxStorageLocalstorage = vi.hoisted(() => vi.fn());
const mockWrappedValidateAjvStorage = vi.hoisted(() => vi.fn());

vi.mock('rxdb', () => ({
  createRxDatabase: mockCreateRxDatabase,
  addRxPlugin: mockAddRxPlugin,
}));

vi.mock('rxdb/plugins/dev-mode', () => ({
  RxDBDevModePlugin: { name: 'dev-mode-plugin' },
}));

vi.mock('rxdb/plugins/query-builder', () => ({
  RxDBQueryBuilderPlugin: { name: 'query-builder-plugin' },
}));

vi.mock('rxdb/plugins/leader-election', () => ({
  RxDBLeaderElectionPlugin: { name: 'leader-election-plugin' },
}));

vi.mock('rxdb/plugins/validate-ajv', () => ({
  wrappedValidateAjvStorage: mockWrappedValidateAjvStorage,
}));

vi.mock('rxdb/plugins/storage-dexie', () => ({
  getRxStorageDexie: mockGetRxStorageDexie,
}));

vi.mock('rxdb/plugins/storage-localstorage', () => ({
  getRxStorageLocalstorage: mockGetRxStorageLocalstorage,
}));

describe('RxDB Setup', () => {
  let mockDb: any;
  let mockDexieStorage: any;
  let mockLocalStorage: any;
  let mockValidatedStorage: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset plugin registration state for each test
    resetPluginRegistration();
    
    // Reset environment detection - MUST be done before importing status
    mockIsNodeEnvironment.mockReturnValue(false); // Default to browser
    
    // Reset database status for each test
    resetDbStatus();
    
    // Setup mock schema conversion
    mockZodToJsonSchema.mockReturnValue({
      type: 'object',
      properties: { id: { type: 'string' } },
      required: ['id'],
    });
    
    // Setup mock database
    mockDb = {
      addCollections: vi.fn().mockResolvedValue({}),
      remove: vi.fn().mockResolvedValue(undefined),
      $: {
        subscribe: vi.fn(),
      },
      user_settings: {},
      task_sessions: {},
      chat_threads: {},
      conversation_messages: {},
      custom_rules: {},
      knowledge_chunks: {},
    };
    
    // Setup storage adapters
    mockDexieStorage = {
      name: 'dexie',
      createStorageInstance: vi.fn(),
    };
    
    mockLocalStorage = {
      name: 'localstorage',
      createStorageInstance: vi.fn(),
    };
    
    mockValidatedStorage = {
      name: 'validate-ajv-storage',
      createStorageInstance: vi.fn(),
    };
    
    mockGetRxStorageDexie.mockReturnValue(mockDexieStorage);
    mockGetRxStorageLocalstorage.mockReturnValue(mockLocalStorage);
    mockWrappedValidateAjvStorage.mockReturnValue(mockValidatedStorage);
    
    // Setup database creation
    mockCreateRxDatabase.mockResolvedValue(mockDb);
  });

  afterEach(async () => {
    // Clean up any existing database instance
    try {
      await destroyDb();
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Environment Detection', () => {
    it('should use Dexie storage adapter with AJV validation in browser environment', async () => {
      mockIsNodeEnvironment.mockReturnValue(false);
      
      await initializeDb();
      
      expect(mockGetRxStorageDexie).toHaveBeenCalled();
      expect(mockWrappedValidateAjvStorage).toHaveBeenCalledWith({
        storage: mockDexieStorage,
      });
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          storage: mockValidatedStorage,
        })
      );
    });

    it('should use LocalStorage storage adapter with AJV validation in Node.js environment', async () => {
      mockIsNodeEnvironment.mockReturnValue(true);
      
      await initializeDb();
      
      expect(mockGetRxStorageLocalstorage).toHaveBeenCalled();
      expect(mockWrappedValidateAjvStorage).toHaveBeenCalledWith({
        storage: mockLocalStorage,
      });
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          storage: mockValidatedStorage,
        })
      );
    });

    it('should register leader election plugin only in browser', async () => {
      mockIsNodeEnvironment.mockReturnValue(false);
      
      await initializeDb();
      
      expect(mockAddRxPlugin).toHaveBeenCalledWith({ name: 'leader-election-plugin' });
    });

    it('should not register leader election plugin in Node.js', async () => {
      mockIsNodeEnvironment.mockReturnValue(true);
      
      await initializeDb();
      
      expect(mockAddRxPlugin).not.toHaveBeenCalledWith({ name: 'leader-election-plugin' });
    });
  });

  describe('Database Initialization', () => {
    it('should initialize database with default options', async () => {
      const db = await initializeDb();
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'n8n-ai-assistant',
          multiInstance: true, // Browser default
          ignoreDuplicate: true,
          cleanupPolicy: expect.objectContaining({
            minimumDeletedTime: 1000 * 60 * 60 * 24 * 30, // 30 days
            minimumCollectionAge: 1000 * 60, // 1 minute
            runEach: 1000 * 60 * 5, // 5 minutes
            awaitReplicationsInSync: true,
            waitForLeadership: true, // Browser environment
          }),
        })
      );
      
      expect(db).toBe(mockDb);
    });

    it('should initialize database with custom options', async () => {
      const options = {
        databaseName: 'custom-db',
        multiInstance: false,
        cleanup: false,
      };
      
      await initializeDb(options);
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'custom-db',
          multiInstance: false,
          cleanupPolicy: undefined, // cleanup disabled
        })
      );
    });

    it('should disable cleanup policy when cleanup option is false', async () => {
      await initializeDb({ cleanup: false });
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          cleanupPolicy: undefined,
        })
      );
    });

    it('should configure cleanup policy for Node.js environment', async () => {
      mockIsNodeEnvironment.mockReturnValue(true);
      
      await initializeDb();
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          cleanupPolicy: expect.objectContaining({
            waitForLeadership: false, // Node.js environment
          }),
        })
      );
    });

    it('should create all required collections', async () => {
      await initializeDb();
      
      expect(mockDb.addCollections).toHaveBeenCalledWith({
        user_settings: { schema: expect.any(Object) },
        task_sessions: { schema: expect.any(Object) },
        chat_threads: { schema: expect.any(Object) },
        conversation_messages: { schema: expect.any(Object) },
        custom_rules: { schema: expect.any(Object) },
        knowledge_chunks: { schema: expect.any(Object) },
      });
    });

    it('should register required plugins', async () => {
      await initializeDb();
      
      expect(mockAddRxPlugin).toHaveBeenCalledWith({ name: 'query-builder-plugin' });
    });

    it('should register dev mode plugin in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';
      
      await initializeDb();
      
      expect(mockAddRxPlugin).toHaveBeenCalledWith({ name: 'dev-mode-plugin' });
      
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('AJV Validation', () => {
    it('should wrap storage with AJV validation in browser', async () => {
      mockIsNodeEnvironment.mockReturnValue(false);
      
      await initializeDb();
      
      expect(mockWrappedValidateAjvStorage).toHaveBeenCalledWith({
        storage: mockDexieStorage,
      });
    });

    it('should wrap storage with AJV validation in Node.js', async () => {
      mockIsNodeEnvironment.mockReturnValue(true);
      
      await initializeDb();
      
      expect(mockWrappedValidateAjvStorage).toHaveBeenCalledWith({
        storage: mockLocalStorage,
      });
    });
  });

  describe('Singleton Pattern', () => {
    it('should return the same database instance on multiple calls', async () => {
      const db1 = await initializeDb();
      const db2 = await initializeDb();
      
      expect(db1).toBe(db2);
      expect(mockCreateRxDatabase).toHaveBeenCalledTimes(1);
    });

    it('should allow re-initialization after destroy', async () => {
      await initializeDb();
      await destroyDb();
      await initializeDb();
      
      expect(mockCreateRxDatabase).toHaveBeenCalledTimes(2);
    });
  });

  describe('Database Status', () => {
    it('should return correct status when not initialized', () => {
      const status = getDbStatus();
      
      expect(status.isInitialized).toBe(false);
      expect(status.name).toBe('');
      expect(status.collections).toEqual([]);
      expect(status.environment).toBe('browser'); // Default environment
    });

    it('should return correct status when initialized in browser', async () => {
      mockIsNodeEnvironment.mockReturnValue(false);
      
      await initializeDb();
      const status = getDbStatus();
      
      expect(status.isInitialized).toBe(true);
      expect(status.name).toBe('n8n-ai-assistant');
      expect(status.environment).toBe('browser');
      expect(status.storageAdapter).toBe('dexie-browser');
      expect(status.collections).toHaveLength(6);
      expect(status.collections).toContain('user_settings');
      expect(status.collections).toContain('task_sessions');
      expect(status.collections).toContain('chat_threads');
      expect(status.collections).toContain('conversation_messages');
      expect(status.collections).toContain('custom_rules');
      expect(status.collections).toContain('knowledge_chunks');
    });

    it('should track Node.js environment in status', async () => {
      mockIsNodeEnvironment.mockReturnValue(true);
      resetDbStatus(); // Reset after setting the environment
      
      await initializeDb();
      const status = getDbStatus();
      
      expect(status.environment).toBe('node');
      expect(status.storageAdapter).toBe('localStorage-node');
    });

    it('should update status with custom database name', async () => {
      await initializeDb({ databaseName: 'my-custom-db' });
      const status = getDbStatus();
      
      expect(status.name).toBe('my-custom-db');
    });
  });

  describe('Database Ready Check', () => {
    it('should return false when not initialized', () => {
      expect(isDbReady()).toBe(false);
    });

    it('should return true when initialized', async () => {
      await initializeDb();
      expect(isDbReady()).toBe(true);
    });

    it('should return false after destroy', async () => {
      await initializeDb();
      await destroyDb();
      expect(isDbReady()).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle database creation failure', async () => {
      const error = new Error('Database creation failed');
      mockCreateRxDatabase.mockRejectedValueOnce(error);
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed: Database creation failed');
      
      const status = getDbStatus();
      expect(status.isInitialized).toBe(false);
    });

    it('should handle collection creation failure', async () => {
      const error = new Error('Collection creation failed');
      mockDb.addCollections.mockRejectedValueOnce(error);
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed: Collection creation failed');
    });

    it('should reset singleton on initialization failure', async () => {
      mockCreateRxDatabase.mockRejectedValueOnce(new Error('Init failed'));
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed: Init failed');
      
      // Should allow retry
      mockCreateRxDatabase.mockResolvedValueOnce(mockDb);
      await expect(initializeDb()).resolves.toBe(mockDb);
    });

    it('should throw error when getting uninitialized database', async () => {
      await expect(getDb()).rejects.toThrow('Database not initialized. Call initializeDb() first.');
    });

    it('should handle storage adapter loading failure', async () => {
      const error = new Error('Storage adapter failed');
      mockGetRxStorageDexie.mockImplementationOnce(() => {
        throw error;
      });
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed');
    });

    it('should handle AJV validation wrapper failure', async () => {
      const error = new Error('AJV validation failed');
      mockWrappedValidateAjvStorage.mockImplementationOnce(() => {
        throw error;
      });
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed');
    });
  });

  describe('Database Destruction', () => {
    it('should destroy database and reset state', async () => {
      await initializeDb();
      expect(isDbReady()).toBe(true);
      
      await destroyDb();
      
      expect(mockDb.remove).toHaveBeenCalled();
      expect(isDbReady()).toBe(false);
      
      const status = getDbStatus();
      expect(status.isInitialized).toBe(false);
      expect(status.collections).toEqual([]);
    });

    it('should handle destruction errors gracefully', async () => {
      await initializeDb();
      
      const error = new Error('Destruction failed');
      mockDb.remove.mockRejectedValueOnce(error);
      
      // Should throw the error (changed behavior from old implementation)
      await expect(destroyDb()).rejects.toThrow('Destruction failed');
      
      // Should still reset state
      expect(isDbReady()).toBe(false);
    });

    it('should handle destroy when not initialized', async () => {
      // Should not throw
      await expect(destroyDb()).resolves.toBeUndefined();
    });
  });

  describe('Schema Conversion', () => {
    it('should create valid RxDB schemas from Zod schemas', async () => {
      await initializeDb();
      
      const addCollectionsCall = mockDb.addCollections.mock.calls[0][0];
      
      // Check that all schemas have required RxDB properties
      Object.values(addCollectionsCall).forEach((collection: any) => {
        const schema = collection.schema;
        expect(schema).toHaveProperty('title');
        expect(schema).toHaveProperty('version', 0);
        expect(schema).toHaveProperty('primaryKey', 'id');
        expect(schema).toHaveProperty('type', 'object');
        expect(schema).toHaveProperty('properties');
        expect(schema).toHaveProperty('additionalProperties', false);
        expect(schema).toHaveProperty('indexes');
      });
    });

    it('should include appropriate indexes for different collections', async () => {
      await initializeDb();
      
      const addCollectionsCall = mockDb.addCollections.mock.calls[0][0];
      
      // Check specific indexes for knowledge_chunks (vector database indexes)
      const knowledgeChunksSchema = addCollectionsCall.knowledge_chunks.schema;
      expect(knowledgeChunksSchema.indexes).toContain('idx0');
      expect(knowledgeChunksSchema.indexes).toContain('idx1');
      expect(knowledgeChunksSchema.indexes).toContain('idx2');
      expect(knowledgeChunksSchema.indexes).toContain('idx3');
      expect(knowledgeChunksSchema.indexes).toContain('idx4');
      
      // Check composite indexes
      expect(knowledgeChunksSchema.indexes).toContainEqual(['sourceType', 'isActive']);
    });

    it('should handle schema conversion errors gracefully', async () => {
      // Mock a failed schema conversion by making zodToJsonSchema throw
      mockZodToJsonSchema.mockImplementationOnce(() => {
        throw new Error('Schema conversion failed');
      });
      
      await expect(initializeDb()).rejects.toThrow('Database initialization failed');
    });
  });

  describe('Configuration Options', () => {
    it('should support password option for encryption', async () => {
      await initializeDb({ password: 'test-password' });
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          password: 'test-password',
        })
      );
    });

    it('should respect ignoreDuplicate option', async () => {
      await initializeDb({ ignoreDuplicate: false });
      
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          ignoreDuplicate: false,
        })
      );
    });

    it('should configure multiInstance based on environment by default', async () => {
      // Browser environment
      mockIsNodeEnvironment.mockReturnValue(false);
      await initializeDb();
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          multiInstance: true,
        })
      );
      
      // Reset and destroy for next test
      await destroyDb();
      mockCreateRxDatabase.mockClear();
      
      // Node.js environment
      mockIsNodeEnvironment.mockReturnValue(true);
      await initializeDb();
      expect(mockCreateRxDatabase).toHaveBeenCalledWith(
        expect.objectContaining({
          multiInstance: false,
        })
      );
    });
  });
}); 