/**
 * Persistence Layer Index
 * 
 * Central export point for all persistence-related functionality including
 * database setup, repositories, and type definitions.
 */

// Export database setup and utilities
export { 
  initializeDb, 
  getDb, 
  destroyDb, 
  isDbReady,
  getDbStatus
} from './rxdbSetup';

// Export database types
export type {
  N8nAidDatabase,
  N8nAidDbCollections,
  DatabaseInitializationOptions,
  DatabaseStatus,
  
  // Document types
  UserSettingsDocument,
  UserSettingsCollection,
  TaskSessionDocument,
  TaskSessionCollection,
  ChatThreadDocument,
  ChatThreadCollection,
  ChatMessageDocument,
  ChatMessageCollection,
  CustomRuleDocument,
  CustomRuleCollection,
  KnowledgeChunkDocument,
  KnowledgeChunkCollection,
} from './types/database.types';

// Export repositories
export { UserSettingsRepository } from './repositories/UserSettingsRepository';
export { CustomRulesRepository } from './repositories/CustomRulesRepository';
export { TaskSessionRepository } from './repositories/TaskSessionRepository';

// Export repository types
export type { RuleQueryOptions } from './repositories/CustomRulesRepository';
export type { TaskSessionQueryOptions } from './repositories/TaskSessionRepository';

// TODO: Export other repositories as they are implemented
// export { ChatRepository } from './repositories/ChatRepository';
// export { KnowledgeRepository } from './repositories/KnowledgeRepository'; 