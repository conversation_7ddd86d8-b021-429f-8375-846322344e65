/**
 * Custom Rules Repository
 * 
 * Provides a data access layer for interacting with the custom_rules RxDB collection.
 * It encapsulates all direct RxDB CRUD operations for user-defined N8nRules.
 * 
 * Key Features:
 * - Standard CRUD operations for custom rules
 * - Query operations with filtering (by type, agent, active status)
 * - Automatic ID generation and timestamp management
 * - Type-safe interactions with RxDB using Zod validation
 * - Comprehensive error handling and logging
 * 
 * Usage Example:
 * ```typescript
 * const repository = new CustomRulesRepository();
 * const rule = await repository.addRule({
 *   name: "Always use error workflow",
 *   description: "Ensures error handling",
 *   type: "ALWAYS_APPLY",
 *   // ... other rule properties
 * });
 * const rules = await repository.getActiveRulesForAgent('n8n-architect-agent');
 * ```
 */

import { 
  N8nAidDatabase, 
  CustomRuleCollection, 
  CustomRuleDocument, 
  CustomRuleDocType 
} from '../types/database.types';
import { N8nRule, N8nRuleSchema, RuleType } from '../../types/rules.types';
import { getDb } from '../rxdbSetup';
import { generateUUID } from '../../utils/uuid';

/**
 * Query options for filtering custom rules
 */
export interface RuleQueryOptions {
  /** Filter by active status */
  isActive?: boolean;
  /** Filter by rule type */
  type?: RuleType;
  /** Find rules applicable to a specific agent */
  appliesToAgent?: string;
  /** Filter by rule source */
  source?: 'builtin' | 'user_defined' | 'community';
  /** Filter by category */
  category?: string;
  /** Limit the number of results */
  limit?: number;
  /** Skip a number of results (for pagination) */
  skip?: number;
}

/**
 * Repository for managing custom rules in RxDB
 */
export class CustomRulesRepository {
  private readonly dbPromise: Promise<N8nAidDatabase>;

  /**
   * Creates a new CustomRulesRepository instance
   * @param dbPromise - Optional database promise for dependency injection (useful for testing)
   */
  constructor(dbPromise?: Promise<N8nAidDatabase>) {
    this.dbPromise = dbPromise || getDb();
  }

  /**
   * Gets the custom_rules collection from the database
   * @private
   */
  private async getCollection(): Promise<CustomRuleCollection> {
    const db = await this.dbPromise;
    return db.custom_rules;
  }

  /**
   * Adds a new custom rule to the database
   * @param ruleData - Rule data without id, createdAt, updatedAt
   * @returns Promise resolving to the created rule
   */
  public async addRule(ruleData: Omit<N8nRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<N8nRule> {
    try {
      const collection = await this.getCollection();
      
      // Create full rule object with generated ID and timestamps
      const newRule: CustomRuleDocType = {
        id: generateUUID(),
        ...ruleData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Validate the rule data using Zod schema
      const validatedRule = N8nRuleSchema.parse(newRule);
      
      // Insert into database
      const doc = await collection.insert(validatedRule);
      
      console.log(`CustomRulesRepository: Added new rule with ID: ${newRule.id}`);
      return doc.toJSON() as N8nRule;
    } catch (error) {
      console.error('CustomRulesRepository: Failed to add rule:', error);
      throw new Error(`Failed to add custom rule: ${error}`);
    }
  }

  /**
   * Retrieves a rule by its ID
   * @param id - Rule ID
   * @returns Promise resolving to the rule or null if not found
   */
  public async getRuleById(id: string): Promise<N8nRule | null> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      return doc ? (doc.toJSON() as N8nRule) : null;
    } catch (error) {
      console.error(`CustomRulesRepository: Failed to get rule by ID ${id}:`, error);
      throw new Error(`Failed to retrieve rule by ID: ${error}`);
    }
  }

  /**
   * Updates an existing rule
   * @param id - Rule ID to update
   * @param updates - Partial rule data to update
   * @returns Promise resolving to the updated rule or null if not found
   */
  public async updateRule(
    id: string, 
    updates: Partial<Omit<N8nRule, 'id' | 'createdAt'>>
  ): Promise<N8nRule | null> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      if (!doc) {
        console.warn(`CustomRulesRepository: Attempted to update non-existent rule with ID: ${id}`);
        return null;
      }

      // Prepare update object with timestamp
      const patchData = { 
        ...updates, 
        updatedAt: new Date() 
      };

      // Validate the resulting data would be valid
      const currentData = doc.toJSON();
      const dataToValidate = { ...currentData, ...patchData };
      N8nRuleSchema.parse(dataToValidate);

      // Apply the patch
      const updatedDoc = await doc.incrementalPatch(patchData);
      
      console.log(`CustomRulesRepository: Updated rule with ID: ${id}`);
      return updatedDoc.toJSON() as N8nRule;
    } catch (error) {
      console.error(`CustomRulesRepository: Failed to update rule with ID ${id}:`, error);
      throw new Error(`Failed to update rule: ${error}`);
    }
  }

  /**
   * Deletes a rule by its ID
   * @param id - Rule ID to delete
   * @returns Promise resolving to true if deleted, false if not found
   */
  public async deleteRule(id: string): Promise<boolean> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      if (!doc) {
        console.warn(`CustomRulesRepository: Attempted to delete non-existent rule with ID: ${id}`);
        return false;
      }

      await doc.remove();
      
      console.log(`CustomRulesRepository: Deleted rule with ID: ${id}`);
      return true;
    } catch (error) {
      console.error(`CustomRulesRepository: Failed to delete rule with ID ${id}:`, error);
      throw new Error(`Failed to delete rule: ${error}`);
    }
  }

  /**
   * Retrieves all rules matching the specified options
   * @param options - Query options for filtering
   * @returns Promise resolving to array of matching rules
   */
  public async getAllRules(options: RuleQueryOptions = {}): Promise<N8nRule[]> {
    try {
      const collection = await this.getCollection();
      let query = collection.find();

      // Apply filters based on options
      if (options.isActive !== undefined) {
        query = query.where('isActive').eq(options.isActive);
      }

      if (options.type) {
        query = query.where('type').eq(options.type);
      }

      if (options.source) {
        query = query.where('source').eq(options.source);
      }

      if (options.category) {
        query = query.where('category').eq(options.category);
      }

      if (options.appliesToAgent) {
        // appliesToAgents is an array, so use elemMatch
        query = query.where('appliesToAgents').elemMatch({ $eq: options.appliesToAgent });
      }

      // Apply pagination
      if (options.skip) {
        query = query.skip(options.skip);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      // Execute query
      const docs = await query.exec();
      
      return docs.map(doc => doc.toJSON() as N8nRule);
    } catch (error) {
      console.error('CustomRulesRepository: Failed to get all rules:', error);
      throw new Error(`Failed to retrieve rules: ${error}`);
    }
  }

  /**
   * Retrieves active rules applicable to a specific agent
   * @param agentSlug - The agent slug to find rules for
   * @returns Promise resolving to array of active rules for the agent
   */
  public async getActiveRulesForAgent(agentSlug: string): Promise<N8nRule[]> {
    return this.getAllRules({ 
      isActive: true, 
      appliesToAgent: agentSlug 
    });
  }

  /**
   * Retrieves rules by type
   * @param type - Rule type to filter by
   * @param activeOnly - Whether to only return active rules (default: true)
   * @returns Promise resolving to array of rules of the specified type
   */
  public async getRulesByType(type: RuleType, activeOnly: boolean = true): Promise<N8nRule[]> {
    return this.getAllRules({ 
      type, 
      isActive: activeOnly ? true : undefined 
    });
  }

  /**
   * Counts the total number of rules matching the specified options
   * @param options - Query options for filtering
   * @returns Promise resolving to the count of matching rules
   */
  public async countRules(options: RuleQueryOptions = {}): Promise<number> {
    try {
      const collection = await this.getCollection();
      let query = collection.find();

      // Apply same filters as getAllRules
      if (options.isActive !== undefined) {
        query = query.where('isActive').eq(options.isActive);
      }

      if (options.type) {
        query = query.where('type').eq(options.type);
      }

      if (options.source) {
        query = query.where('source').eq(options.source);
      }

      if (options.category) {
        query = query.where('category').eq(options.category);
      }

      if (options.appliesToAgent) {
        query = query.where('appliesToAgents').elemMatch({ $eq: options.appliesToAgent });
      }

      // Execute count query
      // Note: Current RxDB version/adapter doesn't expose .count() method on query objects
      // Using .exec().length as a functional approach for counting matching documents
      const docs = await query.exec();
      const count = docs.length;
      
      return count;
    } catch (error) {
      console.error('CustomRulesRepository: Failed to count rules:', error);
      throw new Error(`Failed to count rules: ${error}`);
    }
  }
} 