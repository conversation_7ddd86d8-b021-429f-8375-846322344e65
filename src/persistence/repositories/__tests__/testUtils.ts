/**
 * Test Utilities for Repository Layer
 * 
 * Common utilities, mocks, and helpers for testing repository components.
 * Provides reusable mock factories and test data for consistent testing.
 */

import { vi, expect, type MockedFunction } from 'vitest';
import type { CustomRuleCollection, CustomRuleDocument, N8nAidDatabase } from '../../types/database.types';
import { N8nRule, RuleType, RulePriority } from '../../../types/rules.types';

/**
 * Generic mock database creator
 */
export function createMockDatabase(collections: Partial<any> = {}): any {
  return {
    ...collections,
    destroy: vi.fn(),
    remove: vi.fn(),
  };
}

/**
 * Generic mock collection creator
 */
export function createMockCollection(overrides: Partial<any> = {}): any {
  const defaults = {
    insert: vi.fn(),
    findOne: vi.fn(),
    find: vi.fn(),
    count: vi.fn(),
    remove: vi.fn(),
  };
  
  return { ...defaults, ...overrides };
}

/**
 * Generic mock document creator
 */
export function createMockDocument(data: any, overrides: Partial<any> = {}): any {
  const defaults = {
    toJSON: vi.fn().mockReturnValue(data),
    toMutableJSON: vi.fn().mockReturnValue(data),
    incrementalPatch: vi.fn(),
    remove: vi.fn(),
    ...overrides,
  };
  
  return defaults;
}

/**
 * Create a mock CustomRuleCollection with configurable responses
 */
export function createMockCustomRuleCollection(
  overrides: Partial<CustomRuleCollection> = {}
): CustomRuleCollection {
  return createMockCollection(overrides);
}

/**
 * Create a mock CustomRuleDocument with configurable responses
 */
export function createMockCustomRuleDocument(
  data: N8nRule,
  overrides: Partial<CustomRuleDocument> = {}
): CustomRuleDocument {
  return createMockDocument(data, overrides);
}

/**
 * Factory for creating test N8nRule objects
 */
export function createTestRule(
  overrides: Partial<N8nRule> = {}
): N8nRule {
  const defaults: N8nRule = {
    id: `test-rule-${Math.random().toString(36).substr(2, 9)}`,
    name: 'Test Rule',
    description: 'A test rule for unit testing',
    descriptionForLLM: 'This is a test rule description optimized for LLM understanding.',
    type: 'ALWAYS_APPLY',
    priority: 'medium',
    category: 'test-category',
    appliesToAgents: ['test-agent'],
    appliesToNodeTypes: [],
    triggerContext: {
      keywords: ['test', 'example'],
      workflowPatterns: ['test-pattern'],
    },
    recommendation: 'Follow this test recommendation.',
    example: 'Example: Use this pattern in your workflow.',
    reasoning: 'This rule exists for testing purposes.',
    source: 'user_defined',
    version: 1,
    isActive: true,
    usageCount: 0,
    relatedRules: [],
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    additionalData: {},
  };

  return { ...defaults, ...overrides };
}

/**
 * Factory for creating contextual suggestion rules
 */
export function createTestContextualRule(
  keywords: string[] = ['api', 'webhook'],
  overrides: Partial<N8nRule> = {}
): N8nRule {
  return createTestRule({
    type: 'CONTEXTUAL_SUGGESTION',
    name: 'Test Contextual Rule',
    triggerContext: {
      keywords,
      nodeTypes: ['HTTP Request', 'Webhook'],
    },
    ...overrides,
  });
}

/**
 * Factory for creating always-apply rules
 */
export function createTestAlwaysApplyRule(
  overrides: Partial<N8nRule> = {}
): N8nRule {
  return createTestRule({
    type: 'ALWAYS_APPLY',
    name: 'Test Always Apply Rule',
    priority: 'critical',
    ...overrides,
  });
}

/**
 * Factory for creating anti-pattern rules
 */
export function createTestAntiPatternRule(
  overrides: Partial<N8nRule> = {}
): N8nRule {
  return createTestRule({
    type: 'ANTI_PATTERN_CHECK',
    name: 'Test Anti-Pattern Rule',
    priority: 'high',
    validation: {
      pattern: 'avoid-this-pattern',
      operator: 'notContains',
    },
    ...overrides,
  });
}

/**
 * Helper to create rules for specific agents
 */
export function createRulesForAgent(agentSlug: string, count: number = 3): N8nRule[] {
  return Array.from({ length: count }, (_, index) =>
    createTestRule({
      id: `${agentSlug}-rule-${index + 1}`,
      name: `Rule for ${agentSlug} #${index + 1}`,
      appliesToAgents: [agentSlug],
      type: index % 2 === 0 ? 'ALWAYS_APPLY' : 'CONTEXTUAL_SUGGESTION',
    })
  );
}

/**
 * Helper to create rules with different priorities
 */
export function createRulesWithPriorities(): N8nRule[] {
  const priorities: RulePriority[] = ['critical', 'high', 'medium', 'low', 'informational'];
  return priorities.map((priority, index) =>
    createTestRule({
      id: `priority-rule-${priority}`,
      name: `${priority} Priority Rule`,
      priority,
      type: 'CONTEXTUAL_SUGGESTION',
      triggerContext: {
        keywords: [priority, 'test'],
      },
    })
  );
}

/**
 * Helper to create rules with different categories
 */
export function createRulesWithCategories(): N8nRule[] {
  const categories = ['workflow-design', 'error-handling', 'performance', 'security'];
  return categories.map((category, index) =>
    createTestRule({
      id: `category-rule-${category}`,
      name: `${category} Rule`,
      category,
      triggerContext: {
        keywords: [category],
      },
    })
  );
}

/**
 * Collection of test error objects for consistent error testing
 */
export const TestErrors = {
  insertError: new Error('Insert failed'),
  updateError: new Error('Update failed'),
  deleteError: new Error('Delete failed'),
  findError: new Error('Find failed'),
  validationError: new Error('Validation failed'),
  databaseError: new Error('Database connection failed'),
  queryError: new Error('Query failed'),
} as const;

/**
 * Mock query builder helper
 */
export function createMockQueryBuilder(docs: CustomRuleDocument[] = []) {
  const mockQuery = {
    where: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    elemMatch: vi.fn().mockReturnThis(),
    skip: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    exec: vi.fn().mockResolvedValue(docs),
  };

  return mockQuery;
}

/**
 * Helper to setup RxDB collection mock with standard CRUD operations
 */
export function setupCollectionMock(
  collection: CustomRuleCollection,
  rules: N8nRule[] = []
) {
  const documents = rules.map(rule => createMockCustomRuleDocument(rule));
  
  // Setup insert mock
  collection.insert = vi.fn().mockImplementation(async (data) => {
    const newDoc = createMockCustomRuleDocument(data);
    return newDoc;
  });
  
  // Setup findOne mock
  collection.findOne = vi.fn().mockImplementation((id: string) => ({
    exec: vi.fn().mockResolvedValue(
      documents.find(doc => doc.toJSON().id === id) || null
    ),
  }));
  
  // Setup find mock with query builder
  collection.find = vi.fn().mockImplementation(() => {
    const queryBuilder = createMockQueryBuilder(documents);
    return queryBuilder;
  });
  
  return { collection, documents };
}

/**
 * Common test assertions helper
 */
export const testAssertions = {
  /**
   * Assert that an object has all expected N8nRule properties
   */
  assertRuleStructure(rule: any) {
    expect(rule).toHaveProperty('id');
    expect(rule).toHaveProperty('name');
    expect(rule).toHaveProperty('description');
    expect(rule).toHaveProperty('descriptionForLLM');
    expect(rule).toHaveProperty('type');
    expect(rule).toHaveProperty('priority');
    expect(rule).toHaveProperty('appliesToAgents');
    expect(rule).toHaveProperty('recommendation');
    expect(rule).toHaveProperty('source');
    expect(rule).toHaveProperty('version');
    expect(rule).toHaveProperty('isActive');
    expect(rule).toHaveProperty('createdAt');
    expect(rule).toHaveProperty('updatedAt');
  },

  /**
   * Assert that a rule has valid enum values
   */
  assertRuleValidEnums(rule: N8nRule) {
    expect(['ALWAYS_APPLY', 'CONTEXTUAL_SUGGESTION', 'ANTI_PATTERN_CHECK']).toContain(rule.type);
    expect(['critical', 'high', 'medium', 'low', 'informational']).toContain(rule.priority);
    expect(['builtin', 'user_defined', 'community']).toContain(rule.source);
  },

  /**
   * Assert that rules are sorted correctly by priority
   */
  assertRulesSortedByPriority(rules: N8nRule[], ascending: boolean = false) {
    const priorityValues = {
      critical: 5,
      high: 4,
      medium: 3,
      low: 2,
      informational: 1,
    };

    for (let i = 1; i < rules.length; i++) {
      const prev = priorityValues[rules[i - 1].priority];
      const curr = priorityValues[rules[i].priority];
      
      if (ascending) {
        expect(curr).toBeGreaterThanOrEqual(prev);
      } else {
        expect(curr).toBeLessThanOrEqual(prev);
      }
    }
  },
};

/**
 * Setup console mocks for testing
 */
export function setupConsoleMocks() {
  const originalConsole = global.console;
  
  const mockConsole = {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  };
  
  global.console = { ...originalConsole, ...mockConsole };
  
  return {
    ...mockConsole, // Spread the individual functions directly
    mockConsole, // Also keep the nested structure for compatibility
    restore: () => {
      global.console = originalConsole;
    },
  };
} 