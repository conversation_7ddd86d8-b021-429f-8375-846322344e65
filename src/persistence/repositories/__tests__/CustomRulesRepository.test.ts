/**
 * CustomRulesRepository Unit Tests
 * 
 * Comprehensive test suite for the CustomRulesRepository class, covering:
 * - Constructor and dependency injection
 * - All CRUD operations (Create, Read, Update, Delete)
 * - Query operations with filtering and pagination
 * - Zod validation and error handling
 * - Edge cases and boundary conditions
 * - Mock RxDB interactions and database operations
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { CustomRulesRepository, RuleQueryOptions } from '../CustomRulesRepository';
import { N8nAidDatabase, CustomRuleCollection } from '../../types/database.types';
import { N8nRule, N8nRuleSchema, RuleType, RulePriority } from '../../../types/rules.types';
import {
  createTestRule,
  createTestAlwaysApplyRule,
  createTestContextualRule,
  createTestAntiPatternRule,
  createRulesForAgent,
  createRulesWithPriorities,
  createRulesWithCategories,
  createMockCustomRuleCollection,
  createMockCustomRuleDocument,
  createMockQueryBuilder,
  setupCollectionMock,
  testAssertions,
  setupConsoleMocks,
  TestErrors,
} from './testUtils';

// Mock the database setup
const mockGetDb = vi.hoisted(() => vi.fn());
vi.mock('../../rxdbSetup', () => ({
  getDb: mockGetDb,
}));

// Mock UUID generation
const mockGenerateUUID = vi.hoisted(() => vi.fn());
vi.mock('../../../utils/uuid', () => ({
  generateUUID: mockGenerateUUID,
}));

// Mock Zod validation
const mockN8nRuleSchemaeParse = vi.hoisted(() => vi.fn());
vi.mock('../../../types/rules.types', async (importOriginal) => {
  const actual = await importOriginal<typeof import('../../../types/rules.types')>();
  return {
    ...actual,
    N8nRuleSchema: {
      ...actual.N8nRuleSchema,
      parse: mockN8nRuleSchemaeParse,
    },
  };
});

describe('CustomRulesRepository', () => {
  let repository: CustomRulesRepository;
  let mockDb: N8nAidDatabase;
  let mockCollection: CustomRuleCollection;
  let consoleMocks: ReturnType<typeof setupConsoleMocks>;

  const testRule = createTestRule({
    id: 'test-rule-123',
    name: 'Test Rule',
    description: 'A test rule for unit testing',
  });

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup console mocks
    consoleMocks = setupConsoleMocks();
    
    // Setup UUID generation
    mockGenerateUUID.mockReturnValue('generated-uuid-123');
    
    // Setup Zod validation to pass by default
    mockN8nRuleSchemaeParse.mockImplementation((data) => data);
    
    // Setup mock collection
    mockCollection = createMockCustomRuleCollection();
    
    // Setup mock database
    mockDb = {
      custom_rules: mockCollection,
    } as any;
    
    mockGetDb.mockResolvedValue(mockDb);
    
    // Create repository instance
    repository = new CustomRulesRepository();
  });

  afterEach(() => {
    consoleMocks.restore();
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should accept optional database promise for dependency injection', () => {
      const customDbPromise = Promise.resolve(mockDb);
      const customRepository = new CustomRulesRepository(customDbPromise);
      
      expect(customRepository).toBeInstanceOf(CustomRulesRepository);
    });

    it('should use default database if no promise provided', () => {
      const defaultRepository = new CustomRulesRepository();
      
      expect(defaultRepository).toBeInstanceOf(CustomRulesRepository);
      expect(mockGetDb).toHaveBeenCalledWith();
    });
  });

  describe('addRule()', () => {
    const ruleData = {
      name: 'New Test Rule',
      description: 'A new test rule',
      descriptionForLLM: 'LLM description',
      type: 'ALWAYS_APPLY' as const,
      priority: 'high' as const,
      appliesToAgents: ['test-agent'],
      appliesToNodeTypes: [],
      recommendation: 'Test recommendation',
      source: 'user_defined' as const,
      version: 1,
      isActive: true,
      usageCount: 0,
      relatedRules: [],
      additionalData: {},
    };

    it('should add a new rule with generated ID and timestamps', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.insert = vi.fn().mockResolvedValue(mockDoc);

      const result = await repository.addRule(ruleData);

      expect(mockGenerateUUID).toHaveBeenCalled();
      expect(mockN8nRuleSchemaeParse).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'generated-uuid-123',
          ...ruleData,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        })
      );
      expect(mockCollection.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'generated-uuid-123',
          ...ruleData,
        })
      );
      expect(result).toEqual(testRule);
    });

    it('should log successful rule addition', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.insert = vi.fn().mockResolvedValue(mockDoc);

      await repository.addRule(ruleData);

      expect(consoleMocks.mockConsole.log).toHaveBeenCalledWith(
        'CustomRulesRepository: Added new rule with ID: generated-uuid-123'
      );
    });

    it('should handle Zod validation errors', async () => {
      const validationError = new Error('Zod validation failed');
      mockN8nRuleSchemaeParse.mockImplementation(() => {
        throw validationError;
      });

      await expect(repository.addRule(ruleData)).rejects.toThrow(
        'Failed to add custom rule: Error: Zod validation failed'
      );
      
      expect(consoleMocks.mockConsole.error).toHaveBeenCalledWith(
        'CustomRulesRepository: Failed to add rule:',
        validationError
      );
    });

    it('should handle database insertion errors', async () => {
      mockCollection.insert = vi.fn().mockRejectedValue(TestErrors.insertError);

      await expect(repository.addRule(ruleData)).rejects.toThrow(
        'Failed to add custom rule: Error: Insert failed'
      );
    });

    it('should validate all required fields are present', async () => {
      const incompleteData = {
        name: 'Incomplete Rule',
        // Missing required fields
      } as any;

      const validationError = new Error('Missing required fields');
      mockN8nRuleSchemaeParse.mockImplementation(() => {
        throw validationError;
      });

      await expect(repository.addRule(incompleteData)).rejects.toThrow();
    });
  });

  describe('getRuleById()', () => {
    it('should return rule when found', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });

      const result = await repository.getRuleById('test-rule-123');

      expect(mockCollection.findOne).toHaveBeenCalledWith('test-rule-123');
      expect(result).toEqual(testRule);
    });

    it('should return null when rule not found', async () => {
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const result = await repository.getRuleById('non-existent-id');

      expect(result).toBeNull();
    });

    it('should handle database query errors', async () => {
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockRejectedValue(TestErrors.findError),
      });

      await expect(repository.getRuleById('test-id')).rejects.toThrow(
        'Failed to retrieve rule by ID: Error: Find failed'
      );
    });
  });

  describe('updateRule()', () => {
    const updates = {
      name: 'Updated Rule Name',
      priority: 'critical' as const,
      isActive: false,
    };

    it('should update existing rule and return updated data', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      const updatedRule = { ...testRule, ...updates, updatedAt: new Date() };
      const updatedMockDoc = createMockCustomRuleDocument(updatedRule);
      
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });
      
      mockDoc.incrementalPatch = vi.fn().mockResolvedValue(updatedMockDoc);

      const result = await repository.updateRule('test-rule-123', updates);

      expect(mockCollection.findOne).toHaveBeenCalledWith('test-rule-123');
      expect(mockN8nRuleSchemaeParse).toHaveBeenCalledWith(
        expect.objectContaining({
          ...testRule,
          ...updates,
          updatedAt: expect.any(Date),
        })
      );
      expect(mockDoc.incrementalPatch).toHaveBeenCalledWith(
        expect.objectContaining({
          ...updates,
          updatedAt: expect.any(Date),
        })
      );
      expect(result).toEqual(updatedRule);
    });

    it('should return null when rule not found', async () => {
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const result = await repository.updateRule('non-existent-id', updates);

      expect(result).toBeNull();
      expect(consoleMocks.mockConsole.warn).toHaveBeenCalledWith(
        'CustomRulesRepository: Attempted to update non-existent rule with ID: non-existent-id'
      );
    });

    it('should handle validation errors for updated data', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });

      const validationError = new Error('Validation failed for update');
      mockN8nRuleSchemaeParse.mockImplementation(() => {
        throw validationError;
      });

      await expect(repository.updateRule('test-rule-123', updates)).rejects.toThrow(
        'Failed to update rule: Error: Validation failed for update'
      );
    });

    it('should handle database update errors', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });
      
      mockDoc.incrementalPatch = vi.fn().mockRejectedValue(TestErrors.updateError);

      await expect(repository.updateRule('test-rule-123', updates)).rejects.toThrow(
        'Failed to update rule: Error: Update failed'
      );
    });

    it('should preserve createdAt timestamp during update', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      const updatedRule = { ...testRule, ...updates, updatedAt: new Date() };
      const updatedMockDoc = createMockCustomRuleDocument(updatedRule);
      
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });
      
      mockDoc.incrementalPatch = vi.fn().mockResolvedValue(updatedMockDoc);

      await repository.updateRule('test-rule-123', updates);

      expect(mockN8nRuleSchemaeParse).toHaveBeenCalledWith(
        expect.objectContaining({
          createdAt: testRule.createdAt, // Should preserve original createdAt
        })
      );
    });
  });

  describe('deleteRule()', () => {
    it('should delete existing rule and return true', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });
      
      mockDoc.remove = vi.fn().mockResolvedValue(undefined);

      const result = await repository.deleteRule('test-rule-123');

      expect(mockCollection.findOne).toHaveBeenCalledWith('test-rule-123');
      expect(mockDoc.remove).toHaveBeenCalled();
      expect(result).toBe(true);
      expect(consoleMocks.mockConsole.log).toHaveBeenCalledWith(
        'CustomRulesRepository: Deleted rule with ID: test-rule-123'
      );
    });

    it('should return false when rule not found', async () => {
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const result = await repository.deleteRule('non-existent-id');

      expect(result).toBe(false);
      expect(consoleMocks.mockConsole.warn).toHaveBeenCalledWith(
        'CustomRulesRepository: Attempted to delete non-existent rule with ID: non-existent-id'
      );
    });

    it('should handle database deletion errors', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc),
      });
      
      mockDoc.remove = vi.fn().mockRejectedValue(TestErrors.deleteError);

      await expect(repository.deleteRule('test-rule-123')).rejects.toThrow(
        'Failed to delete rule: Error: Delete failed'
      );
    });
  });

  describe('getAllRules()', () => {
    const testRules = createRulesForAgent('test-agent', 5);

    beforeEach(() => {
      setupCollectionMock(mockCollection, testRules);
    });

    it('should return all rules when no options provided', async () => {
      const result = await repository.getAllRules();

      expect(mockCollection.find).toHaveBeenCalled();
      expect(result).toHaveLength(testRules.length);
      expect(result).toEqual(testRules);
    });

    it('should filter by isActive status', async () => {
      const activeRules = testRules.filter(rule => rule.isActive);
      const mockQuery = createMockQueryBuilder(
        activeRules.map(rule => createMockCustomRuleDocument(rule))
      );
      
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      const result = await repository.getAllRules({ isActive: true });

      expect(mockQuery.where).toHaveBeenCalledWith('isActive');
      expect(mockQuery.eq).toHaveBeenCalledWith(true);
    });

    it('should filter by rule type', async () => {
      const alwaysApplyRules = testRules.filter(rule => rule.type === 'ALWAYS_APPLY');
      const mockQuery = createMockQueryBuilder(
        alwaysApplyRules.map(rule => createMockCustomRuleDocument(rule))
      );
      
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      const result = await repository.getAllRules({ type: 'ALWAYS_APPLY' });

      expect(mockQuery.where).toHaveBeenCalledWith('type');
      expect(mockQuery.eq).toHaveBeenCalledWith('ALWAYS_APPLY');
    });

    it('should filter by source', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await repository.getAllRules({ source: 'builtin' });

      expect(mockQuery.where).toHaveBeenCalledWith('source');
      expect(mockQuery.eq).toHaveBeenCalledWith('builtin');
    });

    it('should filter by category', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await repository.getAllRules({ category: 'workflow-design' });

      expect(mockQuery.where).toHaveBeenCalledWith('category');
      expect(mockQuery.eq).toHaveBeenCalledWith('workflow-design');
    });

    it('should filter by appliesToAgent using elemMatch', async () => {
      const agentRules = testRules.filter(rule => rule.appliesToAgents.includes('test-agent'));
      const mockQuery = createMockQueryBuilder(
        agentRules.map(rule => createMockCustomRuleDocument(rule))
      );
      
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      const result = await repository.getAllRules({ appliesToAgent: 'test-agent' });

      expect(mockQuery.where).toHaveBeenCalledWith('appliesToAgents');
      expect(mockQuery.elemMatch).toHaveBeenCalledWith({ $eq: 'test-agent' });
    });

    it('should apply pagination with skip and limit', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await repository.getAllRules({ skip: 10, limit: 5 });

      expect(mockQuery.skip).toHaveBeenCalledWith(10);
      expect(mockQuery.limit).toHaveBeenCalledWith(5);
    });

    it('should combine multiple filters', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await repository.getAllRules({
        isActive: true,
        type: 'CONTEXTUAL_SUGGESTION',
        appliesToAgent: 'test-agent',
        limit: 10,
      });

      expect(mockQuery.where).toHaveBeenCalledWith('isActive');
      expect(mockQuery.where).toHaveBeenCalledWith('type');
      expect(mockQuery.where).toHaveBeenCalledWith('appliesToAgents');
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
    });

    it('should handle query execution errors', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockQuery.exec = vi.fn().mockRejectedValue(TestErrors.queryError);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await expect(repository.getAllRules()).rejects.toThrow(
        'Failed to retrieve rules: Error: Query failed'
      );
    });
  });

  describe('getActiveRulesForAgent()', () => {
    it('should call getAllRules with correct parameters', async () => {
      const getAllRulesSpy = vi.spyOn(repository, 'getAllRules').mockResolvedValue([]);

      await repository.getActiveRulesForAgent('test-agent');

      expect(getAllRulesSpy).toHaveBeenCalledWith({
        isActive: true,
        appliesToAgent: 'test-agent',
      });
    });

    it('should return rules for specific agent', async () => {
      const agentRules = createRulesForAgent('specific-agent', 3);
      vi.spyOn(repository, 'getAllRules').mockResolvedValue(agentRules);

      const result = await repository.getActiveRulesForAgent('specific-agent');

      expect(result).toEqual(agentRules);
      expect(result).toHaveLength(3);
    });
  });

  describe('getRulesByType()', () => {
    it('should call getAllRules with type filter and active by default', async () => {
      const getAllRulesSpy = vi.spyOn(repository, 'getAllRules').mockResolvedValue([]);

      await repository.getRulesByType('ALWAYS_APPLY');

      expect(getAllRulesSpy).toHaveBeenCalledWith({
        type: 'ALWAYS_APPLY',
        isActive: true,
      });
    });

    it('should allow filtering inactive rules', async () => {
      const getAllRulesSpy = vi.spyOn(repository, 'getAllRules').mockResolvedValue([]);

      await repository.getRulesByType('CONTEXTUAL_SUGGESTION', false);

      expect(getAllRulesSpy).toHaveBeenCalledWith({
        type: 'CONTEXTUAL_SUGGESTION',
        isActive: undefined,
      });
    });

    it('should return rules of specific type', async () => {
      const contextualRules = [
        createTestContextualRule(),
        createTestContextualRule(['error', 'handling']),
      ];
      vi.spyOn(repository, 'getAllRules').mockResolvedValue(contextualRules);

      const result = await repository.getRulesByType('CONTEXTUAL_SUGGESTION');

      expect(result).toEqual(contextualRules);
      expect(result.every(rule => rule.type === 'CONTEXTUAL_SUGGESTION')).toBe(true);
    });
  });

  describe('countRules()', () => {
    it('should count all rules when no options provided', async () => {
      const testRules = createRulesForAgent('test-agent', 7);
      const mockQuery = createMockQueryBuilder(
        testRules.map(rule => createMockCustomRuleDocument(rule))
      );
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      const result = await repository.countRules();

      expect(result).toBe(7);
    });

    it('should apply same filters as getAllRules', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await repository.countRules({
        isActive: true,
        type: 'ALWAYS_APPLY',
        category: 'security',
      });

      expect(mockQuery.where).toHaveBeenCalledWith('isActive');
      expect(mockQuery.where).toHaveBeenCalledWith('type');
      expect(mockQuery.where).toHaveBeenCalledWith('category');
    });

    it('should handle count errors', async () => {
      const mockQuery = createMockQueryBuilder([]);
      mockQuery.exec = vi.fn().mockRejectedValue(TestErrors.queryError);
      mockCollection.find = vi.fn().mockReturnValue(mockQuery);

      await expect(repository.countRules()).rejects.toThrow(
        'Failed to count rules: Error: Query failed'
      );
    });
  });

  describe('Edge Cases and Validation', () => {
    it('should handle empty rule data gracefully', async () => {
      const emptyData = {} as any;
      const validationError = new Error('Empty data validation failed');
      mockN8nRuleSchemaeParse.mockImplementation(() => {
        throw validationError;
      });

      await expect(repository.addRule(emptyData)).rejects.toThrow();
    });

    it('should validate rule structure with testAssertions helper', async () => {
      const mockDoc = createMockCustomRuleDocument(testRule);
      mockCollection.insert = vi.fn().mockResolvedValue(mockDoc);

             const result = await repository.addRule({
         name: 'Valid Rule',
         description: 'Valid description',
         descriptionForLLM: 'Valid LLM description',
         type: 'ALWAYS_APPLY',
         priority: 'medium',
         appliesToAgents: ['test-agent'],
         appliesToNodeTypes: [],
         recommendation: 'Valid recommendation',
         source: 'user_defined',
         version: 1,
         isActive: true,
         usageCount: 0,
         relatedRules: [],
         additionalData: {},
       });

      testAssertions.assertRuleStructure(result);
      testAssertions.assertRuleValidEnums(result);
    });

    it('should handle concurrent operations gracefully', async () => {
      const rule1 = createTestRule({ id: 'rule-1' });
      const rule2 = createTestRule({ id: 'rule-2' });
      
      const mockDoc1 = createMockCustomRuleDocument(rule1);
      const mockDoc2 = createMockCustomRuleDocument(rule2);
      
      mockCollection.insert = vi.fn()
        .mockResolvedValueOnce(mockDoc1)
        .mockResolvedValueOnce(mockDoc2);

             const [result1, result2] = await Promise.all([
         repository.addRule({
           name: 'Rule 1',
           description: 'Description 1',
           descriptionForLLM: 'LLM description 1',
           type: 'ALWAYS_APPLY',
           priority: 'high',
           appliesToAgents: ['agent-1'],
           appliesToNodeTypes: [],
           recommendation: 'Recommendation 1',
           source: 'user_defined',
           version: 1,
           isActive: true,
           usageCount: 0,
           relatedRules: [],
           additionalData: {},
         }),
         repository.addRule({
           name: 'Rule 2',
           description: 'Description 2',
           descriptionForLLM: 'LLM description 2',
           type: 'CONTEXTUAL_SUGGESTION',
           priority: 'medium',
           appliesToAgents: ['agent-2'],
           appliesToNodeTypes: [],
           recommendation: 'Recommendation 2',
           source: 'user_defined',
           version: 1,
           isActive: true,
           usageCount: 0,
           relatedRules: [],
           additionalData: {},
         }),
       ]);

      expect(result1).toEqual(rule1);
      expect(result2).toEqual(rule2);
      expect(mockCollection.insert).toHaveBeenCalledTimes(2);
    });
  });
}); 