/**
 * TaskSessionRepository Tests
 * 
 * Comprehensive test suite for the TaskSessionRepository class.
 * Tests all CRUD operations, query filtering, error handling, and edge cases.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { TaskSessionRepository, type TaskSessionQueryOptions } from '../TaskSessionRepository';
import type { 
  N8nAidDatabase, 
  TaskSessionCollection, 
  TaskSessionDocument 
} from '../../types/database.types';
import { 
  TaskSession, 
  TaskSessionSchema, 
  TaskSessionStatus,
  TaskExecutionMetadataSchema 
} from '../../../types/tasks.types';
import { generateUUID } from '../../../utils/uuid';

// Test utilities
import { 
  createMockDatabase,
  createMockCollection,
  createMockDocument,
  setupConsoleMocks,
  TestErrors
} from './testUtils';

/**
 * Factory for creating test TaskSession objects
 */
function createTestSession(overrides: Partial<TaskSession> = {}): TaskSession {
  const defaults: TaskSession = {
    id: generateUUID(),
    originalUserInput: 'Create a workflow for processing customer data',
    title: 'Test Task Session',
    description: 'A test task session for unit testing',
    status: 'initializing',
    chatId: generateUUID(),
    currentGraphExecutionContextId: undefined,
    executionHistory: [],
    graphExecutionContextSnapshot: undefined,
    llmContextSummary: undefined,
    metadata: TaskExecutionMetadataSchema.parse({}),
    priority: 'normal',
    tags: [],
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    completedAt: undefined,
    additionalData: {},
  };

  return { ...defaults, ...overrides };
}

/**
 * Factory for creating test sessions with different statuses
 */
function createSessionsWithStatuses(): TaskSession[] {
  const statuses: TaskSessionStatus[] = ['initializing', 'awaiting_user_input', 'processing', 'paused', 'completed', 'failed', 'cancelled'];
  return statuses.map((status, index) =>
    createTestSession({
      id: `session-${status}`,
      originalUserInput: `User input for ${status} session`,
      title: `Session ${status}`,
      status,
      chatId: index % 2 === 0 ? 'chat-1' : 'chat-2', // Alternate between two chats
      priority: index % 3 === 0 ? 'high' : index % 3 === 1 ? 'normal' : 'low',
      tags: [`tag-${index}`, 'common-tag'],
    })
  );
}

/**
 * Create mock TaskSessionCollection with configurable responses
 */
function createMockTaskSessionCollection(
  overrides: Partial<TaskSessionCollection> = {}
): TaskSessionCollection {
  return createMockCollection(overrides);
}

/**
 * Create mock TaskSessionDocument with configurable responses
 */
function createMockTaskSessionDocument(
  data: TaskSession,
  overrides: Partial<TaskSessionDocument> = {}
): TaskSessionDocument {
  return createMockDocument(data, overrides);
}

describe('TaskSessionRepository', () => {
  let repository: TaskSessionRepository;
  let mockDb: N8nAidDatabase;
  let mockCollection: TaskSessionCollection;
  let consoleMocks: ReturnType<typeof setupConsoleMocks>;

  beforeEach(() => {
    // Setup console mocks
    consoleMocks = setupConsoleMocks();
    
    // Create mock collection
    mockCollection = createMockTaskSessionCollection();
    
    // Create mock database
    mockDb = createMockDatabase({
      task_sessions: mockCollection
    });

    // Create repository with mocked database
    repository = new TaskSessionRepository(Promise.resolve(mockDb));
  });

  afterEach(() => {
    // Restore console methods
    consoleMocks.restore();
    
    // Clear all mocks
    vi.clearAllMocks();
  });

  describe('createSession', () => {
    it('should create a new session with required fields', async () => {
      // Arrange
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Create a workflow for processing invoices'
      };
      const mockDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      const result = await repository.createSession(sessionData);

      // Assert
      expect(mockCollection.insert).toHaveBeenCalledTimes(1);
      const insertCall = vi.mocked(mockCollection.insert).mock.calls[0][0] as TaskSession;
      expect(insertCall).toMatchObject({
        chatId: sessionData.chatId,
        originalUserInput: sessionData.originalUserInput,
        status: 'initializing',
        priority: 'normal',
        tags: [],
        executionHistory: [],
        additionalData: {},
      });
      expect(insertCall.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
      expect(insertCall.title).toContain('Task: Create a workflow for processing invoices');
      expect(insertCall.createdAt).toBeInstanceOf(Date);
      expect(insertCall.updatedAt).toBeInstanceOf(Date);
      expect(result).toEqual(mockDoc.toJSON());
    });

    it('should create a session with optional fields', async () => {
      // Arrange
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Create a complex automation workflow',
        title: 'Custom Session Title',
        description: 'Test description',
        status: 'processing' as TaskSessionStatus,
        priority: 'high' as const,
        tags: ['urgent', 'workflow']
      };
      const mockDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      const result = await repository.createSession(sessionData);

      // Assert
      const insertCall = vi.mocked(mockCollection.insert).mock.calls[0][0] as TaskSession;
      expect(insertCall).toMatchObject({
        chatId: sessionData.chatId,
        originalUserInput: sessionData.originalUserInput,
        title: sessionData.title,
        description: sessionData.description,
        status: sessionData.status,
        priority: sessionData.priority,
        tags: sessionData.tags,
      });
    });

    it('should generate default title from originalUserInput when title not provided', async () => {
      // Arrange
      const longInput = 'This is a very long user input that exceeds fifty characters and should be truncated';
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: longInput
      };
      const mockDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      await repository.createSession(sessionData);

      // Assert
      const insertCall = vi.mocked(mockCollection.insert).mock.calls[0][0] as TaskSession;
      expect(insertCall.title).toBe('Task: This is a very long user input that exceeds fifty ...');
    });

    it('should use TaskExecutionMetadataSchema defaults for metadata', async () => {
      // Arrange
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Test user input'
      };
      const mockDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      await repository.createSession(sessionData);

      // Assert
      const insertCall = vi.mocked(mockCollection.insert).mock.calls[0][0] as TaskSession;
      expect(insertCall.metadata).toEqual(TaskExecutionMetadataSchema.parse({}));
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidData = {
        chatId: '', // Invalid empty string
        originalUserInput: 'Test input'
      } as any;

      // Act & Assert
      await expect(repository.createSession(invalidData)).rejects.toThrow('Failed to create task session');
      expect(consoleMocks.error).toHaveBeenCalled();
    });

    it('should handle database insertion errors', async () => {
      // Arrange
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Test user input'
      };
      
      vi.mocked(mockCollection.insert).mockRejectedValue(TestErrors.insertError);

      // Act & Assert
      await expect(repository.createSession(sessionData)).rejects.toThrow('Failed to create task session');
      expect(consoleMocks.error).toHaveBeenCalled();
    });

    it('should handle llmContextSummary as string and graphExecutionContextSnapshot', async () => {
      // Arrange
      const graphSnapshot = {
        graphDefinitionId: 'test-graph-def',
        graphDefinitionVersion: '1.0.0',
        taskSessionId: 'task-123',
        currentGraphRunId: 'run-456',
        status: 'PAUSED_FOR_USER_INPUT' as const,
        initialInputs: { input1: 'value1' },
        nodeOutputs: [],
        currentNodeIdBeingProcessed: 'node-1',
        activeInterruptDetails: undefined,
        lastError: undefined,
        lastCompletedNodeId: undefined,
        processingQueue: ['node-2', 'node-3'],
        totalStepsExecuted: 5,
        tokenUsage: {
          promptTokens: 100,
          completionTokens: 50,
          totalTokens: 150
        }
      };

      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Test user input',
        llmContextSummary: 'This is a textual summary of the conversation context.',
        graphExecutionContextSnapshot: graphSnapshot
      };
      
      const mockDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      await repository.createSession(sessionData);

      // Assert
      const insertCall = vi.mocked(mockCollection.insert).mock.calls[0][0] as TaskSession;
      expect(insertCall.llmContextSummary).toBe('This is a textual summary of the conversation context.');
      expect(insertCall.graphExecutionContextSnapshot).toEqual(graphSnapshot);
    });
  });

  describe('getSessionById', () => {
    it('should retrieve an existing session', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act
      const result = await repository.getSessionById('session-123');

      // Assert
      expect(mockCollection.findOne).toHaveBeenCalledWith('session-123');
      expect(result).toEqual(session);
    });

    it('should return null for non-existent session', async () => {
      // Arrange
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act
      const result = await repository.getSessionById('non-existent');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle database errors', async () => {
      // Arrange
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockRejectedValue(TestErrors.findError)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act & Assert
      await expect(repository.getSessionById('session-123')).rejects.toThrow('Failed to retrieve task session by ID');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('updateSession', () => {
    it('should update an existing session', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      const updatedMockDoc = createMockTaskSessionDocument({ 
        ...session, 
        status: 'completed',
        updatedAt: new Date('2024-01-02T00:00:00Z')
      });
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      vi.mocked(mockDoc.incrementalPatch).mockResolvedValue(updatedMockDoc);

      const updates = { status: 'completed' as TaskSessionStatus };

      // Act
      const result = await repository.updateSession('session-123', updates);

      // Assert
      expect(mockDoc.incrementalPatch).toHaveBeenCalledWith({
        ...updates,
        updatedAt: expect.any(Date)
      });
      expect(result).toEqual(updatedMockDoc.toJSON());
      expect(consoleMocks.log).toHaveBeenCalledWith('TaskSessionRepository: Updated session with ID: session-123');
    });

    it('should return null for non-existent session', async () => {
      // Arrange
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act
      const result = await repository.updateSession('non-existent', { status: 'completed' });

      // Assert
      expect(result).toBeNull();
      expect(consoleMocks.warn).toHaveBeenCalledWith('TaskSessionRepository: Attempted to update non-existent session with ID: non-existent');
    });

    it('should validate merged data before update', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Try to update with invalid data
      const invalidUpdates = { status: 'invalid-status' as any };

      // Act & Assert
      await expect(repository.updateSession('session-123', invalidUpdates)).rejects.toThrow('Validation failed during update');
      expect(consoleMocks.error).toHaveBeenCalled();
    });

    it('should handle database update errors', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      vi.mocked(mockDoc.incrementalPatch).mockRejectedValue(TestErrors.updateError);

      // Act & Assert
      await expect(repository.updateSession('session-123', { status: 'completed' })).rejects.toThrow('Failed to update task session');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('saveSession', () => {
    it('should create a new session when document does not exist', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null) // Document doesn't exist
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      vi.mocked(mockCollection.insert).mockResolvedValue(mockDoc);

      // Act
      const result = await repository.saveSession(session);

      // Assert
      expect(mockCollection.findOne).toHaveBeenCalledWith(session.id);
      expect(mockCollection.insert).toHaveBeenCalled();
      expect(result).toEqual(session);
      expect(consoleMocks.log).toHaveBeenCalledWith(`TaskSessionRepository: Created new session with ID: ${session.id}`);
    });

    it('should update existing session when document exists', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      const updatedMockDoc = createMockTaskSessionDocument({ 
        ...session, 
        updatedAt: new Date('2024-01-02T00:00:00Z')
      });
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc) // Document exists
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      vi.mocked(mockDoc.incrementalPatch).mockResolvedValue(updatedMockDoc);

      // Act
      const result = await repository.saveSession(session);

      // Assert
      expect(mockCollection.findOne).toHaveBeenCalledWith(session.id);
      expect(mockDoc.incrementalPatch).toHaveBeenCalled();
      expect(result).toEqual(updatedMockDoc.toJSON());
      expect(consoleMocks.log).toHaveBeenCalledWith(`TaskSessionRepository: Updated existing session with ID: ${session.id}`);
    });

    it('should validate session data before saving', async () => {
      // Arrange
      const invalidSession = { 
        ...createTestSession(), 
        status: 'invalid-status' 
      } as any;

      // Act & Assert
      await expect(repository.saveSession(invalidSession)).rejects.toThrow('Failed to save task session');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('deleteSession', () => {
    it('should delete an existing session', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act
      const result = await repository.deleteSession('session-123');

      // Assert
      expect(mockDoc.remove).toHaveBeenCalled();
      expect(result).toBe(true);
      expect(consoleMocks.log).toHaveBeenCalledWith('TaskSessionRepository: Deleted session with ID: session-123');
    });

    it('should return false for non-existent session', async () => {
      // Arrange
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);

      // Act
      const result = await repository.deleteSession('non-existent');

      // Assert
      expect(result).toBe(false);
      expect(consoleMocks.warn).toHaveBeenCalledWith('TaskSessionRepository: Attempted to delete non-existent session with ID: non-existent');
    });

    it('should handle database deletion errors', async () => {
      // Arrange
      const session = createTestSession({ id: 'session-123' });
      const mockDoc = createMockTaskSessionDocument(session);
      
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      vi.mocked(mockDoc.remove).mockRejectedValue(TestErrors.deleteError);

      // Act & Assert
      await expect(repository.deleteSession('session-123')).rejects.toThrow('Failed to delete task session');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('findSessions', () => {
    beforeEach(() => {
      // Setup mock query builder
      const sessions = createSessionsWithStatuses();
      const mockDocs = sessions.map(session => createMockTaskSessionDocument(session));
      
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        in: vi.fn().mockReturnThis(),
        elemMatch: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        skip: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue(mockDocs)
      };
      
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);
    });

    it('should find all sessions with no filters', async () => {
      // Act
      const result = await repository.findSessions();

      // Assert
      expect(mockCollection.find).toHaveBeenCalled();
      expect(result).toHaveLength(7); // All 7 status types
    });

    it('should filter by single status', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        status: 'processing'
      };

      // Act
      const result = await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.where).toHaveBeenCalledWith('status');
      expect(mockQuery.eq).toHaveBeenCalledWith('processing');
    });

    it('should filter by multiple statuses', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        status: ['processing', 'completed']
      };

      // Act
      const result = await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.where).toHaveBeenCalledWith('status');
      expect(mockQuery.in).toHaveBeenCalledWith(['processing', 'completed']);
    });

    it('should filter by chatId', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        chatId: 'chat-1'
      };

      // Act
      await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.where).toHaveBeenCalledWith('chatId');
      expect(mockQuery.eq).toHaveBeenCalledWith('chat-1');
    });

    it('should filter by priority', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        priority: 'high'
      };

      // Act
      await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.where).toHaveBeenCalledWith('priority');
      expect(mockQuery.eq).toHaveBeenCalledWith('high');
    });

    it('should filter by tags', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        tags: ['urgent', 'workflow']
      };

      // Act
      await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.where).toHaveBeenCalledWith('tags');
      expect(mockQuery.elemMatch).toHaveBeenCalledWith({
        $in: ['urgent', 'workflow']
      });
    });

    it('should apply sorting', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        sortBy: 'createdAt',
        sortDirection: 'asc'
      };

      // Act
      await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.sort).toHaveBeenCalledWith({ createdAt: 'asc' });
    });

    it('should apply default sorting when no sortBy specified', async () => {
      // Act
      await repository.findSessions({});

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.sort).toHaveBeenCalledWith({ updatedAt: 'desc' });
    });

    it('should apply pagination', async () => {
      // Arrange
      const options: TaskSessionQueryOptions = {
        skip: 10,
        limit: 5
      };

      // Act
      await repository.findSessions(options);

      // Assert
      const mockQuery = vi.mocked(mockCollection.find).mock.results[0].value;
      expect(mockQuery.skip).toHaveBeenCalledWith(10);
      expect(mockQuery.limit).toHaveBeenCalledWith(5);
    });

    it('should handle database query errors', async () => {
      // Arrange
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        exec: vi.fn().mockRejectedValue(TestErrors.queryError)
      };
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);

      // Act & Assert
      await expect(repository.findSessions()).rejects.toThrow('Failed to find task sessions');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('countSessions', () => {
    beforeEach(() => {
      // Setup mock count method
      const mockCountQuery = {
        exec: vi.fn().mockResolvedValue(7) // Mock return count of 7
      };
      
      vi.mocked(mockCollection.count).mockReturnValue(mockCountQuery as any);
    });

    it('should count all sessions with no filters', async () => {
      // Act
      const result = await repository.countSessions();

      // Assert
      expect(mockCollection.count).toHaveBeenCalledWith({
        selector: {}
      });
      expect(result).toBe(7);
    });

    it('should count with filters', async () => {
      // Arrange
      const options = {
        status: 'processing' as TaskSessionStatus,
        chatId: 'chat-1',
        priority: 'high' as const,
        tags: ['urgent', 'workflow']
      };

      // Act
      const result = await repository.countSessions(options);

      // Assert
      expect(mockCollection.count).toHaveBeenCalledWith({
        selector: {
          status: { $eq: 'processing' },
          chatId: { $eq: 'chat-1' },
          priority: { $eq: 'high' },
          tags: { $elemMatch: { $in: ['urgent', 'workflow'] } }
        }
      });
      expect(result).toBe(7);
    });

    it('should handle array of statuses', async () => {
      // Arrange
      const options = {
        status: ['processing', 'completed'] as TaskSessionStatus[]
      };

      // Act
      await repository.countSessions(options);

      // Assert
      expect(mockCollection.count).toHaveBeenCalledWith({
        selector: {
          status: { $in: ['processing', 'completed'] }
        }
      });
    });

    it('should handle database count errors', async () => {
      // Arrange
      const mockCountQuery = {
        exec: vi.fn().mockRejectedValue(TestErrors.queryError)
      };
      vi.mocked(mockCollection.count).mockReturnValue(mockCountQuery as any);

      // Act & Assert
      await expect(repository.countSessions()).rejects.toThrow('Failed to count task sessions');
      expect(consoleMocks.error).toHaveBeenCalled();
    });
  });

  describe('getLatestSessionForChat', () => {
    it('should return the latest session for a chat', async () => {
      // Arrange
      const sessions = [
        createTestSession({ id: 'session-1', chatId: 'chat-1', updatedAt: new Date('2024-01-01') }),
        createTestSession({ id: 'session-2', chatId: 'chat-1', updatedAt: new Date('2024-01-02') })
      ];
      const mockDocs = sessions.map(session => createMockTaskSessionDocument(session));
      
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue([mockDocs[1]]) // Return the latest
      };
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);

      // Act
      const result = await repository.getLatestSessionForChat('chat-1');

      // Assert
      expect(result).toEqual(sessions[1]);
      expect(mockQuery.where).toHaveBeenCalledWith('chatId');
      expect(mockQuery.eq).toHaveBeenCalledWith('chat-1');
      expect(mockQuery.sort).toHaveBeenCalledWith({ updatedAt: 'desc' });
      expect(mockQuery.limit).toHaveBeenCalledWith(1);
    });

    it('should return null when no sessions found for chat', async () => {
      // Arrange
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue([])
      };
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);

      // Act
      const result = await repository.getLatestSessionForChat('non-existent-chat');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getActiveSessions', () => {
    it('should return active sessions for all chats', async () => {
      // Arrange
      const sessions = createSessionsWithStatuses();
      const activeSessions = sessions.filter(s => 
        ['initializing', 'awaiting_user_input', 'processing', 'paused'].includes(s.status)
      );
      const mockDocs = activeSessions.map(session => createMockTaskSessionDocument(session));
      
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        in: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue(mockDocs)
      };
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);

      // Act
      const result = await repository.getActiveSessions();

      // Assert
      expect(mockQuery.where).toHaveBeenCalledWith('status');
      expect(mockQuery.in).toHaveBeenCalledWith(['initializing', 'awaiting_user_input', 'processing', 'paused']);
      expect(mockQuery.sort).toHaveBeenCalledWith({ updatedAt: 'desc' });
      expect(result).toHaveLength(activeSessions.length);
    });

    it('should return active sessions for specific chat', async () => {
      // Arrange
      const sessions = createSessionsWithStatuses();
      const activeSessions = sessions.filter(s => 
        ['initializing', 'awaiting_user_input', 'processing', 'paused'].includes(s.status) &&
        s.chatId === 'chat-1'
      );
      const mockDocs = activeSessions.map(session => createMockTaskSessionDocument(session));
      
      const mockQuery = {
        where: vi.fn().mockReturnThis(),
        in: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        sort: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue(mockDocs)
      };
      vi.mocked(mockCollection.find).mockReturnValue(mockQuery as any);

      // Act
      const result = await repository.getActiveSessions('chat-1');

      // Assert
      expect(mockQuery.where).toHaveBeenCalledWith('status');
      expect(mockQuery.in).toHaveBeenCalledWith(['initializing', 'awaiting_user_input', 'processing', 'paused']);
      expect(mockQuery.where).toHaveBeenCalledWith('chatId');
      expect(mockQuery.eq).toHaveBeenCalledWith('chat-1');
      expect(result).toHaveLength(activeSessions.length);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete CRUD lifecycle', async () => {
      // This test verifies the complete lifecycle of a session
      const sessionData = {
        chatId: 'chat-123',
        originalUserInput: 'Create a workflow for processing invoices'
      };

      // Create
      const mockCreatedDoc = createMockTaskSessionDocument(createTestSession(sessionData));
      vi.mocked(mockCollection.insert).mockResolvedValue(mockCreatedDoc);
      
      const created = await repository.createSession(sessionData);
      expect(created.originalUserInput).toBe(sessionData.originalUserInput);

      // Read
      const mockFindOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockCreatedDoc)
      });
      vi.mocked(mockCollection.findOne).mockImplementation(mockFindOne);
      
      const retrieved = await repository.getSessionById(created.id);
      expect(retrieved).toEqual(created);

      // Update
      const mockUpdatedDoc = createMockTaskSessionDocument({
        ...created,
        status: 'completed',
        updatedAt: new Date()
      });
      vi.mocked(mockCreatedDoc.incrementalPatch).mockResolvedValue(mockUpdatedDoc);
      
      const updated = await repository.updateSession(created.id, { status: 'completed' });
      expect(updated?.status).toBe('completed');

      // Delete
      const deleted = await repository.deleteSession(created.id);
      expect(deleted).toBe(true);
    });
  });
}); 