/**
 * UserSettingsRepository Unit Tests
 * 
 * Comprehensive test suite for the UserSettingsRepository class, covering:
 * - Constructor and dependency injection
 * - Database collection access
 * - Settings retrieval with default creation
 * - Settings saving with partial updates
 * - Settings reset to defaults
 * - Error handling and recovery
 * - RxDB document type conversion
 * - Singleton document management
 */

import { describe, it, expect, beforeEach, afterEach, vi, type MockedFunction } from 'vitest';
import { UserSettingsRepository } from '../UserSettingsRepository';
import { UserSettings, UserSettingsSchema } from '../../../types/userSettings.types';
import type { 
  N8nAidDatabase, 
  UserSettingsCollection, 
  UserSettingsDocument 
} from '../../types/database.types';

// Mock the database setup module
const mockGetDb = vi.hoisted(() => vi.fn());
vi.mock('../../rxdbSetup', () => ({
  getDb: mockGetDb,
}));

// Mock console methods to suppress log output during tests
const mockConsoleLog = vi.fn();
const mockConsoleWarn = vi.fn();
const mockConsoleError = vi.fn();

vi.stubGlobal('console', {
  ...console,
  log: mockConsoleLog,
  warn: mockConsoleWarn,
  error: mockConsoleError,
});

describe('UserSettingsRepository', () => {
  let repository: UserSettingsRepository;
  let mockDb: N8nAidDatabase;
  let mockCollection: UserSettingsCollection;
  let mockDocument: UserSettingsDocument;

  const mockUserSettings: UserSettings = {
    id: 'singleton_user_settings',
    version: 1,
    modelPreferences: {
      CORE_ORCHESTRATION_AND_PLANNING: {
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        temperature: 0.1,
      },
    },
    apiKeys: {
      openai: 'sk-test-key',
      anthropic: undefined,
      google: undefined,
    },
    preferences: {
      enableChromeAI: true,
      enableAutoSummarization: true,
      maxContextWindowTokens: 8000,
      debugMode: false,
      autoSaveWorkflows: true,
      defaultWorkflowTags: [],
    },
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock document
    mockDocument = {
      toMutableJSON: vi.fn().mockReturnValue(mockUserSettings),
      incrementalPatch: vi.fn(),
    } as any;

    // Setup mock collection
    mockCollection = {
      findOne: vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockDocument),
      }),
      insert: vi.fn().mockResolvedValue(mockDocument),
    } as any;

    // Setup mock database
    mockDb = {
      user_settings: mockCollection,
    } as any;

    // Setup getDb mock
    mockGetDb.mockResolvedValue(mockDb);

    // Create repository instance
    repository = new UserSettingsRepository();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should use provided database promise when passed', async () => {
      // Create a completely separate mock for this test
      const separateMockDb = {
        user_settings: mockCollection,
      } as any;
      
      const customDbPromise = Promise.resolve(separateMockDb);
      const customRepository = new UserSettingsRepository(customDbPromise);
      
      // Clear the global mock before testing
      mockGetDb.mockClear();
      
      // Access collection to trigger database usage
      await customRepository.getSettings();
      
      // getDb should not be called when custom promise is provided
      expect(mockGetDb).not.toHaveBeenCalled();
    });

    it('should use default getDb when no database promise provided', async () => {
      await repository.getSettings();
      
      expect(mockGetDb).toHaveBeenCalled();
    });

    it('should store singleton document ID correctly', () => {
      expect((repository as any).settingsDocId).toBe('singleton_user_settings');
    });
  });

  describe('getSettings()', () => {
    it('should return existing settings when document exists', async () => {
      const result = await repository.getSettings();

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockDocument.toMutableJSON).toHaveBeenCalled();
      expect(result).toEqual(mockUserSettings);
    });

    it('should create default settings when document does not exist', async () => {
      // Mock no existing document
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const result = await repository.getSettings();

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockCollection.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'singleton_user_settings',
          version: 1,
          modelPreferences: {},
          apiKeys: {},
          preferences: expect.objectContaining({
            enableChromeAI: true,
            enableAutoSummarization: true,
            maxContextWindowTokens: 8000,
            debugMode: false,
            autoSaveWorkflows: true,
            defaultWorkflowTags: [],
          }),
        })
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: User settings document not found, creating with defaults'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: Created new user settings document with defaults'
      );
      expect(result).toEqual(mockUserSettings);
    });

    it('should handle database errors gracefully', async () => {
      // Create a fresh repository instance for this test to avoid interference
      const dbError = new Error('Database connection failed');
      const failingRepository = new UserSettingsRepository(Promise.reject(dbError));

      await expect(failingRepository.getSettings()).rejects.toThrow(
        'Failed to retrieve user settings: Error: Database connection failed'
      );
      expect(mockConsoleError).toHaveBeenCalledWith(
        'UserSettingsRepository: Failed to get settings:',
        dbError
      );
    });

    it('should handle collection query errors gracefully', async () => {
      const queryError = new Error('Query failed');
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockRejectedValue(queryError),
      });

      await expect(repository.getSettings()).rejects.toThrow(
        'Failed to retrieve user settings: Error: Query failed'
      );
      expect(mockConsoleError).toHaveBeenCalledWith(
        'UserSettingsRepository: Failed to get settings:',
        queryError
      );
    });

    it('should validate default settings against Zod schema', async () => {
      // Mock no existing document to trigger default creation
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      // Spy on UserSettingsSchema.parse to verify it's called
      const parseSpy = vi.spyOn(UserSettingsSchema, 'parse');

      await repository.getSettings();

      expect(parseSpy).toHaveBeenCalledWith({
        id: 'singleton_user_settings',
      });
    });
  });

  describe('saveSettings()', () => {
    it('should update existing document with partial settings', async () => {
      const partialSettings: Partial<UserSettings> = {
        apiKeys: {
          openai: 'sk-new-key',
          anthropic: 'sk-anthropic-key',
          google: undefined,
        },
      };

      const updatedDocument = {
        ...mockDocument,
        toMutableJSON: vi.fn().mockReturnValue({
          ...mockUserSettings,
          ...partialSettings,
          updatedAt: expect.any(Date),
        }),
      };

      mockDocument.incrementalPatch = vi.fn().mockResolvedValue(updatedDocument);

      const result = await repository.saveSettings(partialSettings);

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockDocument.incrementalPatch).toHaveBeenCalledWith({
        ...partialSettings,
        updatedAt: expect.any(Date),
      });
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: Updated existing settings document'
      );
      expect(result).toEqual({
        ...mockUserSettings,
        ...partialSettings,
        updatedAt: expect.any(Date),
      });
    });

    it('should create new document when none exists', async () => {
      const partialSettings: Partial<UserSettings> = {
        apiKeys: {
          openai: 'sk-new-key',
          anthropic: undefined,
          google: undefined,
        },
      };

      // Mock no existing document
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const newDocument = {
        toMutableJSON: vi.fn().mockReturnValue({
          ...mockUserSettings,
          ...partialSettings,
        }),
      };

      mockCollection.insert = vi.fn().mockResolvedValue(newDocument);

      const result = await repository.saveSettings(partialSettings);

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockCollection.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'singleton_user_settings',
          ...partialSettings,
          updatedAt: expect.any(Date),
        })
      );
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        'UserSettingsRepository: Attempted to save settings but document did not exist. Creating new with provided partial data and defaults'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: Created new settings document with merged data'
      );
      expect(result).toEqual({
        ...mockUserSettings,
        ...partialSettings,
      });
    });

    it('should merge partial settings with defaults when creating new document', async () => {
      const partialSettings: Partial<UserSettings> = {
        apiKeys: {
          openai: 'sk-test-key',
          anthropic: undefined,
          google: undefined,
        },
        preferences: {
          enableChromeAI: true,
          enableAutoSummarization: true,
          maxContextWindowTokens: 8000,
          debugMode: true,
          autoSaveWorkflows: true,
          defaultWorkflowTags: [],
        },
      };

      // Mock no existing document
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      await repository.saveSettings(partialSettings);

      expect(mockCollection.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'singleton_user_settings',
          version: 1,
          modelPreferences: {},
          apiKeys: partialSettings.apiKeys,
          preferences: expect.objectContaining({
            ...partialSettings.preferences,
            enableChromeAI: true, // Default value
            enableAutoSummarization: true, // Default value
            maxContextWindowTokens: 8000, // Default value
          }),
          updatedAt: expect.any(Date),
        })
      );
    });

    it('should handle database errors during save', async () => {
      const saveError = new Error('Save failed');
      mockDocument.incrementalPatch = vi.fn().mockRejectedValue(saveError);

      const partialSettings: Partial<UserSettings> = {
        preferences: {
          enableChromeAI: true,
          enableAutoSummarization: true,
          maxContextWindowTokens: 8000,
          debugMode: true,
          autoSaveWorkflows: true,
          defaultWorkflowTags: [],
        },
      };

      await expect(repository.saveSettings(partialSettings)).rejects.toThrow(
        'Failed to save user settings: Error: Save failed'
      );
      expect(mockConsoleError).toHaveBeenCalledWith(
        'UserSettingsRepository: Failed to save settings:',
        saveError
      );
    });

    it('should validate merged data against Zod schema when creating new document', async () => {
      // Mock no existing document
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const parseSpy = vi.spyOn(UserSettingsSchema, 'parse');

      const partialSettings: Partial<UserSettings> = {
        preferences: {
          enableChromeAI: true,
          enableAutoSummarization: true,
          maxContextWindowTokens: 8000,
          debugMode: true,
          autoSaveWorkflows: true,
          defaultWorkflowTags: [],
        },
      };

      await repository.saveSettings(partialSettings);

      // Should be called twice: once for defaults, once for validation of merged data
      expect(parseSpy).toHaveBeenCalledTimes(2);
    });

    it('should preserve existing settings when updating with partial data', async () => {
      const partialSettings: Partial<UserSettings> = {
        preferences: {
          enableChromeAI: true,
          enableAutoSummarization: true,
          maxContextWindowTokens: 8000,
          debugMode: true,
          autoSaveWorkflows: true,
          defaultWorkflowTags: [],
        },
      };

      const updatedDocument = {
        ...mockDocument,
        toMutableJSON: vi.fn().mockReturnValue({
          ...mockUserSettings,
          preferences: {
            ...mockUserSettings.preferences,
            debugMode: true,
          },
          updatedAt: expect.any(Date),
        }),
      };

      mockDocument.incrementalPatch = vi.fn().mockResolvedValue(updatedDocument);

      const result = await repository.saveSettings(partialSettings);

      // Should preserve all existing settings except the updated ones
      expect(result.apiKeys).toEqual(mockUserSettings.apiKeys);
      expect(result.modelPreferences).toEqual(mockUserSettings.modelPreferences);
      expect(result.preferences.enableChromeAI).toBe(true); // Preserved
      expect(result.preferences.debugMode).toBe(true); // Updated
    });
  });

  describe('resetSettingsToDefaults()', () => {
    it('should reset existing document to defaults', async () => {
      const resetDocument = {
        ...mockDocument,
        toMutableJSON: vi.fn().mockReturnValue({
          ...mockUserSettings,
          apiKeys: {},
          modelPreferences: {},
          preferences: {
            enableChromeAI: true,
            enableAutoSummarization: true,
            maxContextWindowTokens: 8000,
            debugMode: false,
            autoSaveWorkflows: true,
            defaultWorkflowTags: [],
          },
          updatedAt: expect.any(Date),
        }),
      };

      mockDocument.incrementalPatch = vi.fn().mockResolvedValue(resetDocument);

      const result = await repository.resetSettingsToDefaults();

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockDocument.incrementalPatch).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'singleton_user_settings',
          version: 1,
          modelPreferences: {},
          apiKeys: {},
          preferences: expect.objectContaining({
            enableChromeAI: true,
            enableAutoSummarization: true,
            maxContextWindowTokens: 8000,
            debugMode: false,
            autoSaveWorkflows: true,
            defaultWorkflowTags: [],
          }),
          updatedAt: expect.any(Date),
        })
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: Reset existing settings document to defaults'
      );
      expect(result.apiKeys).toEqual({});
      expect(result.modelPreferences).toEqual({});
    });

    it('should create new document with defaults when none exists', async () => {
      // Mock no existing document
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const newDocument = {
        toMutableJSON: vi.fn().mockReturnValue({
          ...mockUserSettings,
          apiKeys: {},
          modelPreferences: {},
        }),
      };

      mockCollection.insert = vi.fn().mockResolvedValue(newDocument);

      const result = await repository.resetSettingsToDefaults();

      expect(mockCollection.findOne).toHaveBeenCalledWith('singleton_user_settings');
      expect(mockCollection.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'singleton_user_settings',
          version: 1,
          modelPreferences: {},
          apiKeys: {},
        })
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'UserSettingsRepository: Created new settings document with defaults'
      );
      expect(result).toEqual({
        ...mockUserSettings,
        apiKeys: {},
        modelPreferences: {},
      });
    });

    it('should handle errors during reset', async () => {
      const resetError = new Error('Reset failed');
      mockDocument.incrementalPatch = vi.fn().mockRejectedValue(resetError);

      await expect(repository.resetSettingsToDefaults()).rejects.toThrow(
        'Failed to reset user settings to defaults: Error: Reset failed'
      );
      expect(mockConsoleError).toHaveBeenCalledWith(
        'UserSettingsRepository: Failed to reset settings to defaults:',
        resetError
      );
    });

    it('should generate valid default settings using Zod schema', async () => {
      // Mock no existing document to trigger creation
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      const parseSpy = vi.spyOn(UserSettingsSchema, 'parse');

      await repository.resetSettingsToDefaults();

      expect(parseSpy).toHaveBeenCalledWith({
        id: 'singleton_user_settings',
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle RxDB incrementalPatch method not available', async () => {
      // Mock document without incrementalPatch method (older RxDB version)
      const documentWithoutPatch = {
        toMutableJSON: vi.fn().mockReturnValue(mockUserSettings),
        // No incrementalPatch method
      } as any;

      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(documentWithoutPatch),
      });

      const partialSettings: Partial<UserSettings> = {
        preferences: {
          enableChromeAI: true,
          enableAutoSummarization: true,
          maxContextWindowTokens: 8000,
          debugMode: true,
          autoSaveWorkflows: true,
          defaultWorkflowTags: [],
        },
      };

      await expect(repository.saveSettings(partialSettings)).rejects.toThrow();
    });

    it('should handle invalid Zod schema validation', async () => {
      // Mock Zod parse to throw validation error
      const validationError = new Error('Invalid schema');
      vi.spyOn(UserSettingsSchema, 'parse').mockImplementation(() => {
        throw validationError;
      });

      // Mock no existing document to trigger schema validation
      mockCollection.findOne = vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(null),
      });

      await expect(repository.getSettings()).rejects.toThrow();
    });
  });

  describe('Integration with RxDB Types', () => {
    it('should correctly convert RxDB readonly document to mutable UserSettings', async () => {
      const readonlyMockData = Object.freeze({
        ...mockUserSettings,
        preferences: Object.freeze({
          ...mockUserSettings.preferences,
          defaultWorkflowTags: Object.freeze([]),
        }),
      });

      mockDocument.toMutableJSON = vi.fn().mockReturnValue(readonlyMockData);

      const result = await repository.getSettings();

      expect(mockDocument.toMutableJSON).toHaveBeenCalled();
      expect(result).toEqual(readonlyMockData);
    });

    it('should maintain type safety with UserSettings interface', async () => {
      const result = await repository.getSettings();

      // TypeScript compile-time checks - these should not cause TS errors
      expect(typeof result.id).toBe('string');
      expect(typeof result.version).toBe('number');
      expect(typeof result.modelPreferences).toBe('object');
      expect(typeof result.apiKeys).toBe('object');
      expect(typeof result.preferences).toBe('object');
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });
  });
}); 