/**
 * Task Session Repository
 * 
 * Provides a data access layer for interacting with the task_sessions RxDB collection.
 * It encapsulates all direct RxDB CRUD operations for TaskSession objects, which represent
 * the state of a single, cohesive AI operation or job.
 * 
 * Key Features:
 * - Standard CRUD operations for task sessions
 * - Query operations with filtering (by status, chatId, etc.)
 * - Automatic ID generation and timestamp management
 * - Type-safe interactions with RxDB using Zod validation
 * - Upsert logic for session management
 * - Comprehensive error handling and logging
 * 
 * Usage Example:
 * ```typescript
 * const repository = new TaskSessionRepository();
 * const session = await repository.createSession({
 *   chatId: 'chat-123',
 *   originalUserInput: 'Create a workflow for...',
 *   title: 'Workflow Creation Task'
 * });
 * const sessions = await repository.findSessions({ status: 'processing' });
 * ```
 */

import { 
  N8nAidDatabase, 
  TaskSessionCollection, 
  TaskSessionDocument, 
  TaskSessionDocType 
} from '../types/database.types';
import { TaskSession, TaskSessionSchema, TaskSessionStatus, TaskExecutionMetadataSchema } from '../../types/tasks.types';
import { getDb } from '../rxdbSetup';
import { generateUUID } from '../../utils/uuid';

/**
 * Query options for filtering task sessions
 */
export interface TaskSessionQueryOptions {
  /** Filter by session status */
  status?: TaskSessionStatus | TaskSessionStatus[];
  /** Filter by associated chat ID */
  chatId?: string;
  /** Filter by priority level */
  priority?: 'low' | 'normal' | 'high';
  /** Filter by tags (session must have at least one of these tags) */
  tags?: string[];
  /** Limit the number of results */
  limit?: number;
  /** Skip a number of results (for pagination) */
  skip?: number;
  /** Sort by field */
  sortBy?: 'createdAt' | 'updatedAt' | 'priority';
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
}

/**
 * Repository for managing task sessions in RxDB
 */
export class TaskSessionRepository {
  private readonly dbPromise: Promise<N8nAidDatabase>;

  /**
   * Creates a new TaskSessionRepository instance
   * @param dbPromise - Optional database promise for dependency injection (useful for testing)
   */
  constructor(dbPromise?: Promise<N8nAidDatabase>) {
    this.dbPromise = dbPromise || getDb();
  }

  /**
   * Gets the task_sessions collection from the database
   * @private
   */
  private async getCollection(): Promise<TaskSessionCollection> {
    const db = await this.dbPromise;
    return db.task_sessions;
  }

  /**
   * Creates a new task session
   * @param data - Task session data with required fields and optional overrides
   * @returns Promise resolving to the created task session
   */
  public async createSession(
    data: Pick<TaskSession, 'chatId' | 'originalUserInput'> & 
    Partial<Omit<TaskSession, 'id' | 'chatId' | 'originalUserInput' | 'createdAt' | 'updatedAt'>>
  ): Promise<TaskSession> {
    try {
      const collection = await this.getCollection();
      
      // Generate unique session ID
      const sessionId = generateUUID();
      const now = new Date();
      
      // Create full session object with generated ID and timestamps
      const newSessionData: TaskSessionDocType = {
        id: sessionId,
        chatId: data.chatId,
        originalUserInput: data.originalUserInput,
        title: data.title || `Task: ${data.originalUserInput.slice(0, 50)}${data.originalUserInput.length > 50 ? '...' : ''}`,
        description: data.description,
        status: data.status || 'initializing',
        currentGraphExecutionContextId: data.currentGraphExecutionContextId,
        executionHistory: data.executionHistory || [],
        graphExecutionContextSnapshot: data.graphExecutionContextSnapshot,
        llmContextSummary: data.llmContextSummary,
        metadata: data.metadata || TaskExecutionMetadataSchema.parse({}),
        priority: data.priority || 'normal',
        tags: data.tags || [],
        createdAt: now,
        updatedAt: now,
        completedAt: data.completedAt,
        additionalData: data.additionalData || {},
      };

      // Validate the session data using Zod schema
      const validatedSession = TaskSessionSchema.parse(newSessionData);
      
      // Insert into database
      const doc = await collection.insert(validatedSession);
      
      console.log(`TaskSessionRepository: Created new session with ID: ${sessionId}`);
      return doc.toJSON() as TaskSession;
    } catch (error) {
      console.error('TaskSessionRepository: Failed to create session:', error);
      throw new Error(`Failed to create task session: ${error}`);
    }
  }

  /**
   * Retrieves a task session by its ID
   * @param id - Session ID
   * @returns Promise resolving to the session or null if not found
   */
  public async getSessionById(id: string): Promise<TaskSession | null> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      return doc ? (doc.toJSON() as TaskSession) : null;
    } catch (error) {
      console.error(`TaskSessionRepository: Failed to get session by ID ${id}:`, error);
      throw new Error(`Failed to retrieve task session by ID: ${error}`);
    }
  }

  /**
   * Updates an existing task session
   * @param id - Session ID to update
   * @param updates - Partial session data to update
   * @returns Promise resolving to the updated session or null if not found
   */
  public async updateSession(
    id: string, 
    updates: Partial<Omit<TaskSession, 'id' | 'createdAt'>>
  ): Promise<TaskSession | null> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      if (!doc) {
        console.warn(`TaskSessionRepository: Attempted to update non-existent session with ID: ${id}`);
        return null;
      }

      // Prepare patch data with updated timestamp
      const patchData = { 
        ...updates, 
        updatedAt: new Date() 
      };

      // Pre-validate merged state
      const currentData = doc.toJSON();
      const dataToValidate = { ...currentData, ...patchData };
      try {
        TaskSessionSchema.parse(dataToValidate);
      } catch (validationError) {
        console.error(`TaskSessionRepository: Validation failed for update on session ${id}`, validationError);
        throw new Error(`Validation failed during update: ${(validationError as Error).message}`);
      }

      // Apply the patch using incrementalPatch
      const updatedDoc = await doc.incrementalPatch(patchData);
      
      console.log(`TaskSessionRepository: Updated session with ID: ${id}`);
      return updatedDoc.toJSON() as TaskSession;
    } catch (error) {
      console.error(`TaskSessionRepository: Failed to update session with ID ${id}:`, error);
      throw new Error(`Failed to update task session: ${error}`);
    }
  }

  /**
   * Saves a task session using upsert logic (creates or updates based on existence)
   * @param session - Complete session object to save
   * @returns Promise resolving to the saved session
   */
  public async saveSession(session: TaskSession): Promise<TaskSession> {
    try {
      const collection = await this.getCollection();
      const existingDoc = await collection.findOne(session.id).exec();

      // Validate session data
      const validatedSession = TaskSessionSchema.parse({
        ...session,
        updatedAt: new Date()
      });

      if (!existingDoc) {
        // Document doesn't exist, create new one
        const doc = await collection.insert(validatedSession);
        console.log(`TaskSessionRepository: Created new session with ID: ${session.id}`);
        return doc.toJSON() as TaskSession;
      } else {
        // Document exists, update it
        const patchData = { 
          ...validatedSession, 
          updatedAt: new Date() 
        };
        
        const updatedDoc = await existingDoc.incrementalPatch(patchData);
        console.log(`TaskSessionRepository: Updated existing session with ID: ${session.id}`);
        return updatedDoc.toJSON() as TaskSession;
      }
    } catch (error) {
      console.error(`TaskSessionRepository: Failed to save session with ID ${session.id}:`, error);
      throw new Error(`Failed to save task session: ${error}`);
    }
  }

  /**
   * Deletes a task session by its ID
   * @param id - Session ID to delete
   * @returns Promise resolving to true if deleted, false if not found
   */
  public async deleteSession(id: string): Promise<boolean> {
    try {
      const collection = await this.getCollection();
      const doc = await collection.findOne(id).exec();
      
      if (!doc) {
        console.warn(`TaskSessionRepository: Attempted to delete non-existent session with ID: ${id}`);
        return false;
      }

      await doc.remove();
      
      console.log(`TaskSessionRepository: Deleted session with ID: ${id}`);
      return true;
    } catch (error) {
      console.error(`TaskSessionRepository: Failed to delete session with ID ${id}:`, error);
      throw new Error(`Failed to delete task session: ${error}`);
    }
  }

  /**
   * Retrieves task sessions matching the specified options
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of matching sessions
   */
  public async findSessions(options: TaskSessionQueryOptions = {}): Promise<TaskSession[]> {
    try {
      const collection = await this.getCollection();
      let query = collection.find();

      // Build query based on options
      if (options.status) {
        if (Array.isArray(options.status)) {
          query = query.where('status').in(options.status);
        } else {
          query = query.where('status').eq(options.status);
        }
      }

      if (options.chatId) {
        query = query.where('chatId').eq(options.chatId);
      }

      if (options.priority) {
        query = query.where('priority').eq(options.priority);
      }

      if (options.tags && options.tags.length > 0) {
        // Find sessions that have at least one of the specified tags
        query = query.where('tags').elemMatch({
          $in: options.tags
        });
      }

      // Apply sorting
      if (options.sortBy) {
        const sortDirection = options.sortDirection || 'desc';
        query = query.sort({ [options.sortBy]: sortDirection });
      } else {
        // Default sort by updatedAt descending
        query = query.sort({ updatedAt: 'desc' });
      }

      // Apply pagination
      if (options.skip) {
        query = query.skip(options.skip);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const docs = await query.exec();
      return docs.map(doc => doc.toJSON() as TaskSession);
    } catch (error) {
      console.error('TaskSessionRepository: Failed to find sessions:', error);
      throw new Error(`Failed to find task sessions: ${error}`);
    }
  }

  /**
   * Counts task sessions matching the specified options
   * @param options - Query options for filtering (excludes pagination and sorting)
   * @returns Promise resolving to the count of matching sessions
   */
  public async countSessions(
    options: Omit<TaskSessionQueryOptions, 'limit' | 'skip' | 'sortBy' | 'sortDirection'> = {}
  ): Promise<number> {
    try {
      const collection = await this.getCollection();
      
      // Build selector for count query
      const selector: any = {};
      
      if (options.status) {
        if (Array.isArray(options.status)) {
          selector.status = { $in: options.status };
        } else {
          selector.status = { $eq: options.status };
        }
      }

      if (options.chatId) {
        selector.chatId = { $eq: options.chatId };
      }

      if (options.priority) {
        selector.priority = { $eq: options.priority };
      }

      if (options.tags && options.tags.length > 0) {
        selector.tags = { $elemMatch: { $in: options.tags } };
      }

      // Use RxDB's dedicated count() method for better performance
      const countQuery = collection.count({
        selector
      });

      const count = await countQuery.exec();
      return count;
    } catch (error) {
      console.error('TaskSessionRepository: Failed to count sessions:', error);
      throw new Error(`Failed to count task sessions: ${error}`);
    }
  }

  /**
   * Retrieves the most recent task session for a specific chat
   * @param chatId - Chat ID to find the latest session for
   * @returns Promise resolving to the most recent session or null if none found
   */
  public async getLatestSessionForChat(chatId: string): Promise<TaskSession | null> {
    try {
      const sessions = await this.findSessions({
        chatId,
        sortBy: 'updatedAt',
        sortDirection: 'desc',
        limit: 1
      });

      return sessions.length > 0 ? sessions[0] : null;
    } catch (error) {
      console.error(`TaskSessionRepository: Failed to get latest session for chat ${chatId}:`, error);
      throw new Error(`Failed to get latest session for chat: ${error}`);
    }
  }

  /**
   * Retrieves all active (non-completed/failed/cancelled) sessions
   * @param chatId - Optional chat ID to filter by
   * @returns Promise resolving to array of active sessions
   */
  public async getActiveSessions(chatId?: string): Promise<TaskSession[]> {
    const activeStatuses: TaskSessionStatus[] = ['initializing', 'awaiting_user_input', 'processing', 'paused'];
    
    return this.findSessions({
      status: activeStatuses,
      chatId,
      sortBy: 'updatedAt',
      sortDirection: 'desc'
    });
  }
} 