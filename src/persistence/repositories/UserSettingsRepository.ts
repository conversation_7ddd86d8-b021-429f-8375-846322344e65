/**
 * User Settings Repository
 * 
 * Provides a data access layer for interacting with the user_settings RxDB collection.
 * Manages the singleton user settings document with robust CRUD operations, default value
 * creation, and proper error handling.
 * 
 * Key Features:
 * - Singleton document management with fixed ID 'singleton_user_settings'
 * - Automatic default settings creation using Zod schema defaults
 * - Atomic operations for safe concurrent access
 * - Type-safe interactions with RxDB
 * - Comprehensive error handling and logging
 * 
 * Usage Example:
 * ```typescript
 * const repository = new UserSettingsRepository();
 * const settings = await repository.getSettings();
 * await repository.saveSettings({ apiKeys: { openai: 'sk-...' } });
 * ```
 */

import { 
  N8nAidDatabase, 
  UserSettingsCollection, 
  UserSettingsDocument, 
  UserSettingsDocType 
} from '../types/database.types';
import { UserSettings, UserSettingsSchema } from '../../types/userSettings.types';
import { getDb } from '../rxdbSetup';

/**
 * Fixed singleton document ID for user settings
 */
const USER_SETTINGS_DOC_ID = 'singleton_user_settings' as const;

/**
 * Repository for managing user settings in RxDB
 */
export class UserSettingsRepository {
  private readonly dbPromise: Promise<N8nAidDatabase>;
  private readonly settingsDocId = USER_SETTINGS_DOC_ID;

  /**
   * Creates a new UserSettingsRepository instance
   * @param dbPromise - Optional database promise for dependency injection (useful for testing)
   */
  constructor(dbPromise?: Promise<N8nAidDatabase>) {
    this.dbPromise = dbPromise || getDb();
  }

  /**
   * Gets the user_settings collection from the database
   * @private
   */
  private async getCollection(): Promise<UserSettingsCollection> {
    const db = await this.dbPromise;
    return db.user_settings;
  }

  /**
   * Retrieves user settings, creating defaults if document doesn't exist
   * @returns Promise resolving to the user settings object
   */
  public async getSettings(): Promise<UserSettings> {
    try {
      const collection = await this.getCollection();
      let settingsDoc = await collection.findOne(this.settingsDocId).exec();

      if (!settingsDoc) {
        console.log('UserSettingsRepository: User settings document not found, creating with defaults');
        
        // Create default settings using Zod schema defaults
        const defaultSettingsData: UserSettingsDocType = UserSettingsSchema.parse({ 
          id: this.settingsDocId 
        });
        
        settingsDoc = await collection.insert(defaultSettingsData);
        console.log('UserSettingsRepository: Created new user settings document with defaults');
      }

      // Convert RxDB's readonly document to mutable UserSettings type
      return settingsDoc.toMutableJSON() as UserSettings;
    } catch (error) {
      console.error('UserSettingsRepository: Failed to get settings:', error);
      throw new Error(`Failed to retrieve user settings: ${error}`);
    }
  }

  /**
   * Saves partial user settings, merging with existing data
   * @param settingsToSave - Partial settings object to merge and save
   * @returns Promise resolving to the updated complete settings object
   */
  public async saveSettings(settingsToSave: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const collection = await this.getCollection();
      const currentDoc = await collection.findOne(this.settingsDocId).exec();

      if (!currentDoc) {
        console.warn('UserSettingsRepository: Attempted to save settings but document did not exist. Creating new with provided partial data and defaults');
        
        // Get defaults and merge with provided settings
        const baseDefaults: UserSettingsDocType = UserSettingsSchema.parse({ 
          id: this.settingsDocId 
        });
        
        const mergedData = { 
          ...baseDefaults, 
          ...settingsToSave, 
          id: this.settingsDocId, 
          updatedAt: new Date() 
        };
        
        // Validate the merged data fully
        const validatedMergedData = UserSettingsSchema.parse(mergedData);
        const newDoc = await collection.insert(validatedMergedData);
        
        console.log('UserSettingsRepository: Created new settings document with merged data');
        return newDoc.toMutableJSON() as UserSettings;
      } else {
        // Document exists, perform incremental patch (RxDB v14+ method)
        const patchData = { 
          ...settingsToSave, 
          updatedAt: new Date() 
        };
        
        const updatedDoc = await currentDoc.incrementalPatch(patchData);
        console.log('UserSettingsRepository: Updated existing settings document');
        return updatedDoc.toMutableJSON() as UserSettings;
      }
    } catch (error) {
      console.error('UserSettingsRepository: Failed to save settings:', error);
      throw new Error(`Failed to save user settings: ${error}`);
    }
  }

  /**
   * Resets all user settings to default values
   * @returns Promise resolving to the reset settings object
   */
  public async resetSettingsToDefaults(): Promise<UserSettings> {
    try {
      const collection = await this.getCollection();
      let settingsDoc = await collection.findOne(this.settingsDocId).exec();

      // Create default settings data
      const defaultSettingsData: UserSettingsDocType = UserSettingsSchema.parse({ 
        id: this.settingsDocId 
      });

      if (!settingsDoc) {
        // Document doesn't exist, create it
        settingsDoc = await collection.insert(defaultSettingsData);
        console.log('UserSettingsRepository: Created new settings document with defaults');
      } else {
        // Document exists, reset to defaults while preserving ID and updating timestamp
        const resetData = {
          ...defaultSettingsData,
          updatedAt: new Date()
        };
        
        settingsDoc = await settingsDoc.incrementalPatch(resetData);
        console.log('UserSettingsRepository: Reset existing settings document to defaults');
      }

      return settingsDoc.toMutableJSON() as UserSettings;
    } catch (error) {
      console.error('UserSettingsRepository: Failed to reset settings to defaults:', error);
      throw new Error(`Failed to reset user settings to defaults: ${error}`);
    }
  }
} 