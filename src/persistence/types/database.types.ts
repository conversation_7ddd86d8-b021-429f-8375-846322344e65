/**
 * Database Types
 * 
 * This file defines all TypeScript interfaces and types for the RxDB database setup.
 * These types provide comprehensive coverage for RxDB document types, collections,
 * and the overall database structure.
 */

import { RxDatabase, RxCollection, RxDocument } from 'rxdb';

// Import the base types from our Zod schemas
import type { UserSettings } from '../../types/userSettings.types';
import type { TaskSession } from '../../types/tasks.types';
import type { ChatThread, ChatMessage } from '../../types/chat.types';
import type { N8nRule } from '../../types/rules.types';
import type { KnowledgeChunk } from '../../types/knowledge.types';

/**
 * RxDB document types - these extend the base types with RxDocument methods and properties
 */

// User Settings Collection
export type UserSettingsDocType = UserSettings;
export type UserSettingsDocument = RxDocument<UserSettingsDocType>;
export type UserSettingsCollection = RxCollection<UserSettingsDocType>;

// Task Sessions Collection
export type TaskSessionDocType = TaskSession;
export type TaskSessionDocument = RxDocument<TaskSessionDocType>;
export type TaskSessionCollection = RxCollection<TaskSessionDocType>;

// Chat Threads Collection
export type ChatThreadDocType = ChatThread;
export type ChatThreadDocument = RxDocument<ChatThreadDocType>;
export type ChatThreadCollection = RxCollection<ChatThreadDocType>;

// Conversation Messages Collection
export type ChatMessageDocType = ChatMessage;
export type ChatMessageDocument = RxDocument<ChatMessageDocType>;
export type ChatMessageCollection = RxCollection<ChatMessageDocType>;

// Custom Rules Collection
export type CustomRuleDocType = N8nRule;
export type CustomRuleDocument = RxDocument<CustomRuleDocType>;
export type CustomRuleCollection = RxCollection<CustomRuleDocType>;

// Knowledge Chunks Collection
export type KnowledgeChunkDocType = KnowledgeChunk;
export type KnowledgeChunkDocument = RxDocument<KnowledgeChunkDocType>;
export type KnowledgeChunkCollection = RxCollection<KnowledgeChunkDocType>;

/**
 * Define the structure of our database collections
 * This interface maps collection names to their corresponding RxCollection types
 */
export interface N8nAidDbCollections {
  user_settings: UserSettingsCollection;
  task_sessions: TaskSessionCollection;
  chat_threads: ChatThreadCollection;
  conversation_messages: ChatMessageCollection;
  custom_rules: CustomRuleCollection;
  knowledge_chunks: KnowledgeChunkCollection;
}

/**
 * Define the type for our RxDatabase instance
 * This is the main database type that includes all collections
 */
export type N8nAidDatabase = RxDatabase<N8nAidDbCollections>;

/**
 * Database initialization options
 */
export interface DatabaseInitializationOptions {
  /** Name of the database (default: 'n8n-ai-assistant') */
  databaseName?: string;
  /** Enable multi-instance mode for browser environments (default: true for browser, false for Node.js) */
  multiInstance?: boolean;
  /** Ignore duplicate database creation attempts (default: true) */
  ignoreDuplicate?: boolean;
  /** Enable automatic cleanup policy (default: true) */
  cleanup?: boolean;
  /** Database password for encryption (optional) */
  password?: string;
}

/**
 * Database status information
 */
export interface DatabaseStatus {
  isInitialized: boolean;
  name: string;
  environment: 'node' | 'browser';
  storageAdapter: string;
  collections: string[];
  lastInitialized?: Date;
  error?: string;
} 