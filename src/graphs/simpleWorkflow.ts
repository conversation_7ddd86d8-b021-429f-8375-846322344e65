/**
 * Simple Workflow Example
 * 
 * This example demonstrates how to use the GraphBuilder to create a simple workflow
 * that processes user input through a sequence of agent calls and tools.
 */

import { GraphBuilder } from '../engine/graph/GraphBuilder';
import { LLMTaskCategory } from '../engine/graph/types/graph.types';
import { AgentDependencies, BaseN8nAgent } from '../engine/graph/types/builder.types';

// Mock agent classes for demonstration
class IntentRouterAgent implements BaseN8nAgent<{ userInput: string; chatHistory: any[] }, { intent: string }> {
  static agentSlug = 'n8n-intent-router-agent';
  static defaultLlmTaskCategory = LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING;
  static description = 'Determines the user\'s intent from their message';
  
  constructor(private deps: AgentDependencies) {}
  
  async processInput(input: { userInput: string; chatHistory: any[] }): Promise<{ intent: string }> {
    // Mock implementation
    return { intent: 'create_workflow' };
  }
}

class RequirementsGatheringAgent implements BaseN8nAgent<{ userInput: string; chatHistory: any[] }, { requirements: string }> {
  static agentSlug = 'n8n-requirements-gathering-agent';
  static defaultLlmTaskCategory = LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION;
  static description = 'Gathers detailed requirements for the workflow';
  
  constructor(private deps: AgentDependencies) {}
  
  async processInput(input: { userInput: string; chatHistory: any[] }): Promise<{ requirements: string }> {
    // Mock implementation
    return { requirements: 'Sample requirements' };
  }
}

class ArchitectAgent implements BaseN8nAgent<{ requirements: string }, { design: string }> {
  static agentSlug = 'n8n-architect-agent';
  static defaultLlmTaskCategory = LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING;
  static description = 'Creates a high-level design for the n8n workflow';
  
  constructor(private deps: AgentDependencies) {}
  
  async processInput(input: { requirements: string }): Promise<{ design: string }> {
    // Mock implementation
    return { design: 'Sample workflow design' };
  }
}

class KnowledgeAgent implements BaseN8nAgent<{ userInput: string; chatHistory: any[] }, { explanation: string }> {
  static agentSlug = 'n8n-knowledge-agent';
  static defaultLlmTaskCategory = LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION;
  static description = 'Provides explanations about n8n concepts and features';
  
  constructor(private deps: AgentDependencies) {}
  
  async processInput(input: { userInput: string; chatHistory: any[] }): Promise<{ explanation: string }> {
    // Mock implementation
    return { explanation: 'Sample explanation' };
  }
}

// Create a simple workflow that:
// 1. Takes user input
// 2. Uses an intent router agent to determine what to do
// 3. Branches based on intent:
//    - For 'create_workflow': calls a requirements agent, then an architect agent
//    - For 'explain': calls a knowledge agent
//    - Otherwise: returns a default response
export function createSimpleWorkflow() {
  const builder = new GraphBuilder(
    'simple_workflow_v1',
    'A simple workflow that processes user input and routes to different agents based on intent'
  );
  
  // Define expected inputs
  builder.addExpectedInitialInput('userInput', 'The user message to process');
  builder.addExpectedInitialInput('chatHistory', 'Previous conversation history', undefined, true);
  
  // Define constants
  builder.addGraphConstant('defaultResponse', 'I\'m not sure how to help with that. Could you rephrase?');
  
  // Add nodes
  
  // Intent router to determine what the user wants
  builder.addAgentNode(
    'intent_router',
    IntentRouterAgent,
    undefined,
    {
      userInput: builder.graphInput('userInput'),
      chatHistory: builder.graphInput('chatHistory', [])
    }
  );
  
  // Requirements gathering agent for workflow creation
  builder.addAgentNode(
    'requirements_agent',
    RequirementsGatheringAgent,
    undefined,
    {
      userInput: builder.graphInput('userInput'),
      chatHistory: builder.graphInput('chatHistory', [])
    }
  );
  
  // Architect agent to design the workflow
  builder.addAgentNode(
    'architect_agent',
    ArchitectAgent,
    undefined,
    {
      requirements: builder.nodeOutput('requirements_agent', 'payload.requirements')
    }
  );
  
  // Knowledge agent to explain concepts
  builder.addAgentNode(
    'knowledge_agent',
    KnowledgeAgent,
    undefined,
    {
      userInput: builder.graphInput('userInput'),
      chatHistory: builder.graphInput('chatHistory', [])
    }
  );
  
  // Data transform for default response
  builder.addDataTransformNode(
    'default_response_transform',
    (inputs: Record<string, any>) => ({ response: inputs.defaultResponse }),
    {
      defaultResponse: builder.constant('defaultResponse')
    },
    'Formats the default response'
  );
  
  // End nodes for different outcomes
  builder.addEndNode(
    'workflow_creation_result',
    'Returns the workflow design from the architect',
    false,
    builder.nodeOutput('architect_agent')
  );
  
  builder.addEndNode(
    'explanation_result',
    'Returns the explanation from the knowledge agent',
    false,
    builder.nodeOutput('knowledge_agent')
  );
  
  builder.addEndNode(
    'default_result',
    'Returns the default response',
    false,
    builder.nodeOutput('default_response_transform')
  );
  
  // Conditional branch based on intent
  builder.addConditionalNode(
    'intent_branch',
    (inputs: Record<string, any>) => {
      const intent = inputs.intent?.payload?.intent?.toLowerCase();
      if (intent === 'create_workflow') {
        return 'create_workflow';
      } else if (intent === 'explain') {
        return 'explain';
      } else {
        return 'default';
      }
    },
    {
      intent: builder.nodeOutput('intent_router')
    },
    'Branches based on the detected intent'
  );
  
  // Connect the nodes with edges
  
  // Start with the intent router
  builder.setInitialNode('intent_router');
  
  // Connect intent router to the branch
  builder.setLastAddedNodeId('intent_router').onSuccess('intent_branch');
  
  // Connect the branch to appropriate nodes
  builder.setLastAddedNodeId('intent_branch')
    .onBranch('create_workflow', 'requirements_agent')
    .onBranch('explain', 'knowledge_agent')
    .otherwise('default_response_transform');
  
  // Connect requirements to architect
  builder.setLastAddedNodeId('requirements_agent').onSuccess('architect_agent');
  
  // Connect to end nodes
  builder.setLastAddedNodeId('architect_agent').onSuccess('workflow_creation_result');
  builder.setLastAddedNodeId('knowledge_agent').onSuccess('explanation_result');
  builder.setLastAddedNodeId('default_response_transform').onSuccess('default_result');
  
  // Compile the workflow
  return builder.compile();
}

// Example usage - cross-environment friendly
// Only run this code if this file is being executed directly
if (typeof window === 'undefined' && typeof process !== 'undefined' && process.argv[1] === __filename) {
  const workflow = createSimpleWorkflow();
  console.log(JSON.stringify(workflow.definition, null, 2));
} 