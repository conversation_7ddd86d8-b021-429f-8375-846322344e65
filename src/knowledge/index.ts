/**
 * Knowledge & RAG System Index
 * 
 * Central export point for the Retrieval-Augmented Generation (RAG) system
 * that provides access to n8n documentation, workflow examples, and other
 * knowledge sources.
 */

// TODO: Export knowledge services when implemented
// export { EmbeddingService } from './EmbeddingService';
// export { KnowledgeService } from './KnowledgeService';
// export { KnowledgeRepository } from './KnowledgeRepository';

// TODO: Export knowledge-related types when defined
// export type { KnowledgeChunk, EmbeddingVector, SearchResult } from './types';