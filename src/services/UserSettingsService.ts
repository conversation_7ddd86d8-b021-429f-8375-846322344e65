/**
 * User Settings Service
 * 
 * Provides a high-level API for managing user settings with business logic abstraction.
 * This service sits on top of UserSettingsRepository and offers convenient methods for
 * accessing specific settings, with automatic fallback to system defaults when user
 * preferences are not configured.
 * 
 * Key Features:
 * - Convenient API for common settings operations
 * - Automatic fallback to system defaults
 * - Type-safe settings access and modification
 * - Business logic encapsulation for settings interpretation
 * - Support for all settings categories (API keys, model preferences, user preferences)
 * 
 * Usage Example:
 * ```typescript
 * const service = new UserSettingsService(repository);
 * const apiKey = await service.getApiKeyForProvider('openai');
 * await service.saveModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING, {
 *   provider: 'anthropic',
 *   modelId: 'claude-3-5-sonnet-20241022'
 * });
 * ```
 */

import { UserSettingsRepository } from '../persistence/repositories/UserSettingsRepository';
import { 
  UserSettings, 
  ApiKeysConfig, 
  ModelProviderConfig, 
  UserPreferences,
  UserModelPreferences
} from '../types/userSettings.types';
import { LLMTaskCategory } from '../engine/graph/types/graph.types';
import { 
  DEFAULT_FALLBACK_MODELS_FOR_SERVICE,
  getDefaultModelForTaskCategory
} from '../config/userSettings.defaults';

/**
 * Service for managing user settings with business logic and convenient API
 */
export class UserSettingsService {
  private readonly repository: UserSettingsRepository;

  /**
   * Creates a new UserSettingsService instance
   * @param repository - The UserSettingsRepository instance for data persistence
   */
  constructor(repository: UserSettingsRepository) {
    this.repository = repository;
  }

  /**
   * Retrieves all user settings
   * @returns Promise resolving to the complete user settings object
   */
  public async getAllSettings(): Promise<UserSettings> {
    return this.repository.getSettings();
  }

  /**
   * Saves partial user settings, merging with existing data
   * @param settings - Partial settings object to merge and save
   * @returns Promise resolving to the updated complete settings object
   */
  public async saveAllSettings(settings: Partial<UserSettings>): Promise<UserSettings> {
    return this.repository.saveSettings(settings);
  }

  /**
   * Gets the API key for a specific provider
   * @param providerName - The provider name (e.g., 'openai', 'anthropic', 'google')
   * @returns Promise resolving to the API key or undefined if not set
   */
  public async getApiKeyForProvider(providerName: keyof ApiKeysConfig): Promise<string | undefined> {
    const settings = await this.repository.getSettings();
    return settings.apiKeys[providerName];
  }

  /**
   * Saves an API key for a specific provider
   * @param providerName - The provider name to save the key for
   * @param apiKey - The API key to save
   * @returns Promise that resolves when the key is saved
   */
  public async saveApiKey(providerName: keyof ApiKeysConfig, apiKey: string): Promise<void> {
    const currentSettings = await this.repository.getSettings();
    const updatedApiKeys = { ...currentSettings.apiKeys, [providerName]: apiKey };
    await this.repository.saveSettings({ apiKeys: updatedApiKeys });
  }

  /**
   * Gets the model preference for a specific task category, with fallback to defaults
   * @param taskCategory - The LLM task category to get preferences for
   * @returns Promise resolving to the model configuration (user preference or default)
   */
  public async getModelPreference(taskCategory: LLMTaskCategory): Promise<ModelProviderConfig> {
    const settings = await this.repository.getSettings();
    const userPreference = settings.modelPreferences[taskCategory];

    // If user has a preference configured with both provider and modelId, use it
    if (userPreference?.provider && userPreference?.modelId) {
      return userPreference;
    }

    // Otherwise, return the system default for this category
    return getDefaultModelForTaskCategory(taskCategory);
  }

  /**
   * Saves a model preference for a specific task category
   * @param taskCategory - The LLM task category to save preferences for
   * @param preference - The model configuration to save
   * @returns Promise that resolves when the preference is saved
   */
  public async saveModelPreference(
    taskCategory: LLMTaskCategory, 
    preference: ModelProviderConfig
  ): Promise<void> {
    const currentSettings = await this.repository.getSettings();
    const updatedModelPrefs = { 
      ...currentSettings.modelPreferences, 
      [taskCategory]: preference 
    };
    await this.repository.saveSettings({ modelPreferences: updatedModelPrefs });
  }

  /**
   * Gets all user preferences
   * @returns Promise resolving to the user preferences object
   */
  public async getPreferences(): Promise<UserPreferences> {
    const settings = await this.repository.getSettings();
    return settings.preferences;
  }

  /**
   * Saves a specific user preference
   * @param key - The preference key to update
   * @param value - The new value for the preference
   * @returns Promise that resolves when the preference is saved
   */
  public async savePreference<K extends keyof UserPreferences>(
    key: K, 
    value: UserPreferences[K]
  ): Promise<void> {
    const currentSettings = await this.repository.getSettings();
    const updatedPrefs = { ...currentSettings.preferences, [key]: value };
    await this.repository.saveSettings({ preferences: updatedPrefs });
  }

  /**
   * Checks if a specific feature is enabled
   * @param featureKey - The feature key to check
   * @returns Promise resolving to boolean indicating if the feature is enabled
   */
  public async isFeatureEnabled(
    featureKey: keyof Pick<UserPreferences, 'enableChromeAI' | 'enableAutoSummarization' | 'debugMode' | 'autoSaveWorkflows'>
  ): Promise<boolean> {
    const prefs = await this.getPreferences();
    return !!prefs[featureKey]; // Convert to boolean (handles undefined with falsy default)
  }

  /**
   * Resets all user settings to default values
   * @returns Promise resolving to the reset settings object
   */
  public async resetAllSettings(): Promise<UserSettings> {
    return this.repository.resetSettingsToDefaults();
  }

  /**
   * Gets model preferences for all categories, with defaults filled in where needed
   * @returns Promise resolving to complete model preferences with defaults
   */
  public async getCompleteModelPreferences(): Promise<Record<LLMTaskCategory, ModelProviderConfig>> {
    const settings = await this.repository.getSettings();
    const completePreferences: Record<LLMTaskCategory, ModelProviderConfig> = {} as any;

    // For each task category, use user preference or default
    for (const category of Object.values(LLMTaskCategory)) {
      completePreferences[category] = await this.getModelPreference(category);
    }

    return completePreferences;
  }

  /**
   * Checks if the user has configured API keys for any providers
   * @returns Promise resolving to boolean indicating if any API keys are configured
   */
  public async hasConfiguredApiKeys(): Promise<boolean> {
    const settings = await this.repository.getSettings();
    const apiKeys = settings.apiKeys;
    
    return Object.values(apiKeys).some(key => key && key.trim().length > 0);
  }

  /**
   * Gets all configured API key provider names
   * @returns Promise resolving to array of provider names that have API keys configured
   */
  public async getConfiguredProviders(): Promise<(keyof ApiKeysConfig)[]> {
    const settings = await this.repository.getSettings();
    const apiKeys = settings.apiKeys;
    
    return Object.entries(apiKeys)
      .filter(([_, key]) => key && key.trim().length > 0)
      .map(([provider, _]) => provider as keyof ApiKeysConfig);
  }

  /**
   * Bulk update model preferences for multiple categories
   * @param preferences - Object mapping task categories to model configurations
   * @returns Promise that resolves when all preferences are saved
   */
  public async saveMultipleModelPreferences(
    preferences: Partial<UserModelPreferences>
  ): Promise<void> {
    const currentSettings = await this.repository.getSettings();
    const updatedModelPrefs = { 
      ...currentSettings.modelPreferences, 
      ...preferences 
    };
    await this.repository.saveSettings({ modelPreferences: updatedModelPrefs });
  }

  /**
   * Removes an API key for a specific provider
   * @param providerName - The provider name to remove the key for
   * @returns Promise that resolves when the key is removed
   */
  public async removeApiKey(providerName: keyof ApiKeysConfig): Promise<void> {
    const currentSettings = await this.repository.getSettings();
    const updatedApiKeys = { ...currentSettings.apiKeys };
    delete updatedApiKeys[providerName];
    await this.repository.saveSettings({ apiKeys: updatedApiKeys });
  }
} 