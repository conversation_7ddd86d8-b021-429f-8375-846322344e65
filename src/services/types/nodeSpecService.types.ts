/**
 * NodeSpecService Types
 * 
 * This file defines all TypeScript interfaces and types for the NodeSpecService component.
 * These types provide comprehensive coverage for n8n node specifications, including Zod schemas
 * for runtime validation, lightweight descriptors for agent selection, and error handling.
 */

import { z } from 'zod';

/**
 * Zod schema for node property options (used in dropdown parameters, etc.)
 */
export const NodePropertyOptionSchema = z.object({
  name: z.string(),
  value: z.union([z.string(), z.number(), z.boolean()]),
  description: z.string().optional(),
  action: z.string().optional(),
});

/**
 * Zod schema for node property display options (conditional display logic)
 */
export const NodePropertyDisplayOptionsSchema = z.object({
  show: z.record(z.array(z.union([z.string(), z.number(), z.boolean()]))).optional(),
  hide: z.record(z.array(z.union([z.string(), z.number(), z.boolean()]))).optional(),
});

/**
 * Zod schema for node property type options (additional configuration)
 */
export const NodePropertyTypeOptionsSchema = z.object({
  loadOptionsMethod: z.string().optional(),
  loadOptionsDependsOn: z.array(z.string()).optional(),
  multipleValues: z.boolean().optional(),
  multipleValueButtonText: z.string().optional(),
  showAlpha: z.boolean().optional(),
  alwaysOpenEditWindow: z.boolean().optional(),
  editor: z.string().optional(),
  rows: z.number().optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  numberPrecision: z.number().optional(),
  sortable: z.boolean().optional(),
}).catchall(z.any()); // Allow additional unknown properties

/**
 * Zod schema for a single node parameter/property
 */
export const NodeParameterSchema = z.object({
  displayName: z.string(),
  name: z.string(),
  type: z.string(), // e.g., 'string', 'number', 'boolean', 'options', 'collection', 'fixedCollection', 'multiOptions', etc.
  default: z.any(),
  description: z.string().optional(),
  placeholder: z.string().optional(),
  hint: z.string().optional(),
  required: z.boolean().optional(),
  noDataExpression: z.boolean().optional(),
  typeOptions: NodePropertyTypeOptionsSchema.optional(),
  options: z.array(NodePropertyOptionSchema).optional(),
  displayOptions: NodePropertyDisplayOptionsSchema.optional(),
  validateType: z.string().optional(),
  ignoreValidationDuringExecution: z.boolean().optional(),
}).catchall(z.any()); // Allow additional unknown properties

/**
 * Zod schema for node credentials configuration
 */
export const NodeCredentialSchema = z.object({
  name: z.string(),
  required: z.boolean().optional(),
  displayName: z.string().optional(),
  testedBy: z.string().optional(),
});

/**
 * Zod schema for node webhooks configuration
 */
export const NodeWebhookSchema = z.object({
  name: z.string(),
  httpMethod: z.string(),
  responseMode: z.string().optional(),
  responseData: z.string().optional(),
  path: z.string().optional(),
  restartWebhook: z.boolean().optional(),
  isFullPath: z.boolean().optional(),
});

/**
 * Zod schema for node codex information (documentation and categorization)
 */
export const NodeCodexSchema = z.object({
  categories: z.array(z.string()).optional(),
  subcategories: z.record(z.array(z.string())).optional(),
  alias: z.array(z.string()).optional(),
  resources: z.object({
    primaryDocumentation: z.array(z.object({
      url: z.string(),
    })).optional(),
    credentialDocumentation: z.array(z.object({
      url: z.string(),
    })).optional(),
  }).optional(),
});

/**
 * Zod schema for trigger panel configuration
 */
export const NodeTriggerPanelSchema = z.object({
  header: z.string().optional(),
  executionsHelp: z.object({
    inactive: z.string().optional(),
    active: z.string().optional(),
  }).optional(),
  activationHint: z.string().optional(),
});

/**
 * Main Zod schema for a complete node description (from /types/nodes.snapshot.json)
 */
export const NodeDescriptionSchema = z.object({
  // Basic identification
  name: z.string(), // Internal type ID, e.g., "n8n-nodes-base.if"
  displayName: z.string(),
  
  // Visual and grouping
  icon: z.string().optional(),
  iconColor: z.string().optional(),
  iconUrl: z.string().optional(),
  group: z.array(z.string()), // e.g., ["transform"], ["trigger"], ["output"]
  
  // Versioning
  version: z.union([z.number(), z.array(z.number())]), // Can be single number or array for multi-version nodes
  defaultVersion: z.number().optional(),
  
  // Description and documentation
  description: z.string(),
  subtitle: z.string().optional(),
  eventTriggerDescription: z.string().optional(),
  
  // Node configuration
  defaults: z.record(z.any()).optional(), // Default values like name, color
  inputs: z.array(z.string()).optional(), // e.g., ["main"] for input anchors
  outputs: z.array(z.string()).optional(), // e.g., ["main"], or ["main", "main"] for multiple outputs
  
  // Properties and parameters
  properties: z.array(NodeParameterSchema), // The list of node parameters
  
  // Authentication and external connections
  credentials: z.array(NodeCredentialSchema).optional(),
  webhooks: z.array(NodeWebhookSchema).optional(),
  
  // Metadata and categorization
  codex: NodeCodexSchema.optional(),
  triggerPanel: NodeTriggerPanelSchema.optional(),
  
  // Feature flags
  usableAsTool: z.boolean().optional(),
  
  // Additional fields that might be present
  maxNodes: z.number().optional(),
  documentationUrl: z.string().optional(),
}).catchall(z.any()); // Allow additional unknown properties for future extensibility

/**
 * TypeScript type inferred from the NodeDescriptionSchema
 */
export type NodeDescription = z.infer<typeof NodeDescriptionSchema>;

/**
 * Lightweight descriptor for NodeSelectorAgent
 * Contains only the essential information needed for node selection
 */
export interface NodeSelectorDescriptor {
  typeId: string; // Corresponds to NodeDescription.name
  displayName: string;
  description?: string;
  categories?: string[]; // Derived from NodeDescription.group
  primaryGroup?: string; // First item in group array
  icon?: string;
  iconUrl?: string;
  version?: number | number[]; // Version information
  usableAsTool?: boolean; // Whether this node can be used as a tool
}

/**
 * Configuration for node searching and filtering
 */
export interface NodeSearchOptions {
  limit?: number; // Maximum number of results to return
  includeDescription?: boolean; // Whether to search in descriptions
  caseSensitive?: boolean; // Whether search should be case sensitive
  categories?: string[]; // Filter by specific categories
  usableAsTool?: boolean; // Filter by tool usability
}

/**
 * Result from a node search operation
 */
export interface NodeSearchResult {
  nodes: NodeSelectorDescriptor[];
  totalMatches: number;
  searchTerm?: string;
  appliedFilters?: NodeSearchOptions;
}

/**
 * Information about the current data source
 */
export interface NodeSpecDataSource {
  type: 'file' | 'json_string' | 'array' | 'unknown';
  sourceName: string;
  loadedAt: Date;
  nodeCount: number;
  version?: string; // If available from source
}

/**
 * Error types for NodeSpecService operations
 */
export class NodeSpecServiceError extends Error {
  constructor(
    message: string,
    public readonly type: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'NodeSpecServiceError';
  }
}

export class NodeSpecLoadingError extends NodeSpecServiceError {
  constructor(message: string, originalError?: any) {
    super(message, 'NodeSpecLoadingError', originalError);
  }
}

export class NodeSpecValidationError extends NodeSpecServiceError {
  constructor(message: string, validationErrors?: any) {
    super(message, 'NodeSpecValidationError', validationErrors);
  }
}

export class NodeSpecNotInitializedError extends NodeSpecServiceError {
  constructor() {
    super('NodeSpecService has not been initialized. Call one of the loadSpecs methods first.', 'NodeSpecNotInitializedError');
  }
}

export class NodeSpecFileNotFoundError extends NodeSpecServiceError {
  constructor(filePath: string, originalError?: any) {
    super(`Node specifications file not found: ${filePath}`, 'NodeSpecFileNotFoundError', originalError);
  }
}

export class NodeSpecInvalidJsonError extends NodeSpecServiceError {
  constructor(message: string, originalError?: any) {
    super(`Invalid JSON in node specifications: ${message}`, 'NodeSpecInvalidJsonError', originalError);
  }
} 