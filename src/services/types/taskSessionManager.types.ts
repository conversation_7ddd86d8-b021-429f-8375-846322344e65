/**
 * Task Session Manager Types
 * 
 * This file defines all TypeScript interfaces and types for the TaskSessionManager component.
 * These types provide comprehensive coverage for task session lifecycle management,
 * context summarization, and graph execution state persistence.
 */

import { TaskSession, TaskSessionStatus, GraphExecutionContextSnapshot } from '../../types/tasks.types';
import { LLMTaskCategory } from '../../engine/graph/types/graph.types';
import { ModelMessage } from 'ai';

/**
 * Data required to create a new task session
 */
export interface CreateTaskSessionData {
  /** Associated chat thread ID */
  chatId: string;
  /** The initial user input that triggered this task */
  originalUserInput: string;
  /** Optional title for the task session */
  title?: string;
  /** Optional initial status (defaults to 'initializing') */
  initialStatus?: TaskSessionStatus;
  /** Optional initial graph definition ID if known */
  initialGraphDefinitionId?: string;
  /** Optional initial description */
  description?: string;
  /** Optional priority level */
  priority?: 'low' | 'normal' | 'high';
  /** Optional tags for categorization */
  tags?: string[];
}

/**
 * Result of a summarization operation
 */
export interface SummarizationResult {
  /** The newly generated summary text */
  newSummary: string;
  /** Token usage for the summarization call */
  tokensUsed?: number;
  /** Cost of the summarization operation in USD */
  cost?: number;
  /** Whether the summarization was successful */
  success: boolean;
  /** Error details if summarization failed */
  error?: {
    message: string;
    type: string;
    originalError?: any;
  };
}

/**
 * Context provided when requesting summarization
 * This is what PromptBuilderService might pass to TaskSessionManager when requesting summarization
 */
export interface SummarizationRequestContext {
  /** ID of the task session to summarize for */
  taskSessionId: string;
  /** History messages to be summarized - PromptBuilder prepares this from ConversationStore/ChatRepository */
  messagesToSummarize: ModelMessage[];
  /** The existing summary to be built upon */
  currentLlmContextSummary?: string;
  /** Target model context window for optimization */
  targetModelContextWindow: number;
  /** Agent slug for the summarizer (e.g., 'n8n-summarizer-agent') */
  summarizerAgentSlug: string;
  /** LLM task category for the summarizer call */
  summarizerTaskCategory: LLMTaskCategory;
}

/**
 * Options for context summarization management
 */
export interface ContextManagementOptions {
  /** Force summarization even if not strictly needed */
  forceSummarization?: boolean;
  /** Override the default summarizer agent slug */
  summarizerAgentSlug?: string;
  /** Override the default task category for summarization */
  summarizerTaskCategory?: LLMTaskCategory;
  /** Maximum tokens to allow before triggering summarization */
  maxTokensBeforeSummarization?: number;
}

/**
 * Result of ensuring context is summarized
 */
export interface ContextSummarizationResult {
  /** The task session (updated if summarization occurred) */
  taskSession: TaskSession | null;
  /** Whether the summary was updated during this operation */
  summaryUpdated: boolean;
  /** The new summary if it was updated */
  newSummary?: string;
  /** Details about the summarization process */
  summarizationDetails?: SummarizationResult;
}

/**
 * Options for updating task session status
 */
export interface UpdateStatusOptions {
  /** Optional completion data for final states */
  finalOutput?: any;
  /** Optional error details for failed states */
  errorDetails?: any;
  /** Whether to clear graph snapshot on completion/failure */
  clearGraphSnapshot?: boolean;
}

/**
 * Configuration for TaskSessionManager
 */
export interface TaskSessionManagerConfig {
  /** Default summarizer agent slug to use */
  defaultSummarizerAgentSlug: string;
  /** Default task category for summarization calls */
  defaultSummarizerTaskCategory: LLMTaskCategory;
  /** Default maximum iterations for graph execution */
  defaultMaxGraphIterations: number;
  /** Whether to automatically clean up completed sessions */
  autoCleanupCompletedSessions: boolean;
}

/**
 * Error thrown when task session is not found
 */
export class TaskSessionNotFoundError extends Error {
  constructor(sessionId: string) {
    super(`Task session not found: ${sessionId}`);
    this.name = 'TaskSessionNotFoundError';
  }
}

/**
 * Error thrown when summarization operations fail
 */
export class SummarizationError extends Error {
  constructor(
    message: string,
    public readonly sessionId: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'SummarizationError';
  }
}

/**
 * Error thrown when graph snapshot operations fail
 */
export class GraphSnapshotError extends Error {
  constructor(
    message: string,
    public readonly sessionId: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'GraphSnapshotError';
  }
}

/**
 * Error thrown when session status transitions are invalid
 */
export class InvalidStatusTransitionError extends Error {
  constructor(
    sessionId: string,
    currentStatus: TaskSessionStatus,
    targetStatus: TaskSessionStatus
  ) {
    super(`Invalid status transition for session ${sessionId}: ${currentStatus} -> ${targetStatus}`);
    this.name = 'InvalidStatusTransitionError';
  }
} 