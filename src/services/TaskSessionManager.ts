/**
 * Task Session Manager
 * 
 * @deprecated Parts of this service use the old TemplateRegistry system and need updating
 * TODO: Replace TemplateRegistry usage with ConversationHistoryService for summarization
 * 
 * Provides a high-level service responsible for the business logic and lifecycle management
 * of TaskSession objects. It acts as the primary interface for other services (like 
 * OrchestratorService and PromptBuilderService) to create, retrieve, update, and manage 
 * task-specific state and context.
 * 
 * Key Features:
 * - Task session lifecycle management (create, update, complete, fail, abort)
 * - LLM context summarization coordination
 * - Graph execution context snapshot persistence
 * - Clean API for other services to interact with task sessions
 * - Centralized task state management and validation
 * 
 * Usage Example:
 * \`\`\`typescript
 * const manager = new TaskSessionManager(taskSessionRepo, llmService);
 * const session = await manager.createSession({
 *   chatId: 'chat-123',
 *   originalUserInput: 'Create a workflow for...',
 *   title: 'Workflow Creation Task'
 * });
 * await manager.ensureContextIsSummarized({
 *   taskSessionId: session.id,
 *   messagesToSummarize: historyMessages,
 *   // ... other context
 * });
 * \`\`\`
 */

import { TaskSessionRepository } from '../persistence/repositories/TaskSessionRepository';
import { LLMService } from '../engine/llm/LLMService';
import { getAgentConfig } from '../engine/agents/agentRegistry';
// TODO: Remove when replacing with AgentPromptBuilder
// import { TemplateRegistry } from '../prompts/TemplateRegistry';

// Temporary stub for deprecated TemplateRegistry
interface TemplateRegistry {
  loadCommonTemplate(key: string): string;
  fillTemplate(template: string, variables: Record<string, any>): string;
}
import { TaskSession, TaskSessionStatus, GraphExecutionContextSnapshot } from '../types/tasks.types';
import { LLMTaskCategory } from '../engine/graph/types/graph.types';
import { ModelMessage } from 'ai';
import {
  CreateTaskSessionData,
  SummarizationResult,
  SummarizationRequestContext,
  ContextManagementOptions,
  ContextSummarizationResult,
  UpdateStatusOptions,
  TaskSessionManagerConfig,
  TaskSessionNotFoundError,
  SummarizationError,
  GraphSnapshotError,
  InvalidStatusTransitionError
} from './types/taskSessionManager.types';

/**
 * Default configuration for TaskSessionManager
 */
const DEFAULT_CONFIG: TaskSessionManagerConfig = {
  defaultSummarizerAgentSlug: 'n8n-summarizer-agent',
  defaultSummarizerTaskCategory: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
  defaultMaxGraphIterations: 100,
  autoCleanupCompletedSessions: false,
};

/**
 * TaskSessionManager - High-level service for task session lifecycle management
 */
export class TaskSessionManager {
  private readonly taskSessionRepo: TaskSessionRepository;
  private readonly llmService: LLMService;
  private readonly templateRegistry: TemplateRegistry;
  private readonly config: TaskSessionManagerConfig;

  /**
   * Creates a new TaskSessionManager instance
   * 
   * @param taskSessionRepo - Repository for task session persistence
   * @param llmService - Service for LLM interactions (for summarization)
   * @param templateRegistry - Template registry for prompt templates
   * @param config - Optional configuration overrides
   */
  constructor(
    taskSessionRepo: TaskSessionRepository,
    llmService: LLMService,
    templateRegistry: TemplateRegistry,
    config?: Partial<TaskSessionManagerConfig>
  ) {
    this.taskSessionRepo = taskSessionRepo;
    this.llmService = llmService;
    this.templateRegistry = templateRegistry;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // === Core Session Management ===

  /**
   * Creates a new task session with appropriate defaults
   * 
   * @param data - Task session creation data
   * @returns Promise resolving to the created task session
   */
  public async createSession(data: CreateTaskSessionData): Promise<TaskSession> {
    try {
      const sessionData = {
        chatId: data.chatId,
        originalUserInput: data.originalUserInput,
        title: data.title,
        description: data.description,
        status: data.initialStatus || 'initializing' as TaskSessionStatus,
        priority: data.priority || 'normal' as const,
        tags: data.tags || [],
        // Add initial graph definition ID to additionalData if provided
        additionalData: data.initialGraphDefinitionId 
          ? { initialGraphDefinitionId: data.initialGraphDefinitionId }
          : {},
      };

      const session = await this.taskSessionRepo.createSession(sessionData);
      
      console.log(`TaskSessionManager: Created new session '${session.id}' for chat '${data.chatId}'`);
      return session;
    } catch (error) {
      console.error('TaskSessionManager: Failed to create session:', error);
      throw new Error(`Failed to create task session: ${error}`);
    }
  }

  /**
   * Retrieves a task session by its ID
   * 
   * @param sessionId - Session ID to retrieve
   * @returns Promise resolving to the session or null if not found
   */
  public async getSession(sessionId: string): Promise<TaskSession | null> {
    try {
      return await this.taskSessionRepo.getSessionById(sessionId);
    } catch (error) {
      console.error(`TaskSessionManager: Failed to get session ${sessionId}:`, error);
      throw new Error(`Failed to retrieve task session: ${error}`);
    }
  }

  /**
   * Updates a task session's status with proper validation
   * 
   * @param sessionId - Session ID to update
   * @param status - New status to set
   * @param options - Additional options for the status update
   * @returns Promise resolving to the updated session or null if not found
   */
  public async updateSessionStatus(
    sessionId: string, 
    status: TaskSessionStatus,
    options: UpdateStatusOptions = {}
  ): Promise<TaskSession | null> {
    try {
      const currentSession = await this.getSession(sessionId);
      if (!currentSession) {
        throw new TaskSessionNotFoundError(sessionId);
      }

      // Validate status transition
      this.validateStatusTransition(currentSession.status, status);

      const updateData: Partial<TaskSession> = { status };

      // Handle completion states
      if (status === 'completed') {
        updateData.completedAt = new Date();
        if (options.finalOutput !== undefined) {
          updateData.additionalData = {
            ...currentSession.additionalData,
            finalOutput: options.finalOutput
          };
        }
        if (options.clearGraphSnapshot !== false) {
          updateData.graphExecutionContextSnapshot = undefined;
          updateData.currentGraphExecutionContextId = undefined;
        }
      }

      // Handle failed states
      if (status === 'failed') {
        updateData.completedAt = new Date();
        if (options.errorDetails !== undefined) {
          updateData.additionalData = {
            ...currentSession.additionalData,
            lastError: options.errorDetails
          };
        }
        if (options.clearGraphSnapshot !== false) {
          updateData.graphExecutionContextSnapshot = undefined;
          updateData.currentGraphExecutionContextId = undefined;
        }
      }

      // Handle cancelled states
      if (status === 'cancelled') {
        updateData.completedAt = new Date();
        if (options.clearGraphSnapshot !== false) {
          updateData.graphExecutionContextSnapshot = undefined;
          updateData.currentGraphExecutionContextId = undefined;
        }
      }

      const updatedSession = await this.taskSessionRepo.updateSession(sessionId, updateData);
      
      console.log(`TaskSessionManager: Updated session ${sessionId} status to '${status}'`);
      return updatedSession;
    } catch (error) {
      if (error instanceof TaskSessionNotFoundError || error instanceof InvalidStatusTransitionError) {
        throw error;
      }
      console.error(`TaskSessionManager: Failed to update session status for ${sessionId}:`, error);
      throw new Error(`Failed to update task session status: ${error}`);
    }
  }

  // === LLM Context Summarization Management ===

  /**
   * Updates the LLM context summary for a task session
   * 
   * @param sessionId - Session ID to update
   * @param newSummary - New summary text
   * @returns Promise resolving to the updated session or null if not found
   */
  public async updateLlmContextSummary(sessionId: string, newSummary: string): Promise<TaskSession | null> {
    try {
      const updatedSession = await this.taskSessionRepo.updateSession(sessionId, { 
        llmContextSummary: newSummary 
      });
      
      if (updatedSession) {
        console.log(`TaskSessionManager: Updated LLM context summary for session ${sessionId}`);
      }
      
      return updatedSession;
    } catch (error) {
      console.error(`TaskSessionManager: Failed to update LLM context summary for ${sessionId}:`, error);
      throw new Error(`Failed to update LLM context summary: ${error}`);
    }
  }

  /**
   * Retrieves the current LLM context summary for a task session
   * 
   * @param sessionId - Session ID to get summary for
   * @returns Promise resolving to the summary string or undefined if not set/found
   */
  public async getLlmContextSummary(sessionId: string): Promise<string | undefined> {
    try {
      const session = await this.getSession(sessionId);
      // Convert null to undefined to match return type
      return session?.llmContextSummary ?? undefined;
    } catch (error) {
      console.error(`TaskSessionManager: Failed to get LLM context summary for ${sessionId}:`, error);
      throw new Error(`Failed to get LLM context summary: ${error}`);
    }
  }

  /**
   * Ensures that the context for a task session is properly summarized
   * 
   * This is the key method for proactive context management. It coordinates
   * with LLMService to generate summaries when needed using a simple prompt approach.
   * 
   * @param context - Summarization request context from PromptBuilderService
   * @param options - Additional options for context management
   * @returns Promise resolving to the context summarization result
   */
  public async ensureContextIsSummarized(
    context: SummarizationRequestContext,
    options: ContextManagementOptions = {}
  ): Promise<ContextSummarizationResult> {
    try {
      const { 
        taskSessionId, 
        messagesToSummarize, 
        currentLlmContextSummary, 
        targetModelContextWindow,
        summarizerAgentSlug,
        summarizerTaskCategory 
      } = context;

      // Get the current task session
      const currentSession = await this.getSession(taskSessionId);
      if (!currentSession) {
        throw new TaskSessionNotFoundError(taskSessionId);
      }

      // Use provided summarizer configuration or defaults
      const effectiveSummarizerSlug = options.summarizerAgentSlug || 
                                      summarizerAgentSlug || 
                                      this.config.defaultSummarizerAgentSlug;
      const effectiveTaskCategory = options.summarizerTaskCategory || 
                                    summarizerTaskCategory || 
                                    this.config.defaultSummarizerTaskCategory;

      // Get summarizer agent configuration
      const summarizerAgentConfig = getAgentConfig(effectiveSummarizerSlug);
      if (!summarizerAgentConfig) {
        throw new SummarizationError(
          `Summarizer agent not found: ${effectiveSummarizerSlug}`,
          taskSessionId
        );
      }

      // Determine if summarization is needed
      const shouldSummarize = options.forceSummarization || 
                              this.shouldTriggerSummarization(
                                messagesToSummarize, 
                                targetModelContextWindow,
                                options.maxTokensBeforeSummarization
                              );

      if (!shouldSummarize) {
        console.log(`TaskSessionManager: Summarization not needed for session ${taskSessionId}`);
        return {
          taskSession: currentSession,
          summaryUpdated: false
        };
      }

      // Perform summarization
      const summarizationResult = await this.performSummarization(
        taskSessionId,
        messagesToSummarize,
        currentLlmContextSummary,
        effectiveTaskCategory
      );

      if (summarizationResult.success) {
        // Update the task session with the new summary
        const updatedSession = await this.updateLlmContextSummary(
          taskSessionId, 
          summarizationResult.newSummary
        );

        console.log(`TaskSessionManager: Successfully updated context summary for session ${taskSessionId}`);
        
        return {
          taskSession: updatedSession,
          summaryUpdated: true,
          newSummary: summarizationResult.newSummary,
          summarizationDetails: summarizationResult
        };
      } else {
        // Summarization failed, return current session state
        console.error(`TaskSessionManager: Summarization failed for session ${taskSessionId}:`, summarizationResult.error);
        
        return {
          taskSession: currentSession,
          summaryUpdated: false,
          summarizationDetails: summarizationResult
        };
      }
    } catch (error) {
      if (error instanceof TaskSessionNotFoundError || error instanceof SummarizationError) {
        throw error;
      }
      console.error(`TaskSessionManager: Failed to ensure context is summarized for ${context.taskSessionId}:`, error);
      throw new SummarizationError(
        `Failed to ensure context is summarized: ${error}`,
        context.taskSessionId,
        error
      );
    }
  }

  // === Graph Execution Context Snapshot Management ===

  /**
   * Saves a graph execution context snapshot to a task session
   * 
   * @param sessionId - Session ID to save snapshot for
   * @param snapshot - Graph execution context snapshot to save
   * @returns Promise resolving to the updated session or null if not found
   */
  public async saveGraphSnapshot(
    sessionId: string, 
    snapshot: GraphExecutionContextSnapshot
  ): Promise<TaskSession | null> {
    try {
      const updateData = {
        graphExecutionContextSnapshot: snapshot,
        currentGraphExecutionContextId: snapshot.currentGraphRunId
      };

      const updatedSession = await this.taskSessionRepo.updateSession(sessionId, updateData);
      
      if (updatedSession) {
        console.log(`TaskSessionManager: Saved graph snapshot for session ${sessionId} (graph run: ${snapshot.currentGraphRunId})`);
      }
      
      return updatedSession;
    } catch (error) {
      console.error(`TaskSessionManager: Failed to save graph snapshot for ${sessionId}:`, error);
      throw new GraphSnapshotError(
        `Failed to save graph snapshot: ${error}`,
        sessionId,
        error
      );
    }
  }

  /**
   * Retrieves the graph execution context snapshot for a task session
   * 
   * @param sessionId - Session ID to get snapshot for
   * @returns Promise resolving to the snapshot or undefined if not set/found
   */
  public async getGraphSnapshot(sessionId: string): Promise<GraphExecutionContextSnapshot | undefined> {
    try {
      const session = await this.getSession(sessionId);
      return session?.graphExecutionContextSnapshot;
    } catch (error) {
      console.error(`TaskSessionManager: Failed to get graph snapshot for ${sessionId}:`, error);
      throw new GraphSnapshotError(
        `Failed to get graph snapshot: ${error}`,
        sessionId,
        error
      );
    }
  }

  /**
   * Clears the graph execution context snapshot for a task session
   * 
   * @param sessionId - Session ID to clear snapshot for
   * @returns Promise resolving to the updated session or null if not found
   */
  public async clearGraphSnapshot(sessionId: string): Promise<TaskSession | null> {
    try {
      const updateData = {
        graphExecutionContextSnapshot: undefined,
        currentGraphExecutionContextId: undefined
      };

      const updatedSession = await this.taskSessionRepo.updateSession(sessionId, updateData);
      
      if (updatedSession) {
        console.log(`TaskSessionManager: Cleared graph snapshot for session ${sessionId}`);
      }
      
      return updatedSession;
    } catch (error) {
      console.error(`TaskSessionManager: Failed to clear graph snapshot for ${sessionId}:`, error);
      throw new GraphSnapshotError(
        `Failed to clear graph snapshot: ${error}`,
        sessionId,
        error
      );
    }
  }

  // === Convenience Methods for Final States ===

  /**
   * Marks a task session as completed
   * 
   * @param sessionId - Session ID to mark as completed
   * @param finalOutput - Optional final output data
   * @returns Promise resolving to the updated session or null if not found
   */
  public async markAsCompleted(sessionId: string, finalOutput?: any): Promise<TaskSession | null> {
    return this.updateSessionStatus(sessionId, 'completed', { finalOutput, clearGraphSnapshot: true });
  }

  /**
   * Marks a task session as failed
   * 
   * @param sessionId - Session ID to mark as failed
   * @param errorDetails - Error details for the failure
   * @returns Promise resolving to the updated session or null if not found
   */
  public async markAsFailed(sessionId: string, errorDetails: any): Promise<TaskSession | null> {
    return this.updateSessionStatus(sessionId, 'failed', { errorDetails, clearGraphSnapshot: true });
  }

  /**
   * Marks a task session as cancelled/aborted
   * 
   * @param sessionId - Session ID to mark as cancelled
   * @returns Promise resolving to the updated session or null if not found
   */
  public async markAsAborted(sessionId: string): Promise<TaskSession | null> {
    return this.updateSessionStatus(sessionId, 'cancelled', { clearGraphSnapshot: true });
  }

  // === Utility Methods ===

  /**
   * Retrieves the most recent task session for a specific chat
   * 
   * @param chatId - Chat ID to find the latest session for
   * @returns Promise resolving to the most recent session or null if none found
   */
  public async getLatestSessionForChat(chatId: string): Promise<TaskSession | null> {
    try {
      return await this.taskSessionRepo.getLatestSessionForChat(chatId);
    } catch (error) {
      console.error(`TaskSessionManager: Failed to get latest session for chat ${chatId}:`, error);
      throw new Error(`Failed to get latest session for chat: ${error}`);
    }
  }

  /**
   * Retrieves all active (non-completed/failed/cancelled) sessions
   * 
   * @param chatId - Optional chat ID to filter by
   * @returns Promise resolving to array of active sessions
   */
  public async getActiveSessions(chatId?: string): Promise<TaskSession[]> {
    try {
      return await this.taskSessionRepo.getActiveSessions(chatId);
    } catch (error) {
      console.error(`TaskSessionManager: Failed to get active sessions:`, error);
      throw new Error(`Failed to get active sessions: ${error}`);
    }
  }

  // === Private Helper Methods ===

  /**
   * Validates that a status transition is valid
   * 
   * @private
   * @param currentStatus - Current session status
   * @param targetStatus - Target status to transition to
   * @throws {InvalidStatusTransitionError} If the transition is invalid
   */
  private validateStatusTransition(currentStatus: TaskSessionStatus, targetStatus: TaskSessionStatus): void {
    // Define valid transitions
    const validTransitions: Record<TaskSessionStatus, TaskSessionStatus[]> = {
      'initializing': ['awaiting_user_input', 'processing', 'failed', 'cancelled'],
      'awaiting_user_input': ['processing', 'failed', 'cancelled'],
      'processing': ['paused', 'completed', 'failed', 'cancelled', 'awaiting_user_input'],
      'paused': ['processing', 'failed', 'cancelled'],
      'completed': [], // Terminal state
      'failed': [], // Terminal state
      'cancelled': [] // Terminal state
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    
    if (!allowedTransitions.includes(targetStatus)) {
      throw new InvalidStatusTransitionError('unknown', currentStatus, targetStatus);
    }
  }

  /**
   * Determines if summarization should be triggered based on context size
   * 
   * @private
   * @param messages - Messages to check
   * @param targetContextWindow - Target model context window
   * @param maxTokensOverride - Optional override for max tokens threshold
   * @returns Whether summarization should be triggered
   */
  private shouldTriggerSummarization(
    messages: ModelMessage[], 
    targetContextWindow: number,
    maxTokensOverride?: number
  ): boolean {
    // Simple heuristic: if we don't have a way to count tokens exactly,
    // use a rough estimate based on character count
    const estimatedTokens = messages.reduce((total, msg) => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      return total + Math.ceil(content.length / 4); // Rough token estimation
    }, 0);

    const threshold = maxTokensOverride || Math.floor(targetContextWindow * 0.7); // 70% threshold
    
    return estimatedTokens > threshold;
  }

  /**
   * Performs the actual summarization using a direct LLM approach
   * 
   * @private
   * @param sessionId - Session ID for logging/error context
   * @param messagesToSummarize - Messages to summarize
   * @param currentSummary - Existing summary to build upon
   * @param taskCategory - LLM task category for the summarization
   * @returns Promise resolving to summarization result
   */
  private async performSummarization(
    sessionId: string,
    messagesToSummarize: ModelMessage[],
    currentSummary: string | undefined,
    taskCategory: LLMTaskCategory
  ): Promise<SummarizationResult> {
    try {
      // Load system prompt from template registry
      const systemPrompt = this.templateRegistry.loadCommonTemplate('N8N_CONVERSATION_SUMMARIZER_SYSTEM');

      // Build user prompt using template registry
      const userPrompt = this.buildSummarizationUserPrompt(messagesToSummarize, currentSummary);

      const messages: ModelMessage[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ];

      // Invoke LLM directly for summarization
      const llmResult = await this.llmService.invokeModel(
        taskCategory,
        messages,
        { responseFormat: { type: 'text' } }
      );

      if (llmResult.status === 'success' && llmResult.text) {
        return {
          newSummary: llmResult.text,
          tokensUsed: llmResult.details.tokenUsage.totalTokens,
          cost: llmResult.details.cost?.totalCost,
          success: true
        };
      } else {
        return {
          newSummary: '',
          success: false,
          error: {
            message: llmResult.error?.message || 'Summarization failed',
            type: llmResult.error?.type || 'SummarizationError',
            originalError: llmResult.error?.originalError
          }
        };
      }
    } catch (error) {
      console.error(`TaskSessionManager: Summarization failed for session ${sessionId}:`, error);
      return {
        newSummary: '',
        success: false,
        error: {
          message: `Summarization failed: ${error}`,
          type: 'SummarizationError',
          originalError: error
        }
      };
    }
  }

  /**
   * Builds the user prompt for summarization
   * 
   * @private
   * @param messages - Messages to include in summarization
   * @param currentSummary - Existing summary to build upon
   * @returns Formatted user prompt for summarization
   */
  private buildSummarizationUserPrompt(messages: ModelMessage[], currentSummary?: string): string {
    // Prepare template data
    const previousSummarySection = currentSummary 
      ? `PREVIOUS SUMMARY:\n${currentSummary}\n\n` 
      : '';

    const conversationHistory = messages.map((msg, index) => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content, null, 2);
      return `[${msg.role.toUpperCase()}]: ${content}`;
    }).join('\n\n');

    // Load and fill template from registry
    const userTemplate = this.templateRegistry.loadCommonTemplate('N8N_CONVERSATION_SUMMARIZER_USER');
    
    return this.templateRegistry.fillTemplate(userTemplate, {
      previousSummarySection,
      conversationHistory
    });
  }
} 