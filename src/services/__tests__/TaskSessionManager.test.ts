/// <reference types="vitest" />
/**
 * TaskSessionManager Tests
 * 
 * Comprehensive test suite for TaskSessionManager functionality including:
 * - Core session management (create, get, update status)
 * - LLM context summarization coordination
 * - Graph execution context snapshot persistence
 * - Error handling and edge cases
 * - Status transition validation
 */

import { describe, it, expect, beforeEach, vi, type Mocked } from 'vitest';
import { TaskSessionManager } from '../TaskSessionManager';
import { TaskSessionRepository } from '../../persistence/repositories/TaskSessionRepository';
import { LLMService } from '../../engine/llm/LLMService';
// TODO: Remove when replacing with AgentPromptBuilder
// import { TemplateRegistry } from '../../prompts/TemplateRegistry';
import { getAgentConfig } from '../../engine/agents/agentRegistry';
import { TaskSession, TaskSessionStatus, GraphExecutionContextSnapshot } from '../../types/tasks.types';
import { LLMTaskCategory } from '../../engine/graph/types/graph.types';
import { ModelMessage } from 'ai';
import {
  CreateTaskSessionData,
  SummarizationRequestContext,
  TaskSessionNotFoundError,
  SummarizationError,
  GraphSnapshotError,
  InvalidStatusTransitionError
} from '../types/taskSessionManager.types';

// Mock dependencies
vi.mock('../../persistence/repositories/TaskSessionRepository');
vi.mock('../../engine/services/LLMService');
vi.mock('../../prompts/TemplateRegistry');
vi.mock('../../engine/agents/agentRegistry');

describe.skip('TaskSessionManager (DEPRECATED - needs TemplateRegistry replacement)', () => {
  let taskSessionManager: TaskSessionManager;
  let mockTaskSessionRepo: Mocked<TaskSessionRepository>;
  let mockLLMService: Mocked<LLMService>;
  let mockTemplateRegistry: Mocked<TemplateRegistry>;
  
  // Test data
  const mockSessionId = 'test-session-123';
  const mockChatId = 'test-chat-456';
  const mockUserInput = 'Create a workflow for processing emails';
  
  const mockTaskSession: TaskSession = {
    id: mockSessionId,
    chatId: mockChatId,
    originalUserInput: mockUserInput,
    title: 'Test Task Session',
    status: 'initializing',
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      totalStepsExecuted: 0,
      totalLLMTokensUsed: 0,
      estimatedCostUSD: 0,
      executionTimeMs: 0
    },
    priority: 'normal',
    tags: [],
    executionHistory: [],
    additionalData: {}
  };

  const mockGraphSnapshot: GraphExecutionContextSnapshot = {
    graphDefinitionId: 'test-graph',
    graphDefinitionVersion: '1.0',
    taskSessionId: mockSessionId,
    currentGraphRunId: 'test-run-123',
    status: 'RUNNING',
    initialInputs: {},
    nodeOutputs: [],
    currentNodeIdBeingProcessed: null,
    processingQueue: [],
    totalStepsExecuted: 5,
    tokenUsage: {
      promptTokens: 100,
      completionTokens: 50,
      totalTokens: 150
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock repository
    mockTaskSessionRepo = {
      createSession: vi.fn(),
      getSessionById: vi.fn(),
      updateSession: vi.fn(),
      saveSession: vi.fn(),
      deleteSession: vi.fn(),
      findSessions: vi.fn(),
      countSessions: vi.fn(),
      getLatestSessionForChat: vi.fn(),
      getActiveSessions: vi.fn(),
    } as any;

    // Setup mock LLM service
    mockLLMService = {
      invokeModel: vi.fn(),
    } as any;

    // Setup mock template registry
    mockTemplateRegistry = {
      loadCommonTemplate: vi.fn(),
      fillTemplate: vi.fn(),
      loadAgentTemplate: vi.fn(),
      registerAgentTemplates: vi.fn(),
      hasAgentTemplates: vi.fn(),
      getRegisteredAgents: vi.fn(),
      validateTemplate: vi.fn(),
      getTemplatePlaceholders: vi.fn(),
      clearCache: vi.fn(),
      clearAgentCache: vi.fn(),
    } as any;

    // Setup template registry mocks
    mockTemplateRegistry.loadCommonTemplate.mockImplementation((templateKey) => {
      if (templateKey === 'N8N_CONVERSATION_SUMMARIZER_SYSTEM') {
        return 'System prompt for summarization';
      }
      if (templateKey === 'N8N_CONVERSATION_SUMMARIZER_USER') {
        return '{{previousSummarySection}}\nCONVERSATION HISTORY:\n{{conversationHistory}}\nSummarize this conversation.';
      }
      return 'Mock template';
    });

    mockTemplateRegistry.fillTemplate.mockImplementation((template, data) => {
      let result = template;
      Object.entries(data).forEach(([key, value]) => {
        result = result.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value || '');
      });
      return result;
    });

    // Setup agent registry mock
    vi.mocked(getAgentConfig).mockReturnValue({
      slug: 'n8n-summarizer-agent',
      description: 'Test summarizer agent',
      constructor: vi.fn() as any,
      defaultLlmTaskCategory: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
    });

    // Create TaskSessionManager instance
    taskSessionManager = new TaskSessionManager(
      mockTaskSessionRepo,
      mockLLMService,
      mockTemplateRegistry
    );
  });

  describe('Core Session Management', () => {
    describe('createSession', () => {
      it('should create a new session with provided data', async () => {
        const sessionData: CreateTaskSessionData = {
          chatId: mockChatId,
          originalUserInput: mockUserInput,
          title: 'Custom Title',
          initialStatus: 'processing',
          priority: 'high',
          tags: ['test', 'workflow']
        };

        mockTaskSessionRepo.createSession.mockResolvedValue(mockTaskSession);

        const result = await taskSessionManager.createSession(sessionData);

        expect(mockTaskSessionRepo.createSession).toHaveBeenCalledWith({
          chatId: mockChatId,
          originalUserInput: mockUserInput,
          title: 'Custom Title',
          description: undefined,
          status: 'processing',
          priority: 'high',
          tags: ['test', 'workflow'],
          additionalData: {}
        });
        expect(result).toBe(mockTaskSession);
      });

      it('should create a session with defaults when optional fields not provided', async () => {
        const sessionData: CreateTaskSessionData = {
          chatId: mockChatId,
          originalUserInput: mockUserInput
        };

        mockTaskSessionRepo.createSession.mockResolvedValue(mockTaskSession);

        await taskSessionManager.createSession(sessionData);

        expect(mockTaskSessionRepo.createSession).toHaveBeenCalledWith({
          chatId: mockChatId,
          originalUserInput: mockUserInput,
          title: undefined,
          description: undefined,
          status: 'initializing',
          priority: 'normal',
          tags: [],
          additionalData: {}
        });
      });

      it('should include initialGraphDefinitionId in additionalData when provided', async () => {
        const sessionData: CreateTaskSessionData = {
          chatId: mockChatId,
          originalUserInput: mockUserInput,
          initialGraphDefinitionId: 'graph-123'
        };

        mockTaskSessionRepo.createSession.mockResolvedValue(mockTaskSession);

        await taskSessionManager.createSession(sessionData);

        expect(mockTaskSessionRepo.createSession).toHaveBeenCalledWith(
          expect.objectContaining({
            additionalData: { initialGraphDefinitionId: 'graph-123' }
          })
        );
      });

      it('should throw error when repository fails', async () => {
        const sessionData: CreateTaskSessionData = {
          chatId: mockChatId,
          originalUserInput: mockUserInput
        };

        mockTaskSessionRepo.createSession.mockRejectedValue(new Error('Database error'));

        await expect(taskSessionManager.createSession(sessionData))
          .rejects.toThrow('Failed to create task session: Error: Database error');
      });
    });

    describe('getSession', () => {
      it('should retrieve session by ID', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);

        const result = await taskSessionManager.getSession(mockSessionId);

        expect(mockTaskSessionRepo.getSessionById).toHaveBeenCalledWith(mockSessionId);
        expect(result).toBe(mockTaskSession);
      });

      it('should return null when session not found', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(null);

        const result = await taskSessionManager.getSession('non-existent');

        expect(result).toBeNull();
      });

      it('should throw error when repository fails', async () => {
        mockTaskSessionRepo.getSessionById.mockRejectedValue(new Error('Database error'));

        await expect(taskSessionManager.getSession(mockSessionId))
          .rejects.toThrow('Failed to retrieve task session: Error: Database error');
      });
    });

    describe('updateSessionStatus', () => {
      beforeEach(() => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);
        mockTaskSessionRepo.updateSession.mockResolvedValue({
          ...mockTaskSession,
          status: 'processing'
        });
      });

      it('should update session status successfully', async () => {
        const result = await taskSessionManager.updateSessionStatus(mockSessionId, 'processing');

        expect(mockTaskSessionRepo.getSessionById).toHaveBeenCalledWith(mockSessionId);
        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'processing'
        });
        expect(result?.status).toBe('processing');
      });

      it('should handle completion status with final output', async () => {
        // Set up session in processing state to allow transition to completed
        const processingSession = { ...mockTaskSession, status: 'processing' as TaskSessionStatus };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(processingSession);
        
        const finalOutput = { workflowId: 'wf-123' };
        
        await taskSessionManager.updateSessionStatus(mockSessionId, 'completed', {
          finalOutput,
          clearGraphSnapshot: true
        });

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'completed',
          completedAt: expect.any(Date),
          additionalData: {
            ...mockTaskSession.additionalData,
            finalOutput
          },
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
      });

      it('should handle failed status with error details', async () => {
        const errorDetails = { message: 'Processing failed', code: 'PROC_ERR' };
        
        await taskSessionManager.updateSessionStatus(mockSessionId, 'failed', {
          errorDetails
        });

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'failed',
          completedAt: expect.any(Date),
          additionalData: {
            ...mockTaskSession.additionalData,
            lastError: errorDetails
          },
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
      });

      it('should handle cancelled status', async () => {
        await taskSessionManager.updateSessionStatus(mockSessionId, 'cancelled');

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'cancelled',
          completedAt: expect.any(Date),
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
      });

      it('should throw TaskSessionNotFoundError when session does not exist', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(null);

        await expect(taskSessionManager.updateSessionStatus('non-existent', 'processing'))
          .rejects.toThrow(TaskSessionNotFoundError);
      });

      it('should validate status transitions', async () => {
        const completedSession = { ...mockTaskSession, status: 'completed' as TaskSessionStatus };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(completedSession);

        await expect(taskSessionManager.updateSessionStatus(mockSessionId, 'processing'))
          .rejects.toThrow(InvalidStatusTransitionError);
      });

      it('should allow valid status transitions', async () => {
        const validTransitions: [TaskSessionStatus, TaskSessionStatus][] = [
          ['initializing', 'processing'],
          ['processing', 'paused'],
          ['paused', 'processing'],
          ['processing', 'completed'],
          ['processing', 'failed'],
          ['awaiting_user_input', 'processing']
        ];

        for (const [from, to] of validTransitions) {
          const sessionWithStatus = { ...mockTaskSession, status: from };
          mockTaskSessionRepo.getSessionById.mockResolvedValue(sessionWithStatus);
          mockTaskSessionRepo.updateSession.mockResolvedValue({ ...sessionWithStatus, status: to });

          const result = await taskSessionManager.updateSessionStatus(mockSessionId, to);
          expect(result?.status).toBe(to);
        }
      });
    });
  });

  describe('LLM Context Summarization Management', () => {
    describe('updateLlmContextSummary', () => {
      it('should update LLM context summary', async () => {
        const newSummary = 'Updated conversation summary';
        const updatedSession = { ...mockTaskSession, llmContextSummary: newSummary };
        
        mockTaskSessionRepo.updateSession.mockResolvedValue(updatedSession);

        const result = await taskSessionManager.updateLlmContextSummary(mockSessionId, newSummary);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          llmContextSummary: newSummary
        });
        expect(result?.llmContextSummary).toBe(newSummary);
      });

      it('should handle repository failure', async () => {
        mockTaskSessionRepo.updateSession.mockRejectedValue(new Error('Update failed'));

        await expect(taskSessionManager.updateLlmContextSummary(mockSessionId, 'summary'))
          .rejects.toThrow('Failed to update LLM context summary: Error: Update failed');
      });
    });

    describe('getLlmContextSummary', () => {
      it('should retrieve LLM context summary', async () => {
        const sessionWithSummary = { ...mockTaskSession, llmContextSummary: 'Test summary' };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(sessionWithSummary);

        const result = await taskSessionManager.getLlmContextSummary(mockSessionId);

        expect(result).toBe('Test summary');
      });

      it('should return undefined when no summary exists', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);

        const result = await taskSessionManager.getLlmContextSummary(mockSessionId);

        expect(result).toBeUndefined();
      });

      it('should return undefined when session not found', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(null);

        const result = await taskSessionManager.getLlmContextSummary(mockSessionId);

        expect(result).toBeUndefined();
      });
    });

    describe('ensureContextIsSummarized', () => {
      const mockMessages: ModelMessage[] = [
        { role: 'user', content: 'Create a workflow' },
        { role: 'assistant', content: 'I can help you create a workflow' }
      ];

      const mockSummarizationContext: SummarizationRequestContext = {
        taskSessionId: mockSessionId,
        messagesToSummarize: mockMessages,
        currentLlmContextSummary: 'Previous summary',
        targetModelContextWindow: 8000,
        summarizerAgentSlug: 'n8n-summarizer-agent',
        summarizerTaskCategory: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION
      };

      beforeEach(() => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);
      });

      it('should not summarize when context is small enough', async () => {
        const smallMessages: ModelMessage[] = [
          { role: 'user', content: 'Hi' }
        ];

        const context = {
          ...mockSummarizationContext,
          messagesToSummarize: smallMessages
        };

        const result = await taskSessionManager.ensureContextIsSummarized(context);

        expect(result.summaryUpdated).toBe(false);
        expect(result.taskSession).toBe(mockTaskSession);
        expect(mockLLMService.invokeModel).not.toHaveBeenCalled();
      });

      it('should force summarization when requested', async () => {
        const newSummary = 'AI generated summary';
        mockLLMService.invokeModel.mockResolvedValue({
          status: 'success',
          text: newSummary,
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 },
            cost: { inputTokenCost: 0.001, outputTokenCost: 0.002, totalCost: 0.003 }
          }
        });

        const updatedSession = { ...mockTaskSession, llmContextSummary: newSummary };
        mockTaskSessionRepo.updateSession.mockResolvedValue(updatedSession);

        const result = await taskSessionManager.ensureContextIsSummarized(
          mockSummarizationContext,
          { forceSummarization: true }
        );

        expect(mockLLMService.invokeModel).toHaveBeenCalled();
        expect(result.summaryUpdated).toBe(true);
        expect(result.newSummary).toBe(newSummary);
        expect(result.taskSession?.llmContextSummary).toBe(newSummary);
      });

      it('should trigger summarization when context is large', async () => {
        const largeMessages: ModelMessage[] = Array(100).fill(null).map((_, i) => ({
          role: 'user' as const,
          content: `This is a very long message that will trigger summarization ${i.toString().repeat(100)}`
        }));

        const context = {
          ...mockSummarizationContext,
          messagesToSummarize: largeMessages
        };

        const newSummary = 'AI generated summary for large context';
        mockLLMService.invokeModel.mockResolvedValue({
          status: 'success',
          text: newSummary,
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 200, completionTokens: 75, totalTokens: 275 }
          }
        });

        const updatedSession = { ...mockTaskSession, llmContextSummary: newSummary };
        mockTaskSessionRepo.updateSession.mockResolvedValue(updatedSession);

        const result = await taskSessionManager.ensureContextIsSummarized(context);

        expect(mockLLMService.invokeModel).toHaveBeenCalledWith(
          LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
          expect.arrayContaining([
            expect.objectContaining({ role: 'system' }),
            expect.objectContaining({ role: 'user' })
          ]),
          { responseFormat: { type: 'text' } }
        );
        expect(result.summaryUpdated).toBe(true);
        expect(result.newSummary).toBe(newSummary);
      });

      it('should handle summarization failure gracefully', async () => {
        mockLLMService.invokeModel.mockResolvedValue({
          status: 'failure',
          error: { message: 'LLM call failed', type: 'APIError' },
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
          }
        });

        const result = await taskSessionManager.ensureContextIsSummarized(
          mockSummarizationContext,
          { forceSummarization: true }
        );

        expect(result.summaryUpdated).toBe(false);
        expect(result.summarizationDetails?.success).toBe(false);
        expect(result.summarizationDetails?.error?.message).toBe('LLM call failed');
      });

      it('should throw SummarizationError when agent not found', async () => {
        vi.mocked(getAgentConfig).mockReturnValue(undefined);

        await expect(taskSessionManager.ensureContextIsSummarized(mockSummarizationContext))
          .rejects.toThrow(SummarizationError);
      });

      it('should throw TaskSessionNotFoundError when session not found', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(null);

        await expect(taskSessionManager.ensureContextIsSummarized(mockSummarizationContext))
          .rejects.toThrow(TaskSessionNotFoundError);
      });

      it('should use default configuration when not provided', async () => {
        const contextWithoutDefaults = {
          taskSessionId: mockSessionId,
          messagesToSummarize: mockMessages,
          targetModelContextWindow: 8000,
          summarizerAgentSlug: '',
          summarizerTaskCategory: '' as any
        };

        mockLLMService.invokeModel.mockResolvedValue({
          status: 'success',
          text: 'Summary',
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 }
          }
        });

        mockTaskSessionRepo.updateSession.mockResolvedValue(mockTaskSession);

        await taskSessionManager.ensureContextIsSummarized(
          contextWithoutDefaults,
          { forceSummarization: true }
        );

        expect(vi.mocked(getAgentConfig)).toHaveBeenCalledWith('n8n-summarizer-agent');
        expect(mockLLMService.invokeModel).toHaveBeenCalledWith(
          LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION,
          expect.any(Array),
          expect.any(Object)
        );
      });

      it('should use template registry for summarization prompts', async () => {
        mockLLMService.invokeModel.mockResolvedValue({
          status: 'success',
          text: 'Generated summary',
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 }
          }
        });

        mockTaskSessionRepo.updateSession.mockResolvedValue(mockTaskSession);

        await taskSessionManager.ensureContextIsSummarized(
          mockSummarizationContext,
          { forceSummarization: true }
        );

        // Verify template registry methods were called
        expect(mockTemplateRegistry.loadCommonTemplate).toHaveBeenCalledWith('N8N_CONVERSATION_SUMMARIZER_SYSTEM');
        expect(mockTemplateRegistry.loadCommonTemplate).toHaveBeenCalledWith('N8N_CONVERSATION_SUMMARIZER_USER');
        expect(mockTemplateRegistry.fillTemplate).toHaveBeenCalledWith(
          expect.stringContaining('CONVERSATION HISTORY'),
          expect.objectContaining({
            previousSummarySection: expect.stringContaining('Previous summary'),
            conversationHistory: expect.any(String)
          })
        );
      });
    });
  });

  describe('Graph Execution Context Snapshot Management', () => {
    describe('saveGraphSnapshot', () => {
      it('should save graph snapshot successfully', async () => {
        const updatedSession = { 
          ...mockTaskSession, 
          graphExecutionContextSnapshot: mockGraphSnapshot,
          currentGraphExecutionContextId: mockGraphSnapshot.currentGraphRunId
        };
        
        mockTaskSessionRepo.updateSession.mockResolvedValue(updatedSession);

        const result = await taskSessionManager.saveGraphSnapshot(mockSessionId, mockGraphSnapshot);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          graphExecutionContextSnapshot: mockGraphSnapshot,
          currentGraphExecutionContextId: mockGraphSnapshot.currentGraphRunId
        });
        expect(result).toBe(updatedSession);
      });

      it('should throw GraphSnapshotError when repository fails', async () => {
        mockTaskSessionRepo.updateSession.mockRejectedValue(new Error('Update failed'));

        await expect(taskSessionManager.saveGraphSnapshot(mockSessionId, mockGraphSnapshot))
          .rejects.toThrow(GraphSnapshotError);
      });
    });

    describe('getGraphSnapshot', () => {
      it('should retrieve graph snapshot', async () => {
        const sessionWithSnapshot = { 
          ...mockTaskSession, 
          graphExecutionContextSnapshot: mockGraphSnapshot 
        };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(sessionWithSnapshot);

        const result = await taskSessionManager.getGraphSnapshot(mockSessionId);

        expect(result).toBe(mockGraphSnapshot);
      });

      it('should return undefined when no snapshot exists', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);

        const result = await taskSessionManager.getGraphSnapshot(mockSessionId);

        expect(result).toBeUndefined();
      });

      it('should throw GraphSnapshotError when repository fails', async () => {
        mockTaskSessionRepo.getSessionById.mockRejectedValue(new Error('Get failed'));

        await expect(taskSessionManager.getGraphSnapshot(mockSessionId))
          .rejects.toThrow(GraphSnapshotError);
      });
    });

    describe('clearGraphSnapshot', () => {
      it('should clear graph snapshot successfully', async () => {
        const updatedSession = { 
          ...mockTaskSession, 
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        };
        
        mockTaskSessionRepo.updateSession.mockResolvedValue(updatedSession);

        const result = await taskSessionManager.clearGraphSnapshot(mockSessionId);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
        expect(result).toBe(updatedSession);
      });

      it('should throw GraphSnapshotError when repository fails', async () => {
        mockTaskSessionRepo.updateSession.mockRejectedValue(new Error('Clear failed'));

        await expect(taskSessionManager.clearGraphSnapshot(mockSessionId))
          .rejects.toThrow(GraphSnapshotError);
      });
    });
  });

  describe('Convenience Methods for Final States', () => {
    beforeEach(() => {
      // Set up session in processing state to allow transitions to final states
      const processingSession = { ...mockTaskSession, status: 'processing' as TaskSessionStatus };
      mockTaskSessionRepo.getSessionById.mockResolvedValue(processingSession);
    });

    describe('markAsCompleted', () => {
      it('should mark session as completed with final output', async () => {
        const finalOutput = { workflowId: 'wf-123' };
        const completedSession = { ...mockTaskSession, status: 'completed' as TaskSessionStatus };
        mockTaskSessionRepo.updateSession.mockResolvedValue(completedSession);

        const result = await taskSessionManager.markAsCompleted(mockSessionId, finalOutput);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'completed',
          completedAt: expect.any(Date),
          additionalData: {
            ...mockTaskSession.additionalData,
            finalOutput
          },
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
        expect(result?.status).toBe('completed');
      });
    });

    describe('markAsFailed', () => {
      it('should mark session as failed with error details', async () => {
        const errorDetails = { message: 'Processing failed' };
        const failedSession = { ...mockTaskSession, status: 'failed' as TaskSessionStatus };
        mockTaskSessionRepo.updateSession.mockResolvedValue(failedSession);

        const result = await taskSessionManager.markAsFailed(mockSessionId, errorDetails);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'failed',
          completedAt: expect.any(Date),
          additionalData: {
            ...mockTaskSession.additionalData,
            lastError: errorDetails
          },
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
        expect(result?.status).toBe('failed');
      });
    });

    describe('markAsAborted', () => {
      it('should mark session as cancelled', async () => {
        const cancelledSession = { ...mockTaskSession, status: 'cancelled' as TaskSessionStatus };
        mockTaskSessionRepo.updateSession.mockResolvedValue(cancelledSession);

        const result = await taskSessionManager.markAsAborted(mockSessionId);

        expect(mockTaskSessionRepo.updateSession).toHaveBeenCalledWith(mockSessionId, {
          status: 'cancelled',
          completedAt: expect.any(Date),
          graphExecutionContextSnapshot: undefined,
          currentGraphExecutionContextId: undefined
        });
        expect(result?.status).toBe('cancelled');
      });
    });
  });

  describe('Utility Methods', () => {
    describe('getLatestSessionForChat', () => {
      it('should retrieve latest session for chat', async () => {
        mockTaskSessionRepo.getLatestSessionForChat.mockResolvedValue(mockTaskSession);

        const result = await taskSessionManager.getLatestSessionForChat(mockChatId);

        expect(mockTaskSessionRepo.getLatestSessionForChat).toHaveBeenCalledWith(mockChatId);
        expect(result).toBe(mockTaskSession);
      });

      it('should handle repository failure', async () => {
        mockTaskSessionRepo.getLatestSessionForChat.mockRejectedValue(new Error('Query failed'));

        await expect(taskSessionManager.getLatestSessionForChat(mockChatId))
          .rejects.toThrow('Failed to get latest session for chat: Error: Query failed');
      });
    });

    describe('getActiveSessions', () => {
      it('should retrieve active sessions', async () => {
        const activeSessions = [mockTaskSession];
        mockTaskSessionRepo.getActiveSessions.mockResolvedValue(activeSessions);

        const result = await taskSessionManager.getActiveSessions();

        expect(mockTaskSessionRepo.getActiveSessions).toHaveBeenCalledWith(undefined);
        expect(result).toBe(activeSessions);
      });

      it('should retrieve active sessions for specific chat', async () => {
        const activeSessions = [mockTaskSession];
        mockTaskSessionRepo.getActiveSessions.mockResolvedValue(activeSessions);

        const result = await taskSessionManager.getActiveSessions(mockChatId);

        expect(mockTaskSessionRepo.getActiveSessions).toHaveBeenCalledWith(mockChatId);
        expect(result).toBe(activeSessions);
      });

      it('should handle repository failure', async () => {
        mockTaskSessionRepo.getActiveSessions.mockRejectedValue(new Error('Query failed'));

        await expect(taskSessionManager.getActiveSessions())
          .rejects.toThrow('Failed to get active sessions: Error: Query failed');
      });
    });
  });

  describe('Configuration', () => {
    it('should use custom configuration when provided', () => {
      const customConfig = {
        defaultSummarizerAgentSlug: 'custom-summarizer',
        defaultSummarizerTaskCategory: LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING,
        defaultMaxGraphIterations: 200,
        autoCleanupCompletedSessions: true
      };

      const manager = new TaskSessionManager(
        mockTaskSessionRepo,
        mockLLMService,
        mockTemplateRegistry,
        customConfig
      );

      // Test that custom config is used (this would be visible in method calls)
      expect(manager).toBeDefined();
    });

    it('should merge custom config with defaults', () => {
      const partialConfig = {
        defaultSummarizerAgentSlug: 'custom-summarizer'
      };

      const manager = new TaskSessionManager(
        mockTaskSessionRepo,
        mockLLMService,
        mockTemplateRegistry,
        partialConfig
      );

      expect(manager).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should properly chain errors with context', async () => {
      const originalError = new Error('Original database error');
      mockTaskSessionRepo.createSession.mockRejectedValue(originalError);

      const sessionData: CreateTaskSessionData = {
        chatId: mockChatId,
        originalUserInput: mockUserInput
      };

      await expect(taskSessionManager.createSession(sessionData))
        .rejects.toThrow('Failed to create task session: Error: Original database error');
    });

    it('should preserve custom error types', async () => {
      mockTaskSessionRepo.getSessionById.mockResolvedValue(null);

      await expect(taskSessionManager.updateSessionStatus('non-existent', 'processing'))
        .rejects.toThrow(TaskSessionNotFoundError);
    });
  });

  describe('Private Helper Methods (via public interface)', () => {
    describe('Status Transition Validation', () => {
      const validTransitions = [
        ['initializing', 'processing'],
        ['processing', 'paused'],
        ['processing', 'completed'],
        ['paused', 'processing'],
        ['awaiting_user_input', 'processing']
      ] as const;

      const invalidTransitions = [
        ['completed', 'processing'],
        ['failed', 'processing'],
        ['cancelled', 'processing'],
        ['completed', 'failed'],
        ['initializing', 'paused']
      ] as const;

      it.each(validTransitions)('should allow transition from %s to %s', async (from, to) => {
        const session = { ...mockTaskSession, status: from };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(session);
        mockTaskSessionRepo.updateSession.mockResolvedValue({ ...session, status: to });

        const result = await taskSessionManager.updateSessionStatus(mockSessionId, to);
        expect(result?.status).toBe(to);
      });

      it.each(invalidTransitions)('should reject transition from %s to %s', async (from, to) => {
        const session = { ...mockTaskSession, status: from };
        mockTaskSessionRepo.getSessionById.mockResolvedValue(session);

        await expect(taskSessionManager.updateSessionStatus(mockSessionId, to))
          .rejects.toThrow(InvalidStatusTransitionError);
      });
    });

    describe('Summarization Triggering Logic', () => {
      it('should not trigger summarization for small contexts', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);

        const smallContext: SummarizationRequestContext = {
          taskSessionId: mockSessionId,
          messagesToSummarize: [{ role: 'user', content: 'Hi' }],
          targetModelContextWindow: 8000,
          summarizerAgentSlug: 'n8n-summarizer-agent',
          summarizerTaskCategory: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION
        };

        const result = await taskSessionManager.ensureContextIsSummarized(smallContext);

        expect(result.summaryUpdated).toBe(false);
        expect(mockLLMService.invokeModel).not.toHaveBeenCalled();
      });

      it('should respect custom token threshold', async () => {
        mockTaskSessionRepo.getSessionById.mockResolvedValue(mockTaskSession);
        mockLLMService.invokeModel.mockResolvedValue({
          status: 'success',
          text: 'Summary',
          details: {
            modelProvider: 'test',
            modelName: 'test-model',
            tokenUsage: { promptTokens: 100, completionTokens: 50, totalTokens: 150 }
          }
        });
        mockTaskSessionRepo.updateSession.mockResolvedValue(mockTaskSession);

        const context: SummarizationRequestContext = {
          taskSessionId: mockSessionId,
          messagesToSummarize: [{ role: 'user', content: 'Medium length message'.repeat(10) }],
          targetModelContextWindow: 8000,
          summarizerAgentSlug: 'n8n-summarizer-agent',
          summarizerTaskCategory: LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION
        };

        const result = await taskSessionManager.ensureContextIsSummarized(context, {
          maxTokensBeforeSummarization: 50 // Very low threshold
        });

        expect(result.summaryUpdated).toBe(true);
        expect(mockLLMService.invokeModel).toHaveBeenCalled();
      });
    });
  });
}); 