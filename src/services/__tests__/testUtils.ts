/**
 * Test Utilities for Services Layer
 * 
 * Common utilities, mocks, and helpers for testing service components.
 * Provides reusable mock factories and test data for consistent testing.
 */

import { vi, expect, type MockedFunction } from 'vitest';
import type { UserSettingsRepository } from '../../persistence/repositories/UserSettingsRepository';
import { UserSettings, ModelProviderConfig } from '../../types/userSettings.types';
import { LLMTaskCategory } from '../../engine/graph/types/graph.types';

// Create a proper mock type for UserSettingsRepository
type MockedUserSettingsRepository = {
  [K in keyof UserSettingsRepository]: MockedFunction<UserSettingsRepository[K]>;
};

/**
 * Create a mock UserSettingsRepository with configurable responses
 */
export function createMockUserSettingsRepository(
  overrides: Partial<UserSettingsRepository> = {}
): MockedUserSettingsRepository {
  const defaults = {
    getSettings: vi.fn(),
    saveSettings: vi.fn(),
    resetSettingsToDefaults: vi.fn(),
  };
  
  return { ...defaults, ...overrides } as any;
}

/**
 * Factory for creating test UserSettings objects
 */
export function createTestUserSettings(
  overrides: Partial<UserSettings> = {}
): UserSettings {
  const defaults: UserSettings = {
    id: 'singleton_user_settings',
    version: 1,
    modelPreferences: {
      CORE_ORCHESTRATION_AND_PLANNING: {
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        temperature: 0.1,
      },
    },
    apiKeys: {
      openai: 'sk-test-openai-key',
      anthropic: undefined,
      google: undefined,
    },
    preferences: {
      enableChromeAI: true,
      enableAutoSummarization: true,
      maxContextWindowTokens: 8000,
      debugMode: false,
      autoSaveWorkflows: true,
      defaultWorkflowTags: [],
    },
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  return { ...defaults, ...overrides };
}

/**
 * Factory for creating test ModelProviderConfig objects
 */
export function createTestModelConfig(
  overrides: Partial<ModelProviderConfig> = {}
): ModelProviderConfig {
  const defaults: ModelProviderConfig = {
    provider: 'anthropic',
    modelId: 'claude-3-5-haiku-20241022',
    temperature: 0.1,
  };

  return { ...defaults, ...overrides };
}

/**
 * Helper to create test API keys configuration
 */
export function createTestApiKeys(overrides: {
  openai?: string;
  anthropic?: string;
  google?: string;
} = {}) {
  return {
    openai: overrides.openai ?? 'sk-test-openai-key',
    anthropic: overrides.anthropic ?? undefined,
    google: overrides.google ?? undefined,
  };
}

/**
 * Helper to create test user preferences
 */
export function createTestPreferences(overrides: Partial<UserSettings['preferences']> = {}) {
  return {
    enableChromeAI: true,
    enableAutoSummarization: true,
    maxContextWindowTokens: 8000,
    debugMode: false,
    autoSaveWorkflows: true,
    defaultWorkflowTags: [],
    ...overrides,
  };
}

/**
 * Collection of test error objects for consistent error testing
 */
export const TestErrors = {
  repositoryError: new Error('Repository failed'),
  saveError: new Error('Save failed'),
  resetError: new Error('Reset failed'),
  validationError: new Error('Validation failed'),
  databaseError: new Error('Database connection failed'),
  queryError: new Error('Query failed'),
} as const;

/**
 * Mock setup helper for repository responses
 */
export function setupMockRepositoryResponses(
  mockRepository: MockedUserSettingsRepository,
  settings: UserSettings = createTestUserSettings()
) {
  mockRepository.getSettings.mockResolvedValue(settings);
  mockRepository.saveSettings.mockResolvedValue(settings);
  mockRepository.resetSettingsToDefaults.mockResolvedValue(settings);
}

/**
 * Helper to create settings with no API keys configured
 */
export function createSettingsWithoutApiKeys(): UserSettings {
  return createTestUserSettings({
    apiKeys: {
      openai: undefined,
      anthropic: undefined,
      google: undefined,
    },
  });
}

/**
 * Helper to create settings with empty/whitespace API keys
 */
export function createSettingsWithEmptyApiKeys(): UserSettings {
  return createTestUserSettings({
    apiKeys: {
      openai: '',
      anthropic: '   ',
      google: undefined,
    },
  });
}

/**
 * Helper to create settings with partial model preferences
 */
export function createSettingsWithPartialModelPreferences(): UserSettings {
  return createTestUserSettings({
    modelPreferences: {
      CORE_ORCHESTRATION_AND_PLANNING: {
        provider: 'anthropic',
        modelId: '', // Empty modelId should trigger fallback
        temperature: 0.1,
      },
    },
  });
}

/**
 * Test data for different LLM task categories
 */
export const TEST_TASK_CATEGORIES = Object.values(LLMTaskCategory);

/**
 * Common test assertions helper
 */
export const testAssertions = {
  /**
   * Assert that an object has all expected user settings properties
   */
  assertUserSettingsStructure(settings: any) {
    expect(settings).toHaveProperty('id');
    expect(settings).toHaveProperty('version');
    expect(settings).toHaveProperty('modelPreferences');
    expect(settings).toHaveProperty('apiKeys');
    expect(settings).toHaveProperty('preferences');
    expect(settings).toHaveProperty('createdAt');
    expect(settings).toHaveProperty('updatedAt');
  },

  /**
   * Assert that preferences have all required fields
   */
  assertPreferencesStructure(preferences: any) {
    expect(preferences).toHaveProperty('enableChromeAI');
    expect(preferences).toHaveProperty('enableAutoSummarization');
    expect(preferences).toHaveProperty('maxContextWindowTokens');
    expect(preferences).toHaveProperty('debugMode');
    expect(preferences).toHaveProperty('autoSaveWorkflows');
    expect(preferences).toHaveProperty('defaultWorkflowTags');
  },

  /**
   * Assert that model config has required structure
   */
  assertModelConfigStructure(config: any) {
    expect(config).toHaveProperty('provider');
    expect(config).toHaveProperty('modelId');
    expect(typeof config.temperature).toBe('number');
  },
} as const;

/**
 * Mock console methods helper to suppress output during tests
 */
export function setupConsoleMocks() {
  const mockConsoleLog = vi.fn();
  const mockConsoleWarn = vi.fn();
  const mockConsoleError = vi.fn();

  vi.stubGlobal('console', {
    ...console,
    log: mockConsoleLog,
    warn: mockConsoleWarn,
    error: mockConsoleError,
  });

  return {
    mockConsoleLog,
    mockConsoleWarn,
    mockConsoleError,
  };
} 