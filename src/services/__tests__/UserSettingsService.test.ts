/**
 * UserSettingsService Unit Tests
 * 
 * Comprehensive test suite for the UserSettingsService class, covering:
 * - Constructor and dependency injection
 * - All business logic methods for settings access
 * - API key management operations
 * - Model preference handling with fallback logic
 * - User preferences management
 * - Feature flag checking
 * - Bulk operations and convenience methods
 * - Error handling and edge cases
 */

import { describe, it, expect, beforeEach, afterEach, vi, type MockedFunction } from 'vitest';
import { UserSettingsService } from '../UserSettingsService';
import { UserSettingsRepository } from '../../persistence/repositories/UserSettingsRepository';
import { 
  UserSettings, 
  ApiKeysConfig, 
  ModelProviderConfig, 
  UserPreferences 
} from '../../types/userSettings.types';
import { LLMTaskCategory } from '../../engine/graph/types/graph.types';
import {
  DEFAULT_FALLBACK_MODELS_FOR_SERVICE,
  getDefaultModelForTaskCategory
} from '../userSettings.config';

// Mock the userSettings.config module
const mockGetDefaultModelForTaskCategory = vi.hoisted(() => vi.fn());
vi.mock('../userSettings.config', async (importOriginal) => {
  const actual = await importOriginal<typeof import('../userSettings.config')>();
  return {
    ...actual,
    getDefaultModelForTaskCategory: mockGetDefaultModelForTaskCategory,
  };
});

// Create a proper mock type for UserSettingsRepository
type MockedUserSettingsRepository = {
  [K in keyof UserSettingsRepository]: MockedFunction<UserSettingsRepository[K]>;
};

describe('UserSettingsService', () => {
  let service: UserSettingsService;
  let mockRepository: MockedUserSettingsRepository;

  const mockUserSettings: UserSettings = {
    id: 'singleton_user_settings',
    version: 1,
    modelPreferences: {
      CORE_ORCHESTRATION_AND_PLANNING: {
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-********',
        temperature: 0.1,
      },
      N8N_CODE_GENERATION_COMPOSITION: {
        provider: 'openai',
        modelId: 'gpt-4',
        temperature: 0.0,
      },
    },
    apiKeys: {
      openai: 'sk-test-openai-key',
      anthropic: 'sk-test-anthropic-key',
      google: undefined,
    },
    preferences: {
      enableChromeAI: true,
      enableAutoSummarization: false,
      maxContextWindowTokens: 8000,
      debugMode: true,
      autoSaveWorkflows: false,
      defaultWorkflowTags: ['ai-generated'],
    },
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-02T00:00:00Z'),
  };

  const mockDefaultModel: ModelProviderConfig = {
    provider: 'anthropic',
    modelId: 'claude-3-5-haiku-********',
    temperature: 0.1,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Create mock repository with all required methods
    mockRepository = {
      getSettings: vi.fn(),
      saveSettings: vi.fn(),
      resetSettingsToDefaults: vi.fn(),
    } as any;

    // Setup default repository responses
    mockRepository.getSettings.mockResolvedValue(mockUserSettings);
    mockRepository.saveSettings.mockResolvedValue(mockUserSettings);
    mockRepository.resetSettingsToDefaults.mockResolvedValue(mockUserSettings);

    // Setup default model fallback
    mockGetDefaultModelForTaskCategory.mockReturnValue(mockDefaultModel);

    // Create service instance
    service = new UserSettingsService(mockRepository as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should accept and store repository instance', () => {
      expect(service).toBeInstanceOf(UserSettingsService);
      expect((service as any).repository).toBe(mockRepository);
    });
  });

  describe('getAllSettings()', () => {
    it('should return all settings from repository', async () => {
      const result = await service.getAllSettings();

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toEqual(mockUserSettings);
    });

    it('should propagate repository errors', async () => {
      const repositoryError = new Error('Repository failed');
      mockRepository.getSettings.mockRejectedValue(repositoryError);

      await expect(service.getAllSettings()).rejects.toThrow('Repository failed');
    });
  });

  describe('saveAllSettings()', () => {
    it('should save partial settings via repository', async () => {
      const partialSettings: Partial<UserSettings> = {
        apiKeys: {
          openai: 'new-api-key',
          anthropic: undefined,
          google: undefined,
        },
      };

      const result = await service.saveAllSettings(partialSettings);

      expect(mockRepository.saveSettings).toHaveBeenCalledWith(partialSettings);
      expect(result).toEqual(mockUserSettings);
    });

    it('should propagate repository save errors', async () => {
      const saveError = new Error('Save failed');
      mockRepository.saveSettings.mockRejectedValue(saveError);

      await expect(service.saveAllSettings({})).rejects.toThrow('Save failed');
    });
  });

  describe('getApiKeyForProvider()', () => {
    it('should return API key for existing provider', async () => {
      const result = await service.getApiKeyForProvider('openai');

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toBe('sk-test-openai-key');
    });

    it('should return undefined for provider without key', async () => {
      const result = await service.getApiKeyForProvider('google');

      expect(result).toBeUndefined();
    });

    it('should handle all supported provider types', async () => {
      const openaiResult = await service.getApiKeyForProvider('openai');
      const anthropicResult = await service.getApiKeyForProvider('anthropic');
      const googleResult = await service.getApiKeyForProvider('google');

      expect(openaiResult).toBe('sk-test-openai-key');
      expect(anthropicResult).toBe('sk-test-anthropic-key');
      expect(googleResult).toBeUndefined();
    });
  });

  describe('saveApiKey()', () => {
    it('should save API key for provider', async () => {
      await service.saveApiKey('google', 'new-google-key');

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: {
          ...mockUserSettings.apiKeys,
          google: 'new-google-key',
        },
      });
    });

    it('should update existing API key', async () => {
      await service.saveApiKey('openai', 'updated-openai-key');

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: {
          ...mockUserSettings.apiKeys,
          openai: 'updated-openai-key',
        },
      });
    });

    it('should preserve other API keys when updating one', async () => {
      await service.saveApiKey('google', 'new-google-key');

      const expectedApiKeys = {
        openai: 'sk-test-openai-key',
        anthropic: 'sk-test-anthropic-key',
        google: 'new-google-key',
      };

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: expectedApiKeys,
      });
    });
  });

  describe('getModelPreference()', () => {
    it('should return user preference when configured', async () => {
      const result = await service.getModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING);

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toEqual({
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-********',
        temperature: 0.1,
      });
    });

    it('should return default when user preference not configured', async () => {
      const result = await service.getModelPreference(LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING);

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockGetDefaultModelForTaskCategory).toHaveBeenCalledWith(LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING);
      expect(result).toEqual(mockDefaultModel);
    });

    it('should return default when user preference has incomplete data', async () => {
      const settingsWithIncompletePreference: UserSettings = {
        ...mockUserSettings,
        modelPreferences: {
          CORE_ORCHESTRATION_AND_PLANNING: {
            provider: 'anthropic',
            modelId: '', // Empty modelId should trigger fallback
            temperature: 0.1,
          },
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithIncompletePreference);

      const result = await service.getModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING);

      expect(mockGetDefaultModelForTaskCategory).toHaveBeenCalledWith(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING);
      expect(result).toEqual(mockDefaultModel);
    });

    it('should validate all LLM task categories', async () => {
      const allCategories = Object.values(LLMTaskCategory);
      
      for (const category of allCategories) {
        mockGetDefaultModelForTaskCategory.mockReturnValue(mockDefaultModel);
        const result = await service.getModelPreference(category);
        expect(result).toBeDefined();
      }
    });
  });

  describe('saveModelPreference()', () => {
    it('should save model preference for category', async () => {
      const newPreference: ModelProviderConfig = {
        provider: 'openai',
        modelId: 'gpt-4-turbo',
        temperature: 0.2,
      };

      await service.saveModelPreference(LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION, newPreference);

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        modelPreferences: {
          ...mockUserSettings.modelPreferences,
          [LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION]: newPreference,
        },
      });
    });

    it('should preserve existing model preferences when adding new one', async () => {
      const newPreference: ModelProviderConfig = {
        provider: 'google',
        modelId: 'gemini-pro',
        temperature: 0.5,
      };

      await service.saveModelPreference(LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE, newPreference);

      const expectedPreferences = {
        ...mockUserSettings.modelPreferences,
        [LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE]: newPreference,
      };

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        modelPreferences: expectedPreferences,
      });
    });

    it('should update existing model preference', async () => {
      const updatedPreference: ModelProviderConfig = {
        provider: 'openai',
        modelId: 'gpt-4-turbo',
        temperature: 0.1,
      };

      await service.saveModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING, updatedPreference);

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        modelPreferences: {
          ...mockUserSettings.modelPreferences,
          [LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING]: updatedPreference,
        },
      });
    });
  });

  describe('getPreferences()', () => {
    it('should return user preferences', async () => {
      const result = await service.getPreferences();

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toEqual(mockUserSettings.preferences);
    });

    it('should return preferences object with all required fields', async () => {
      const result = await service.getPreferences();

      expect(result).toHaveProperty('enableChromeAI');
      expect(result).toHaveProperty('enableAutoSummarization');
      expect(result).toHaveProperty('maxContextWindowTokens');
      expect(result).toHaveProperty('debugMode');
      expect(result).toHaveProperty('autoSaveWorkflows');
      expect(result).toHaveProperty('defaultWorkflowTags');
    });
  });

  describe('savePreference()', () => {
    it('should save individual preference', async () => {
      await service.savePreference('debugMode', false);

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        preferences: {
          ...mockUserSettings.preferences,
          debugMode: false,
        },
      });
    });

    it('should preserve other preferences when updating one', async () => {
      await service.savePreference('maxContextWindowTokens', 16000);

      const expectedPreferences = {
        ...mockUserSettings.preferences,
        maxContextWindowTokens: 16000,
      };

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        preferences: expectedPreferences,
      });
    });

    it('should handle array preferences', async () => {
      const newTags = ['custom', 'workflow'];
      
      await service.savePreference('defaultWorkflowTags', newTags);

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        preferences: {
          ...mockUserSettings.preferences,
          defaultWorkflowTags: newTags,
        },
      });
    });

    it('should be type-safe for preference keys', async () => {
      // These should compile without TypeScript errors
      await service.savePreference('enableChromeAI', true);
      await service.savePreference('enableAutoSummarization', false);
      await service.savePreference('maxContextWindowTokens', 12000);
      await service.savePreference('debugMode', true);
      await service.savePreference('autoSaveWorkflows', true);
      await service.savePreference('defaultWorkflowTags', []);
    });
  });

  describe('isFeatureEnabled()', () => {
    it('should return true for enabled boolean features', async () => {
      const result = await service.isFeatureEnabled('enableChromeAI');

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toBe(true);
    });

    it('should return false for disabled boolean features', async () => {
      const result = await service.isFeatureEnabled('enableAutoSummarization');

      expect(result).toBe(false);
    });

    it('should handle all supported feature flags', async () => {
      const chromeAI = await service.isFeatureEnabled('enableChromeAI');
      const autoSummarization = await service.isFeatureEnabled('enableAutoSummarization');
      const debugMode = await service.isFeatureEnabled('debugMode');
      const autoSave = await service.isFeatureEnabled('autoSaveWorkflows');

      expect(typeof chromeAI).toBe('boolean');
      expect(typeof autoSummarization).toBe('boolean');
      expect(typeof debugMode).toBe('boolean');
      expect(typeof autoSave).toBe('boolean');
    });

    it('should convert truthy values to true', async () => {
      const settingsWithTruthyValues: UserSettings = {
        ...mockUserSettings,
        preferences: {
          ...mockUserSettings.preferences,
          enableChromeAI: true,
          debugMode: true,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithTruthyValues);

      const chromeAI = await service.isFeatureEnabled('enableChromeAI');
      const debugMode = await service.isFeatureEnabled('debugMode');

      expect(chromeAI).toBe(true);
      expect(debugMode).toBe(true);
    });
  });

  describe('resetAllSettings()', () => {
    it('should reset all settings via repository', async () => {
      const result = await service.resetAllSettings();

      expect(mockRepository.resetSettingsToDefaults).toHaveBeenCalledWith();
      expect(result).toEqual(mockUserSettings);
    });

    it('should propagate repository reset errors', async () => {
      const resetError = new Error('Reset failed');
      mockRepository.resetSettingsToDefaults.mockRejectedValue(resetError);

      await expect(service.resetAllSettings()).rejects.toThrow('Reset failed');
    });
  });

  describe('getCompleteModelPreferences()', () => {
    it('should return preferences for all task categories', async () => {
      const result = await service.getCompleteModelPreferences();

      const allCategories = Object.values(LLMTaskCategory);
      for (const category of allCategories) {
        expect(result).toHaveProperty(category);
        expect(result[category]).toBeDefined();
      }
    });

    it('should mix user preferences and defaults', async () => {
      const result = await service.getCompleteModelPreferences();

      // Should have user preference for configured category
      expect(result[LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING]).toEqual(
        mockUserSettings.modelPreferences.CORE_ORCHESTRATION_AND_PLANNING
      );

      // Should have defaults for non-configured categories
      expect(mockGetDefaultModelForTaskCategory).toHaveBeenCalledWith(
        LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING
      );
    });
  });

  describe('hasConfiguredApiKeys()', () => {
    it('should return true when API keys are configured', async () => {
      const result = await service.hasConfiguredApiKeys();

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toBe(true);
    });

    it('should return false when no API keys are configured', async () => {
      const settingsWithoutKeys: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: undefined,
          anthropic: undefined,
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithoutKeys);

      const result = await service.hasConfiguredApiKeys();

      expect(result).toBe(false);
    });

    it('should return false when API keys are empty strings', async () => {
      const settingsWithEmptyKeys: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: '',
          anthropic: '   ',
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithEmptyKeys);

      const result = await service.hasConfiguredApiKeys();

      expect(result).toBe(false);
    });

    it('should return true when at least one API key is configured', async () => {
      const settingsWithOneKey: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: 'sk-valid-key',
          anthropic: undefined,
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithOneKey);

      const result = await service.hasConfiguredApiKeys();

      expect(result).toBe(true);
    });
  });

  describe('getConfiguredProviders()', () => {
    it('should return providers with configured API keys', async () => {
      const result = await service.getConfiguredProviders();

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(result).toEqual(['openai', 'anthropic']);
    });

    it('should return empty array when no providers configured', async () => {
      const settingsWithoutKeys: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: undefined,
          anthropic: undefined,
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithoutKeys);

      const result = await service.getConfiguredProviders();

      expect(result).toEqual([]);
    });

    it('should filter out empty string keys', async () => {
      const settingsWithMixedKeys: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: 'sk-valid-key',
          anthropic: '',
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithMixedKeys);

      const result = await service.getConfiguredProviders();

      expect(result).toEqual(['openai']);
    });
  });

  describe('saveMultipleModelPreferences()', () => {
    it('should save multiple model preferences at once', async () => {
      const multiplePreferences = {
        [LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION]: {
          provider: 'openai',
          modelId: 'gpt-4',
          temperature: 0.3,
        },
        [LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE]: {
          provider: 'anthropic',
          modelId: 'claude-3-opus',
          temperature: 0.1,
        },
      } as const;

      await service.saveMultipleModelPreferences(multiplePreferences);

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        modelPreferences: {
          ...mockUserSettings.modelPreferences,
          ...multiplePreferences,
        },
      });
    });

    it('should preserve existing preferences when adding multiple new ones', async () => {
      const multiplePreferences = {
        [LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING]: {
          provider: 'google',
          modelId: 'gemini-pro',
          temperature: 0.0,
        },
      } as const;

      await service.saveMultipleModelPreferences(multiplePreferences);

      const expectedPreferences = {
        ...mockUserSettings.modelPreferences,
        ...multiplePreferences,
      };

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        modelPreferences: expectedPreferences,
      });
    });
  });

  describe('removeApiKey()', () => {
    it('should remove API key for provider', async () => {
      await service.removeApiKey('openai');

      expect(mockRepository.getSettings).toHaveBeenCalledWith();
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: {
          anthropic: 'sk-test-anthropic-key',
          google: undefined,
        },
      });
    });

    it('should preserve other API keys when removing one', async () => {
      await service.removeApiKey('google');

      const expectedApiKeys = {
        openai: 'sk-test-openai-key',
        anthropic: 'sk-test-anthropic-key',
      };

      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: expectedApiKeys,
      });
    });

    it('should handle removing non-existent key gracefully', async () => {
      const settingsWithoutGoogleKey: UserSettings = {
        ...mockUserSettings,
        apiKeys: {
          openai: 'sk-test-openai-key',
          anthropic: 'sk-test-anthropic-key',
          google: undefined,
        },
      };

      mockRepository.getSettings.mockResolvedValue(settingsWithoutGoogleKey);

      await service.removeApiKey('google');

      // Should still update settings even if key wasn't present
      expect(mockRepository.saveSettings).toHaveBeenCalledWith({
        apiKeys: {
          openai: 'sk-test-openai-key',
          anthropic: 'sk-test-anthropic-key',
        },
      });
    });
  });

  describe('Error Handling', () => {
    it('should propagate repository errors in all methods', async () => {
      const repositoryError = new Error('Repository error');
      mockRepository.getSettings.mockRejectedValue(repositoryError);

      // Test that all methods properly propagate repository errors
      await expect(service.getApiKeyForProvider('openai')).rejects.toThrow('Repository error');
      await expect(service.getModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING)).rejects.toThrow('Repository error');
      await expect(service.getPreferences()).rejects.toThrow('Repository error');
      await expect(service.hasConfiguredApiKeys()).rejects.toThrow('Repository error');
      await expect(service.getConfiguredProviders()).rejects.toThrow('Repository error');
    });

    it('should handle save operation failures gracefully', async () => {
      const saveError = new Error('Save operation failed');
      mockRepository.saveSettings.mockRejectedValue(saveError);

      // Test that save operations properly propagate errors
      await expect(service.saveApiKey('openai', 'new-key')).rejects.toThrow('Save operation failed');
      await expect(service.savePreference('debugMode', true)).rejects.toThrow('Save operation failed');
      await expect(service.saveModelPreference(LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING, {
        provider: 'openai',
        modelId: 'gpt-4',
        temperature: 0.1,
      })).rejects.toThrow('Save operation failed');
    });
  });
}); 