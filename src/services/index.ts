/**
 * Services Layer Index
 * 
 * Central export point for all service-level functionality including
 * business logic services, configurations, and utilities.
 */

// Export user settings service and configuration
export { UserSettingsService } from './UserSettingsService';
export { 
  DEFAULT_FALLBACK_MODELS_FOR_SERVICE,
  DEFAULT_API_KEYS_TEMPLATE,
  DEFAULT_USER_PREFERENCES,
  getDefaultModelForTaskCategory,
  validateDefaultModelConfiguration
} from '../config/userSettings.defaults';

// Export node spec service
export { NodeSpecService } from './NodeSpecService';

// Export node spec service types
export type * from './types/nodeSpecService.types';

// Export task session manager service
export { TaskSessionManager } from './TaskSessionManager';

// Export task session manager types
export type {
  CreateTaskSessionData,
  SummarizationResult,
  SummarizationRequestContext,
  ContextManagementOptions,
  ContextSummarizationResult,
  UpdateStatusOptions,
  TaskSessionManagerConfig
} from './types/taskSessionManager.types';

// Export task session manager errors
export {
  TaskSessionNotFoundError,
  SummarizationError,
  GraphSnapshotError,
  InvalidStatusTransitionError
} from './types/taskSessionManager.types';

// TODO: Export other services as they are implemented
// export { OrchestratorService } from './OrchestratorService';
// export { LLMService } from './LLMService';
// export { PromptBuilderService } from './PromptBuilderService';
// export { ToolExecutorService } from './ToolExecutorService';
// export { EmbeddingService } from './EmbeddingService'; 