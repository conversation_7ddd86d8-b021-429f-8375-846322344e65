/**
 * NodeSpecService
 * 
 * The NodeSpecService is responsible for loading, caching, and providing access to the full 
 * specifications (schemas, descriptions, properties, defaults, etc.) of all available n8n nodes.
 * 
 * Key Responsibilities:
 * - Loading node specifications from files (Node.js) or data passed from live instances (browser)
 * - Caching loaded specifications in memory for fast access
 * - Providing methods to query node specifications and descriptors
 * - Validating node specifications against Zod schemas
 * - Supporting both Node.js and browser environments
 * 
 * Dependencies:
 * - Zod for schema validation
 * - fs/promises for Node.js file operations
 * - NodeDescriptionSchema and related types
 * 
 * Usage Example:
 * ```typescript
 * const nodeSpecService = new NodeSpecService();
 * 
 * // In Node.js
 * await nodeSpecService.loadSpecsFromFile('./config/nodes.snapshot.json');
 * 
 * // In browser (after tool fetches data)
 * await nodeSpecService.loadSpecsFromJsonString(fetchedJsonString, 'live-instance');
 * 
 * // Query specifications
 * const ifNodeSpec = await nodeSpecService.getNodeSpec('n8n-nodes-base.if');
 * const allDescriptors = await nodeSpecService.getAllNodeDescriptors();
 * const searchResults = await nodeSpecService.findNodes('email');
 * ```
 */

import { z } from 'zod';
import { isNodeEnvironment } from '../utils/environment';
import {
  NodeDescriptionSchema,
  NodeDescription,
  NodeSelectorDescriptor,
  NodeSearchOptions,
  NodeSearchResult,
  NodeSpecDataSource,
  NodeSpecServiceError,
  NodeSpecLoadingError,
  NodeSpecValidationError,
  NodeSpecNotInitializedError,
  NodeSpecFileNotFoundError,
  NodeSpecInvalidJsonError,
} from './types/nodeSpecService.types';

/**
 * Main service class for managing n8n node specifications
 */
export class NodeSpecService {
  // Internal state
  private nodeSpecsMap: Map<string, NodeDescription> = new Map();
  private nodeDescriptors: NodeSelectorDescriptor[] = [];
  private isInitialized: boolean = false;
  private dataSource: NodeSpecDataSource = {
    type: 'unknown',
    sourceName: 'No data loaded',
    loadedAt: new Date(),
    nodeCount: 0,
  };

  constructor() {
    // Constructor is intentionally minimal to support dependency injection patterns
  }

  // --- Public Initialization Methods ---

  /**
   * Load node specifications from a file (Node.js only)
   * 
   * @param filePath - Path to the JSON file containing node specifications
   * @throws {NodeSpecFileNotFoundError} If the file cannot be found
   * @throws {NodeSpecInvalidJsonError} If the file contains invalid JSON
   * @throws {NodeSpecValidationError} If the JSON doesn't match the expected schema
   */
  public async loadSpecsFromFile(filePath: string): Promise<void> {
    if (!isNodeEnvironment()) {
      throw new NodeSpecServiceError(
        'loadSpecsFromFile() is only available in Node.js environment',
        'EnvironmentError'
      );
    }

    try {
      // Dynamic import for fs only when needed (Node.js environment)
      const { promises: fs } = await import('fs');
      const jsonString = await fs.readFile(filePath, 'utf-8');
      await this.loadSpecsFromJsonString(jsonString, `File: ${filePath}`);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        throw new NodeSpecFileNotFoundError(filePath, error);
      }
      throw new NodeSpecLoadingError(
        `Failed to read file ${filePath}: ${error.message}`,
        error
      );
    }
  }

  /**
   * Load node specifications from a JSON string
   * 
   * @param jsonString - JSON string containing node specifications
   * @param sourceName - Optional name describing the source of the data
   * @throws {NodeSpecInvalidJsonError} If the string contains invalid JSON
   * @throws {NodeSpecValidationError} If the JSON doesn't match the expected schema
   */
  public async loadSpecsFromJsonString(
    jsonString: string,
    sourceName: string = 'JSON String'
  ): Promise<void> {
    let rawData: any;

    try {
      rawData = JSON.parse(jsonString);
    } catch (error: any) {
      throw new NodeSpecInvalidJsonError(
        `Failed to parse JSON: ${error.message}`,
        error
      );
    }

    // Validate the parsed data against our schema
    const validationResult = z.array(NodeDescriptionSchema).safeParse(rawData);
    if (!validationResult.success) {
      const errorDetails = validationResult.error.format();
      throw new NodeSpecValidationError(
        `Node specifications do not match expected schema. First error: ${JSON.stringify(errorDetails._errors?.[0] || 'Unknown validation error')}`,
        errorDetails
      );
    }

    this.processAndCacheSpecs(validationResult.data, sourceName, 'json_string');
  }

  /**
   * Load node specifications from an array of NodeDescription objects
   * 
   * @param specsArray - Array of node specifications
   * @param sourceName - Optional name describing the source of the data
   * @throws {NodeSpecValidationError} If the array doesn't match the expected schema
   */
  public loadSpecsFromArray(
    specsArray: NodeDescription[],
    sourceName: string = 'Direct Array'
  ): void {
    // Create a deep copy to ensure we're working with plain objects for validation
    const plainArray = JSON.parse(JSON.stringify(specsArray));

    // Validate the array against our schema
    const validationResult = z.array(NodeDescriptionSchema).safeParse(plainArray);
    if (!validationResult.success) {
      const errorDetails = validationResult.error.format();
      throw new NodeSpecValidationError(
        `Node specifications array does not match expected schema. First error: ${JSON.stringify(errorDetails._errors?.[0] || 'Unknown validation error')}`,
        errorDetails
      );
    }

    this.processAndCacheSpecs(validationResult.data, sourceName, 'array');
  }

  // --- Private Data Processing Methods ---

  /**
   * Process and cache validated node specifications
   * 
   * @private
   * @param specs - Array of validated node specifications
   * @param sourceName - Name describing the source of the data
   * @param sourceType - Type of source (file, json_string, array)
   */
  private processAndCacheSpecs(
    specs: NodeDescription[],
    sourceName: string,
    sourceType: 'file' | 'json_string' | 'array'
  ): void {
    // Clear existing cache
    this.nodeSpecsMap.clear();
    this.nodeDescriptors = [];

    // Populate the specs map
    for (const spec of specs) {
      // For V1, we handle multi-version nodes by keeping the last one encountered
      // TODO V2: Enhance to support multiple versions properly
      this.nodeSpecsMap.set(spec.name, spec);
    }

    // Create lightweight descriptors for agents
    this.nodeDescriptors = this.createSelectorDescriptors(specs);

    // Update service state
    this.isInitialized = true;
    this.dataSource = {
      type: sourceType,
      sourceName,
      loadedAt: new Date(),
      nodeCount: specs.length,
    };

    console.log(
      `NodeSpecService initialized with ${specs.length} node specifications from ${sourceName}`
    );
  }

  /**
   * Create lightweight node descriptors for agent consumption
   * 
   * @private
   * @param specs - Array of node specifications
   * @returns Array of lightweight node descriptors
   */
  private createSelectorDescriptors(specs: NodeDescription[]): NodeSelectorDescriptor[] {
    const descriptors: NodeSelectorDescriptor[] = [];

    for (const spec of specs) {
      const descriptor: NodeSelectorDescriptor = {
        typeId: spec.name,
        displayName: spec.displayName,
        description: spec.description,
        categories: spec.group,
        primaryGroup: spec.group?.[0],
        icon: spec.icon,
        iconUrl: spec.iconUrl,
        version: spec.version,
        usableAsTool: spec.usableAsTool,
      };
      descriptors.push(descriptor);
    }

    return descriptors;
  }

  // --- Public Accessor Methods ---

  /**
   * Get the full specification for a single node type
   * 
   * @param typeId - The node type ID (e.g., 'n8n-nodes-base.if')
   * @param version - Optional version number (V2 feature, currently ignored)
   * @returns The node specification or null if not found
   * @throws {NodeSpecNotInitializedError} If the service hasn't been initialized
   */
  public async getNodeSpec(typeId: string, version?: number): Promise<NodeDescription | null> {
    if (!this.isInitialized) {
      throw new NodeSpecNotInitializedError();
    }

    // TODO V2: Implement version-specific lookup when version is provided
    // For V1, we ignore the version parameter and return the cached spec
    return this.nodeSpecsMap.get(typeId) || null;
  }

  /**
   * Get lightweight descriptors for all available nodes
   * 
   * @returns Array of node descriptors
   * @throws {NodeSpecNotInitializedError} If the service hasn't been initialized
   */
  public async getAllNodeDescriptors(): Promise<NodeSelectorDescriptor[]> {
    if (!this.isInitialized) {
      throw new NodeSpecNotInitializedError();
    }

    // Return a copy to prevent external modification
    return [...this.nodeDescriptors];
  }

  /**
   * Find nodes based on a search query and optional filters
   * 
   * @param query - Search term to match against node properties
   * @param options - Optional search configuration
   * @returns Search results with matching nodes
   * @throws {NodeSpecNotInitializedError} If the service hasn't been initialized
   */
  public async findNodes(
    query: string,
    options: NodeSearchOptions = {}
  ): Promise<NodeSearchResult> {
    if (!this.isInitialized) {
      throw new NodeSpecNotInitializedError();
    }

    const {
      limit = 50,
      includeDescription = true,
      caseSensitive = false,
      categories,
      usableAsTool,
    } = options;

    const searchTerm = caseSensitive ? query : query.toLowerCase();
    const matchingNodes: NodeSelectorDescriptor[] = [];

    for (const descriptor of this.nodeDescriptors) {
      // Apply category filter if specified
      if (categories && categories.length > 0) {
        const hasMatchingCategory = descriptor.categories?.some(cat =>
          categories.includes(cat)
        );
        if (!hasMatchingCategory) continue;
      }

      // Apply usableAsTool filter if specified
      if (usableAsTool !== undefined && descriptor.usableAsTool !== usableAsTool) {
        continue;
      }

      // Perform text matching
      const displayName = caseSensitive ? descriptor.displayName : descriptor.displayName.toLowerCase();
      const typeId = caseSensitive ? descriptor.typeId : descriptor.typeId.toLowerCase();
      const description = descriptor.description
        ? (caseSensitive ? descriptor.description : descriptor.description.toLowerCase())
        : '';

      const matchesDisplayName = displayName.includes(searchTerm);
      const matchesTypeId = typeId.includes(searchTerm);
      const matchesDescription = includeDescription && description.includes(searchTerm);
      const matchesCategories = descriptor.categories?.some(cat =>
        (caseSensitive ? cat : cat.toLowerCase()).includes(searchTerm)
      );

      if (matchesDisplayName || matchesTypeId || matchesDescription || matchesCategories) {
        matchingNodes.push(descriptor);
      }

      // Apply limit if specified
      if (limit && matchingNodes.length >= limit) {
        break;
      }
    }

    return {
      nodes: matchingNodes,
      totalMatches: matchingNodes.length,
      searchTerm: query,
      appliedFilters: options,
    };
  }

  // --- Public Utility Methods ---

  /**
   * Get information about the current data source
   * 
   * @returns Information about how the specifications were loaded
   */
  public getDataSourceInfo(): NodeSpecDataSource {
    return { ...this.dataSource }; // Return a copy
  }

  /**
   * Check if the service has been initialized
   * 
   * @returns True if initialized, false otherwise
   */
  public getIsInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get the total number of loaded node specifications
   * 
   * @returns Number of loaded specifications
   */
  public getLoadedNodeCount(): number {
    return this.nodeSpecsMap.size;
  }

  /**
   * Get a list of all available node type IDs
   * 
   * @returns Array of node type IDs
   * @throws {NodeSpecNotInitializedError} If the service hasn't been initialized
   */
  public async getAllNodeTypeIds(): Promise<string[]> {
    if (!this.isInitialized) {
      throw new NodeSpecNotInitializedError();
    }

    return Array.from(this.nodeSpecsMap.keys()).sort();
  }

  /**
   * Get nodes grouped by their primary category
   * 
   * @returns Map of category to node descriptors
   * @throws {NodeSpecNotInitializedError} If the service hasn't been initialized
   */
  public async getNodesByCategory(): Promise<Map<string, NodeSelectorDescriptor[]>> {
    if (!this.isInitialized) {
      throw new NodeSpecNotInitializedError();
    }

    const categoryMap = new Map<string, NodeSelectorDescriptor[]>();

    for (const descriptor of this.nodeDescriptors) {
      const primaryCategory = descriptor.primaryGroup || 'uncategorized';
      
      if (!categoryMap.has(primaryCategory)) {
        categoryMap.set(primaryCategory, []);
      }
      
      categoryMap.get(primaryCategory)!.push(descriptor);
    }

    return categoryMap;
  }

  /**
   * Clear all loaded specifications and reset the service
   */
  public clear(): void {
    this.nodeSpecsMap.clear();
    this.nodeDescriptors = [];
    this.isInitialized = false;
    this.dataSource = {
      type: 'unknown',
      sourceName: 'No data loaded',
      loadedAt: new Date(),
      nodeCount: 0,
    };
  }
} 