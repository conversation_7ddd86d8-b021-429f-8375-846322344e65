/**
 * Chat Types
 * 
 * Defines the structure for chat threads and messages, aligned with 
 * Vercel AI SDK v5 UIMessage format for conversation management.
 */

import { z } from 'zod';

/**
 * Chat message role enumeration
 */
export const ChatMessageRoleSchema = z.enum([
  'user',
  'assistant',
  'system',
  'tool',
  'system_internal'
]);

/**
 * Content part for text
 */
export const TextContentPartSchema = z.object({
  type: z.literal('text'),
  text: z.string(),
});

/**
 * Content part for tool calls
 */
export const ToolCallContentPartSchema = z.object({
  type: z.literal('tool-call'),
  toolCallId: z.string(),
  toolName: z.string(),
  args: z.record(z.any()),
});

/**
 * Content part for tool results
 */
export const ToolResultContentPartSchema = z.object({
  type: z.literal('tool-result'),
  toolCallId: z.string(),
  result: z.any(),
  isError: z.boolean().optional(),
});

/**
 * Union of all content part types
 */
export const ContentPartSchema = z.union([
  TextContentPartSchema,
  ToolCallContentPartSchema,
  ToolResultContentPartSchema,
]);

/**
 * Message metadata for additional information
 */
export const MessageMetadataSchema = z.object({
  // LLM usage information
  tokenUsage: z.object({
    inputTokens: z.number(),
    outputTokens: z.number(),
    totalTokens: z.number(),
  }).optional(),
  
  // Model information
  modelProvider: z.string().optional(),
  modelId: z.string().optional(),
  
  // Agent information
  agentSlug: z.string().optional(),
  thought: z.string().optional(),
  
  // Execution context
  graphExecutionId: z.string().optional(),
  nodeId: z.string().optional(),
  
  // Additional metadata
  cost: z.number().optional(),
  duration: z.number().optional(),
  custom: z.record(z.any()).optional(),
});

/**
 * Chat message schema (aligned with Vercel AI SDK UIMessage)
 */
export const ChatMessageSchema = z.object({
  id: z.string(),
  
  // Core message properties
  role: ChatMessageRoleSchema,
  content: z.union([
    z.string(), // Simple text content
    z.array(ContentPartSchema), // Complex multi-part content
  ]),
  
  // Add taskId as optional for linking to TaskSession directly if needed
  taskId: z.string().optional().describe("Optional TaskSession ID if this message is directly related to one"),
  name: z.string().optional().describe("Optional: For tool role, the name of the tool"),
  userId: z.string().optional().describe("User ID of the sender, if applicable"),
  
  // Message metadata
  metadata: MessageMetadataSchema.optional(),
  
  // Chat association
  chatId: z.string(),
  
  // Ordering and timestamps
  timestamp: z.date().default(() => new Date()),
  sequenceNumber: z.number(),
  
  // Message state
  status: z.enum(['sending', 'sent', 'error']).default('sent'),
  errorMessage: z.string().optional(),
  
  // Additional data
  additionalData: z.record(z.any()).default({}),
});

/**
 * Chat thread status enumeration
 */
export const ChatThreadStatusSchema = z.enum([
  'active',
  'paused',
  'completed',
  'archived'
]);

/**
 * Chat thread schema
 */
export const ChatThreadSchema = z.object({
  id: z.string(),
  
  // Basic information
  title: z.string(),
  description: z.string().optional(),
  status: ChatThreadStatusSchema.default('active'),
  
  // Associated task sessions
  taskSessionIds: z.array(z.string()).default([]),
  
  // Thread metadata
  messageCount: z.number().default(0),
  lastMessageId: z.string().optional(),
  
  // Participant information
  participantTypes: z.array(z.string()).default(['user', 'assistant']),
  
  // Configuration
  tags: z.array(z.string()).default([]),
  isArchived: z.boolean().default(false),
  
  // Timestamps
  createdAt: z.date().default(() => new Date()),
  lastUpdatedAt: z.date().default(() => new Date()),
  lastMessageAt: z.date().optional(),
  
  // Additional data
  additionalData: z.record(z.any()).default({}),
});

// Derived TypeScript types
export type ChatMessageRole = z.infer<typeof ChatMessageRoleSchema>;
export type TextContentPart = z.infer<typeof TextContentPartSchema>;
export type ToolCallContentPart = z.infer<typeof ToolCallContentPartSchema>;
export type ToolResultContentPart = z.infer<typeof ToolResultContentPartSchema>;
export type ContentPart = z.infer<typeof ContentPartSchema>;
export type MessageMetadata = z.infer<typeof MessageMetadataSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type ChatThreadStatus = z.infer<typeof ChatThreadStatusSchema>;
export type ChatThread = z.infer<typeof ChatThreadSchema>; 