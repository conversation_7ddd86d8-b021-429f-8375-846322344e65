/**
 * Rules Types
 * 
 * Defines the structure for n8n best practices and user-defined rules
 * that guide agent behavior and workflow validation.
 */

import { z } from 'zod';

/**
 * Rule type enumeration
 */
export const RuleTypeSchema = z.enum([
  'ALWAYS_APPLY',           // Rules that always apply to agent calls
  'CONTEXTUAL_SUGGESTION',  // Rules that apply in specific contexts
  'ANTI_PATTERN_CHECK'      // Rules that check for anti-patterns
]);

/**
 * Rule priority enumeration
 */
export const RulePrioritySchema = z.enum([
  'critical',
  'high',
  'medium',
  'low',
  'informational'
]);

/**
 * Rule trigger context schema
 */
export const RuleTriggerContextSchema = z.object({
  keywords: z.array(z.string()).optional(),
  nodeTypes: z.array(z.string()).optional(),
  workflowPatterns: z.array(z.string()).optional(),
  agentPhases: z.array(z.string()).optional(),
  conditions: z.record(z.string(), z.any()).optional(),
});

/**
 * Rule validation schema
 */
export const RuleValidationSchema = z.object({
  pattern: z.string().optional(),      // Regex pattern for validation
  jsonPath: z.string().optional(),     // JSONPath for specific field validation
  customFunctionKey: z.string().optional(), // Reference to custom validation function
  expectedValue: z.any().optional(),    // Expected value for comparison
  operator: z.enum(['equals', 'notEquals', 'contains', 'notContains', 'matches', 'exists']).optional(),
});

/**
 * N8n rule schema
 */
export const N8nRuleSchema = z.object({
  id: z.string(),
  
  // Basic rule information
  name: z.string(),
  description: z.string(),
  descriptionForLLM: z.string(), // LLM-optimized description
  
  // Rule classification
  type: RuleTypeSchema,
  priority: RulePrioritySchema.default('medium'),
  category: z.string().optional(), // e.g., 'workflow-design', 'performance', 'security'
  
  // Application scope
  appliesToAgents: z.array(z.string()), // Agent slugs this rule applies to
  appliesToNodeTypes: z.array(z.string()).default([]), // Node types this rule applies to
  
  // Trigger conditions
  triggerContext: RuleTriggerContextSchema.optional(),
  
  // Validation logic
  validation: RuleValidationSchema.optional(),
  
  // Rule content
  recommendation: z.string(),
  example: z.string().optional(),
  reasoning: z.string().optional(),
  
  // Rule metadata
  source: z.enum(['builtin', 'user_defined', 'community']).default('user_defined'),
  version: z.number().default(1),
  isActive: z.boolean().default(true),
  
  // Usage tracking
  usageCount: z.number().default(0),
  lastUsedAt: z.date().optional(),
  
  // External references
  documentationUrl: z.string().url().optional(),
  relatedRules: z.array(z.string()).default([]), // IDs of related rules
  
  // Timestamps
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  
  // Additional data
  additionalData: z.record(z.any()).default({}),
});

/**
 * Rule application result schema
 */
export const RuleApplicationResultSchema = z.object({
  ruleId: z.string(),
  applied: z.boolean(),
  result: z.enum(['passed', 'failed', 'warning', 'info']),
  message: z.string().optional(),
  suggestions: z.array(z.string()).default([]),
  confidence: z.number().min(0).max(1).optional(),
  metadata: z.record(z.any()).default({}),
});

// Derived TypeScript types
export type RuleType = z.infer<typeof RuleTypeSchema>;
export type RulePriority = z.infer<typeof RulePrioritySchema>;
export type RuleTriggerContext = z.infer<typeof RuleTriggerContextSchema>;
export type RuleValidation = z.infer<typeof RuleValidationSchema>;
export type N8nRule = z.infer<typeof N8nRuleSchema>;
export type RuleApplicationResult = z.infer<typeof RuleApplicationResultSchema>; 