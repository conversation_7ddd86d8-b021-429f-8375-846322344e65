/**
 * User Settings Types
 * 
 * Defines the structure for user preferences, API keys, model preferences,
 * and feature toggles that persist across sessions.
 */

import { z } from 'zod';

/**
 * Model provider configuration
 */
export const ModelProviderConfigSchema = z.object({
  provider: z.string(), // e.g., 'openai', 'anthropic', 'google'
  modelId: z.string(),  // e.g., 'gpt-4o', 'claude-3-5-sonnet-20241022'
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
});

/**
 * User model preferences per task category
 */
export const UserModelPreferencesSchema = z.object({
  CORE_ORCHESTRATION_AND_PLANNING: ModelProviderConfigSchema.optional(),
  N8N_CODE_GENERATION_COMPOSITION: ModelProviderConfigSchema.optional(),
  KNOWLEDGE_AUGMENTED_EXPLANATION: ModelProviderConfigSchema.optional(),
  ANALYTICAL_VALIDATION_CRITIQUE: ModelProviderConfigSchema.optional(),
  UTILITY_CLASSIFICATION_ROUTING: ModelProviderConfigSchema.optional(),
  UTILITY_CONVERSATION_SUMMARIZATION: ModelProviderConfigSchema.optional(),
});

/**
 * API keys for different providers
 */
export const ApiKeysConfigSchema = z.object({
  openai: z.string().optional(),
  anthropic: z.string().optional(),
  google: z.string().optional(),
});

/**
 * Feature flags and user preferences
 */
export const UserPreferencesSchema = z.object({
  enableChromeAI: z.boolean().default(true),
  enableAutoSummarization: z.boolean().default(true),
  maxContextWindowTokens: z.number().positive().default(8000),
  debugMode: z.boolean().default(false),
  autoSaveWorkflows: z.boolean().default(true),
  defaultWorkflowTags: z.array(z.string()).default([]),
});

/**
 * Complete user settings schema (singleton document in RxDB)
 */
export const UserSettingsSchema = z.object({
  id: z.literal('singleton_user_settings'), // Fixed ID for singleton pattern
  version: z.number().default(1),
  modelPreferences: UserModelPreferencesSchema.default({}),
  apiKeys: ApiKeysConfigSchema.default({}),
  preferences: UserPreferencesSchema.default({}),
  
  // Metadata
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

// Derived TypeScript types
export type ModelProviderConfig = z.infer<typeof ModelProviderConfigSchema>;
export type UserModelPreferences = z.infer<typeof UserModelPreferencesSchema>;
export type ApiKeysConfig = z.infer<typeof ApiKeysConfigSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type UserSettings = z.infer<typeof UserSettingsSchema>; 