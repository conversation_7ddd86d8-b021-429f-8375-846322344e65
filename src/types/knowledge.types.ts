/**
 * Knowledge Types
 * 
 * Defines the structure for knowledge chunks used in Retrieval Augmented Generation (RAG),
 * including text chunks, embeddings, and metadata for semantic search.
 */

import { z } from 'zod';

/**
 * Knowledge source type enumeration
 */
export const KnowledgeSourceTypeSchema = z.enum([
  'n8n_documentation',
  'workflow_template',
  'best_practice',
  'user_upload',
  'community_content',
  'node_documentation',
  'integration_guide',
  'tutorial',
  'faq'
]);

/**
 * Knowledge chunk metadata schema
 */
export const KnowledgeChunkMetadataSchema = z.object({
  // Source information
  sourceUrl: z.string().url().optional(),
  sourceTitle: z.string().optional(),
  sourceAuthor: z.string().optional(),
  sourceDate: z.date().optional(),
  
  // Content structure
  section: z.string().optional(),        // e.g., "Getting Started", "Advanced Features"
  subsection: z.string().optional(),     // More specific section
  tags: z.array(z.string()).default([]), // Content tags for categorization
  
  // Content metadata
  language: z.string().default('en'),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  contentType: z.enum(['text', 'code', 'json', 'markdown', 'html']).default('text'),
  
  // Processing metadata
  chunkIndex: z.number(),                // Position within the source document
  totalChunks: z.number(),               // Total chunks from the same source
  chunkOverlap: z.number().default(0),   // Overlap with adjacent chunks in tokens
  
  // Quality metrics
  qualityScore: z.number().min(0).max(1).optional(),
  relevanceScore: z.number().min(0).max(1).optional(),
  
  // Additional metadata
  nodeTypes: z.array(z.string()).default([]),      // Related n8n node types
  workflowCategories: z.array(z.string()).default([]), // Related workflow categories
  keywords: z.array(z.string()).default([]),       // Extracted keywords
  
  // Custom metadata
  custom: z.record(z.any()).default({}),
});

/**
 * Embedding metadata schema
 */
export const EmbeddingMetadataSchema = z.object({
  model: z.string(),                     // e.g., "Xenova/all-MiniLM-L6-v2"
  dimensions: z.number(),                // Embedding vector dimensions
  generatedAt: z.date().default(() => new Date()),
  version: z.string().default('1.0'),   // Embedding model version
  processingTime: z.number().optional(), // Time to generate embedding in ms
});

/**
 * Knowledge chunk schema
 */
export const KnowledgeChunkSchema = z.object({
  id: z.string(),
  
  // Core content
  chunkText: z.string(),
  sourceType: KnowledgeSourceTypeSchema,
  
  // Embedding data
  embedding: z.array(z.number()), // Vector embedding
  embeddingMetadata: EmbeddingMetadataSchema,
  
  // Vector search optimization (RxDB custom indexes)
  // These are pre-computed hash values for efficient vector search
  idx0: z.number().optional(),
  idx1: z.number().optional(),
  idx2: z.number().optional(),
  idx3: z.number().optional(),
  idx4: z.number().optional(),
  
  // Content metadata
  metadata: KnowledgeChunkMetadataSchema,
  
  // Content analytics
  tokenCount: z.number(),
  characterCount: z.number(),
  
  // Content status
  isActive: z.boolean().default(true),
  isVerified: z.boolean().default(false),
  
  // Usage tracking
  accessCount: z.number().default(0),
  lastAccessedAt: z.date().optional(),
  
  // Timestamps
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  
  // Additional data
  additionalData: z.record(z.any()).default({}),
});

/**
 * Knowledge search result with score
 */
export const KnowledgeChunkWithScoreSchema = z.object({
  chunk: KnowledgeChunkSchema,
  score: z.number().min(0).max(1), // Similarity score
  matchingKeywords: z.array(z.string()).default([]),
  relevanceExplanation: z.string().optional(),
});

/**
 * Knowledge search query schema
 */
export const KnowledgeSearchQuerySchema = z.object({
  query: z.string(),
  embedding: z.array(z.number()).optional(), // Pre-computed query embedding
  filters: z.object({
    sourceTypes: z.array(KnowledgeSourceTypeSchema).optional(),
    tags: z.array(z.string()).optional(),
    nodeTypes: z.array(z.string()).optional(),
    workflowCategories: z.array(z.string()).optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    language: z.string().optional(),
  }).optional(),
  options: z.object({
    topK: z.number().positive().default(5),
    minScore: z.number().min(0).max(1).default(0.1),
    includeMetadata: z.boolean().default(true),
  }).default({}),
});

// Derived TypeScript types
export type KnowledgeSourceType = z.infer<typeof KnowledgeSourceTypeSchema>;
export type KnowledgeChunkMetadata = z.infer<typeof KnowledgeChunkMetadataSchema>;
export type EmbeddingMetadata = z.infer<typeof EmbeddingMetadataSchema>;
export type KnowledgeChunk = z.infer<typeof KnowledgeChunkSchema>;
export type KnowledgeChunkWithScore = z.infer<typeof KnowledgeChunkWithScoreSchema>;
export type KnowledgeSearchQuery = z.infer<typeof KnowledgeSearchQuerySchema>; 