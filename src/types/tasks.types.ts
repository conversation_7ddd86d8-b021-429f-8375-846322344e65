/**
 * Task Session Types
 * 
 * Defines the structure for managing high-level user tasks and their 
 * execution state across graph-based workflows.
 */

import { z } from 'zod';

/**
 * Task session status enumeration
 */
export const TaskSessionStatusSchema = z.enum([
  'initializing',
  'awaiting_user_input',
  'processing',
  'paused',
  'completed',
  'failed',
  'cancelled'
]);

/**
 * Task execution metadata
 */
export const TaskExecutionMetadataSchema = z.object({
  totalStepsExecuted: z.number().default(0),
  totalLLMTokensUsed: z.number().default(0),
  estimatedCostUSD: z.number().default(0),
  executionTimeMs: z.number().default(0),
  lastActiveGraphId: z.string().optional(),
  activeGraphRunId: z.string().optional(),
});

/**
 * Task context summary for LLM interactions
 * This is used for data that might inform the summarization prompt,
 * not as the type of the stored summary itself
 */
export const TaskContextSummarySchema = z.object({
  userIntent: z.string(),
  currentPhase: z.string(),
  keyDecisions: z.array(z.string()).default([]),
  pendingActions: z.array(z.string()).default([]),
  lastSummaryTokens: z.number().default(0),
  createdAt: z.date().default(() => new Date()),
});

/**
 * LLM Token Usage Schema (detailed with per-node breakdown)
 */
export const LLMTokenUsageSchema = z.object({
  promptTokens: z.number(),
  completionTokens: z.number(),
  totalTokens: z.number(),
  byNode: z.record(z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number(),
  })),
});

/**
 * Aggregated LLM Token Usage Schema (simplified for graph-level summaries)
 */
export const AggregatedLLMTokenUsageSchema = z.object({
  promptTokens: z.number(),
  completionTokens: z.number(),
  totalTokens: z.number(),
});

/**
 * Node Output Version Schema
 */
export const NodeOutputVersionSchema = z.object({
  runId: z.string().uuid().describe("Unique identifier for this specific execution instance of the node within the graph run."),
  timestamp: z.number(),
  output: z.any(),
  status: z.enum(['success', 'failure', 'interrupted_for_user_input']),
  error: z.object({
    message: z.string(),
    stack: z.string().optional(),
    type: z.string().optional(),
    data: z.any().optional(),
  }).optional(),
  thought: z.string().optional(),
  llmTokenUsage: z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number(),
  }).optional(),
  interruptPayload: z.any().optional(),
});

/**
 * Graph Execution Context Snapshot Schema
 * For persistence and resumption of graph execution state
 */
export const GraphExecutionContextSnapshotSchema = z.object({
  graphDefinitionId: z.string(),
  graphDefinitionVersion: z.string(),
  taskSessionId: z.string(),
  currentGraphRunId: z.string(),
  status: z.enum(['IDLE', 'RUNNING', 'PAUSED_FOR_USER_INPUT', 'COMPLETED', 'FAILED', 'ABORTED', 'RESUMING']),
  initialInputs: z.record(z.any()),
  nodeOutputs: z.array(z.tuple([z.string(), z.array(NodeOutputVersionSchema)])), // Serializable form of Map
  currentNodeIdBeingProcessed: z.string().nullable(),
  activeInterruptDetails: z.any().optional(),
  lastError: z.any().optional(),
  lastCompletedNodeId: z.string().optional(),
  processingQueue: z.array(z.string()),
  totalStepsExecuted: z.number(),
  tokenUsage: AggregatedLLMTokenUsageSchema,
});

/**
 * Task session schema
 */
export const TaskSessionSchema = z.object({
  id: z.string(),
  
  // Basic information
  originalUserInput: z.string().describe("The initial user input that triggered this task."),
  title: z.string().optional().describe("Optional title for the task session."),
  description: z.string().optional(),
  status: TaskSessionStatusSchema,
  
  // Associated chat thread
  chatId: z.string(),
  
  // Execution state
  currentGraphExecutionContextId: z.string().optional(),
  executionHistory: z.array(z.string()).default([]), // Array of execution context IDs
  
  // Graph execution state snapshot for pause/resume functionality
  graphExecutionContextSnapshot: GraphExecutionContextSnapshotSchema.optional().describe("Snapshot of the graph executor state if paused or completed."),
  
  // Context management for LLM
  llmContextSummary: z.string().nullish().describe("AI-generated textual summary of older parts of the conversation relevant to this task."),
  summaryLastMessageId: z.string().nullish().describe("ID of the last ChatMessage that was included when llmContextSummary was generated."),
  
  // Execution metadata
  metadata: TaskExecutionMetadataSchema.default({}),
  
  // Task configuration
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  tags: z.array(z.string()).default([]),
  
  // Timestamps
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  completedAt: z.date().optional(),
  
  // Additional data
  additionalData: z.record(z.any()).default({}),
});

// Derived TypeScript types
export type TaskSessionStatus = z.infer<typeof TaskSessionStatusSchema>;
export type TaskExecutionMetadata = z.infer<typeof TaskExecutionMetadataSchema>;
export type TaskContextSummary = z.infer<typeof TaskContextSummarySchema>;
export type LLMTokenUsage = z.infer<typeof LLMTokenUsageSchema>;
export type AggregatedLLMTokenUsage = z.infer<typeof AggregatedLLMTokenUsageSchema>;
export type NodeOutputVersion = z.infer<typeof NodeOutputVersionSchema>;
export type GraphExecutionContextSnapshot = z.infer<typeof GraphExecutionContextSnapshotSchema>;
export type TaskSession = z.infer<typeof TaskSessionSchema>; 