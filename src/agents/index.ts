/**
 * Concrete AI Agents Index
 * 
 * Central export point for all specialized AI agent implementations.
 * These agents perform specific cognitive tasks within the AI Brain.
 */

// TODO: Export concrete agent implementations when implemented
// export { N8nIntentRouterAgent } from './intentRouter/N8nIntentRouterAgent';
// export { N8nRequirementsGatheringAgent } from './requirementsGatherer/N8nRequirementsGatheringAgent';
// export { N8nArchitectAgent } from './architect/N8nArchitectAgent';
// export { N8nNodeSelectorAgent } from './nodeSelector/N8nNodeSelectorAgent';
// export { N8nNodeComposerAgent } from './composer/N8nNodeComposerAgent';
// export { N8nWorkflowDocumenterAgent } from './workflowDocumenter/N8nWorkflowDocumenterAgent';
// export { N8nConnectionComposerAgent } from './connectionComposer/N8nConnectionComposerAgent';
// export { N8nBestPracticeValidatorAgent } from './validator/N8nBestPracticeValidatorAgent';
// export { N8nReflectionAgent } from './reflection/N8nReflectionAgent';

// TODO: Export agent-specific types when defined
// export type { ArchitectPlan, NodeSelection, ComposedNode } from './types';