/**
 * Test Setup
 * 
 * Global test setup file for Vitest. This file is executed before all tests.
 * It configures test utilities, global mocks, and test environment.
 */

import { vi, beforeEach } from 'vitest';

// Set up global test environment
global.console = {
  ...console,
  // Suppress console.log in tests unless DEBUG env var is set
  log: process.env.DEBUG ? console.log : vi.fn(),
  warn: process.env.DEBUG ? console.warn : vi.fn(),
  error: console.error, // Always show errors
  info: process.env.DEBUG ? console.info : vi.fn(),
  debug: process.env.DEBUG ? console.debug : vi.fn(),
};

// Mock crypto for Node.js environment if needed
if (typeof global.crypto === 'undefined') {
  global.crypto = {
    randomUUID: () => `test-uuid-${Math.random().toString(36).substr(2, 9)}`,
  } as any;
}

// Set up common test utilities
export const createTestGraphId = () => `test-graph-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const createMockAgent = (agentSlug: string) => {
  return class MockAgent {
    static readonly agentSlug = agentSlug;
    static readonly defaultLlmTaskCategory = 'CORE_ORCHESTRATION_AND_PLANNING';
    
    constructor(deps: any) {
      // Mock constructor
    }
    
    async processInput(input: any) {
      return { success: true, payload: { mocked: true, input } };
    }
  };
};

// Test data factories
export const createTestData = {
  simpleInputMappings: () => ({
    testInput: {
      _type: 'StaticValue' as const,
      value: 'test value',
    }
  }),
  
  complexInputMappings: () => ({
    userInput: {
      _type: 'GraphInitialInputReference' as const,
      inputKey: 'userMessage',
    },
    previousResult: {
      _type: 'NodeOutputReference' as const,
      nodeId: 'previous-node',
      path: 'payload.result',
    },
    constantValue: {
      _type: 'GraphConstantReference' as const,
      constantKey: 'apiEndpoint',
    }
  }),
};

// Configure test behavior
beforeEach(() => {
  vi.clearAllMocks();
}); 