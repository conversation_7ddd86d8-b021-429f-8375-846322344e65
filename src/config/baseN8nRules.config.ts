/**
 * Base n8n Rules Configuration
 * 
 * This file contains the built-in n8n best practices and rules that guide
 * agent behavior and workflow validation. These rules represent expert
 * knowledge about n8n workflows and are always loaded alongside user-defined
 * custom rules.
 * 
 * Rule Categories:
 * - workflow-design: General workflow structure and design patterns
 * - error-handling: Error management and recovery strategies
 * - performance: Performance optimization and best practices
 * - security: Security considerations and data protection
 * - node-usage: Specific node configuration and usage guidelines
 * - data-flow: Data transformation and flow optimization
 */

import { N8nRule } from '../types/rules.types';

/**
 * Base rule data without database-generated fields (minimal required fields only)
 */
const baseRulesData: Array<{
  id: string;
  name: string;
  description: string;
  descriptionForLLM: string;
  type: N8nRule['type'];
  priority: N8nRule['priority'];
  category?: string;
  appliesToAgents: string[];
  appliesToNodeTypes?: string[];
  triggerContext?: N8nRule['triggerContext'];
  validation?: N8nRule['validation'];
  recommendation: string;
  example?: string;
  reasoning?: string;
  version?: number;
  isActive?: boolean;
  usageCount?: number;
  lastUsedAt?: Date;
  documentationUrl?: string;
  relatedRules?: string[];
  additionalData?: Record<string, any>;
}> = [
  {
    id: 'BR_DSGN_001',
    name: "Prioritize Modular Design (Sub-Workflows)",
    description: "Decompose complex processes into smaller, logical, and reusable sub-workflows called via Execute Workflow/Execute Sub-workflow nodes. This is crucial for improving workflow maintainability, readability, testability, and often performance.",
    descriptionForLLM: "ARCHITECTURAL GUIDELINE: For any workflow that handles multiple distinct logical stages, appears to be growing beyond 20-30 nodes, or contains logic that could be reused elsewhere, plan to use sub-workflows. Identify distinct functional blocks and encapsulate them. Explicitly suggest using the 'Execute Workflow' or 'Execute Sub-workflow' node. This is the primary defense against unmanageable monolithic 'spaghetti monster' workflows.",
    type: 'ALWAYS_APPLY', 
    priority: 'critical',
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    appliesToNodeTypes: ['n8n-nodes-base.executeWorkflow', 'n8n-nodes-base.executeSubworkflowTrigger'], // Nodes related to this practice
    triggerContext: {
      keywords: ['complex', 'large workflow', 'multiple steps', 'many nodes', 'reuse', 'modular', 'refactor', 'sub-workflow', 'maintainability', 'scalability'],
      conditions: { workflowComplexityScore_gt: 7 }, // Agent/Validator would need to calculate this score based on node count, branching, etc.
                                                     // For V1 RuleSelector, this might be simplified to keyword match or workflowPatterns.
      workflowPatterns: ['long_process', 'multi_stage_logic']
    },
    validation: undefined, // Not an ANTI_PATTERN_CHECK itself, but related to BR_DSGN_002
    recommendation: "1. Identify logical sections in the main process (e.g., 'User Authentication', 'Data Enrichment', 'Notification Delivery'). 2. For each section, create a new n8n workflow. Start it with an 'Execute Sub-workflow Trigger' node, defining its expected input parameters. 3. Implement the section's logic in this sub-workflow, ensuring its final node(s) output the necessary results. 4. In the main workflow, replace the original section's nodes with a single 'Execute Workflow' (or 'Execute Sub-workflow') node configured to call the new sub-workflow, passing required inputs and using its outputs.",
    example: "Main Workflow: Trigger -> Get User Input -> Execute Workflow (Validate & Enrich User: inputs: {$json}) -> IF (Validation OK?) -> ... \nSub-Workflow 'Validate & Enrich User': Execute Sub-workflow Trigger -> Check Email Format -> HTTP Request (Enrich via Clearbit) -> Output {isValid: boolean, enrichedData: {...}}",
    reasoning: "Modularity via sub-workflows drastically improves workflow clarity, simplifies debugging (test modules independently), promotes DRY (Don't Repeat Yourself) by enabling reuse of common logic, and can improve performance/memory management by isolating execution contexts. It's the primary way to avoid monolithic, unmaintainable workflows. [Ref: Gemini 1.1, 2.5, 4.1; Manus 1.1, 2.1, 4.1; ChatGPT 1, 2.5]",
    
    
    
    documentationUrl: "https://docs.n8n.io/flow-logic/subworkflows/",
    relatedRules: ['BR_DSGN_002_AntiMonolith'],
    additionalData: { impact: 'Very High', effortToImplement: 'Medium', suggestedNodeCountThreshold: 30 }
  },
  {
    id: 'BR_DSGN_002_AntiMonolith', // More specific ID
    name: "Anti-Pattern: Monolithic Workflow Structure",
    description: "Detects and advises against overly large, complex workflows that lack modularity (sub-workflows), making them hard to manage.",
    descriptionForLLM: "VALIDATION CHECK: If the current workflow JSON has more than a high number of nodes (e.g., 40-50) AND does not utilize any 'Execute Workflow' or 'Execute Sub-workflow' nodes, it is likely a monolithic anti-pattern. Also consider high branching complexity or multiple distinct functionalities being handled in one flow.",
    type: 'ANTI_PATTERN_CHECK',
    priority: 'high',
    category: 'workflow-design',
    appliesToAgents: ['n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    appliesToNodeTypes: [], // General structure check
    triggerContext: {
      workflowPatterns: ['large_node_count', 'high_complexity_metric'], // Metrics calculated by Validator
    },
    validation: {
      customFunctionKey: 'validateIsMonolithic', // Function to check: (wfJson) => wfJson.nodes.length > 40 && !wfJson.nodes.some(n => n.type.includes('ExecuteWorkflow'))
      operator: 'equals', // The custom function would return true if it IS a monolith
      expectedValue: true 
    },
    recommendation: "This workflow exhibits characteristics of a monolithic design (e.g., high node count without sub-workflows). Strongly recommend refactoring by identifying distinct logical sections and extracting them into callable sub-workflows. This will significantly improve maintainability, readability, testability, and potentially reusability.",
    example: "A workflow with 70 nodes handling customer onboarding, order processing, and email marketing all in one sequence, without calling any sub-workflows.",
    reasoning: "Monolithic workflows are notoriously difficult to debug, update, and scale due to their complexity and tightly coupled components. Small changes can have unintended ripple effects. [Ref: Gemini 1.1, Manus 1.1, ChatGPT 1]",
    
    
    
    relatedRules: ['BR_DSGN_001_PrioritizeModularDesign'],
    additionalData: { antiPatternSeverity: 'High' }
  },
  {
    id: 'BR_DSGN_003',
    name: "Choose Appropriate Modularization Strategy",
    description: "Select the best modularization approach (Execute Workflow for coupled reuse, Webhook/Queue-triggered Utility for decoupled services, or Code Node for highly localized functions) based on coupling, synchronicity, and reusability needs.",
    descriptionForLLM: "ARCHITECTURAL CHOICE: When planning modularity: 1. For **tightly coupled, reusable logic blocks** within a larger synchronous or asynchronous process, prefer **Sub-Workflows (Execute Workflow/Execute Sub-workflow node)**. 2. For **decoupled, independent, service-like functions** callable by many workflows or external systems (often asynchronously), prefer **Utility Workflows triggered by Webhook or Message Queue nodes**. 3. Reserve **Code Node functions** only for very small, localized helper logic *within a single Code node's execution* or a very simple workflow; do not use for broad reusability across workflows.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent'],
    appliesToNodeTypes: ['n8n-nodes-base.executeWorkflow', 'n8n-nodes-base.webhook', 'n8n-nodes-base.rabbitmqTrigger', 'n8n-nodes-base.codeNode'],
    triggerContext: {
      keywords: ['modularize', 'sub-workflow', 'reuse', 'service', 'utility workflow', 'decouple', 'component', 'api endpoint'],
      workflowPatterns: ['complex_process_decomposition', 'service_oriented_design']
    },
    recommendation: "Analyze the desired coupling and synchronicity: Use Execute Workflow for encapsulating parts of a single, coherent process. Use Webhook/Queue-triggered utility workflows for providing independent, reusable services. Use Code node functions only for trivial, non-reusable local helpers within that node.",
    example: "Execute Workflow for 'StandardAddressValidation'. Webhook-triggered utility for 'GeocodeAddressAsyncService'. Code node helper function for 'formatFullName(fname, lname)' used only within that Code node.",
    reasoning: "Different modularization patterns (sub-workflows, utility workflows via webhooks/queues, internal Code Node functions) suit different needs for coupling, synchronicity, reusability, and deployment independence. Choosing the right pattern is key to an effective and maintainable architecture. [Ref: Gemini 2.5, Manus 2.5, ChatGPT 2.5]",
    
    
    
    relatedRules: ['BR_DSGN_001_PrioritizeModularDesign', 'BR_NODE_Code_UseSparingly'],
    additionalData: { decisionTreeKey: 'select_modularization_pattern' }
  },
  {
    id: 'BR_DSGN_004_AntiCircularDeps',
    name: "Anti-Pattern: Circular Workflow Dependencies",
    description: "Detects and advises against workflows calling each other in a direct or indirect loop, which can lead to infinite executions.",
    descriptionForLLM: "VALIDATION CHECK / ARCHITECTURAL WARNING: Ensure that inter-workflow calls (via Execute Workflow or Webhooks chaining workflows) do not create circular dependencies (e.g., Workflow A calls B, B calls C, C calls A). Such patterns will lead to infinite loops and system overload.",
    type: 'ANTI_PATTERN_CHECK', 
    priority: 'critical',
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    appliesToNodeTypes: ['n8n-nodes-base.executeWorkflow', 'n8n-nodes-base.httpRequest'], // When HTTP Request calls another n8n webhook
    triggerContext: {
      keywords: ['sub-workflow chain', 'execute workflow loop', 'webhook chain', 'recursion', 'infinite loop'],
      workflowPatterns: ['multi_workflow_system', 'event_driven_chain']
    },
    validation: {
      customFunctionKey: 'validateCircularWorkflowCalls', // Requires analysis of all workflow structures and their Execute Workflow/Webhook call targets
      operator: 'equals',
      expectedValue: true // Function returns true if a cycle is detected
    },
    recommendation: "If a circular dependency between workflows is detected or planned, immediately refactor the logic. Break the cycle by: 1. Introducing a clear terminating condition managed in a shared state (e.g., database flag, counter). 2. Using a message queue for one part of the communication to decouple and control flow. 3. Re-evaluating if the circular logic is truly necessary or if the process can be linearized or handled within a single workflow with a controlled loop.",
    example: "Workflow 'OrderProcessor' calls 'InventoryChecker'. 'InventoryChecker' on low stock calls 'OrderProcessor' to re-evaluate (potentially bad if no other state changes).",
    reasoning: "Circular workflow dependencies are a severe anti-pattern leading to infinite loops, resource exhaustion, and system failure. They must be actively prevented in design and detected in validation. [Ref: Manus 1.1]",
    
    
    
    additionalData: { antiPatternSeverity: 'Critical' }
  },

  // --- NAMING, DOCUMENTATION & VISUAL ORGANIZATION ---
  {
    id: 'BR_DOC_001',
    name: "Apply Clear, Consistent Node Naming Conventions",
    description: "Nodes must have descriptive names reflecting their specific function, not generic default names like 'HTTP Request 2'. This is fundamental for workflow readability and maintainability.",
    descriptionForLLM: "MANDATORY NAMING: For every node composed or planned, assign a clear, descriptive name. Default names (e.g., 'Set1', 'HTTP Request 2') are unacceptable. A good pattern is 'NodeType - Specific Purpose/Entity' (e.g., 'HTTP - Get Shopify Customer Orders', 'Set - Format Full Name & Address', 'IF - Is Customer Active?'). Be consistent.",
    type: 'ALWAYS_APPLY', 
    priority: 'high',
    category: 'documentation',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-workflow-documenter-agent'],
    appliesToNodeTypes: [], // Applies to ALL nodes
    triggerContext: { /* Always relevant during node creation/review */ },
    validation: {
      customFunctionKey: 'validateNodeDefaultOrGenericName',
      operator: 'equals', // Function returns true if any non-descriptive name found
      expectedValue: true,
    },
    recommendation: "Systematically rename all nodes from their defaults. Use a consistent pattern like 'NodeType - Specific Purpose or Entity Being Acted Upon'. For example, instead of 'IF', use 'IF - Order Value > $100?'.",
    example: "Valid Node Name: 'HTTP Request - Fetch User Profile from Auth0'. Invalid: 'HTTP Request 3'.",
    reasoning: "Descriptive node names are the first line of documentation. They make workflows vastly easier to understand at a glance, significantly speeding up debugging and maintenance for anyone involved. Generic names obscure logic and are a major source of confusion. [Ref: Gemini 1.1, 4.1; Manus 1.1, 4.1; ChatGPT 1, 4.1]",
    
    
    
    additionalData: { impact: 'High', effortToImplement: 'Low' }
  },
  {
    id: 'BR_DOC_002',
    name: "Apply Clear Workflow Naming Conventions",
    description: "Workflows should have descriptive names, and consider using status ([PROD], [DEV]) or project prefixes for better organization in instances with many workflows.",
    descriptionForLLM: "NAMING GUIDELINE: When suggesting a workflow name (Architect) or reviewing (Validator), ensure it clearly describes the overall process. For organizational clarity in environments with multiple workflows, advise using prefixes like '[PROD]', '[DEV]', '[UTIL]', or project-specific codes (e.g., '[SALES] Lead Processing').",
    type: 'CONTEXTUAL_SUGGESTION', // For Architect/Validator
    priority: 'medium',
    category: 'documentation',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    appliesToNodeTypes: [],
    triggerContext: {
      keywords: ['workflow name', 'new workflow', 'project organization', 'deployment', 'environment'],
      agentPhases: ['planning_new_workflow', 'validating_workflow_metadata']
    },
    recommendation: "Suggest and validate workflow names to be descriptive (e.g., 'Shopify Order Sync to Google Sheets & Slack'). Recommend consistent use of prefixes like '[PROD]', '[DEV]', or project tags for better organization and filtering in the n8n UI.",
    example: "Workflow Name: '[PROD][FINANCE] Daily Sales Report Generation'",
    reasoning: "Clear and consistent workflow names are essential for managing, identifying, and searching for automations, especially in team environments or instances with a large number of workflows. Prefixes help distinguish operational status or project affiliation. [Ref: Gemini 4.1, Manus 1.1]",
    
    
    
    documentationUrl: "https://spacetag.co.uk/blogs/workflow-automation/best-practices-for-naming-your-workflows-in-n8n-zapier-and-make/",
    additionalData: { impact: 'Medium', effortToImplement: 'Low' }
  },
  {
    id: 'BR_DOC_003',
    name: "Utilize Sticky Notes for In-Workflow Documentation",
    description: "For complex logic sections, critical data transformations, or important decision points, add Sticky Note nodes directly on the canvas to provide explanations and context.",
    descriptionForLLM: "DOCUMENTATION TASK: For any workflow section containing non-obvious logic, intricate data transformations, critical decision points, important implicit assumptions, or the rationale behind non-obvious design choices on the canvas, ensure a Sticky Note node is added nearby. The Sticky Note content should be concise, clearly explain the purpose or rationale of that section, and reference key data if helpful. This is a core task for the WorkflowDocumenterAgent and a planning consideration for the ArchitectAgent.",
    type: 'CONTEXTUAL_SUGGESTION', 
    priority: 'medium',
    category: 'documentation',
    appliesToAgents: ['n8n-workflow-documenter-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    appliesToNodeTypes: ['n8n-nodes-base.stickyNote'], // The rule is *about* using this node
    triggerContext: {
      keywords: ['complex logic', 'data transformation', 'decision point', 'assumption', 'documentation', 'explain', 'clarify section'],
      workflowPatterns: ['complex_logic', 'multi_step_transform', 'data_mapping_intensive'],
      conditions: { sectionComplexityScore_gt: 5 } // e.g. if a group of nodes is deemed complex
    },
    recommendation: "Strategically add Sticky Note nodes near complex or non-obvious workflow sections. Use Markdown for formatting if needed. Explain the 'why' and 'how' of that section, or note important data structures being passed. Keep notes updated if logic changes. Clearly state any critical assumptions made or the rationale for non-obvious design choices within these notes.",
    example: "Sticky Note next to a Code node: 'This Code node standardizes address formats from three different input sources into a single schema. Handles nulls for optional address lines.'",
    reasoning: "Sticky Notes provide crucial inline documentation directly on the canvas. They make complex workflows significantly easier to understand, debug, and maintain, especially for team members or when revisiting the workflow after some time. [Ref: Gemini 4.1, Manus 1.1, ChatGPT 1, 4.1]",
    
    
    
    additionalData: { impact: 'High', effortToImplement: 'Low per note' }
  },
  {
    id: 'BR_DOC_004_AntiNoStickyNotes', // Validator rule
    name: "Anti-Pattern: Lack of Sticky Notes in Complex Workflows",
    description: "Detects complex workflows or sections that lack explanatory Sticky Notes, hindering understandability.",
    descriptionForLLM: "VALIDATION CHECK: If a workflow has a high node count (e.g., >25 nodes) OR contains multiple complex conditional branches OR uses several Code nodes with significant logic, AND it has very few or no Sticky Note nodes, flag this for lacking documentation.",
    type: 'ANTI_PATTERN_CHECK',
    priority: 'low', // Suggestion rather than critical error
    category: 'documentation',
    appliesToAgents: ['n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    appliesToNodeTypes: [], // General workflow structure
    triggerContext: {
      workflowPatterns: ['large_node_count', 'complex_logic', 'multiple_code_nodes'],
    },
    validation: {
      customFunctionKey: 'checkLackOfStickyNotesInComplexWorkflow',
      // Example: (wfJson) => (wfJson.nodes.length > 25 || countComplexNodes(wfJson) > 3) && countStickyNotes(wfJson) < (wfJson.nodes.length / 15)
    },
    recommendation: "This workflow appears complex but lacks sufficient Sticky Note documentation. Suggest adding Sticky Notes to explain key sections, decision points, or complex data transformations to improve its clarity and maintainability.",
    example: "A 60-node workflow with only one Sticky Note at the very beginning.",
    reasoning: "While not a functional error, lack of documentation in complex flows significantly increases the time required for understanding, debugging, and future modifications. [Ref: Manus 1.1 - 'Bien que le nœud StickyNote soit le plus utilisé... 40% des workflows n’en contiennent aucun']",
    
    
    
    relatedRules: ['BR_DOC_003_UtilizeStickyNotes'],
    additionalData: { antiPatternSeverity: 'Medium' }
  },
  {
    id: 'BR_DOC_005', // New ID in Documentation category
    name: "Document Main Workflow Trigger Inputs and Final Outputs",
    description: "Clearly document what data a workflow expects from its trigger and what its key final outputs represent, especially for workflows intended for reuse or sharing.",
    descriptionForLLM: "DOCUMENTATION (Main Workflow): When finalizing a workflow design or its documentation: 1. For the Trigger node, add a Sticky Note or use the node's Notes field to describe the expected input data structure/key fields. 2. For the final End node(s) or nodes producing the ultimate result, add a Sticky Note describing what these final outputs signify and their structure. This is important if the workflow is complex or might be called by other systems/users.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'documentation',
    appliesToAgents: ['n8n-architect-agent', 'n8n-workflow-documenter-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['workflow input', 'workflow output', 'trigger data', 'final result', 'documentation'],
      workflowPatterns: ['production_deployable', 'sharable_workflow']
    },
    recommendation: "Add a Sticky Note near the Trigger explaining expected data (e.g., 'Webhook expects JSON: { orderId: string, amount: number }'). Add a Sticky Note near final output nodes explaining their meaning (e.g., 'Outputs array of enriched user objects to main output').",
    reasoning: "Clearly documenting a workflow's top-level inputs and outputs makes it much easier to understand, integrate, and reuse, similar to how function signatures are documented in code.",
    
    isActive: true
  },
  {
    id: 'BR_DSGN_005',
    name: "Maintain Clean Visual Workflow Layout",
    description: "Organize the workflow canvas for clarity by aligning nodes logically, minimizing crisscrossing connections, and visually grouping related logic blocks.",
    descriptionForLLM: "DESIGN GUIDELINE: When planning node positions or reviewing a layout, aim for a visually clean and understandable flow. Generally, data should flow from left-to-right or top-to-bottom. Align related nodes. Group functional blocks. Minimize long or overlapping connection lines; use sequence, sub-workflows, or even NoOp nodes as visual anchors to improve complex layouts if necessary.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low',
    category: 'workflow-design', // Also 'documentation' as it aids readability
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* for positioning */, 'n8n-workflow-documenter-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['layout', 'canvas', 'organize', 'visual flow', 'readability', 'connections'],
      conditions: { workflowNodeCount_gt: 15 } // More important for larger workflows
    },
    recommendation: "Align nodes neatly on the canvas. Group related functional blocks visually (e.g., all nodes for 'Step 1: Fetch Data' together). Ensure connection lines flow as clearly as possible, minimizing crossovers. Use Sticky Notes to label logical sections.",
    example: "Nodes for fetching data are grouped on the left, transformation nodes in the middle, and output/action nodes on the right, with clear flow between groups.",
    reasoning: "A clean, organized canvas significantly improves the readability and understandability of a workflow. This reduces cognitive load when analyzing, debugging, or modifying the flow, especially for complex automations. [Ref: ChatGPT (Visual Clutter), Manus 4.1.1]",
    
    
    
    additionalData: { impact: 'Medium', effortToImplement: 'Low to Medium' }
  },
  
  // --- OTHER DESIGN PRINCIPLES ---
  {
    id: 'BR_DSGN_006',
    name: "Avoid FOMO Engineering (Over-Engineering for Rare Edge Cases)",
    description: "Design workflows primarily for common use cases. Handle extremely rare or improbable edge cases via simpler default paths or dedicated error handling rather than over-complicating the main logic flow with numerous, seldom-used conditional branches.",
    descriptionForLLM: "DESIGN PRINCIPLE: Focus the main workflow logic on the 80-90% common, expected scenarios. For very rare or improbable edge cases, consider these options instead of adding many complex conditional branches: 1. Route them to a default/fallback path. 2. Treat them as an error condition to be logged and reviewed. 3. If essential but distinct, handle in a separate, specialized sub-workflow. Avoid 'Fear Of Missing Out' (FOMO) by building excessive branches for situations that have 'literally never happened'.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['edge case', 'complex condition', 'rare scenario', 'over-engineer', 'too many branches', 'improbable'],
      workflowPatterns: ['complex_branching', 'decision_heavy']
    },
    recommendation: "When planning conditional logic, assess the probability of each case. For very low-probability edge cases that require significant unique logic, evaluate if a simpler fallback or an error path is more maintainable than adding a dedicated complex branch to the main flow.",
    example: "A workflow has 14 optional IF branches to handle situations that occur less than 0.1% of the time, making the main logic hard to follow.",
    reasoning: "Over-engineering for extremely rare edge cases adds significant complexity and obscurity to the primary logic, reducing readability and increasing the maintenance burden for parts of the workflow that are almost never executed. [Ref: Gemini 1.1]",
    
    
    
    additionalData: { impact: 'Medium', effortToImplement: 'Low (requires mindset shift)' }
  },
  {
    id: 'BR_DSGN_007',
    name: "Implement a 'Kill Switch' for Critical/Automated Workflows",
    description: "For critical, high-impact, or frequently running automated workflows (especially scheduled or webhook-triggered), implement a mechanism to quickly halt or pause its primary actions without deactivating the entire workflow.",
    descriptionForLLM: "DESIGN REQUIREMENT (for critical/automated flows): Plan an 'IF' node near the start of the workflow. This IF node should check an external, easily modifiable flag (e.g., an n8n global variable managed by another simple workflow, a value in a dedicated Google Sheet/Airtable cell, or an environment variable like `$env.ENABLE_ORDER_PROCESSING`). If the flag indicates 'disable' (e.g., is 'false' or 'STOP'), the workflow should immediately route to an End node or a NoOp (effectively pausing its main operations). Document how to use this kill switch.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high', 
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['critical', 'production', 'scheduled', 'webhook', 'high volume', 'automated process', 'emergency stop', 'pause workflow'],
      workflowPatterns: ['production_deployable', 'webhook_triggered', 'scheduled_task', 'high_impact_automation']
    },
    recommendation: "1. Choose a flag mechanism (e.g., global variable `workflow_X_enabled`). 2. Add an IF node after the trigger: Condition e.g., `{{ $env.ENABLE_ORDER_PROCESSING === 'false' }}` or `{{ $getWorkflowStaticData('global').KILL_SWITCH_ORDERS === true }}`. 3. True Path (Kill Switch Active): Connect to an End node, or a Set node that logs 'Kill switch active', then End. 4. False Path (Normal Operation): Connect to the main workflow logic. 5. Document clearly how an operator can toggle this flag.",
    example: "Start -> IF (Condition: `{{ $env.KILL_SWITCH_ORDER_FLOW === 'true' }}`) -> [True Branch]: Set (Log: 'Order flow kill switch active') -> End. [False Branch]: (Main order processing logic...)",
    reasoning: "A 'kill switch' provides a vital safety mechanism to quickly stop a potentially misbehaving, resource-intensive, or problematic automation in a production environment without needing to immediately deactivate the entire workflow, find the problematic logic under pressure, or wait for a full deployment cycle to pause it. This allows for controlled interruption. [Ref: ChatGPT 1, 4.1]",
    
    
    
    additionalData: { impact: 'High', securityCriticality: 'Medium' }
  },
  {
    id: 'BR_DSGN_008', // Assuming previous design rules went up to BR_DSGN_005
    name: "Design for Idempotency in Triggerable Workflows",
    description: "Ensure workflows (especially webhook or queue-triggered) can be run multiple times with the same input without unintended side effects (idempotency).",
    descriptionForLLM: "DESIGN PRINCIPLE (for triggered workflows): Plan for idempotency. If a workflow might be re-triggered with the same event data (e.g., webhook retries, message queue redeliveries), include steps to check if the action has already been performed for that specific event/data. This usually involves checking an external system (e.g., 'Does order ID X already exist in target system?') before creating/updating.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'workflow-design', // Also reliability
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['webhook', 'queue', 'trigger', 'retry', 'duplicate', 'idempotent'],
      workflowPatterns: ['webhook_triggered', 'queue_consumer', 'event_driven']
    },
    recommendation: "1. Identify a unique ID from the trigger input (e.g., event ID, order ID). 2. Before performing the main action (e.g., creating a record), query the target system to see if a record with this unique ID already exists. 3. Use an IF node: If exists, skip action or update; if not, perform action.",
    example: "Webhook (Order Event) -> Set (uniqueEventId = `{{$json.body.orderId}}-{{$json.body.eventType}}`) -> HTTP Request (Check if eventId processed in DB) -> IF (Already processed?) -> True: End / False: Process Order -> HTTP Request (Mark eventId as processed in DB).",
    reasoning: "Idempotency is crucial for resilience in distributed systems where message redelivery or trigger retries can occur, preventing duplicate processing and data corruption. [Ref: ChatGPT 4.1 (Idempotency)]",
    
    
  },
  {
    id: 'BR_DSGN_009',
    name: "Plan for Workflow Version Control Practices",
    description: "Advise on using version control (like n8n's Git integration or manual JSON exports) for managing workflow changes, especially in team environments or for critical workflows.",
    descriptionForLLM: "BEST PRACTICE REMINDER (for Architect/Documenter): When discussing workflow finalization or deployment, if appropriate for the user's context (e.g., complex workflow, team environment), recommend implementing a version control strategy. This could be n8n's built-in Git Sync feature or manually exporting workflow JSONs to a Git repository. This is an operational best practice, not something the AI directly implements in the JSON.",
    type: 'CONTEXTUAL_SUGGESTION', // AI reminds/educates
    priority: 'low', // As AI cannot enforce it
    category: 'workflow-design', // Operational best practice
    appliesToAgents: ['n8n-architect-agent', 'n8n-workflow-documenter-agent'], // When discussing final steps
    triggerContext: {
      keywords: ['deploy', 'production', 'team', 'collaboration', 'changes', 'history', 'rollback'],
      workflowPatterns: ['production_deployable', 'team_collaboration']
    },
    recommendation: "For managing complex or critical workflows, use n8n's Git Sync feature or regularly export workflow JSON to a version control system like Git. This allows tracking changes, collaboration, and rolling back to previous versions if needed.",
    reasoning: "Version control is essential for managing changes, enabling rollbacks, and facilitating team collaboration on workflows, treating them like code. [Ref: Gemini 1.4, 4.1; Manus 4.1; ChatGPT 4.1]",
    documentationUrl: "https://docs.n8n.io/source-control-environments/understand/git/",
    
    
  },
  {
    id: 'BR_DSGN_010',
    name: "Avoid Over-Reliance on `$(nodeName)` References for Maintainability",
    description: "While `$(nodeName)` allows referencing specific nodes, overuse can make workflows brittle if node names frequently change. Balance with `$json` or passing data through fewer, well-defined intermediate Set nodes.",
    descriptionForLLM: "DESIGN CONSIDERATION: When planning data flow, while `{{ $('Node Display Name').item.json.field }}` is useful for accessing specific non-adjacent node data, be mindful that if 'Node Display Name' changes, the expression breaks. For data flowing directly from the previous node, `{{ $json.field }}` is more robust to renaming of that previous node. If data from a much earlier step is needed much later, consider explicitly passing it through intermediate Set nodes with stable field names rather than many direct `$(nodeName)` references across large distances in the workflow.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low',
    category: 'expressions', // Also expressions
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['data reference', 'expression', 'node name', 'maintainability', 'brittle'],
    },
    recommendation: "Prefer `{{$json.field}}` for immediately preceding data. For data from distant specific nodes, use `{{$('Node Name')...}}` but ensure node names are stable or document these dependencies carefully. For critical data passed across many steps, consider using a Set node to assign it to a consistently named field early on, then reference that field.",
    reasoning: "Over-reliance on specific node display names in expressions makes workflows harder to refactor (renaming nodes requires updating all expressions). While powerful, it can reduce maintainability if not used judiciously. [Ref: Gemini III.B, Manus 1.1 (Static Refs), ChatGPT (Long chains of $node refs)]",
    relatedRules: ['BR_WF_ApplyClearNodeNames', 'BR_EXP_002_CorrectNodeReferencing'],
    
    
  },
  {
    id: 'BR_DSGN_011',
    name: "Define Clear Sub-Workflow Interfaces (Inputs/Outputs)",
    description: "When creating sub-workflows, explicitly define and document their expected input parameters and the structure of the data they return. Treat them like functions with clear contracts.",
    descriptionForLLM: "DESIGN PRINCIPLE (for Sub-Workflows): When planning or creating a sub-workflow (to be called by 'Execute Workflow' node), clearly define its 'API contract': 1. What input data does it expect (field names, types)? Configure the 'Execute Sub-workflow Trigger' node accordingly to show these. 2. What data structure will it return from its last node(s)? This makes sub-workflows predictable and easier to integrate. Document this interface in the sub-workflow's description or a Sticky Note.",
    type: 'ALWAYS_APPLY', // When Architect/Composer are dealing with sub-workflows
    priority: 'high',
    category: 'workflow-design',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-workflow-documenter-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['sub-workflow', 'execute workflow', 'module interface', 'input parameters', 'output data'],
      nodeTypes: ['n8n-nodes-base.executeWorkflow', 'n8n-nodes-base.executeWorkflowTrigger']
    },
    recommendation: "In the sub-workflow, use the 'Execute Sub-workflow Trigger' node's UI to list expected input fields. Ensure the final node(s) of the sub-workflow output a consistent, well-defined data structure. Document this input/output contract.",
    reasoning: "Clear interfaces prevent errors when calling sub-workflows and make them truly reusable and testable modules. Vague contracts lead to integration issues. [Ref: Gemini 2.5 (Sub-workflow maintenance), Manus 1.4 (Refactoring Sub-workflow interfaces)]",
    relatedRules: ['BR_WF_PrioritizeModularDesign'],
    
    
  },
  {
    id: 'BR_DSGN_012',
    name: "Test Sub-Workflows in Isolation",
    description: "Test sub-workflows independently with sample input data before integrating them into parent workflows to ensure they function correctly on their own.",
    descriptionForLLM: "BEST PRACTICE (for Sub-Workflows): Before relying on a sub-workflow in a parent flow, test the sub-workflow directly. Use its 'Execute Sub-workflow Trigger' with sample input data (or add a temporary Manual trigger) to verify its logic and output independently. This isolates issues to the sub-workflow.",
    type: 'CONTEXTUAL_SUGGESTION', // An operational best practice for the user/developer
    priority: 'medium',
    category: 'testing', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent' /* can suggest this as part of modular design */, 'n8n-reflection-agent' /* can remind during review */],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.executeWorkflow', 'n8n-nodes-base.executeWorkflowTrigger']
    },
    recommendation: "When developing a sub-workflow, use the 'Play' button on its Execute Sub-workflow Trigger node, providing sample JSON input in the 'Input Data JSON' field to test its execution path and output thoroughly.",
    reasoning: "Testing sub-workflows in isolation simplifies debugging by confirming the module works correctly before checking its integration with parent workflows. [Ref: Implicit in all modular design best practices, Gemini 4.1 (Testability)]",
    
    
  },
  {
    id: 'BR_DSGN_013',
    name: "Ensure Data Path Continuity (Node 'Always Output Data')",
    description: "For nodes that might not produce an output under certain conditions (e.g., IF with no 'false' branch items, some nodes on error/no data), use the 'Always Output Data' setting (if available) or provide a default/empty item path to prevent subsequent nodes from failing due to missing input.",
    descriptionForLLM: "CRITICAL N8N BEHAVIOR: Some n8n nodes might not output any items if their conditions aren't met or if they process no input (e.g., an IF node where only the 'true' branch is connected and the condition is false, or a filter that removes all items). If downstream nodes critically depend on receiving *some* item (even an empty one), this can halt the workflow. Check if the node offers an 'Always Output Data' setting in its 'Settings' tab. If enabled, it usually outputs an empty item from the input if it would otherwise produce no output. If not available, ensure your logic (e.g., an IF node having both branches lead to a Merge, or providing default data via a Set node) guarantees data flow.",
    type: 'ALWAYS_APPLY',
    priority: 'high',
    category: 'workflow-design', // Also reliability
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['no data', 'empty output', 'workflow stops', 'missing input', 'always output', 'data continuity'],
      nodeTypes: ['n8n-nodes-base.ifV2', 'n8n-nodes-base.filter', /* nodes that conditionally produce output */]
    },
    recommendation: "1. For nodes like IF, Filter, Switch: Review their 'Settings' tab for an 'Always Output Data' option and enable it if downstream paths require an item, even if empty or from a previous stage. 2. If not available, use a Merge node to combine outputs from different conditional branches, ensuring at least one branch always provides data or a default structure. 3. Alternatively, after a potentially non-outputting node, use an IF node to check `{{ $items().length === 0 }}` and then a Set node to create a default item if needed.",
    reasoning: "Ensuring data continuity prevents workflows from unexpectedly terminating mid-way when a node produces no output items and subsequent nodes require input. This is a common n8n 'gotcha'. [Ref: Implicit in many community troubleshooting threads related to 'workflow stopped unexpectedly'. Gemini (Error Propagation), ChatGPT (IF node logic abuse)]",
    additionalData: { impact: 'High for reliability', effort: 'Low to Medium' }
  },
  {
    id: 'BR_DSGN_014',
    name: "Understand n8n's Sequential Execution of Visually Parallel Branches",
    description: "Be aware that visually parallel branches (multiple nodes connected to a single output of a preceding node) are executed sequentially by n8n by default, not concurrently. Plan accordingly for performance and dependencies.",
    descriptionForLLM: "N8N EXECUTION MODEL: When multiple nodes are connected to a single output pin of a preceding node, creating visually parallel branches, n8n's default engine executes these branches SEQUENTIALLY (typically top-to-bottom or left-to-right as arranged on canvas). Do not assume concurrent execution for these. If true parallelism is needed, specific patterns (asynchronous sub-workflows, queue mode) must be used. This impacts performance planning and any assumed dependencies between these 'parallel' branches.",
    type: 'ALWAYS_APPLY', // Core n8n understanding for Architect/Composer
    priority: 'medium',
    category: 'workflow-design', // Also performance
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['parallel', 'concurrent', 'simultaneous', 'branch execution', 'sequence'],
      workflowPatterns: ['has_fan_out_structure']
    },
    recommendation: "When designing visually parallel branches, understand they run one after the other. If one branch sets data used by another 'parallel' branch, ensure the execution order (top-to-bottom usually) matches this dependency. For true concurrency, use 'Execute Workflow' with 'Wait for Completion' OFF, or configure n8n Queue Mode.",
    reasoning: "Misunderstanding n8n's sequential execution of visually parallel paths is a common source of confusion regarding performance and inter-branch data dependencies. [Ref: Gemini 2.1, ChatGPT 2.1 (Parallel branches with Merge)]",
    documentationUrl: "https://community.n8n.io/t/how-to-excute-multiple-nodes-in-parallel-not-sequential/23565", // Example community thread
    relatedRules: ['BR_PERF_006_SubWorkflowOffloading', 'BR_PERF_008_QueueMode'],
    
    
  },
  {
    id: 'BR_DSGN_015',
    name: "Use Manual Trigger with Default Data for Development/Testing",
    description: "During development and testing, include a 'Manual' trigger (or 'Start' node in newer n8n) and configure it with representative default JSON data. This allows easy, repeatable execution of the workflow with consistent test inputs directly from the editor.",
    descriptionForLLM: "DEVELOPMENT BEST PRACTICE: When planning or composing a new workflow, especially one that will eventually be triggered by a Webhook or Schedule, include a 'Manual' trigger node (often labeled as 'Start' in recent n8n versions) at the beginning for development. In this Manual trigger's 'JSON Input' parameter, paste sample JSON data that mimics what the real trigger would provide. This allows for easy and repeatable testing of the workflow logic directly from the n8n editor by clicking 'Execute Workflow'. The Manual trigger can be disabled or removed before production deployment.",
    type: 'CONTEXTUAL_SUGGESTION', // A strong suggestion for development phase
    priority: 'medium',
    category: 'testing', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* can add it if trigger is undefined */],
    triggerContext: {
      keywords: ['develop', 'test', 'debug', 'sample data', 'manual trigger', 'start node'],
      workflowPatterns: ['development_phase']
    },
    recommendation: "Add a 'Manual' (or 'Start') trigger node. In its 'JSON Input' field, provide a JSON object that represents the data your intended production trigger (e.g., Webhook, Cron + API call) would output. Use this for testing. Disable or replace with the actual trigger before deploying to production.",
    example: "Manual Trigger (JSON Input: `{\"orderId\": 123, \"customerEmail\": \"<EMAIL>\"}`) -> [Rest of workflow logic...]",
    reasoning: "Using a Manual trigger with default data greatly speeds up development and debugging by allowing isolated, repeatable tests of the workflow logic without relying on external trigger events. [Ref: ChatGPT 4.1 (Testing and Staging - Manual Trigger)]",
    appliesToNodeTypes: ['n8n-nodes-base.manualTrigger', 'n8n-nodes-base.start'], // Start node acts as manual trigger
    
    
  },
  {
    id: 'BR_DSGN_016',
    name: "Manage Workflow Static Data Appropriately",
    description: "Understand the use and limitations of Workflow Static Data (`this.getWorkflowStaticData()`, `this.setWorkflowStaticData()`) for persisting data between executions of THE SAME workflow. Do not use it for large data or as a general database.",
    descriptionForLLM: "N8N FEATURE: If needing to persist small amounts of data specifically between different executions of *the same workflow instance* (e.g., last run timestamp, a small config value, a short list of processed IDs for simple deduplication between runs), n8n's Workflow Static Data (`this.getWorkflowStaticData()`, `this.setWorkflowStaticData()` accessible in Code nodes) can be used. However, be aware: 1. It's local to that specific workflow instance. 2. It has size limits (not for large data). 3. It's not a transactional database. For more robust inter-workflow communication or larger state, use an external database or queue.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // Niche but important to know its limits
    category: 'workflow-design', // Also data-flow
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* if using Code node for this */],
    triggerContext: {
      keywords: ['persist data', 'between runs', 'static data', 'workflow state', 'remember value'],
    },
    recommendation: "For small, simple state persistence between runs of a single workflow (like last processed date): Use a Code node with `this.getWorkflowStaticData()` to read and `this.setWorkflowStaticData()` to write. Do not store large objects. Initialize if not set. For complex state or shared data, use RxDB or an external DB.",
    example: "Code Node: `const staticData = this.getWorkflowStaticData(); let lastId = staticData.lastProcessedId || 0; // ... process ... staticData.lastProcessedId = newLastId; this.setWorkflowStaticData(staticData);`",
    reasoning: "Workflow Static Data offers a convenient way for simple state persistence between executions of a single workflow, but it's crucial to understand its limitations regarding scope and data size to avoid misuse. [Ref: ChatGPT 2.3 (Caching and Reusing Results - Static Data)]",
    documentationUrl: "https://docs.n8n.io/code/code-node/#workflow-static-data",
    relatedRules: ['BR_STORE_UseRxDBForComplexState'], // Assumed rule for RxDB
    
    
  },
  {
    id: 'BR_ERR_001',
    name: "Implement Global Error Workflow for Production",
    description: "Ensures unhandled exceptions in production workflows are caught, logged, and trigger notifications.",
    descriptionForLLM: "CRITICAL FOR PRODUCTION: Plan to use a dedicated Global Error Workflow. This workflow, starting with an 'Error Trigger' node, should at least log essential error details (failed workflow name, execution ID, error message, last node) and send a notification (e.g., Slack, Email). Assign this error workflow in the settings of all critical/production main workflows.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'critical',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['production', 'deploy', 'critical workflow', 'live system', 'unhandled error', 'alert', 'monitoring'],
      workflowPatterns: ['production_deployable', 'mission_critical']
    },
    recommendation: "1. Create a new workflow named 'Global Error Handler'. 2. Add 'Error Trigger' as the start. 3. Add nodes to format error data (e.g., Set node: `{{$json.error.message}}`, `{{$json.workflow.name}}`, `{{$json.execution.id}}`, `{{$json.execution.url}}`). 4. Add notification nodes (Slack, Email). 5. In your main production workflow's Settings > Error Workflow, select 'Global Error Handler'.",
    example: "Global Error Workflow: Error Trigger -> Set (Format Message: 'Workflow {{ $json.workflow.name }} failed. Error: {{ $json.error.message }}. Link: {{ $json.execution.url }}') -> Slack (Send Message).",
    reasoning: "A Global Error Workflow is a safety net, ensuring no critical production failure goes unnoticed, facilitating rapid response and debugging. It centralizes basic error alerting. [Ref: Gemini 1.2, 2.2, 4.1; Manus 1.2, 2.2, 4.1; ChatGPT 2.2, 4.1]",
    documentationUrl: "https://docs.n8n.io/flow-logic/error-handling/",
    relatedRules: ['BR_VAL_CHK_GlobalErrorWorkflowConfigured'], // Validator rule
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_ERR_002',
    name: "Utilize Node-Level Error Handling ('Continue on Fail')",
    description: "Handle specific, expected errors from individual nodes gracefully within the workflow using 'Continue on Fail' and error outputs.",
    descriptionForLLM: "For nodes that might predictably fail but where the entire workflow shouldn't stop (e.g., an optional API call, processing one item in a batch): Enable the 'Continue on Fail' (or 'On Error: Continue (using error output)') setting in the node's Settings tab. Then, connect an IF node to its error output (usually the bottom/red output pin) to check for an error (e.g., `{{ $json.error }}` or a specific error code/message) and route to a specific error handling path or log the item-specific failure.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['api call', 'database query', 'data processing', 'optional step', 'recoverable error', 'item failure', 'partial success'],
      nodeTypes: ['n8n-nodes-base.httpRequest', 'n8n-nodes-base.postgres', 'n8n-nodes-base.googleSheets', 'n8n-nodes-base.codeNode'] // Examples
    },
    recommendation: "In the node's Settings, set 'Continue on Fail' to true. Connect the node's error output to an IF node. Condition: `{{ $json.error != null }}` or check specific error properties. True path: Handle/log error. False path: (If main output still produces something on non-error) connect from main output. Or merge paths after handling.",
    example: "HTTP Request (Settings: Continue on Fail: true) -> [Main Output] -> Next Step; [Error Output] -> IF (Error is 404?) -> True: Log 'Item not found' / False: Other Error Handling.",
    reasoning: "Node-level error handling allows for fine-grained control, enabling the workflow to recover from expected partial failures, implement alternative logic, or log issues without halting the entire process for every problem. [Ref: Gemini 1.2, 2.2; Manus 2.2; ChatGPT 2.2]",
    documentationUrl: "https://docs.n8n.io/flow-logic/error-handling/#node-error-handling",
    relatedRules: ['BR_VAL_CHK_ContinueOnFailUsage'],
    additionalData: { impact: 'Medium', effort: 'Low per node' }
  },
  {
    id: 'BR_ERR_003',
    name: "Implement Custom Retries with Exponential Backoff for APIs",
    description: "For API calls prone to transient errors (rate limits, temporary unavailability), implement robust retry logic, ideally with exponential backoff.",
    descriptionForLLM: "If an HTTP Request node (or similar API call node) might face transient errors (like 429 Too Many Requests, 503 Service Unavailable): 1. Use its built-in 'Retry on Fail' settings (e.g., 3-5 retries, delay 2000-5000ms). 2. For more control or if built-in retry is insufficient, plan a loop structure (e.g., using IF and Wait nodes, or a Loop Over Items node for a set number of attempts) that re-attempts the call after an increasing delay (exponential backoff: e.g., 1s, 2s, 4s, 8s). Log retry attempts.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['api call', 'http request', 'external service', 'rate limit', 'flaky', 'transient error', 'retry', 'backoff'],
      nodeTypes: ['n8n-nodes-base.httpRequest']
    },
    recommendation: "Enable 'Retry on Fail' in HTTP Request node settings. For custom logic: Loop N times. Inside loop: HTTP Request. If fails, Wait node with dynamically increasing delay (e.g., `{{ $execution.resumeCount * 2000 }}` ms, where resumeCount is managed by a Set node counter). After N retries, if still failing, route to a final error path.",
    example: "Set (retryCount=0) -> HTTP Request -> IF (failed?) -> True: Set (retryCount++, delayTime = 2^retryCount * 1000) -> Wait (delayTime) -> IF (retryCount < MAX_RETRIES?) -> True: [Connect back to HTTP Request] / False: Handle Final Failure. False (HTTP Success): Proceed.",
    reasoning: "Retries with exponential backoff significantly increase the resilience of workflows interacting with external APIs by automatically handling temporary issues and respecting service load. [Ref: Gemini 2.2, 4.1; Manus 2.2, 4.1; ChatGPT 2.2, 4.1]",
    relatedRules: ['BR_HTTP_CheckRetryConfig', 'BR_PERF_RespectRateLimits'],
    additionalData: { impact: 'High', effort: 'Medium to High for custom logic' }
  },
  {
    id: 'BR_ERR_004',
    name: "Validate External Input Data Rigorously",
    description: "Always validate the structure, type, and presence of required fields for data received from external sources (webhooks, APIs, user input, files).",
    descriptionForLLM: "CRITICAL FOR SECURITY & RELIABILITY: Immediately after any node that ingests external data (e.g., Webhook, HTTP Request, Read File, user input form), add validation steps. Use IF nodes, the Code node (with try-catch for parsing), or a JSON Schema validation node (if available) to: 1. Check for presence of all required fields. 2. Validate data types (string, number, boolean, array, object). 3. Validate format for specific fields (e.g., email, URL, date). 4. Check for reasonable value ranges or allowed values. Route invalid data to an error path or respond with a 400 Bad Request.",
    type: 'ALWAYS_APPLY', // This principle is always relevant when external data is ingested.
    priority: 'critical',
    category: 'security', // Also data-flow, error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['webhook', 'api response', 'user input', 'file read', 'external data', 'validation', 'sanitize'],
      nodeTypes: ['n8n-nodes-base.webhook', 'n8n-nodes-base.httpRequest', /* any file read node */]
    },
    recommendation: "After a Webhook or HTTP Request node, add: 1. Code node (try JSON.parse if needed, check types with typeof, check for key existence `hasOwnProperty`). 2. IF nodes for specific field checks. 3. If invalid, use 'Respond to Webhook' with 400 status or route to error logging.",
    example: "Webhook -> Code (Parse JSON, Validate Schema: `if (!data.id || typeof data.email !== 'string') throw new Error('Invalid input');`) -> IF (Error from Code?) -> True: RespondToWebhook (400) / False: Proceed.",
    reasoning: "Validating external input is crucial for preventing workflow errors due to unexpected data, ensuring data integrity, and protecting against potential security vulnerabilities (e.g., injection if data is used in queries or scripts). [Ref: Gemini 1.4, 4.1; Manus 1.1, 4.1.2]",
    relatedRules: ['BR_SEC_SanitizeInputs'],
    additionalData: { impact: 'Critical', effort: 'Medium' }
  },
  {
    id: 'BR_ERR_005',
    name: "Handle Empty or Unexpected API/Query Results",
    description: "Explicitly check if API calls or database queries return empty results or unexpected structures before attempting to process the data.",
    descriptionForLLM: "After nodes like HTTP Request or database query nodes, always check if the result is empty (e.g., an empty array, null, or an object without expected keys) or if its structure is not what's anticipated. Use an IF node for this check. If empty/unexpected, route to a specific path (e.g., log 'No new data found', stop workflow gracefully, or use default values) instead of letting downstream nodes fail when trying to access missing data.",
    type: 'ALWAYS_APPLY', // For any data-fetching node
    priority: 'high',
    category: 'data-flow',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest', 'n8n-nodes-base.readFile'] // Plus all DB query nodes
    },
    recommendation: "Add an IF node after data retrieval. Condition: `{{ $json.length === 0 }}` (for arrays) or `{{ !$json.expectedKey }}` (for objects). True path: Handle 'no data'. False path: Process data.",
    example: "HTTP Request (Get Users) -> IF (Condition: `{{ $json.users && $json.users.length > 0 }}`) -> True: [Process $json.users] / False: Log ('No users found today').",
    reasoning: "Prevents errors from downstream nodes trying to operate on null or unexpectedly structured data. Allows for controlled workflow termination or alternative paths when no data is available. [Ref: Gemini 1.2, Manus 1.2]",
    relatedRules: ['BR_EXP_SafeNavigation'],
    additionalData: { impact: 'High', effort: 'Low' }
  },
  {
    id: 'BR_ERR_006',
    name: "Isolate Failing Items in Batch Processing (Dead-Letter Queue Concept)",
    description: "When processing a batch of items, isolate individual item failures to prevent halting the entire batch. Route failed items and their errors to a 'dead-letter' mechanism for later review/reprocessing.",
    descriptionForLLM: "If processing items in a loop (e.g., after SplitInBatches or in a Code node 'Run Once for All Items' processing an array), and an individual item's processing might fail (e.g., specific API call for that item): Use node-level 'Continue on Fail' or try-catch within the loop. If an item fails, route that item and its error details to a separate path (e.g., write to an error log table/sheet, send to an error sub-workflow, or add to a 'failed_items' array). The main loop should continue with the next item.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['batch', 'loop', 'multiple items', 'array processing', 'partial failure', 'dead letter'],
      workflowPatterns: ['batch_processing', 'data_integration_large_volume']
    },
    recommendation: "Inside a loop processing items: Node X (processes item, Settings: Continue on Fail: true). Connect Error Output of Node X to a sequence that logs/stores the failing item and its `{{$json.error}}`. Connect Main Output of Node X to continue processing successful items.",
    example: "LoopOverItems -> HTTP Request (for each item, Continue on Fail=true) -> [Error Output] -> Google Sheets (Append Row: item, error). Main loop continues with next item.",
    reasoning: "Maximizes throughput for successful items in a batch and provides a mechanism for handling/reprocessing individual failures without manual intervention for the entire batch. [Ref: Gemini 2.2, ChatGPT 2.2]",
    relatedRules: ['BR_ERR_002_NodeLevelContinueOnFail'],
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_ERR_007',
    name: "Manage Explicit Timeouts for External Calls",
    description: "Set explicit timeouts on nodes making external calls (HTTP, DB, etc.) to prevent workflows from hanging indefinitely.",
    descriptionForLLM: "For any node making a network call to an external service (e.g., HTTP Request, database nodes, specific integration nodes), configure an explicit timeout in its settings if the option is available. Choose a timeout that is reasonable for the expected response time of the service (e.g., 30-60 seconds). If a timeout occurs, ensure it's handled as an error.",
    type: 'ALWAYS_APPLY', // For relevant node types
    priority: 'medium',
    category: 'reliability',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-architect-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest', /* DB nodes, other API nodes */ ]
    },
    recommendation: "In HTTP Request node options, set 'Timeout (ms)'. For DB nodes, check connection or query timeout settings. Handle timeout errors like other API errors (e.g., retry or fail gracefully).",
    reasoning: "Explicit timeouts prevent workflows from getting stuck indefinitely if an external service is unresponsive, improving overall system stability. [Ref: Manus 1.1 (Config Anti-Patterns), ChatGPT 2.2]",
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_ERR_008',
    name: "Ensure Webhooks Respond Promptly (Avoid Caller Timeouts)",
    description: "Webhook-triggered workflows must send an HTTP response (e.g., 200 OK, 202 Accepted) quickly, especially if subsequent processing is lengthy. Use a 'Respond to Webhook' node early.",
    descriptionForLLM: "CRITICAL FOR WEBHOOKS: If the workflow is triggered by a Webhook node, plan to include a 'Respond to Webhook' node VERY EARLY in the flow to send an immediate acknowledgment (e.g., HTTP 200 or 202) back to the calling system. Perform lengthy processing *after* this response. Failure to respond quickly can cause the calling system to timeout and retry, leading to duplicate executions.",
    type: 'ALWAYS_APPLY', // For any workflow using a Webhook trigger
    priority: 'critical',
    category: 'error-handling', // Also reliability, node-usage
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.webhook']
    },
    recommendation: "Place a 'Respond to Webhook' node immediately after the Webhook trigger (or after minimal initial validation). Configure it to send a 200/202 status. Perform all long-running tasks after this response node.",
    example: "Webhook Trigger -> Respond to Webhook (Status 200, Body {status: 'received'}) -> [Main processing logic...]",
    reasoning: "Many systems that send webhooks have short timeouts. If n8n doesn't respond quickly, the sender assumes failure and may retry, causing duplicate processing or being marked as unreliable. [Ref: ChatGPT 2.2, III.A (Webhook)]",
    relatedRules: ['BR_NODE_WebhookBestPractices'], // Assumed specific Webhook node rule
    
    
  },
  {
    id: 'BR_ERR_009',
    name: "Test Error Handling Paths Thoroughly",
    description: "Error handling paths and global error workflows should be tested as rigorously as the main 'happy path' logic to ensure they function correctly when failures occur.",
    descriptionForLLM: "BEST PRACTICE REMINDER (for Architect/Validator, and for user education): Emphasize or check that error handling logic (Continue on Fail paths, global error workflows) has been tested. Use a 'Stop and Error' node during development to deliberately trigger error conditions and verify that error paths execute as expected and notifications/logs are correct.",
    type: 'CONTEXTUAL_SUGGESTION', // More of an operational/testing best practice
    priority: 'medium',
    category: 'error-handling', // Also testing
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['error handling', 'test', 'validate', 'debug', 'error path'],
      workflowPatterns: ['production_deployable', 'has_error_paths']
    },
    recommendation: "During development, temporarily insert a 'Stop and Error' node before error handling sections to simulate failures and test the error paths. Verify that global error workflows are triggered and correctly process error data.",
    reasoning: "Untested error handling is as bad as no error handling. It provides a false sense of security. Ensuring error paths work correctly is crucial for production reliability. [Ref: ChatGPT 2.2]",
    appliesToNodeTypes: ['n8n-nodes-base.stopAndError'],
    
    
  },
  {
    id: 'BR_ERR_010',
    name: "Understand Error Object Structure for Handling",
    description: "Be aware of the typical structure of error objects in n8n (e.g., from 'Continue on Fail' or Error Trigger) to correctly access error messages, codes, and stack traces in error handling paths.",
    descriptionForLLM: "ERROR HANDLING DETAIL: When an error is caught (e.g., via 'Continue on Fail' output or Error Trigger data), the error information is typically in `{{$json.error}}` or similar. This object usually contains `message`, `stack`, and sometimes `name` or `code`. Reference these specific fields when formatting error notifications or making decisions in error paths. Do not assume a simple string error.",
    type: 'ALWAYS_APPLY', // For any agent dealing with error paths
    priority: 'medium',
    category: 'error-handling',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent' /* when planning error formatting */],
    triggerContext: {
      workflowPatterns: ['has_error_paths'] // When error paths are being designed/composed
    },
    recommendation: "In an error path, after a node with 'Continue on Fail', inspect `{{$json.error}}`. Example: Use a Set node to create vars: `errorMessage = {{$json.error.message}}`, `errorStack = {{$json.error.stack}}`. For Error Trigger, it's `{{$json.execution.error.message}}` etc.",
    example: "Set Node (in error path): `fields.errorMessage = {{$json.error.message}}`, `fields.nodeName = {{$json.error.node.name}}` (if available).",
    reasoning: "Knowing the error object structure is essential for extracting meaningful information for logging, notification, or conditional error handling. [Ref: Gemini 1.2 (Error Trigger data), ChatGPT 2.2 (IF checking error field)]",
    documentationUrl: "https://docs.n8n.io/flow-logic/error-handling/#error-object",
    
    
  },
  {
    id: 'BR_ERR_011',
    name: "Clarify Workflow Behavior with 'Continue on Fail'",
    description: "If 'Continue on Fail' is used, ensure the workflow logic explicitly handles both the success and error outputs of that node to avoid ambiguity or silent partial failures.",
    descriptionForLLM: "DESIGN CLARITY: When a node is set to 'Continue on Fail', its main output might still produce data (or empty data) even if an error occurred and error data was sent to the error output. Ensure the workflow logic explicitly differentiates or merges these paths. Do not assume the main path will halt or have no data if the error path is taken. If subsequent nodes rely on output from the fallible node, check for data existence or use data from the error handling path as fallback.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest', /* any node with Continue on Fail */]
    },
    recommendation: "After a node with 'Continue on Fail': 1. Handle its error output. 2. If the main output is also connected, ensure downstream nodes can handle potentially missing/incomplete data from the fallible node if it erred, or use a Merge node to explicitly combine results from success/error paths if necessary.",
    reasoning: "The 'Continue on Fail' behavior can lead to confusing workflow states if not fully understood, potentially leading to downstream errors or processing of incomplete data. Explicit handling of both outputs is key. [Ref: Gemini 1.2 (Error Propagation)]",
    
    
  },
  {
    id: 'BR_ERR_012',
    name: "Handle Trigger Node Errors Separately",
    description: "Recognize that errors occurring within a Trigger node itself (e.g., invalid credentials for a polling trigger) are handled differently by Global Error Workflows (error data structure may vary) and might prevent workflow execution entirely.",
    descriptionForLLM: "ERROR HANDLING (Triggers): Be aware that if a Trigger node itself fails (e.g., connection issue for a polling trigger like Gmail, invalid webhook registration): 1. The main workflow might not even start. 2. If a Global Error Workflow is configured, the error data it receives from a failed trigger can have a different structure than errors from nodes within an already running workflow (e.g., it might lack specific `lastNodeExecuted` details from the main flow). Plan error workflow logic to account for this if detailed trigger error parsing is needed.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent' /* when designing error handling for triggered flows */, 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['trigger error', 'polling trigger', 'webhook activation', 'workflow start failure'],
      // Applies when any polling or event-based trigger is used
      nodeTypes: ['n8n-nodes-base.cron', 'n8n-nodes-base.webhook', 'n8n-nodes-base.googleSheetsTrigger', /* etc. */]
    },
    recommendation: "Monitor trigger node health (e.g., check activation status for webhooks in n8n UI). In Global Error Workflows, add logic to check if `{{$json.error.node}}` information is minimal, which might indicate a trigger-level failure, and adjust notifications accordingly.",
    reasoning: "Trigger node failures prevent workflow execution and have a distinct error signature, requiring specific attention in monitoring and global error handling strategies. [Ref: Gemini 1.2 (Trigger Node Errors)]",
    
    
  },
  {
    id: 'BR_ERR_013',
    name: "Manage Error Propagation from Sub-Workflows",
    description: "Understand how errors in sub-workflows (called by Execute Workflow) propagate to the parent, especially when 'Wait for Completion' is disabled (asynchronous). Plan for explicit error checking or signaling if needed.",
    descriptionForLLM: "ERROR HANDLING (Sub-Workflows): When using 'Execute Workflow' node: 1. If 'Wait for sub-workflow to finish' is ENABLED (default): An unhandled error in the sub-workflow will typically cause the 'Execute Workflow' node in the parent to fail. This failure can then be caught by the parent's error handling. 2. If 'Wait for sub-workflow to finish' is DISABLED (asynchronous): The parent workflow continues immediately. Errors in the sub-workflow will NOT automatically stop or be visible to the parent unless the sub-workflow is designed to signal errors back (e.g., by writing to a shared database/log, calling an error webhook on the parent, or if the parent later polls for results including error status). Plan accordingly.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.executeWorkflow']
    },
    recommendation: "For critical synchronous sub-workflows, keep 'Wait for completion' enabled and handle failures in the parent. For asynchronous sub-workflows, design an explicit error signaling mechanism from sub-workflow back to the parent or a monitoring system if error visibility is required.",
    reasoning: "Error propagation from sub-workflows, especially async ones, is not always intuitive and requires careful design to ensure failures are not missed. [Ref: Gemini 1.2 (Sub-workflow Execution Failures)]",
    relatedRules: ['BR_WF_PrioritizeModularDesign', 'BR_DSGN_009_SubWorkflowInterfaces'],
    
    
  },
  {
    id: 'BR_ERR_014',
    name: "Error Trigger Provides Limited Context from Failed Flow",
    description: "Data from the failed workflow (beyond basic error info and last node data) is not directly available in a Global Error Workflow. Design error workflows knowing they operate in a separate context.",
    descriptionForLLM: "ERROR WORKFLOW DETAIL: When a Global Error Workflow is triggered, it receives standard error information (`$json.error`, `$json.workflow`, `$json.execution`). It does NOT automatically have access to all the data items that were being processed in the middle of the failed workflow. If the error workflow needs specific data from the failed flow for its logic (e.g., a customer ID to look up for sending a more detailed alert), the main workflow must be designed to explicitly pass this data *if an error is anticipated* (e.g., via 'Continue on Fail' and then explicitly calling the error workflow as a sub-workflow with parameters), or the error workflow needs to use the n8n API to fetch more execution data if possible and permissions allow.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent' /* when designing global error handlers */],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.errorTrigger']
    },
    recommendation: "Design Global Error Workflows primarily for notification and logging based on the standard error data. If specific data from the point of failure in the main flow is crucial for the error workflow's action, consider if that specific error should be handled locally in the main flow (e.g., via Continue on Fail) where that data is still in scope.",
    reasoning: "Understanding the data scope limitations of Global Error Workflows is key to designing effective centralized error handling. They are for reacting to failure, not deep data recovery from the failed instance without extra work. [Ref: Gemini 2.2 (Global Error Workflow Recovery Capabilities)]",
    documentationUrl: "https://docs.n8n.io/flow-logic/error-handling/#error-data",
    
    
  },
  {
    id: 'BR_ERR_015',
    name: "Merge Node 'Wait' Mode Can Halt if Input Branch Fails/No Data",
    description: "If a Merge node is in 'Wait' mode (default for some modes) and one of its input branches fails to produce data or produces no items, the Merge node may not execute and halt that path of the workflow.",
    descriptionForLLM: "NODE BEHAVIOR (Merge): When using the Merge node in a mode that implicitly waits for data from all connected input branches (e.g., often default for 'Combine by Matching Fields' or 'Combine by Position'), if one of those input branches fails before reaching the Merge, or simply produces zero items, the Merge node itself might not execute or output any items, potentially halting the workflow path. Ensure all input branches to a 'waiting' Merge node will reliably produce data, or handle the 'no data' case before the Merge, or use a Merge mode like 'Append' that processes items as they arrive.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-usage', // Also error-handling/reliability
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.merge']
    },
    recommendation: "When using Merge node: 1. Understand the behavior of its selected 'Mode' regarding waiting for inputs. 2. If using a 'wait' type mode, ensure all preceding branches connected to its inputs will always provide data (even if an empty item array, if that's acceptable). 3. Consider using 'Append' mode if inputs might arrive independently or some might be empty. 4. Test scenarios where one input branch to the Merge node might produce no items.",
    reasoning: "This behavior of Merge nodes, especially in 'wait' modes, is a common source of workflows unexpectedly stopping if an input branch doesn't deliver data. [Ref: Gemini 1.1 (Merge node issues in discussion), ChatGPT 2.1 (Merge node in 'wait for both' mode could hang)]",
    relatedRules: ['BR_DSGN_011_DataPathContinuity'],
    
    
  },
  {
    id: 'BR_ERR_016',
    name: "Distinguish Between Recoverable and Non-Recoverable Errors",
    description: "Design error handling paths to differentiate between errors that might be fixed by a retry or alternative logic (recoverable) versus those that indicate fundamental data/configuration issues and should halt processing (non-recoverable).",
    descriptionForLLM: "ERROR HANDLING STRATEGY: When planning error paths (e.g., after 'Continue on Fail' or in a custom try-catch Code node), instruct the Architect/Composer to consider if the potential error is recoverable or non-recoverable. Recoverable (e.g., transient API timeout, temporary rate limit): attempt retries or a fallback data source. Non-recoverable (e.g., invalid input data schema, missing critical configuration, permanent API auth failure): log detailed error, notify, and usually stop that specific item's processing or the entire workflow path cleanly.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'error-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* if writing try/catch in Code node */, 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['error path', 'failure handling', 'retry logic', 'data validation error', 'api error'],
      workflowPatterns: ['has_error_paths', 'api_integration', 'data_processing']
    },
    recommendation: "Use IF nodes after an error capture to inspect error details (e.g., `{{$json.error.message}}`, `{{$json.error.httpStatusCode}}`). Route to a retry loop for specific retryable error codes/messages. Route to a final error logging/notification path for non-recoverable errors.",
    example: "HTTP Request (Continue on Fail) -> Error Output -> IF (Error is 429 or 503?) -> True: Retry Logic / False: Log & Notify (Non-recoverable).",
    reasoning: "Differentiating error types allows for more intelligent error handling, improving resilience for transient issues while ensuring critical, unfixable problems are escalated appropriately. [Ref: Implicit in discussions of retry logic vs. global error handlers in all docs, e.g., ChatGPT 2.2 distinction between fail-fast and fail-soft]",
    
    
  },
  {
    id: 'BR_ERR_017',
    name: "Log Sufficient Context for Debugging Errors",
    description: "Ensure that error logs (whether in n8n execution logs, external systems, or notifications) contain enough contextual information to diagnose the problem effectively.",
    descriptionForLLM: "ERROR LOGGING DETAIL: When planning or implementing error logging (e.g., in a Global Error Workflow or a local error path): Ensure the log message includes: 1. Workflow Name & Execution ID. 2. Failed Node Name/ID. 3. Timestamp. 4. Full Error Message & Stack (if available). 5. Key input data or parameters being processed by the failed node (BE CAREFUL NOT TO LOG SENSITIVE DATA - see BR_SEC_00X). Insufficient error context makes debugging extremely difficult.",
    type: 'ALWAYS_APPLY', // For agents planning/implementing logging
    priority: 'high',
    category: 'error-handling', // Also maintainability
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* if composing logging nodes */, 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['log error', 'error notification', 'debug failure', 'monitoring'],
      workflowPatterns: ['has_error_paths', 'production_deployable']
    },
    recommendation: "Use a Set node in error paths to construct a detailed error object/message. Example fields: `workflowName: {{$workflow.name}}`, `executionId: {{$execution.id}}`, `failedNode: {{$json.error.node.name // if available}}`, `errorMessage: {{$json.error.message}}`, `inputDataSample: {{ JSON.stringify($items('NodeBeforeFailure')[0].json).slice(0, 500) // Example, be careful with PII }}`. Then pass this to a logging/notification node.",
    reasoning: "Rich contextual information in error logs is paramount for quick and effective troubleshooting and reduces the need to re-run workflows just to understand a failure. [Ref: Gemini 2.2 (Monitoring/Logging), ChatGPT 4.1 (Logging/Monitoring)]",
    relatedRules: ['BR_SEC_00X_NoSensitiveDataInLogs'], // Placeholder for security rule
    
    
  },
  {
    id: 'BR_ERR_018',
    name: "Consider Timeouts for All Potentially Long-Running Operations",
    description: "Beyond just HTTP/DB nodes, if any custom Code node logic or sub-workflow call could potentially hang or run excessively long, consider implementing a timeout mechanism or safeguard.",
    descriptionForLLM: "RELIABILITY (Timeouts): For any step that could hang indefinitely (not just direct HTTP/DB calls, but potentially complex Code nodes performing loops, or Execute Workflow nodes calling sub-workflows that might stall): Plan for timeouts. n8n workflow settings have a global execution timeout. For Code nodes, if performing operations like waiting for an external non-HTTP event, implement a timeout within the JS. For sub-workflows, if the parent waits, ensure the sub-workflow itself has timeout/error handling to prevent the parent from hanging forever.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'reliability', // Also performance
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* for Code node logic */],
    triggerContext: {
      keywords: ['long running', 'hang', 'stuck', 'timeout', 'looping code', 'sub-workflow wait'],
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.executeWorkflow']
    },
    recommendation: "1. Set Workflow Timeout in global Workflow Settings. 2. In Code nodes with loops or waits: `let startTime = Date.now(); while (condition && (Date.now() - startTime < MAX_WAIT_MS)) { ... }`. 3. For Execute Workflow node with 'Wait for Completion': ensure the sub-workflow is robust and won't hang, or consider disabling 'Wait' and using an event-based completion signal if very long running.",
    reasoning: "Unbounded operations can tie up n8n execution slots and lead to system instability. Timeouts ensure that processes either complete or fail within a defined period. [Ref: Gemini 4.1 (Executions Timeout), ChatGPT 2.2 (Timeouts)]",
    documentationUrl: "https://docs.n8n.io/hosting/configuration/configuration-examples/execution-timeout/",
    
    
  },
  {
    id: 'BR_ERR_019',
    name: "Avoid Silent Failures from 'Continue on Fail'",
    description: "If 'Continue on Fail' is enabled on a node, its error output MUST be connected and processed (e.g., logged, or trigger an alternative path). Do not let errors be silently ignored by an unconnected error output.",
    descriptionForLLM: "DESIGN PRINCIPLE & VALIDATION: If a node's 'Continue on Fail' setting is enabled, its error output pin MUST be connected to subsequent nodes that explicitly handle or log the error. DO NOT leave the error output unconnected, as this leads to silent failures. PLAN for this connection when enabling 'Continue on Fail'. An unconnected error output when 'Continue on Fail' is active means errors might be silently ignored, leading to unexpected behavior or data issues downstream because the main path might proceed with incomplete or missing data from the failed node. This creates a 'silent failure' scenario.",
    type: 'ALWAYS_APPLY', // Also a strong CONTEXTUAL_SUGGESTION for Architect/Composer
    priority: 'critical',
    category: 'error-handling',
    appliesToAgents: ['n8n-best-practice-validator-agent', 'n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-reflection-agent'],
    triggerContext: {
      // This applies whenever a node has 'Continue on Fail' enabled.
      // Validator can check if error output is connected.
      conditions: { nodeSetting_ContinueOnFail_isEnabled: true }
    },
    validation: {
      customFunctionKey: 'checkConnectedErrorOutputWhenContinueOnFail', 
      // Checks if node has 'continueOnFail:true' (or similar actual param name)
      // and if its error output (e.g., output index 1) has outgoing connections.
    },
    recommendation: "If 'Continue on Fail' is used: ALWAYS connect the error output. At a minimum, log the error. Preferably, use an IF node to decide on an alternative action or graceful termination of that item's path.",
    reasoning: "Silently ignoring errors via an unconnected error output when 'Continue on Fail' is active is a major source of difficult-to-debug issues and data integrity problems, as the workflow might seem to succeed while having internal failures. [Ref: Gemini 1.2 (Error Propagation - confusion), ChatGPT 2.2 (Try/Catch - if you simply continue...)]",
    
    
  },
  {
    id: 'BR_PERF_001',
    name: "Optimize Loop Processing for Large Datasets (Memory Bloat Avoidance)",
    description: "Avoid n8n memory bloat and slowdowns when processing large datasets (thousands+ of items) in loops by using appropriate patterns like 'Loop Over Items' for batching, or re-triggering/sub-workflow per batch.",
    descriptionForLLM: "CRITICAL PERFORMANCE: If the workflow needs to iterate over a large number of items (e.g., >500-1000), DO NOT process all items in a single execution's internal loop if that loop accumulates data or makes many individual calls. This causes memory bloat. INSTEAD, plan to: 1. Use the 'Loop Over Items' node (formerly SplitInBatches) to process data in manageable chunks (e.g., 50-200 items per batch), often with a Wait node between batches. 2. For very large scale, consider a pattern where the workflow re-triggers itself for each batch, or calls a sub-workflow for each batch, ensuring memory is released between batches.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'critical',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['loop', 'large dataset', 'many items', 'iteration', 'memory', 'slowdown', 'crash', 'batch'],
      workflowPatterns: ['data_intensive', 'batch_processing_needed'],
      conditions: { itemCount_gt: 500 } // Example condition, RuleSelector needs context
    },
    recommendation: "For iterating many items: 1. Use 'Loop Over Items' to create batches. 2. Inside the loop (processing one batch): perform operations. 3. After processing a batch, if calling APIs, add a 'Wait' node before the 'Loop Over Items' iterates to the next batch. 4. If extreme scale, design a producer workflow that queues items/batches, and a consumer workflow triggered by the queue.",
    example: "Get All Customers (10k items) -> Loop Over Items (Batch Size: 100) -> [Loop Body: Process 100 customers e.g., HTTP Request to update status] -> Wait (1s) -> (Loop continues to next batch).",
    reasoning: "n8n can accumulate all item data in memory during a single long execution loop, leading to severe performance degradation or crashes. Batching with 'Loop Over Items' or using separate executions per batch breaks this accumulation. [Ref: Gemini 1.3, 2.3, 4.1; Manus 1.3, 2.3, 4.1; ChatGPT 1, 2.3, 4.1]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.loopOverItems/", // Or relevant perf docs
    relatedRules: ['BR_PERF_002_BatchAPI'],
    additionalData: { impact: 'Critical', effort: 'Medium', typicalBatchSize: 100 }
  },
  {
    id: 'BR_PERF_002',
    name: "Utilize Batch Operations for APIs and Databases",
    description: "When interacting with APIs or databases for multiple items, use batch/bulk endpoints or operations instead of individual calls per item, if available.",
    descriptionForLLM: "PERFORMANCE: If processing a list of items to send to an API or database: Check if the API/DB supports batch/bulk operations (e.g., 'insert multiple rows', 'update batch of records', 'bulk retrieve details'). If so, plan to aggregate items (e.g., in an array using a Code node or Set node with careful expressions) and make ONE batch call instead of many individual calls within a loop. This significantly reduces overhead and improves throughput.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['api call', 'database', 'multiple items', 'loop', 'insert', 'update', 'performance', 'batch'],
      nodeTypes: ['n8n-nodes-base.httpRequest', /* DB Nodes */ 'n8n-nodes-base.loopOverItems'],
      workflowPatterns: ['data_integration_multiple_records', 'batch_processing_needed']
    },
    recommendation: "Before looping to call an API/DB for each item, check API/DB documentation for batch endpoints. If available, use a Code node (Run Once for All Items) or Set node to structure data for a single batch request. If no batch endpoint, use 'Loop Over Items' with small batches and delays.",
    example: "Instead of: Loop (1000 items) -> HTTP Request (single item). Prefer: Prepare Batch (1000 items) -> HTTP Request (batch endpoint).",
    reasoning: "Batch operations drastically reduce the number of network round-trips and database transactions, leading to significant performance gains and reduced load on external systems. [Ref: Gemini 2.3, 4.1; Manus 1.3, 2.3, 4.1; ChatGPT 2.3, 4.1]",
    relatedRules: ['BR_PERF_001_LoopOptimization', 'BR_PERF_RespectRateLimits'],
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_PERF_003',
    name: "Filter and Reduce Data Payload Early",
    description: "Remove unnecessary data or filter items as early as possible in the workflow to reduce memory usage and processing load for downstream nodes.",
    descriptionForLLM: "OPTIMIZATION: After any node that fetches data (e.g., HTTP Request, Read File, Database query), if only a subset of the data items or a subset of fields within items is needed for subsequent steps, immediately add a node (e.g., IF for item filtering, Edit Fields/Set (with Keep Only Set) for field selection, or 'Simplify Output' option if available on the source node) to filter out or remove the unneeded data. Do not pass large, unused data payloads through the entire workflow.",
    type: 'ALWAYS_APPLY', // Good principle whenever data is fetched
    priority: 'high',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies broadly after data-fetching nodes
      nodeTypes: ['n8n-nodes-base.httpRequest', 'n8n-nodes-base.readFile', 'n8n-nodes-base.webhook'] // Plus DB Nodes
    },
    recommendation: "1. After data fetching, use an IF or Filter node if entire items need to be conditionally discarded. 2. Use 'Edit Fields' node (or Set node with 'Keep Only Set' mode) to select only required fields and remove others. 3. Check if the data source node has a 'Simplify Output' or similar option and enable it if appropriate.",
    example: "HTTP Request (Get All User Details) -> Edit Fields (Mode: Keep only selected fields: 'id', 'email', 'status') -> Further Processing.",
    reasoning: "Reducing data payload early minimizes memory consumption, speeds up downstream processing, and can reduce network traffic if data is passed to other services. It makes workflows more efficient and easier to debug. [Ref: Gemini 2.3, Manus 4.1.3, ChatGPT 2.3]",
    documentationUrl: "https://docs.n8n.io/integrations/creating-nodes/build/reference/ux-guidelines/#simplify-output", // Example link
    additionalData: { impact: 'High', effort: 'Low to Medium' }
  },
  {
    id: 'BR_PERF_004',
    name: "Respect API Rate Limits and Use Delays",
    description: "When making frequent API calls, especially in loops, implement delays (Wait node) or use node-specific batching/retry options to avoid hitting rate limits.",
    descriptionForLLM: "CAUTION: If the workflow involves making multiple calls to an external API in sequence or in a loop, be mindful of the API's rate limits. Add a 'Wait' node between calls (e.g., 500ms to 2 seconds, depending on the API) to throttle requests. For HTTP Request nodes, configure 'Retry on Fail' with delays, especially for 429 (Too Many Requests) errors. Excessive calls without delays can lead to API errors or IP bans.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'performance', // Also reliability
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['api call', 'http request', 'loop', 'multiple requests', 'rate limit', 'throttle'],
      nodeTypes: ['n8n-nodes-base.httpRequest', 'n8n-nodes-base.loopOverItems']
    },
    recommendation: "1. Check the API's documentation for rate limits. 2. If looping through API calls, add a 'Wait' node inside the loop (e.g., 1000ms). 3. Use the 'Retry on Fail' option in HTTP Request for errors like 429, with appropriate delays. 4. Consider batching requests if the API supports it (see BR_PERF_002_BatchAPI).",
    example: "Loop Over Items -> HTTP Request (call API for item) -> Wait (1000ms) -> (Loop continues).",
    reasoning: "Respecting API rate limits prevents workflow failures, ensures fair use of external services, and avoids potential blocking of your n8n instance's IP. [Ref: Gemini 1.1 (Rate Limits), 2.3; Manus 1.1 (Excessive Polling), 4.1; ChatGPT 1, 2.2, 4.1]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/rate-limits/",
    relatedRules: ['BR_ERR_003_APIRetries', 'BR_PERF_002_BatchAPI'],
    additionalData: { impact: 'High', effort: 'Low to Medium' }
  },
  {
    id: 'BR_PERF_005',
    name: "Optimize Code Node Usage for Performance",
    description: "For Code nodes, use efficient JavaScript. Prefer 'Run Once for All Items' mode for batch operations. Avoid heavy computations or external calls within Code nodes if possible.",
    descriptionForLLM: "CODE NODE OPTIMIZATION: 1. If processing an array of items, use 'Run Once for All Items' mode and iterate over `items` array in your script; this is often more performant than 'Run Once for Each Item' for batch logic. 2. Write efficient JS: avoid unnecessary nested loops, use appropriate data structures. 3. Do NOT make external API/DB calls directly from Code node script if a dedicated n8n node exists for it, as n8n nodes have built-in handling for credentials, retries, etc. 4. Keep Code node logic focused; complex multi-stage logic might be better as multiple nodes or a sub-workflow.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'performance', // Also node-usage
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.functionItemNode', 'n8n-nodes-base.functionNode'] // Include older ones
    },
    recommendation: "In Code node settings, select 'Run Mode'. For batch tasks on `items` array, use 'Run Once for All Items'. For individual item processing, 'Run Once for Each Item' is fine but be mindful of overhead if many items. Profile or simplify JS if it's a bottleneck.",
    example: "// Bad: Code node (Run per item) doing complex calculation. // Good: Code node (Run for All Items) `for (const item of items) { /* efficient batch calc */ }`",
    reasoning: "Code node performance is highly dependent on script efficiency and execution mode. Choosing the right mode and writing optimized JS can prevent Code nodes from becoming bottlenecks. [Ref: Gemini 1.3, 2.3; Manus 1.1; ChatGPT 2.3, III.A]",
    documentationUrl: "https://docs.n8n.io/code/code-node/",
    relatedRules: ['BR_NODE_CodeForComplexLogicOnly'],
    additionalData: { impact: 'Medium to High', effort: 'Medium (requires JS knowledge)' }
  },
  {
    id: 'BR_PERF_006',
    name: "Consider Sub-Workflow Offloading for Resource Isolation/Parallelism",
    description: "For very resource-intensive or long-running independent tasks within a larger flow, consider offloading them to sub-workflows. This can help isolate memory/CPU usage and enable potential parallelism if sub-workflows are called asynchronously.",
    descriptionForLLM: "If a segment of the workflow is highly complex, memory/CPU intensive, or involves long waits for external systems, consider encapsulating it in a sub-workflow (called by Execute Workflow node). This allows the sub-workflow to run in a more isolated execution context. If 'Wait for sub-workflow to finish' is disabled, the parent can continue, achieving asynchronous execution (but makes result aggregation complex).",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'performance', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['complex part', 'memory intensive', 'cpu intensive', 'long running', 'parallel', 'isolate'],
      workflowPatterns: ['complex_logic', 'long_running_task', 'potential_parallel_candidate']
    },
    recommendation: "Identify resource-heavy or independent long tasks. Create a sub-workflow for them. In the parent, use 'Execute Workflow' node. For parallelism, uncheck 'Wait for completion' and implement a separate mechanism if results need to be aggregated later (e.g., sub-workflow writes to DB, parent polls or has a final collector workflow).",
    reasoning: "Sub-workflows can provide better resource isolation, especially for memory. Asynchronous calls enable parallelism, reducing overall wall-clock time for independent tasks. [Ref: Gemini 2.3, Manus 2.5, ChatGPT 2.3]",
    relatedRules: ['BR_WF_PrioritizeModularDesign'],
    additionalData: { impact: 'Medium to High', effort: 'Medium to High (result aggregation can be complex)' }
  },
  {
    id: 'BR_PERF_007',
    name: "Use Filesystem for Binary Data (Self-Hosted)",
    description: "For self-hosted n8n instances dealing with large files, configure N8N_DEFAULT_BINARY_DATA_MODE=filesystem to store binary data on disk instead of in memory, reducing memory pressure.",
    descriptionForLLM: "IF SELF-HOSTED and processing large binary files (e.g., images, videos, large attachments): Advise user to consider setting the environment variable `N8N_DEFAULT_BINARY_DATA_MODE=filesystem`. This stores temporary binary data on the filesystem, significantly reducing n8n's memory consumption for such operations.",
    type: 'CONTEXTUAL_SUGGESTION', // This is an environment config, agent can only suggest it.
    priority: 'high',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent' /* when planning file-heavy workflows */, 'n8n-best-practice-validator-agent' /* if it could infer self-hosting and file ops */],
    triggerContext: {
      keywords: ['large file', 'binary data', 'image', 'video', 'attachment', 'memory', 'self-hosted'],
      workflowPatterns: ['file_processing_heavy']
    },
    recommendation: "For self-hosted n8n: Set `N8N_DEFAULT_BINARY_DATA_MODE=filesystem` in your n8n environment configuration (e.g., docker-compose.yml) if workflows frequently handle large binary files.",
    reasoning: "Storing binary data directly in memory (default) can lead to out-of-memory errors when processing large files. Filesystem mode offloads this pressure. [Ref: Gemini 1.3, 2.3; ChatGPT III.A]",
    documentationUrl: "https://docs.n8n.io/hosting/configuration/environment-variables/environment-variables/#n8n_default_binary_data_mode",
    additionalData: { impact: 'High (for memory)', effort: 'Low (env var change)', applicability: 'self-hosted' }
  },
  {
    id: 'BR_PERF_008',
    name: "Consider Queue Mode for High Throughput / Scalability (Self-Hosted)",
    description: "For self-hosted n8n instances needing to handle high volumes of concurrent executions (e.g., many webhooks, frequent triggers), configure n8n to run in Queue Mode with multiple workers.",
    descriptionForLLM: "IF SELF-HOSTED and expecting high load (many concurrent triggers/executions): Advise user that for robust scalability and throughput, n8n should be configured in 'queue' mode (using EXECUTIONS_MODE=queue and EXECUTIONS_PROCESS=main|worker) with a message broker like Redis and multiple worker instances. This distributes execution load.",
    type: 'CONTEXTUAL_SUGGESTION', // Environment config suggestion.
    priority: 'high', // For scalability scenarios
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent' /* if user describes high load needs */],
    triggerContext: {
      keywords: ['high volume', 'many webhooks', 'concurrent', 'scalability', 'throughput', 'overload', 'self-hosted', 'queue'],
      workflowPatterns: ['high_concurrency_expected', 'webhook_heavy']
    },
    recommendation: "For self-hosted n8n scalability: Configure `EXECUTIONS_MODE=queue`, `EXECUTIONS_PROCESS` appropriately for main/worker, set up Redis (or other supported queue), and deploy multiple n8n worker instances. [Ref: Gemini 1.3, 2.3; Manus 2.1, 2.3; ChatGPT 2.3]",
    documentationUrl: "https://docs.n8n.io/hosting/scaling/queue-mode/",
    additionalData: { impact: 'Critical (for scale)', effort: 'High (infra setup)', applicability: 'self-hosted' }
  },
  {
    id: 'BR_PERF_009',
    name: "Understand and Configure EXECUTIONS_PROCESS (Self-Hosted)",
    description: "For self-hosted n8n, understand EXECUTIONS_PROCESS (main vs. own) and EXECUTIONS_MODE (regular vs. queue) settings. Use 'own' or 'queue' mode for better resource isolation and stability if running many resource-heavy or concurrent workflows, instead of the default 'main' process.",
    descriptionForLLM: "IF SELF-HOSTED and discussing performance/scalability for an instance running many heavy or concurrent workflows: Suggest reviewing n8n's `EXECUTIONS_PROCESS` setting. Using `main` (default) can lead to resource contention. `own` (each execution in its own process) or `queue` mode (with workers) provides better isolation and resource management for demanding workloads, though they have different overheads.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium', // Environment config, AI suggests for advanced scenarios
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent' /* when discussing deployment/scaling for self-hosted */],
    triggerContext: {
      keywords: ['self-hosted', 'performance', 'scalability', 'resource contention', 'heavy workflow', 'concurrent execution', 'main process', 'own process'],
      workflowPatterns: ['high_concurrency_expected', 'resource_intensive_instance']
    },
    recommendation: "For self-hosted n8n with high load: Evaluate setting `EXECUTIONS_PROCESS=own` or `EXECUTIONS_MODE=queue` (with `EXECUTIONS_PROCESS=main` for the main instance and `EXECUTIONS_PROCESS=worker` for workers). This can improve stability and resource management over the default `EXECUTIONS_PROCESS=main` for all.",
    reasoning: "Running all executions in the main n8n process can lead to resource exhaustion and instability under heavy load. 'own' process mode or 'queue' mode provides better isolation. [Ref: Gemini 1.1, 1.3]",
    documentationUrl: "https://docs.n8n.io/hosting/scaling/", // And docs on env vars
    additionalData: { applicability: 'self-hosted', impact: 'High for stability/scale' }
  },
  {
    id: 'BR_PERF_010',
    name: "Optimize Database Queries (SELECT Specific Columns, Use WHERE)",
    description: "When fetching data from databases, write efficient SQL queries that select only necessary columns and use WHERE clauses to filter data at the database level, rather than fetching entire tables into n8n.",
    descriptionForLLM: "DATABASE PERFORMANCE: When planning or composing database node operations (e.g., Postgres, MySQL): 1. In SELECT queries, explicitly list only the columns needed (e.g., `SELECT id, name, email FROM users`) instead of `SELECT *`. 2. Use SQL `WHERE` clauses to filter data on the database server before it's sent to n8n. Avoid fetching large unnecessary datasets into n8n for filtering.",
    type: 'ALWAYS_APPLY', // For agents dealing with DB nodes
    priority: 'high',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.postgres', 'n8n-nodes-base.mysql', 'n8n-nodes-base.mssql', /* other DB nodes */]
    },
    recommendation: "SQL Query example: `SELECT user_id, order_date, total_amount FROM orders WHERE status = 'completed' AND order_date > '{{ $json.last_run_date }}'`.",
    reasoning: "Fetching only required data from databases significantly reduces data transfer, n8n memory usage, and processing time, leading to much more efficient workflows. [Ref: Gemini 2.3 (Selective DB Queries)]",
    relatedRules: ['BR_DATA_003_FilterEarly'],
    
    
  },
  {
    id: 'BR_PERF_011',
    name: "Manage Execution Data Saving for High-Volume Workflows",
    description: "For very high-volume or frequent workflows, consider disabling 'Save Successful Executions' or 'Save Failed Executions' (or 'Save Manual Executions') in Workflow Settings to reduce database load and storage, if detailed logs for every run are not essential.",
    descriptionForLLM: "PERFORMANCE (DB Load): If a workflow runs very frequently (e.g., many times per minute) or processes extremely high volumes, and if detailed execution logs for every single successful/failed run are not strictly necessary for audit/debugging, consider adjusting its 'Save Execution Data' settings in the Workflow Settings. Disabling logging for successful runs (or all runs) can significantly reduce load on n8n's database (especially SQLite) and prevent storage issues.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent' /* when discussing deployment of high-volume flows */, 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['high volume', 'frequent execution', 'database load', 'execution log', 'pruning'],
      workflowPatterns: ['high_frequency_trigger', 'data_intensive_instance']
    },
    recommendation: "In Workflow Settings, review 'Save Successful Executions', 'Save Failed Executions', 'Save Manual Executions'. For high-volume, non-critical logging flows, consider turning off successful logs. Ensure critical error logging/notification happens via other means if failed logs are off.",
    reasoning: "Extensive execution logging for high-volume workflows can bloat the database, impacting overall n8n instance performance and UI responsiveness when viewing executions. [Ref: Gemini 1.3 (Storing Extensive Execution Data), ChatGPT 1 (Execution Log Pruning)]",
    documentationUrl: "https://docs.n8n.io/workflows/workflow-settings/#save-execution-data",
    additionalData: { impact: 'Medium to High (for DB health)', effort: 'Low' }
  },
  {
    id: 'BR_PERF_012',
    name: "Understand Merge Node Performance and Behavior Pitfalls",
    description: "Be cautious with Merge node, especially in 'Combine by All Possible Combinations' mode or when merging very large datasets, as it can be resource-intensive. Also, be aware of its behavior when placed after an IF node (potentially executing both IF branches).",
    descriptionForLLM: "NODE USAGE (Merge): The Merge node is powerful but can have performance implications: 1. 'Combine - All Possible Combinations' mode can create a very large number of output items (Cartesian product) and consume significant memory/CPU if inputs are large. Use with extreme caution. 2. Merging very large datasets from multiple branches can be slow, regardless of mode. Consider filtering data *before* merging. 3. PITFALL: If a Merge node is placed after an IF node, it might cause *both* branches of the IF to execute as it tries to gather data from all its inputs. If only one IF branch's data is desired, restructure the flow or use Merge's 'Choose Branch' mode carefully.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium', // High if specific problematic patterns are detected
    category: 'performance', // Also node-usage
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.merge']
    },
    recommendation: "For Merge node: Prefer 'Append' or 'Combine by Matching Fields/Position' for most use cases. Avoid 'Combine by All Possible Combinations' unless explicitly needed and understood. Test Merge behavior after IF nodes; to ensure only one IF branch's data proceeds, either ensure the other branch produces no items or use a more complex structure that prevents the Merge from pulling from the inactive branch.",
    reasoning: "The Merge node's flexibility comes with potential performance costs and non-intuitive behaviors (like the IF-Merge interaction) if not configured correctly for the intended data flow. [Ref: Gemini 1.3, III.A; Manus 1.1; ChatGPT III.A (Merge node quirks)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.merge/",
    relatedRules: ['BR_DSGN_011_DataPathContinuity'],
    additionalData: { impact: 'Medium to High', effort: 'Medium' }
  },
  {
    id: 'BR_PERF_013',
    name: "Avoid Extremely Large Number of Nodes on Canvas (UI Performance)",
    description: "Excessively large workflows (hundreds of nodes on a single canvas) can lead to UI unresponsiveness and slow load times in the n8n editor. Modularize into sub-workflows.",
    descriptionForLLM: "DESIGN FOR UI PERFORMANCE: While n8n can execute large workflows, having hundreds of nodes on a single canvas can make the n8n EDITOR UI slow, laggy when dragging nodes, or take a long time to load. If a workflow plan is becoming extremely large visually, this is another strong reason to modularize it using sub-workflows, even if the execution performance itself is acceptable.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // More about DX than runtime, but can indicate over-complexity
    category: 'performance', // Editor performance
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['ui lag', 'slow editor', 'many nodes', 'canvas unresponsive', 'large workflow'],
      conditions: { estimatedNodeCount_gt: 100 } // Example threshold
    },
    recommendation: "If a workflow design exceeds 75-100 nodes on a single canvas, actively look for sections to extract into sub-workflows to improve editor performance and overall manageability.",
    reasoning: "Editor UI performance can degrade with very high node counts on one canvas, impacting developer productivity. Modularization helps maintain a responsive editing experience. [Ref: Gemini 1.3 (Excessive Number of Nodes), ChatGPT 1 (UI interactions lag)]",
    relatedRules: ['BR_WF_PrioritizeModularDesign', 'BR_WF_ValidateMonolithStructure'],
    
    
  },
  {
    id: 'BR_PERF_014',
    name: "Be Aware of Sub-Workflow Execution Overhead",
    description: "While sub-workflows are excellent for modularity, each 'Execute Workflow' node call has a small overhead. For extremely frequent, very tiny sub-workflow tasks, this overhead might add up. Balance modularity with granularity.",
    descriptionForLLM: "PERFORMANCE NUANCE (Sub-Workflows): Calling a sub-workflow (Execute Workflow node) incurs a small amount of overhead for initiating a new execution context. If a sub-workflow is extremely small (e.g., 1-2 simple nodes) and is called thousands of times in a tight loop from a parent, this overhead *could* become noticeable. In such specific high-frequency, micro-task scenarios, evaluate if the logic could be a Code node function within the parent, or if the sub-workflow processes a batch instead of a single item. For most other cases, sub-workflow benefits outweigh this minor overhead.",
    type: 'CONTEXTUAL_SUGGESTION', // A more advanced optimization point
    priority: 'low',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['sub-workflow overhead', 'many calls', 'tiny task', 'high frequency loop'],
      workflowPatterns: ['high_frequency_subworkflow_calls']
    },
    recommendation: "Generally, favor sub-workflows for modularity. However, if a sub-workflow is trivial (1-2 nodes) AND is called extremely frequently in a performance-critical loop, benchmark if inlining that logic (e.g., as a Code node function in the parent) or making the sub-workflow process batches offers better performance.",
    reasoning: "There's a small fixed cost to each sub-workflow execution. For the vast majority of use cases, this is negligible and outweighed by modularity benefits. In hyper-optimized, high-frequency scenarios, it's a factor to be aware of. [Ref: ChatGPT 2.5 (Overhead of orchestrating multiple workflows)]",
    
    
  },
  {
    id: 'BR_PERF_015',
    name: "Minimize Data Passed Between Nodes if Not Needed Later",
    description: "Each node in n8n passes its full input data along with its own output to the next node by default. If large pieces of input data are no longer needed after a node, explicitly remove them (e.g., with Edit Fields or Set node) to reduce memory per item and data copying overhead.",
    descriptionForLLM: "PERFORMANCE (Data Overhead): By default, n8n nodes pass through all input data they received, plus their own results. If a node receives a large data object but only uses a small part of it, and the original large object is NOT needed by any subsequent downstream nodes, plan to explicitly remove the unused large input fields after this node (using an 'Edit Fields' node set to 'Remove Fields' or a 'Set' node with 'Keep Only Set' for the new, smaller output). This reduces the size of each item passed along, saving memory and potentially speeding up later nodes.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // Micro-optimization, but good for very large items
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['large payload', 'data size', 'memory optimization', 'item data', 'pass through'],
      workflowPatterns: ['data_intensive', 'complex_data_object_flow']
    },
    recommendation: "After a node that processes a large input item but only adds/modifies a few fields, if the original large input fields are no longer needed, add an 'Edit Fields' node to remove them, or use a 'Set' node with 'Keep Only Set' to construct a new, leaner item.",
    example: "Node A (Outputs large object {bigData: {...}, smallResult: 'x'}) -> Edit Fields (Remove 'bigData' field if not needed after this point) -> Node B (Receives only {smallResult: 'x'}).",
    reasoning: "Reducing the data footprint of items passed between nodes minimizes memory usage per item and the overhead of n8n copying data between node executions, especially impactful in loops or with many items. [Ref: ChatGPT 1 (Excessive intermediate data)]",
    relatedRules: ['BR_PERF_003_FilterDataEarly'], // Related, but this is about field reduction within an item, not item filtering.
    
    
  },
  {
    id: 'BR_PERF_016',
    name: "Design for Scalability from the Start for Potentially High-Volume Workflows",
    description: "If a workflow is anticipated to handle significantly increasing data volumes or execution frequencies in the future, incorporate scalable design patterns (batching, asynchronous processing, queue considerations) early, even if initial volumes are low.",
    descriptionForLLM: "ARCHITECTURAL PRINCIPLE (Scalability): When designing a workflow that might eventually process large data volumes or handle high trigger frequencies (even if current needs are small), proactively incorporate scalable patterns. This includes planning for batch processing (BR_PERF_002), considering asynchronous sub-workflow offloading (BR_PERF_006) for independent tasks, and structuring data efficiently (BR_PERF_003, BR_PERF_015). It's easier to build for scale initially than to refactor a deeply embedded inefficient design later.",
    type: 'CONTEXTUAL_SUGGESTION', // Primarily for Architect
    priority: 'medium',
    category: 'workflow-design', // Also performance
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['future growth', 'scalability', 'high volume expected', 'increasing load', 'design for scale'],
      workflowPatterns: ['new_core_process_automation', 'data_pipeline_design']
    },
    recommendation: "During initial design, if future scale is a concern: 1. Default to batch processing for any list iteration. 2. Encapsulate independent, potentially long-running tasks in sub-workflows that could be made asynchronous. 3. Keep data payloads lean. 4. Document scalability assumptions and potential future bottlenecks.",
    reasoning: "Retrofitting scalability into a non-scalable workflow design is often much harder and more error-prone than incorporating scalable patterns from the outset. Proactive design saves significant future refactoring effort. [Ref: Implicit in all performance and modularity discussions across docs, e.g., Gemini (Contextual Awareness)]",
    
    
  },
  {
    id: 'BR_PERF_017',
    name: "Avoid Unnecessary Node Execution with IF/Switch Logic",
    description: "Structure IF/Switch nodes so that computationally expensive branches or unnecessary API calls are only executed when absolutely needed, preventing wasted resources.",
    descriptionForLLM: "PERFORMANCE (Conditional Execution): When using IF or Switch nodes, ensure that branches containing resource-intensive operations (e.g., multiple API calls, complex Code nodes, processing large data) are only entered if their specific conditions are met. Avoid placing such operations on a common path that is later filtered *after* the expensive work has already been done.",
    type: 'ALWAYS_APPLY', // For Architect and Composer designing conditional logic
    priority: 'medium',
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['conditional logic', 'if node', 'switch node', 'expensive operation', 'api call branch'],
      nodeTypes: ['n8n-nodes-base.ifV2', 'n8n-nodes-base.switch']
    },
    recommendation: "Place conditions (IF/Switch) *before* expensive operations if those operations are not needed for all items/paths. Filter or route items first, then execute heavy processing only on the relevant subset.",
    example: "// Bad: Process All Items -> Then IF (filter). // Good: IF (filter condition) -> True: Process Subset / False: Skip or different path.",
    reasoning: "Executing expensive operations unnecessarily consumes resources and slows down the workflow. Conditional logic should be used to bypass such operations when their results are not required. [Ref: General programming best practice, implied by efficient data handling rules]",
    relatedRules: ['BR_PERF_003_FilterDataEarly'],
    
    
  },
  {
    id: 'BR_PERF_018', // New ID
    name: "Consider Workflow Static Data for Caching Infrequent API Calls",
    description: "For API calls that fetch data changing infrequently (e.g., daily configurations, user lists), consider caching results using Workflow Static Data to reduce redundant calls.",
    descriptionForLLM: "PERFORMANCE (API Caching): If an HTTP Request node (or similar) fetches data that is relatively static or changes infrequently (e.g., once a day, or a rarely updated configuration list): Plan to cache this data using n8n's Workflow Static Data. The logic would involve: 1. Checking if a valid (non-stale) cached version exists in static data. 2. If yes, use the cache. 3. If no (or stale), make the API call, store the result in static data along with a timestamp, then use the result. This is for small to medium-sized cacheable data.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // More advanced optimization
    category: 'performance',
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['api call', 'http request', 'cache', 'static data', 'infrequent update', 'configuration data', 'lookup list'],
      nodeTypes: ['n8n-nodes-base.httpRequest']
    },
    recommendation: "1. Before an API call node: Add Code node to `const cache = this.getWorkflowStaticData(); if (cache.myApiData && cache.myApiDataTimestamp > Date.now() - CACHE_DURATION_MS) { item.json.result = cache.myApiData; item.json._cacheHit = true; return item; }`. 2. After API call node: Add Code node `const staticData = this.getWorkflowStaticData(); staticData.myApiData = $json.resultFromApi; staticData.myApiDataTimestamp = Date.now(); this.setWorkflowStaticData(staticData);` Use an IF node to bypass API call if `_cacheHit`.",
    example: "// Code node (Get from Cache): if (staticData.config && isFresh(staticData.configTimestamp)) return [{json: staticData.config}]; else return [{json: {needsFetch: true}}]; // API Call -> Code node (Save to Cache): staticData.config = apiResult; ...",
    reasoning: "Caching infrequently changing data fetched via API calls can significantly reduce the number of external requests, improving workflow speed and reducing load on external services. Workflow Static Data provides a simple mechanism for this within n8n. [Ref: ChatGPT 2.3 (Caching and Reusing Results)]",
    documentationUrl: "https://docs.n8n.io/code/code-node/#workflow-static-data",
    additionalData: { impact: 'Medium (for specific cases)', effort: 'Medium' }
  },
  {
    id: 'BR_DATA_001',
    name: "Use Set Node for Simple Transformations",
    description: "Prefer the Set node (or Edit Fields) with expressions for simple data transformations like adding, renaming, or modifying fields with straightforward logic.",
    descriptionForLLM: "For simple data manipulation (e.g., creating a new field from existing ones, renaming, setting static values, simple calculations): Plan to use a 'Set' node (or 'Edit Fields'). Its visual interface and expression support are ideal for clarity in these cases. Avoid a Code node if a Set node can achieve the task cleanly.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'data-flow',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['transform data', 'map field', 'rename field', 'new field', 'calculate value', 'simple logic'],
      // Applies when planning data manipulation steps
    },
    recommendation: "Use the 'Set' node: Add new properties, set their values using static inputs or expressions `{{ $json.someField }}`. Use 'Keep Only Set' mode to also remove other fields if creating a clean output object. For renaming/removing, 'Edit Fields' node is also suitable.",
    example: "Set Node: Parameters -> Add Value: `fullName` = `{{ $json.firstName }} {{ $json.lastName }}`",
    reasoning: "Set nodes provide a clear, visual, and efficient way to handle common, simple data transformations without requiring custom code, improving maintainability. [Ref: Gemini 2.4, Manus 2.4, 4.1.4; ChatGPT 2.4, III.B]",
    relatedRules: ['BR_DATA_002_CodeNodeForComplex', 'BR_EXP_KeepExpressionsSimple'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_DATA_002',
    name: "Use Code Node for Complex Data Logic",
    description: "Employ the Code node for complex data transformations, custom parsing, intricate conditional mapping, or logic that is cumbersome to implement with expressions or multiple Set nodes.",
    descriptionForLLM: "If a data transformation involves complex conditional logic (many nested IFs equivalent), custom iteration over object properties or array elements *within an item*, intricate data restructuring (pivoting, deep nesting), or custom parsing/formatting not covered by standard nodes/expressions: Plan to use a 'Code' node. Ensure the JavaScript is well-commented and handles potential errors (try-catch).",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'data-flow',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['complex transform', 'custom logic', 'parsing', 'advanced mapping', 'iteration within item', 'custom aggregation'],
      workflowPatterns: ['complex_data_manipulation']
    },
    recommendation: "Use Code node: Write clean, commented JavaScript. Use `items` array (in 'Run Once for All Items' mode) or `$item` (in 'Run Once for Each Item' mode). Return data in n8n format: `return [{json: { outputField: ... }}];`. Include try-catch blocks for robustness.",
    example: "Code Node: `// Complex logic to parse custom string and restructure object \n try { /* ... code ... */ return [{json: result}]; } catch (e) { return [{json: {error: e.message}}]; }`",
    reasoning: "Code nodes offer maximum flexibility for complex transformations that exceed the practical limits of Set nodes or expressions, but require JavaScript knowledge and careful implementation for maintainability. [Ref: Gemini 1.1, 2.4; Manus 1.1, 2.4; ChatGPT 1, 2.4, III.B]",
    relatedRules: ['BR_DATA_001_SetNodeForSimple', 'BR_PERF_005_OptimizeCodeNode', 'BR_NODE_CodeReturnFormat'],
    additionalData: { impact: 'High (for capability)', effort: 'Medium to High' }
  },
  {
    id: 'BR_DATA_003',
    name: "Leverage Specialized Transformation Nodes",
    description: "Utilize n8n's specialized nodes (e.g., Item Lists, Merge, SplitInBatches, Spreadsheet File, Compare Datasets) for their specific data manipulation tasks instead of reinventing with Code nodes.",
    descriptionForLLM: "Before defaulting to a Code node for tasks like list operations, merging data streams, batching, or format conversions (JSON to CSV/Excel), check if a specialized n8n node exists for that purpose (e.g., 'Item Lists' for array ops, 'Merge' for combining inputs, 'Spreadsheet File' for Excel). These are often more efficient and maintainable.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-usage', // Also data-flow
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-selector-agent'],
    triggerContext: {
      keywords: ['list operation', 'sum values', 'unique items', 'merge data', 'combine streams', 'excel', 'csv', 'compare datasets', 'batch'],
    },
    recommendation: "Identify the specific transformation needed. Search n8n nodes for a match (e.g., 'Item Lists' for sum/unique, 'Merge' for data combining, 'SplitInBatches'). Prefer these over custom Code node logic for their intended tasks.",
    example: "To sum values in an array of items: Use Item Lists node with 'Aggregate' operation. To combine data from two branches based on a key: Use Merge node in 'Combine' mode with 'Matching Fields'.",
    reasoning: "Specialized nodes are optimized for their tasks, are visually clear, and require no custom coding, leading to more maintainable and often more performant workflows. [Ref: Gemini 2.4, Manus 2.4]",
    relatedRules: ['BR_DATA_002_CodeNodeForComplex'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_DATA_004',
    name: "Preserve Original Data When Transforming",
    description: "When transforming data, avoid directly modifying original input fields if they might be needed later or for debugging. Prefer creating new fields for transformed values.",
    descriptionForLLM: "When transforming data (e.g., in a Set or Code node), it's often safer to create new fields for the transformed values rather than overwriting the original input fields. This preserves the original data for potential downstream reference or easier debugging if the transformation is incorrect.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // More of a good habit than critical
    category: 'data-flow',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      keywords: ['transform', 'modify data', 'update field', 'map value'],
      nodeTypes: ['n8n-nodes-base.set', 'n8n-nodes-base.codeNode']
    },
    recommendation: "In a Set node, if transforming `sourceField`, create `transformedSourceField` instead of directly changing `sourceField`. If `sourceField` is truly no longer needed, then overwriting is acceptable but be certain.",
    example: "Set Node: `original_price: {{$json.price}}`, `price_with_tax: {{$json.price * 1.2}}` (keeps original).",
    reasoning: "Preserving original data alongside transformed versions aids in debugging, allows for easier re-computation if transformation logic changes, and provides a clearer data lineage. [Ref: Manus 4.1.4]",
    additionalData: { impact: 'Low to Medium', effort: 'Low' }
  },
  {
    id: 'BR_DATA_005',
    name: "Validate Data Structure After Complex Transformations",
    description: "After significant data transformations, especially in Code nodes or complex Merge operations, add a step to validate that the output data structure matches expectations before further processing.",
    descriptionForLLM: "If a Code node performs a complex data restructuring, or a Merge node combines data in a non-trivial way, consider adding an IF node or a Schema Validation node (if available/planned) immediately after to verify that the output data has the expected keys, types, and structure. This helps catch transformation errors early.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'data-flow', // Also error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['complex transform', 'code node output', 'merge output', 'data restructure', 'schema validation'],
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.merge']
    },
    recommendation: "After a Code node or complex Merge: Add IF node. Condition: `{{ !$json.expectedKey || typeof $json.anotherKey !== 'string' }}` -> True branch: Error/Log. False branch: Proceed.",
    reasoning: "Complex transformations are prone to errors. Validating the output structure immediately helps isolate issues to the transformation step itself, rather than discovering them later in downstream nodes. [Ref: Manus 4.1.4]",
    relatedRules: ['BR_ERR_004_ValidateExternalInput'],
    additionalData: { impact: 'Medium', effort: 'Medium' }
  },

  // --- EXPRESSION USAGE ---
  {
    id: 'BR_EXP_001',
    name: "Keep n8n Expressions Simple and Readable",
    description: "Prefer simple, clear expressions in node parameters. For complex logic, use an intermediate Set node or a Code node instead of overly long or nested expressions.",
    descriptionForLLM: "EXPRESSION GUIDELINE: While expressions are powerful, keep them concise and readable. If an expression requires multiple lines, deep nesting of functions, or complex conditional logic (ternaries), it's usually better to: 1. Break it down into multiple steps using intermediate Set nodes. 2. Implement the logic in a Code node if it's significantly complex. Long, unreadable expressions are hard to debug and maintain.",
    type: 'ALWAYS_APPLY', // General principle for Composer/Architect
    priority: 'medium',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies whenever expressions are being written/planned.
    },
    recommendation: "If an expression is longer than 2-3 chained operations or one simple conditional, move its logic to a Set node (to pre-calculate parts) or a Code node. **Leverage n8n's built-in expression functions (e.g., for string manipulation, date formatting) for common tasks.**Use comments in Code nodes for complex expressions.",
    example: "// Bad: {{ ($json.value > 10 && $json.type === 'A') ? ($json.value * 2) : (($json.value > 5 && $json.type === 'B') ? $json.value + 5 : $json.value) }} // Good: Use IF nodes or a Code node for this. // Good simple expression: {{ $json.email.toLowerCase().trim() }}. // Bad: {{ ($json.value > 10 && $json.type === 'A') ? ($json.value * 2) : (($json.value < 5 && $json.type === 'B') ? $json.value + 5 : $json.value) }} // Good: Use IF nodes or a Code node for this complex logic.",
    reasoning: "Simple expressions improve workflow readability and reduce the chance of syntax errors or subtle bugs. Complex logic is better managed in dedicated nodes. [Ref: Gemini 2.4, III.B; Manus III.B; ChatGPT III.B]",
    relatedRules: ['BR_DATA_001_SetNodeForSimple', 'BR_DATA_002_CodeNodeForComplex'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_EXP_002',
    name: "Use Correct Node Referencing in Expressions",
    description: "Understand and consistently use n8n's data referencing methods: $json for immediate previous node, $('Node Name') for specific named nodes. Be mindful of node renaming.",
    descriptionForLLM: "EXPRESSION SYNTAX: 1. To get data from the *directly connected previous node*: use `{{$json.fieldName}}`. 2. To get data from a *specific, named node* (not necessarily directly previous): use `{{ $('Unique Node Display Name').item.json.fieldName }}`. Be cautious: if 'Unique Node Display Name' is changed, this expression will break. 3. For items in general (e.g., in loops processed by Code node): `$items('Source Node Name')[index].json.fieldName` or iterate `items` array where `item.json.fieldName`.",
    type: 'ALWAYS_APPLY', // Core syntax for Composer
    priority: 'critical',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {},
    recommendation: "For previous node: `{{$json.field}}`. For specific node: `{{$('My HTTP Node').item.json.data.id}}`. Ensure display names used in expressions are stable or updated if names change. Use Code node's `items` input for array processing.",
    reasoning: "Correctly referencing data is fundamental to n8n expressions. Misunderstanding $json vs. named node context leads to common errors. [Ref: Gemini III.B, Manus (Static Refs), ChatGPT III.B]",
    documentationUrl: "https://docs.n8n.io/code/expressions/",
    relatedRules: ['BR_WF_ApplyClearNodeNames'], // Because named refs depend on node names
    additionalData: { impact: 'Critical', effort: 'Low (understanding)' }
  },
  {
    id: 'BR_EXP_003',
    name: "Safely Navigate Potentially Missing Properties in Expressions",
    description: "Use logical OR (||) for default values and/or optional chaining (?.) if available/appropriate to prevent errors when accessing nested properties that might not exist.",
    descriptionForLLM: "EXPRESSION SAFETY: When accessing a potentially missing field or nested property in an expression (e.g., `{{$json.user.address.street}}`), if `user` or `address` might be undefined/null, this will cause an error. Prevent this by: 1. Providing a default: `{{ $json.user.address.street || 'N/A' }}`. 2. For deeper checks, use multiple ORs or more complex conditional expressions, or handle in a Code node.",
    type: 'ALWAYS_APPLY',
    priority: 'high',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['optional field', 'missing data', 'null check', 'undefined property', 'nested data']
    },
    recommendation: "Use `{{ $json.path.to.optionalField || 'defaultValue' }}`. For checking existence before complex access, use an IF node: `{{ $json.user && $json.user.address ? $json.user.address.street : 'N/A' }}` or better, handle in Code node if very nested.",
    example: "`{{ $json.order.customer.email || 'no_email_provided' }}`",
    reasoning: "Ensures workflows don't fail due to trying to access properties of undefined/null objects, which is a common source of runtime errors. [Ref: Gemini III.B, Manus III.B, ChatGPT (Unchecked Assumptions)]",
    relatedRules: ['BR_ERR_005_HandleEmptyResults'],
    additionalData: { impact: 'High', effort: 'Low' }
  },
  {
    id: 'BR_EXP_004',
    name: "Check Node Execution Status for Conditional Data Access",
    description: "In expressions or Code nodes referencing data from a node that might not have run (due to conditional branches), check its execution status first using `$('Node Name').isExecuted`.",
    descriptionForLLM: "EXPRESSION SAFETY: If an expression needs data from 'Node X', but 'Node X' is on a conditional branch and might not have executed for the current item, directly accessing `{{ $('Node X').item.json.field }}` will error. First check `{{ $('Node X').isExecuted }}` in an IF condition or expression before attempting to access its data.",
    type: 'CONTEXTUAL_SUGGESTION', // When referencing nodes on conditional paths
    priority: 'high',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-architect-agent'],
    triggerContext: {
      keywords: ['conditional branch', 'optional node', 'data reference', 'isExecuted'],
      workflowPatterns: ['complex_branching']
    },
    recommendation: "Example IF node condition: `{{ $('Optional Data Source Node').isExecuted && $('Optional Data Source Node').item.json.someValue > 10 }}`. Or, in a Set node: `myValue: {{ $('Optional Node').isExecuted ? $('Optional Node').item.json.field : 'default_if_not_run' }}`.",
    reasoning: "Prevents 'Can’t get data for expression' errors when referencing conditionally executed nodes. [Ref: Gemini III.B]",
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_EXP_005',
    name: "Avoid Common Expression Syntax Errors",
    description: "Be aware of common syntax errors in expressions, such as invalid JSON in parameters expecting JSON, or incorrect referencing.",
    descriptionForLLM: "EXPRESSION SYNTAX CHECK: 1. If a node parameter expects JSON (e.g., 'Raw JSON Body' in HTTP Request), ensure the expression output IS a valid JSON string (e.g., `{{ JSON.stringify({key: $json.value}) }}`). Do not output a JS object directly unless the field supports it. 2. Double-check node names and field paths in `{{ $(‘Node Name’).item.json.field }}` for typos. 3. Avoid trailing periods or mismatched brackets. 4. Test complex expressions with sample data using node's 'Execute Node' feature.",
    type: 'ALWAYS_APPLY', // For Composer
    priority: 'critical',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // General expression writing
    },
    recommendation: "When providing JSON to a parameter: ensure it's a string if 'Raw JSON' is selected (e.g., `{{ JSON.stringify({ data: $json }) }}`). If using parameter type 'JSON Object/Array', provide valid JS object/array: `{{ ({ data: $json }) }}`. Validate expression syntax carefully.",
    reasoning: "Syntax errors are common and can be tricky to debug. Leads to errors like 'JSON Output in item 0 contains invalid JSON' or 'Can’t get data for expression'. [Ref: Gemini III.B, Manus III.B]",
    documentationUrl: "https://docs.n8n.io/code/cookbook/expressions/common-issues/",
    additionalData: { impact: 'Critical', effort: 'Medium' }
  },
  {
    id: 'BR_EXP_006',
    name: "Ensure Valid JSON Output for JSON Parameters",
    description: "When an n8n node parameter is configured to expect JSON (e.g., 'JSON/Raw Parameters' in HTTP Request, or some 'JSON Parameters' fields), ensure the expression provides a valid JSON string or a JavaScript object/array that n8n can serialize.",
    descriptionForLLM: "CRITICAL EXPRESSION SYNTAX: If a node parameter expects JSON: 1. If it's a 'string' type field meant for raw JSON, the expression MUST evaluate to a valid JSON string (e.g., `{{ JSON.stringify({key: $json.value}) }}`). 2. If the n8n UI allows direct object/array input for a JSON parameter (often labeled as 'Add Expression' for the whole JSON body), the expression can be `{{ ({key: $json.value}) }}` (a JS object). Failure to provide valid JSON will cause the 'JSON Output in item 0 contains invalid JSON' error.",
    type: 'ALWAYS_APPLY', // For NodeComposer
    priority: 'critical',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest', /* any node with a 'JSON parameters' field */]
    },
    recommendation: "For parameters expecting a JSON string: use `JSON.stringify()` in your expression. E.g., `{{ JSON.stringify({ a: 1, b: $json.name }) }}`. For parameters that accept direct object input via expression: use `{{ ({ a: 1, b: $json.name }) }}`. Test with sample data.",
    example: "HTTP Request Node, Body (JSON/Raw): `{{ JSON.stringify({ orderId: $json.id, items: $json.lineItems }) }}`",
    reasoning: "This is one ofthe most common expression errors in n8n, caused by providing a JavaScript object where a JSON string is expected, or malformed JSON. [Ref: Gemini III.B, Manus III.B]",
    documentationUrl: "https://docs.n8n.io/code/expressions/data-types/#json", // Or similar relevant section
    relatedRules: ['BR_EXP_005_AvoidCommonSyntaxErrors'],
    additionalData: { impact: 'Critical', effort: 'Low' }
  },
  {
    id: 'BR_EXP_007',
    name: "Test Complex Expressions with Sample Data",
    description: "During development, test complex n8n expressions directly in the node's expression editor or with a Set/NoOp node using sample input data to verify their output before running the full workflow.",
    descriptionForLLM: "EXPRESSION DEBUGGING: When writing or planning a complex expression, advise testing it in isolation. Use the n8n expression editor's preview (if available for the field) or temporarily connect a Set node with the expression to a Manual trigger with sample JSON input. Execute just that Set node to verify the expression's output. This helps catch errors early.",
    type: 'CONTEXTUAL_SUGGESTION', // An operational best practice
    priority: 'medium',
    category: 'expressions', // Also testing
    appliesToAgents: ['n8n-node-composer-agent' /* can suggest this to user if generating complex expr */, 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['complex expression', 'debug expression', 'test expression', 'expression editor']
    },
    recommendation: "For a complex expression, create a temporary Set node. Set its input to sample JSON data (using another Set node or a Manual trigger). In the target Set node, put your complex expression. Run only this node to see the result. Delete temporary nodes after testing.",
    reasoning: "Testing expressions in isolation is much faster and easier than debugging them within a long, complex workflow execution. It helps validate syntax and logic quickly. [Ref: ChatGPT 2.4, III.B (Testing transformations)]",
    
    
  },
  {
    id: 'BR_NODE_CodeReturnFormat', // Renamed from a previous iteration to be more findable
    name: "Ensure Correct Code Node Return Format",
    description: "Code nodes (and older Function/Function Item nodes) MUST return data in the n8n-expected format: an array of objects, where each object has a 'json' key containing the item data (e.g., `return [{json: { myKey: 'myValue' }}];`).",
    descriptionForLLM: "CRITICAL (Code Node): The JavaScript in a Code node (or legacy Function/Function Item) MUST return an array of objects. Each object in the array represents an n8n item and MUST have a top-level key named `json` containing the actual data for that item. Example: `return [{ json: { id: 1, name: 'Product A' } }, { json: { id: 2, name: 'Product B' } }];`. Returning a single object, a simple array, or objects without the `json` key will lead to errors or unexpected behavior downstream.",
    type: 'ALWAYS_APPLY', // For NodeComposer when generating Code node content
    priority: 'critical',
    category: 'node-usage', // Specifically for Code/Function nodes
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.functionNode', 'n8n-nodes-base.functionItemNode']
    },
    recommendation: "Always structure the return statement of a Code/Function node as `return [{json: outputData1}, {json: outputData2}, ...];`. Even for a single output item, it must be `return [{json: singleOutputItemData}];`. If the node should output no items, return `[]`.",
    example: "Code Node: `const processedItem = { message: 'Hello ' + $item.json.name }; return [{ json: processedItem }];`",
    reasoning: "n8n's execution engine expects data from Code/Function nodes in a specific array-of-json-key format. Deviating from this is a very common source of errors for users writing custom code. [Ref: Gemini III.A (Code Node), Manus 1.1 (Abus du noeud Function), ChatGPT III.B (Code Node format)]",
    documentationUrl: "https://docs.n8n.io/code/code-node/#data-structure",
    
    
  },
  {
    id: 'BR_NODE_PreferCodeOverFunction',
    name: "Prefer Code Node over Legacy Function/Function Item Nodes",
    description: "For new workflows or when refactoring, prefer using the modern 'Code' node over the older 'Function' and 'Function Item' nodes for custom JavaScript logic.",
    descriptionForLLM: "NODE USAGE: When custom JavaScript is needed, plan to use the 'Code' node. The older 'Function' and 'Function Item' nodes are generally considered legacy and might be deprecated or have fewer features in the future. The 'Code' node is more versatile and is the recommended standard for new development.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-usage',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-selector-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['custom javascript', 'script', 'function', 'custom code'],
      // Trigger if user explicitly asks for "Function node" or if planning custom JS
    },
    recommendation: "If custom JavaScript is required, use the 'Code' node. If an existing workflow uses 'Function' or 'Function Item' nodes, consider refactoring them to 'Code' nodes for consistency and future-proofing, unless there's a very specific reason not to.",
    reasoning: "The Code node is the modern, more powerful replacement for the older Function/Function Item nodes, offering better features and being the focus of ongoing n8n development. [Ref: Gemini III.A (Function Node Legacy), ChatGPT III.B]",
    relatedRules: ['BR_NODE_CodeReturnFormat', 'BR_PERF_005_OptimizeCodeNode'],
    
    
  },
  {
    id: 'BR_EXP_008',
    name: "Ensure Data Type Consistency in Expressions and Parameters",
    description: "Pay close attention to data types when using expressions. Ensure that values passed to node parameters match the expected type (string, number, boolean, object, array) to avoid runtime errors or unexpected behavior.",
    descriptionForLLM: "EXPRESSION DATA TYPES: Verify that data types are consistent. For example: 1. If a node parameter expects a Number, ensure your expression `{{ $json.value }}` resolves to a number (use `{{ parseInt($json.value) }}` or `{{ Number($json.value) }}` if it's a stringified number). 2. Boolean parameters often expect true/false literals, not just truthy/falsy strings (e.g., `{{ $json.isActive }}` is fine if it's a boolean; `{{ $json.status === 'active' }}` evaluates to a boolean). 3. When building JSON objects or arrays within expressions for parameters that expect them, ensure the syntax is correct.",
    type: 'ALWAYS_APPLY', // Core for NodeComposer
    priority: 'high',
    category: 'expressions',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies generally when expressions are used to set node parameters
    },
    recommendation: "When setting a node parameter with an expression: 1. Check the parameter's expected type in the n8n UI (hover over it). 2. Use explicit type conversion functions like `parseInt()`, `parseFloat()`, `Number()`, `String()`, `Boolean()` in expressions if source data type might mismatch. 3. For object/array parameters, ensure the expression evaluates to a well-formed JS object/array.",
    example: "Set Node, Number field: `{{ parseInt($json.countString) }}`. HTTP Request, Send Query Parameters (if expecting boolean as string): `active={{ $json.isActive ? 'true' : 'false' }}`",
    reasoning: "Data type mismatches between expression outputs and node parameter expectations are a common source of errors or silent misbehavior in n8n workflows. [Ref: Gemini 1.2 (Data Transformation Errors), Manus III.B (Problèmes de type)]",
    relatedRules: ['BR_EXP_005_AvoidCommonSyntaxErrors', 'BR_EXP_006_ValidJSONOutput'],
    
    
  },
  // --- HTTP Request Node ---
  {
    id: 'BR_NODE_HTTP_001',
    name: "Configure HTTP Request: Retry on Fail for Transient Errors",
    description: "Always configure the 'Retry on Fail' option in the HTTP Request node's Settings tab for calls to external APIs to handle transient network issues or temporary server errors (e.g., 5xx, 429).",
    descriptionForLLM: "HTTP REQUEST NODE: For reliability when calling external APIs, navigate to the node's 'Settings' tab and ENABLE 'Retry on Fail'. Configure a sensible number of retries (e.g., 3-5) and a delay (e.g., 2000-5000ms, consider exponential backoff pattern if implementing custom loop). This is crucial for handling transient network or server issues.",
    type: 'ALWAYS_APPLY', // This is a strong default for external calls
    priority: 'high',
    category: 'node-http', // More specific category
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent' /* plan for it */, 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest']
    },
    recommendation: "In HTTP Request node -> Settings tab: Set 'Retry on Fail' = true. Retries = 3. Delay = 3000ms (adjust as needed based on API).",
    example: "// Node Settings: Retry on Fail: true, Retries: 3, Retry Delay: 3000ms",
    reasoning: "Automated retries significantly improve workflow resilience against temporary external service disruptions or rate limiting, preventing unnecessary workflow failures. [Ref: Gemini III.A, 4.1; Manus 2.2, 4.1; ChatGPT 2.2, 4.1]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/settings/#retry-on-fail",
    relatedRules: ['BR_ERR_003_APIRetries', 'BR_PERF_004_RespectRateLimits'],
    additionalData: { impact: 'High', effort: 'Low' }
  },
  {
    id: 'BR_NODE_HTTP_002',
    name: "Configure HTTP Request: Explicit Timeouts",
    description: "Set an explicit 'Timeout' in the HTTP Request node's options to prevent workflows from hanging indefinitely on unresponsive APIs.",
    descriptionForLLM: "HTTP REQUEST NODE: In the node's 'Options' section (or 'Add Option' -> 'Timeout'), set an explicit timeout value in milliseconds (e.g., 30000 for 30 seconds). Do not rely on default timeouts which might be too long or undefined. This prevents the workflow from stalling if an API is unresponsive.",
    type: 'ALWAYS_APPLY',
    priority: 'medium',
    category: 'node-http',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest']
    },
    recommendation: "HTTP Request node -> Add Option -> Timeout. Set value (e.g., 30000 for 30s, 60000 for 60s for slower APIs).",
    example: "// Node Options: Timeout (ms): 30000",
    reasoning: "Explicit timeouts ensure that an unresponsive API call doesn't cause the entire workflow to hang, improving overall system stability and predictability. [Ref: Gemini III.A, Manus 1.1, ChatGPT 4.1]",
    relatedRules: ['BR_ERR_007_ManageExplicitTimeouts'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_NODE_HTTP_003',
    name: "Configure HTTP Request: Use 'Simplify Output' or Select Fields",
    description: "If only a subset of data from an API response is needed, use the HTTP Request node's 'Simplify Output' option or follow with an 'Edit Fields' node to reduce the data payload.",
    descriptionForLLM: "HTTP REQUEST NODE: If the API response is large and you only need a few fields: 1. Check the node's 'Options' for a 'Simplify Output' or similar setting and enable it if it prunes effectively for your needs. 2. If more specific field selection is needed, immediately follow the HTTP Request node with an 'Edit Fields' node (Mode: Keep Only Set Fields) or a 'Set' node (Mode: Keep Only Set) to extract only the required data.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-http', // Also performance
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      keywords: ['api response', 'large data', 'select fields', 'performance'],
      nodeTypes: ['n8n-nodes-base.httpRequest']
    },
    recommendation: "Enable 'Simplify Output' in HTTP Request options if it fits. Otherwise, use Edit Fields node after it to select only necessary fields from `{{$json.data}}` (or the relevant path).",
    reasoning: "Reduces data payload size passed through the workflow, improving performance and memory efficiency, and making downstream data easier to manage. [Ref: Gemini 2.3, 4.1; ChatGPT 2.3]",
    relatedRules: ['BR_PERF_003_FilterDataEarly', 'BR_PERF_015_MinimizeDataPassed'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_NODE_HTTP_004',
    name: "Configure HTTP Request: Handle Specific Error Codes",
    description: "When using 'Continue on Fail' with HTTP Request, explicitly check for common and API-specific HTTP error codes (401, 403, 404, 429, 5xx) in the error output to implement nuanced error handling.",
    descriptionForLLM: "HTTP REQUEST NODE (Error Handling): If 'Continue on Fail' is enabled, connect its error output to an IF or Switch node. Check `{{$json.error.context.response.statusCode}}` or `{{$json.error.cause.code}}` (structure may vary, inspect error object) for specific HTTP status codes (e.g., 401/403 for auth, 404 for not found, 429 for rate limit, 500/503 for server issues) and route to appropriate handling logic (e.g., refresh token for 401, retry for 429/503, log 'not found' for 404).",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-http', // Also error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest'],
      conditions: { nodeSetting_ContinueOnFail_isEnabled: true } // If Continue on Fail is active
    },
    recommendation: "Error output of HTTP Request -> IF Node. Condition 1: `{{ $json.error.context.response.statusCode === 401 }}` -> Path: Refresh Token. Condition 2: `{{ $json.error.context.response.statusCode === 429 }}` -> Path: Wait & Retry. Else: General Error Path.",
    example: "// IF Node after HTTP Error Output, checking: {{$json.error.context.response.statusCode}}",
    reasoning: "Handling specific HTTP error codes allows for more intelligent recovery strategies (e.g., retrying only for server errors or rate limits) rather than generic failure handling. [Ref: Gemini III.A, ChatGPT 2.2]",
    relatedRules: ['BR_ERR_002_NodeLevelContinueOnFail', 'BR_ERR_016_DistinguishErrorTypes'],
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_HTTP_005',
    name: "Configure HTTP Request: Use Credential for Authentication",
    description: "Always use n8n's built-in credential types (e.g., HTTP Basic Auth, OAuth2, Header Auth) for authentication with HTTP Request nodes. Avoid manually constructing auth headers with hardcoded tokens.",
    descriptionForLLM: "HTTP REQUEST NODE (Security/Auth): For authentication, select the appropriate 'Authentication' method from the dropdown (e.g., 'Basic Auth', 'OAuth2 API', 'Header Auth'). Then, select or create a pre-configured n8n Credential for that type. DO NOT manually add 'Authorization' headers with hardcoded API keys or tokens in the 'Headers' section.",
    type: 'ALWAYS_APPLY',
    priority: 'critical',
    category: 'node-http', // Also security
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest'],
      keywords: ['authenticate', 'api key', 'token', 'authorization', 'credentials']
    },
    recommendation: "In HTTP Request node: Set 'Authentication' parameter. Choose type. Select existing n8n Credential or create a new one. Example: Authentication: 'Header Auth', Credential: 'My Service API Key', Header Name: 'X-API-Key'.",
    reasoning: "Using n8n Credentials centralizes secret management, enhances security by encrypting secrets, and makes it easy to update credentials without modifying workflows. [Ref: Gemini 4.1, Manus 1.1, 4.1.5; ChatGPT 1, 4.1]",
    relatedRules: ['BR_SEC_001_NoHardcodedSecrets'], // General secret rule
    additionalData: { impact: 'Critical', effort: 'Low' }
  },

  // --- IF Node ---
  {
    id: 'BR_NODE_IF_001',
    name: "Keep IF Node Conditions Simple and Clear",
    description: "Conditions in IF nodes should be simple, direct, and easy to understand. For complex multi-part logic, prefer stacking multiple IF nodes or using a Code node.",
    descriptionForLLM: "IF NODE: Keep the conditions defined in an IF node clear and focused. If you need to check multiple complex conditions with many AND/ORs, it's often more readable and maintainable to: 1. Use multiple IF nodes in sequence (each checking one part of the logic). 2. Or, use a Set node before the IF to compute a boolean flag based on the complex logic, then have the IF node simply check that flag. 3. Or, for very complex decision trees, use a Switch node or a Code node.",
    type: 'ALWAYS_APPLY', // General good practice for IF node usage
    priority: 'medium',
    category: 'node-if', // Specific category
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.ifV2', 'n8n-nodes-base.if'] // Support older if possible
    },
    recommendation: "IF Node condition: `{{ $json.status === 'active' }}` is good. `{{ ($json.value > 10 && $json.type === 'A') || ($json.value < 5 && $json.type === 'B') }}` is becoming complex; consider breaking it down.",
    reasoning: "Overly complex IF conditions are hard to debug and maintain. Simpler conditions or dedicated nodes for complex logic improve workflow clarity. [Ref: Gemini III.A, Manus III.B, ChatGPT 1 (IF node logic abuse)]",
    relatedRules: ['BR_EXP_001_KeepExpressionsSimple', 'BR_DATA_002_CodeNodeForComplex'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_NODE_IF_002',
    name: "Explicitly Handle Both True and False Branches of IF Node",
    description: "Always connect nodes to both the 'true' (output 0 or top) and 'false' (output 1 or bottom) outputs of an IF node, even if one branch does nothing (use a NoOp or log). This makes the logic explicit and avoids unintended workflow halts if data only flows down one path and a downstream Merge node expects input from both.",
    descriptionForLLM: "IF NODE: Ensure both the 'true' and 'false' output paths of an IF node are explicitly handled. If one path is meant to do nothing, connect it to a 'NoOp' node or a logging step for clarity. Leaving a branch disconnected can make the workflow harder to understand and can cause issues if a downstream Merge node is expecting data from all paths.",
    type: 'ALWAYS_APPLY',
    priority: 'medium',
    category: 'node-if',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.ifV2', 'n8n-nodes-base.if']
    },
    recommendation: "Connect both output pins of the IF node. If the 'false' path means 'do nothing and continue with original item', connect it to the same node as the 'true' path if they merge, or to a NoOp node if they don't immediately merge. If it means 'stop processing this item', connect it to an End node or a filter-like behavior.",
    example: "IF (Condition) -> [Output 0 (True)] -> Process A; [Output 1 (False)] -> NoOp (Path B does nothing). Both may then go to a Merge node if needed.",
    reasoning: "Explicitly handling both branches makes the workflow logic clear and prevents ambiguity or errors, especially if downstream nodes (like Merge in 'Wait' mode) expect inputs from all preceding conditional paths. [Ref: Gemini III.A, Manus II.B (Gestion par branche), ChatGPT (IF node logic abuse)]",
    relatedRules: ['BR_DSGN_011_DataPathContinuity', 'BR_NODE_Merge_UnderstandModes'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },

  // --- Merge Node ---
  {
    id: 'BR_NODE_Merge_001',
    name: "Understand and Choose Correct Merge Node Mode",
    description: "Carefully select the Merge node's 'Mode' (Append, Combine by Matching Fields, Combine by Position, etc.) based on the desired data merging behavior. Misunderstanding modes is a common source of errors.",
    descriptionForLLM: "MERGE NODE: The 'Mode' setting is CRITICAL. 1. **Append:** Concatenates items from all inputs sequentially as they arrive (good for collecting all items from different branches). 2. **Combine by Matching Fields (e.g., Key):** Joins items from Input 1 and Input 2 where a specified field value matches. Choose 'Output Type' (Keep Matches, Keep Input 1, etc.) carefully. 3. **Combine by Position (Index):** Joins items based on their order/index in the input streams. Ensure both inputs have the same number of items in the same logical order. 4. **(Newer) SQL Query:** Allows complex SQL-like joins on input data. Understand its specific syntax. Default mode is often 'Append'. Ensure chosen mode matches the data structure and desired outcome.",
    type: 'ALWAYS_APPLY', // When using Merge node
    priority: 'critical',
    category: 'node-merge', // Specific category
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.merge']
    },
    recommendation: "Review Merge node documentation for selected 'Mode'. Test with sample data for all input branches. For joining data (like SQL JOIN), use 'Combine by Matching Fields' and specify correct Key fields. For simply collecting all items from parallel branches, 'Append' is usually correct.",
    example: "Mode 'Append': Input1=[A,B], Input2=[C,D] -> Output=[A,B,C,D]. Mode 'Combine by Key' (key='id'): Input1=[{id:1,val:'x'}], Input2=[{id:1,info:'y'}] -> Output=[{id:1,val:'x',info:'y'}] (if 'Keep Everything' and keys match).",
    reasoning: "The Merge node's behavior varies significantly with its Mode. Choosing the wrong mode leads to unexpected data loss, duplication, or incorrect combinations, which are common and hard-to-debug n8n issues. [Ref: Gemini III.A, Manus 1.1, ChatGPT III.A (Merge node quirks)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.merge/",
    relatedRules: ['BR_ERR_015_MergeNodeHaltOnWait'],
    additionalData: { impact: 'Critical', effort: 'Medium (requires understanding)' }
  },
  {
    id: 'BR_NODE_Merge_002',
    name: "Caution: Merge After IF Can Execute Both IF Branches",
    description: "Be aware that placing a Merge node directly after an IF node (where both IF outputs connect to Merge inputs) can sometimes cause both branches of the IF node to execute, as the Merge node attempts to pull data from all its inputs. Structure flow carefully.",
    descriptionForLLM: "MERGE NODE PITFALL: If an IF node's 'true' and 'false' outputs are *both directly connected* to the inputs of a single Merge node, the Merge node (depending on its mode and n8n version) might attempt to resolve *both* input paths from the IF. This could lead to the logic in *both* IF branches being executed, which is often unintended. If only the data from the *taken* IF branch should proceed, ensure the inactive IF branch produces no items for the Merge, or restructure the flow to avoid this direct IF-to-Merge pattern for both outputs if strict single-path execution is required before merge.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-merge', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      workflowPatterns: ['if_then_merge_pattern'] // Specific pattern to check
    },
    recommendation: "To ensure only one IF branch's data reaches a Merge node: 1. Make sure the untaken IF branch explicitly outputs zero items (e.g., ends or passes through a filter that removes everything for that path). 2. Or, use a more complex structure where each IF branch leads to a common *subsequent node* (not a Merge input directly), and that subsequent node receives data only from the active path. 3. Test this interaction carefully. Consider Merge 'Append' mode as it processes items as they arrive.",
    reasoning: "This non-intuitive Merge behavior when directly consuming both outputs of an IF can lead to unexpected execution of logic and incorrect data, a known n8n 'gotcha'. [Ref: Gemini III.A, Manus 1.1 (Utilisation inappropriée)]",
    relatedRules: ['BR_NODE_IF_002_HandleBothBranches', 'BR_DSGN_011_DataPathContinuity'],
    additionalData: { impact: 'High', effort: 'Medium to High to debug if occurs' }
  },
  // Continuing baseN8nRules.ts content
// Theme: Node-Specific Best Practices - Sub-Chunk 5.2

// Omitted fields for brevity: 
//   isActive: true
// (Timestamps & usage counts are runtime/DB concerns for custom rules)
  // --- Code Node (and legacy Function/Function Item) ---
  {
    id: 'BR_NODE_Code_001', // Was BR_NODE_CodeReturnFormat, now more comprehensive
    name: "Code Node: Adhere to Strict Return Format and Error Handling",
    description: "Code nodes MUST return an array of objects, each with a 'json' key (e.g., `return [{json: data}];`). Implement try-catch blocks within the JS for robust error handling.",
    descriptionForLLM: "CRITICAL (Code Node): 1. **Return Format:** JavaScript in a Code node (or legacy Function/Function Item) MUST return an array of objects. Each object in this array represents an n8n item and MUST have a top-level key named `json` containing the actual data for that item (e.g., `return [{ json: { id: 1, result: 'foo' } }];`). To output no items, return `[]`. 2. **Error Handling:** Wrap your main logic in `try...catch(e)` blocks. In the `catch` block, you can return an error structure (e.g., `return [{ json: { error: true, message: e.message, stack: e.stack } }];`) which can then be handled by downstream IF nodes checking for `.error`.",
    type: 'ALWAYS_APPLY', // For NodeComposer when generating Code node content
    priority: 'critical',
    category: 'node-code', // Specific category
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.functionNode', 'n8n-nodes-base.functionItemNode']
    },
    recommendation: "Always structure Code node return: `return [{json: outputData}];`. Implement `try { /* main logic */ } catch (e) { /* handle error, maybe return error object */ }`. Use `console.log()` within Code node for debugging during development (visible in browser dev tools or n8n logs depending on environment).",
    example: "Code Node: `try { const result = $item.json.value * 2; return [{json: { processedValue: result }}]; } catch (e) { return [{json: { error: e.message, originalItem: $item.json }}]; }`",
    reasoning: "Correct return format is fundamental for data to flow to subsequent n8n nodes. Internal try-catch makes Code nodes more resilient and prevents silent failures or abrupt workflow halts. [Ref: Gemini III.A, Manus 1.1, ChatGPT III.B, III.A (Code Node)]",
    documentationUrl: "https://docs.n8n.io/code/code-node/#data-structure",
    relatedRules: ['BR_PERF_005_OptimizeCodeNode', 'BR_DATA_002_CodeNodeForComplexLogicOnly'],
    additionalData: { impact: 'Critical', effort: 'Medium (requires JS discipline)' }
  },
  {
    id: 'BR_NODE_Code_002',
    name: "Code Node: Choose Correct Execution Mode",
    description: "Select the appropriate 'Execution Mode' in Code Node: 'Run Once for All Items' for batch processing the `items` array, or 'Run Once for Each Item' to process `$item` individually. Understand the performance implications.",
    descriptionForLLM: "CODE NODE (Execution Mode): Carefully choose the 'Execution Mode': 1. **'Run Once for All Items':** Your script receives all input items as an array named `items` (e.g., `items[0].json`). Use this for batch operations, aggregations, or when logic depends on the entire dataset. Often more performant for such tasks. 2. **'Run Once for Each Item':** Your script executes for each incoming item, available as `$item` (e.g., `$item.json`). Simpler for per-item transformations but can be less efficient than batch mode for large numbers of items if the per-item logic is simple.",
    type: 'ALWAYS_APPLY', // Important for NodeComposer
    priority: 'high',
    category: 'node-code', // Also performance
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode']
    },
    recommendation: "For batch transformations or aggregations on an array of items, prefer 'Run Once for All Items'. For simple, independent transformations on each item, 'Run Once for Each Item' is acceptable. Consider performance for >100s of items.",
    example: "'Run Once for All Items': `let sum = 0; for (const item of items) { sum += item.json.value; } return [{json: {totalSum: sum}}];`",
    reasoning: "Choosing the correct execution mode significantly impacts Code node performance and how you access/process data. 'Run Once for All Items' avoids the overhead of repeated script initializations for batch tasks. [Ref: Gemini 1.3, 2.3; Manus (no specific detail); ChatGPT III.A]",
    documentationUrl: "https://docs.n8n.io/code/code-node/#execution-modes",
    relatedRules: ['BR_PERF_005_OptimizeCodeNode'],
    additionalData: { impact: 'High for performance/logic', effort: 'Low (selection)' }
  },
  {
    id: 'BR_NODE_Code_003',
    name: "Code Node: Comment Complex Logic Thoroughly",
    description: "JavaScript within Code nodes, especially if complex or lengthy, MUST be well-commented to explain its purpose, inputs, outputs, and any non-obvious logic.",
    descriptionForLLM: "CODE NODE (Maintainability): If the JavaScript logic in a Code node is non-trivial (e.g., more than a few lines, complex conditions, loops, custom functions): It MUST include comments explaining: 1. The overall purpose of the script. 2. Expected input data structure (`$item.json` or `items`). 3. The intended output data structure. 4. Any complex algorithms or decision points.",
    type: 'ALWAYS_APPLY',
    priority: 'medium',
    category: 'node-code', // Also documentation
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode', 'n8n-nodes-base.functionNode', 'n8n-nodes-base.functionItemNode'],
      conditions: { codeNodeScriptLength_gt: 10 } // Example trigger: if script is > 10 lines
    },
    recommendation: "Add JSDoc-style comments or clear inline comments. Example: `// Calculate weighted average for each item\n// items: array of { json: { value: number, weight: number } }\n// returns: array of { json: { originalItem: any, weightedAvg: number } }`",
    reasoning: "Well-commented Code nodes are crucial for maintainability, as the logic is not visually apparent on the n8n canvas. Un commented complex code becomes a 'black box'. [Ref: Gemini III.A, Manus 1.1, ChatGPT 4.1]",
    additionalData: { impact: 'High for maintainability', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_Code_004',
    name: "Code Node: Avoid Direct External API/DB Calls (Prefer Dedicated Nodes)",
    description: "Avoid making direct network calls (e.g., using `fetch` or database drivers) from within Code node JavaScript if a dedicated n8n integration node exists for that service. Use n8n nodes for their built-in credential management, retry logic, and standardized error handling.",
    descriptionForLLM: "CODE NODE (Best Practice): Do NOT typically make direct HTTP calls (e.g., using `await fetch(...)`) or direct database connections/queries from inside a Code node if an n8n HTTP Request node or a dedicated database node (Postgres, MySQL etc.) is available and can perform the task. Dedicated nodes handle credentials securely, offer built-in retries, and integrate better with n8n's error handling and logging. Use Code nodes for data transformation, not for replacing integration nodes.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-code', // Also node-usage
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.codeNode'],
      keywords: ['fetch', 'axios', 'http call in code', 'database connect in code', 'api call in script']
    },
    recommendation: "If an API call is needed, use an HTTP Request node before or after the Code node. Pass data to/from the Code node. If DB operation needed, use the appropriate DB node. Reserve Code node for data logic.",
    reasoning: "Using dedicated n8n nodes for integrations is more secure (credential handling), more robust (retries, error patterns), and more maintainable than embedding such calls in custom JS. [Ref: Gemini 2.3 (Minimize External Calls from Code Node)]",
    relatedRules: ['BR_NODE_HTTP_005_UseCredentialForAuth', 'BR_NODE_DB_UseCredentials'],
    additionalData: { impact: 'High', effort: 'Low (use existing node)' }
  },

  // --- Set Node / Edit Fields Node ---
  {
    id: 'BR_NODE_Set_001',
    name: "Set Node: Use 'Keep Only Set' for Clean Output Objects",
    description: "When using a Set node to create a new, clean data object with only specific fields, enable 'Keep Only Set' mode. This prevents unintended passthrough of all previous input fields.",
    descriptionForLLM: "SET NODE (Data Shaping): If the goal is to create a new item with a specific, limited set of fields (e.g., preparing a clean payload for an API call), use the Set node and in its 'Options', set 'Keep Only Set' to true. This ensures only the fields explicitly defined in the Set node's parameters are present in the output item, discarding all fields from the input item.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-set', // Specific category
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.set'],
      keywords: ['clean object', 'specific payload', 'remove fields', 'select output fields']
    },
    recommendation: "In Set node -> Options tab: Toggle 'Keep Only Set' to ON if you only want the fields defined in this Set node to be outputted. If OFF (default), all input fields are passed through along with new/modified ones.",
    example: "Input: `{a:1, b:2, c:3}`. Set Node (Keep Only Set: true, Parameters: `d={{$json.a + $json.b}}`). Output: `{d:3}`. (If false, output would be `{a:1, b:2, c:3, d:3}`)",
    reasoning: "'Keep Only Set' is essential for creating precisely structured output items and preventing unintended data leakage or oversized payloads downstream. [Ref: Implicit in data shaping discussions, common n8n user tip]",
    relatedRules: ['BR_PERF_015_MinimizeDataPassed'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  // Note: Edit Fields node provides similar functionality with "Remove Fields" and "Rename Fields" modes.
  // A rule for "Use Edit Fields for Selecting/Renaming/Removing" could be added, or combined with Set node advice.
  // For now, BR_DATA_001 (Set Node for Simple Transformations) generally covers this.

  // --- Webhook Trigger Node & Respond to Webhook Node ---
  // BR_ERR_008 ("Ensure Webhooks Respond Promptly") already covers a critical aspect.
  // BR_DSGN_007 ("Implement 'Kill Switch'") is relevant for webhook-triggered flows.
  // BR_ERR_004 ("Validate External Input Data Rigorously") is critical for webhooks.
  {
    id: 'BR_NODE_Webhook_001',
    name: "Webhook Trigger: Secure with Authentication",
    description: "For Webhook triggers that handle sensitive data or perform critical actions, always use an authentication method (e.g., Basic Auth, Header Auth, Custom Auth Workflow) to prevent unauthorized access.",
    descriptionForLLM: "WEBHOOK TRIGGER (Security): If a Webhook trigger will receive sensitive data or initiate critical actions, DO NOT leave it unauthenticated (public). In the Webhook node settings: 1. Choose an 'Authentication' method (e.g., 'Basic Auth', 'Header Auth'). 2. Configure the associated n8n Credential. 3. Alternatively, for more complex auth, the webhook can call a dedicated authentication sub-workflow as its first step.",
    type: 'ALWAYS_APPLY', // For any sensitive/critical webhook
    priority: 'critical',
    category: 'node-webhook', // Specific category, also security
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.webhook'],
      keywords: ['sensitive data', 'critical action', 'public webhook', 'unauthenticated', 'secure webhook']
    },
    recommendation: "In Webhook node: Set 'Authentication' (e.g., to 'Basic Auth'). Create/select a credential with username/password. The calling service must then provide these credentials.",
    reasoning: "Publicly accessible webhooks without authentication are a significant security risk, allowing anyone who discovers the URL to trigger the workflow and potentially access or manipulate data. [Ref: Gemini 1.1 (Webhook Security), 4.1.5; Manus 1.1, 4.1.5]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/triggers/n8n-nodes-base.webhook/#authentication",
    relatedRules: ['BR_SEC_00X_GeneralInputValidation'], // Related to overall security
    additionalData: { impact: 'Critical', effort: 'Low to Medium' }
  },
  {
    id: 'BR_NODE_Webhook_002',
    name: "Webhook Trigger: Ensure Path Uniqueness and Stability",
    description: "Webhook paths must be unique across the entire n8n instance. Editing a Webhook node can change its URL, requiring updates in calling systems. Use stable, meaningful paths.",
    descriptionForLLM: "WEBHOOK TRIGGER (Configuration): 1. **Path Uniqueness:** The 'Path' set in the Webhook node MUST be unique across all workflows on the n8n instance. A conflict will prevent one of the webhooks from activating. 2. **URL Stability:** Be aware that if you edit a Webhook node (especially its path or HTTP method), its unique production URL (containing an ID) might change. You MUST update this new URL in any system that calls the webhook. 3. Choose a short, descriptive 'Path' (e.g., 'shopify-new-order', 'user-signup-event').",
    type: 'ALWAYS_APPLY', // For Composer/Validator
    priority: 'high',
    category: 'node-webhook',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-architect-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.webhook']
    },
    recommendation: "Use clear, unique paths for Webhook nodes. After any edit to a Webhook node, re-copy its 'Test URL' and 'Production URL' and update all calling systems. Document webhook URLs carefully.",
    reasoning: "Webhook path conflicts prevent activation. Un-updated URLs after node edits lead to 'ghost' webhooks that no longer receive data. [Ref: Gemini 1.1 (Webhook Path Conflicts), 1.2; ChatGPT 1 (Misconfigured Webhooks)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/triggers/n8n-nodes-base.webhook/#:~:text=Webhook%20URLs%20are%20globally%20unique",
    
    
  },

  // --- Database Nodes (General Principles) ---
  // BR_PERF_010 (Optimize DB Queries) and BR_PERF_002 (Batch Ops) cover key DB performance.
  {
    id: 'BR_NODE_DB_001',
    name: "Database Nodes: Use Credentials for Connection",
    description: "Always use n8n's credential system for database connection parameters (host, user, password, database name). Do not hardcode these in node parameters.",
    descriptionForLLM: "DATABASE NODES (Security): When configuring any database node (Postgres, MySQL, SQL Server, MongoDB, etc.), connection details (host, port, user, password, database) MUST be stored in an n8n Credential specific to that database type. Select this credential in the node. DO NOT type connection strings or passwords directly into node parameters.",
    type: 'ALWAYS_APPLY',
    priority: 'critical',
    category: 'node-database', // Specific category, also security
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies to all n8n database nodes
      nodeTypes: ['n8n-nodes-base.postgres', 'n8n-nodes-base.mysql', 'n8n-nodes-base.mssql', 'n8n-nodes-base.mongoDb', /* etc. */]
    },
    recommendation: "Create an n8n Credential for your database type (e.g., 'Postgres Credential'). Enter host, port, user, password, DB name there. In the database node, select this credential from the 'Credential for...' dropdown.",
    reasoning: "Using credentials for database connections is essential for security (secrets are encrypted) and manageability (easy to update connection details in one place). [Ref: Gemini 4.1, Manus 1.1 (Hardcoding)]",
    relatedRules: ['BR_SEC_001_NoHardcodedSecrets'],
    additionalData: { impact: 'Critical', effort: 'Low' }
  },
  {
    id: 'BR_NODE_DB_002',
    name: "Database Nodes: Handle Connection Errors and Query Failures",
    description: "Implement error handling for database nodes to manage issues like connection failures, invalid queries, or constraint violations.",
    descriptionForLLM: "DATABASE NODES (Error Handling): Database operations can fail due to various reasons (connection unavailable, SQL syntax error, primary key violation, etc.). Plan for these failures: 1. Use the node's 'Continue on Fail' setting if appropriate for the specific operation. 2. Connect the error output to an IF node to check the error type or message. 3. Implement retry logic for transient connection issues. 4. Log detailed errors for debugging.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'high',
    category: 'node-database', // Also error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.postgres', 'n8n-nodes-base.mysql', /* etc. */]
    },
    recommendation: "Enable 'Continue on Fail' for DB nodes where partial failure is acceptable. Inspect `{{$json.error}}` from error output. For connection errors, consider a retry loop with delay. For data/constraint errors, log and potentially route the problematic item to a DLQ.",
    reasoning: "Robust error handling for database operations is crucial for data integrity and workflow reliability. [Ref: Gemini 1.2 (API Call Failures - applies to DBs), Manus 1.2]",
    relatedRules: ['BR_ERR_002_NodeLevelContinueOnFail', 'BR_ERR_003_APIRetries' /* Concept applies */],
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  // Continuing baseN8nRules.ts content
// Theme: Node-Specific Best Practices - Sub-Chunk 5.3

// Omitted fields for brevity: 
//   isActive: true
// (Timestamps & usage counts are runtime/DB concerns for custom rules)

  // --- SplitInBatches Node (often named Loop Over Items in UI) ---
  {
    id: 'BR_NODE_SIB_001',
    name: "SplitInBatches/Loop Over Items: Optimal Batch Size and Loop Structure",
    description: "Use 'SplitInBatches' (or 'Loop Over Items') to process large arrays in manageable chunks, preventing memory issues. Configure batch size appropriately and ensure the loop structure correctly processes each batch and terminates.",
    descriptionForLLM: "NODE USAGE (SplitInBatches/Loop Over Items): When processing a large array of items: 1. Use this node to break the array into smaller batches (e.g., `Batch Size`: 10 to 200, depending on item size and downstream processing). 2. The node outputs one item per batch. Connect its 'Output' to the start of your batch processing logic. 3. To loop through all batches, connect the *end* of your batch processing logic back to the *input* of the 'SplitInBatches/Loop Over Items' node. The node automatically handles iteration and stops when all batches are processed. 4. Often include a 'Wait' node in the loop if batch processing involves API calls.",
    type: 'ALWAYS_APPLY', // When this node is used
    priority: 'high',
    category: 'node-loop', // Specific category for looping nodes
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.splitInBatches', 'n8n-nodes-base.loopOverItems'], // Use current official name if one supersedes
      keywords: ['batch processing', 'large array', 'loop items', 'chunk data']
    },
    recommendation: "Set 'Batch Size' (e.g., 50). Connect output to batch logic. Connect end of batch logic back to this node's input. Use `{{$item.json.currentBatch}}` (or similar, check node's output) to access the array for the current batch.",
    example: "Get All Records (outputs array in `records`) -> Set (inputField=`{{$json.records}}`) -> SplitInBatches (Input Field: `inputField`, Batch Size: 100) -> [Output] -> Process Batch Sub-Workflow -> [Input of SplitInBatches]",
    reasoning: "This node is the standard n8n way to iterate over large datasets in a memory-efficient manner by processing in chunks. Correctly structuring the loop is key to its function. [Ref: Gemini 2.3 (Batch Processing), ChatGPT 2.1 (Loop Over Items)]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.loopoveritems/", // Link to official Loop Over Items if it's the current name
    relatedRules: ['BR_PERF_001_LoopOptimization', 'BR_PERF_004_RespectRateLimits'],
    additionalData: { impact: 'High for performance/memory', effort: 'Medium to configure loop correctly' }
  },

  // --- Error Trigger Node ---
  // BR_ERR_001 already mandates planning for Global Error Workflows using this.
  // BR_ERR_012 already covers nuances of trigger errors for Global Error Workflows.
  // BR_ERR_014 already covers data context limitations in Global Error Workflows.
  {
    id: 'BR_NODE_ErrorTrigger_001',
    name: "Error Trigger: Understand its Data Input Structure",
    description: "The Error Trigger node provides specific data about the failed workflow execution (e.g., error message, workflow details, execution ID, last node data if available). Utilize these fields for effective error logging and notification.",
    descriptionForLLM: "NODE USAGE (Error Trigger): When this node starts an error workflow, its output (available as `{{$json}}`) contains details about the original failure. Key fields usually include: `{{$json.error.message}}`, `{{$json.error.stack}}`, `{{$json.error.node.name}}` (name of failed node), `{{$json.error.node.type}}`, `{{$json.workflow.id}}`, `{{$json.workflow.name}}`, `{{$json.execution.id}}`, `{{$json.execution.url}}`. Reference these for logging and notifications. Note: `{{$json.error.itemIndex}}` and `{{$json.error.inputData}}` (partial data from failed node's input) might be available if the error occurred after the trigger in the main flow.",
    type: 'ALWAYS_APPLY', // When composing an error workflow
    priority: 'high',
    category: 'node-errortrigger',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent' /* when planning error workflow content */],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.errorTrigger']
    },
    recommendation: "In an error workflow: Use a Set node after Error Trigger to extract useful fields: `errorMessage = {{$json.error.message}}`, `workflowName = {{$json.workflow.name}}`, `executionUrl = {{$json.execution.url}}`. Pass these to notification/logging nodes.",
    example: "Error Trigger -> Set (Extract error details) -> Slack (Send: `Workflow {{ $json.workflowName }} failed! Error: {{ $json.errorMessage }}. See: {{ $json.executionUrl }}`)",
    reasoning: "Correctly using the data provided by the Error Trigger is essential for creating informative error alerts and logs. [Ref: Gemini 2.2, 4.2; Manus 2.2, 4.2; ChatGPT 2.2]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/triggers/n8n-nodes-base.errortrigger/",
    relatedRules: ['BR_ERR_001_ImplementGlobalErrorWorkflow', 'BR_ERR_017_LogSufficientContext'],
    additionalData: { impact: 'High for debuggability', effort: 'Low' }
  },

  // --- Manual Trigger Node (Start Node) ---
  // BR_DSGN_013 already covers using this for development/testing with default data.
  {
    id: 'BR_NODE_ManualTrigger_001',
    name: "Manual Trigger/Start Node: Use for Ad-Hoc Runs and Testing",
    description: "The 'Manual' trigger (often the default 'Start' node) is primarily for manually executing workflows from the n8n editor, ideal for development, testing, or ad-hoc tasks. It can accept input JSON for testing.",
    descriptionForLLM: "NODE USAGE (Manual Trigger/Start): This node allows you to execute a workflow directly by clicking 'Execute Workflow' in the n8n editor. Key uses: 1. **Development/Testing:** Paste sample JSON into its 'JSON Input' parameter (available when you run it or set fixed input) to simulate trigger data and test your workflow logic. 2. **Ad-Hoc Tasks:** For workflows that are run manually on demand rather than scheduled or webhook-triggered. It has no incoming connections other than itself (for looping if misused).",
    type: 'CONTEXTUAL_SUGGESTION', // When advising on triggers or testing
    priority: 'informational',
    category: 'node-manualtrigger',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.manualTrigger', 'n8n-nodes-base.start']
    },
    recommendation: "For testing, use the 'Start' node: Click 'Execute Workflow', then in the dialog, paste your test JSON into the 'JSON (Workflow input data)' field and run. For fixed test data during development, place a Set node after the Start node with 'Execute Once' enabled, outputting your test data.",
    reasoning: "The Manual/Start trigger is the simplest way to initiate and test workflows with controlled inputs directly from the editor. [Ref: ChatGPT 4.1 (Manual Trigger for testing)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/triggers/n8n-nodes-base.manualtrigger/", // Or Start node docs
    relatedRules: ['BR_DSGN_013_UseManualTriggerForDev'],
    additionalData: { impact: 'High for Dev/Test efficiency', effort: 'Low' }
  },
  
  // --- AI Nodes (General Principles, e.g., OpenAI, LangChain, etc.) ---
  // Specific models/integrations would have their own detailed rules if needed.
  {
    id: 'BR_NODE_AI_001',
    name: "AI Nodes: Manage Prompts and Context Windows Carefully",
    description: "When using AI nodes (OpenAI, LangChain, etc.), craft clear and concise prompts. Be mindful of the specific model's context window limitations and manage input data/history size accordingly to avoid truncation or errors.",
    descriptionForLLM: "AI NODE USAGE (General): 1. **Prompts:** Design system prompts that are clear, specific, and guide the LLM effectively. For user inputs, ensure they are well-formatted. 2. **Context Window:** Be aware of the token limit for the selected AI model (e.g., GPT-3.5-turbo, Claude 3 Haiku). If passing large texts or long conversation histories, summarize or truncate them *before* sending to the AI node to prevent exceeding the context window, which causes errors or poor responses. 3. **Cost:** LLM calls can be expensive; monitor token usage.",
    type: 'ALWAYS_APPLY', // For any AI node usage
    priority: 'high',
    category: 'node-ai',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies to any node that is an AI/LLM integration
      nodeTypes: ['n8n-nodes-ai.openAi', 'n8n-nodes-Langchain.langchainAgent', /* add other AI node type IDs */]
    },
    recommendation: "Before calling an AI node with large text inputs or history: Use a Code node or Set node to summarize/truncate data. Check the AI model's documentation for its specific context window limit in tokens. Use a tokenizer library (if available in Code node) to estimate input tokens.",
    example: "// Conceptual: Get Article Text -> Code Node (Summarize if text > N tokens) -> OpenAI Node (Prompt with summarized text).",
    reasoning: "Effective prompting and context window management are critical for successful and cost-efficient interaction with LLMs. Exceeding context limits is a common failure point. [Ref: Gemini III.A (AI Nodes), ChatGPT 2.2 (OpenAI stuck runs due to context/history)]",
    relatedRules: ['BR_PERF_00X_CostControlLLM' /* Placeholder for a general LLM cost rule */, 'BR_ERR_00X_HandleLLM_APIErrors' /* Placeholder */ ],
    additionalData: { impact: 'High for AI reliability/cost', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_AI_002',
    name: "AI Nodes: Implement Robust Error Handling and Retries for API Calls",
    description: "Calls to AI model APIs (OpenAI, Anthropic, etc.) can fail due to rate limits, server issues, content filtering, or stuck runs. Implement error handling and consider retries for transient issues.",
    descriptionForLLM: "AI NODE ERROR HANDLING: Calls to LLM APIs are network requests and can fail. 1. Use the AI node's 'Continue on Fail' option and inspect the `{{$json.error}}` for specific issues (e.g., rate limits, content policy violations, model overload). 2. Some AI services (like OpenAI Assistants) can have runs get 'stuck'; plan for timeouts or a mechanism to cancel/retry these. 3. For transient errors (server busy, temporary rate limit), implement a retry mechanism (e.g., a loop with Wait node if the AI node itself doesn't have robust built-in retries).",
    type: 'ALWAYS_APPLY', // For any AI node usage
    priority: 'high',
    category: 'node-ai', // Also error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-ai.openAi', 'n8n-nodes-Langchain.langchainAgent', /* etc. */]
    },
    recommendation: "Enable 'Continue on Fail' for AI nodes. Check error output for `statusCode` (e.g., 429 for rate limit, 503 for server busy) or specific error messages. Implement a retry loop for retryable errors. Log all errors.",
    example: "OpenAI Node (Continue on Fail: true) -> [Error Output] -> IF (error is 429?) -> True: Wait & Retry / False: Log & Handle Non-Retryable Error.",
    reasoning: "AI API calls can be unreliable. Robust error handling and retries are essential for building dependable AI-powered workflows. Stuck runs with OpenAI Assistants are a known issue requiring specific attention. [Ref: Gemini III.A (AI Nodes), ChatGPT 2.2 (OpenAI stuck runs, API errors)]",
    relatedRules: ['BR_ERR_002_NodeLevelContinueOnFail', 'BR_ERR_003_APIRetries'],
    additionalData: { impact: 'High', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_AI_003',
    name: "AI Nodes: Define Tools Precisely for Agentic Behavior",
    description: "If using AI nodes that support agentic behavior with tools (e.g., LangChain Agent, OpenAI Assistants with Function Calling), define tools with clear names, detailed descriptions of their purpose, and exact JSON schemas for their input parameters. The AI relies entirely on these definitions to use tools correctly.",
    descriptionForLLM: "AI NODE (Agents/Tools): When configuring an AI node to use tools (function calling): 1. **Tool Name:** Must be concise and descriptive. 2. **Tool Description:** CRITICAL. Clearly explain what the tool does, when the AI should use it, and what kind of questions it answers or actions it performs. 3. **Input Parameters Schema:** Provide a precise JSON schema (or Zod schema if node supports it) for the arguments the tool expects. Define types, required fields, and descriptions for each parameter. The AI will use this schema to generate the arguments when calling your tool.",
    type: 'ALWAYS_APPLY', // When AI node uses tools
    priority: 'critical',
    category: 'node-ai',
    appliesToAgents: ['n8n-node-composer-agent' /* if configuring these nodes */, 'n8n-architect-agent' /* if planning tool usage */],
    triggerContext: {
      nodeTypes: ['n8n-nodes-Langchain.langchainAgent', 'n8n-nodes-ai.openAi' /* when using assistants/function calling */]
    },
    recommendation: "Example Tool Definition for an n8n 'Get Node Info' tool: Name: `getNodeInformation`. Description: 'Fetches detailed information about a specific n8n node, including its parameters and description. Use this when the user asks about a particular node or how to configure it.' Parameters Schema: `{ \"type\": \"object\", \"properties\": { \"node_type_id\": { \"type\": \"string\", \"description\": \"The exact type ID of the n8n node, e.g., n8n-nodes-base.httpRequest\" } }, \"required\": [\"node_type_id\"] }`",
    reasoning: "The AI's ability to correctly decide when and how to use tools depends entirely on the clarity and accuracy of the tool definitions provided to it. Vague or incorrect definitions lead to poor tool usage and agent failures. [Ref: Gemini 4.1 (AI Nodes), General LLM Agent Best Practices]",
    documentationUrl: "https://cookbook.openai.com/examples/how_to_call_functions_with_chat_models", // General concept
    additionalData: { impact: 'Critical for agent functionality', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_RespondWebhook_001',
    name: "Respond to Webhook: Customize Response Code and Body",
    description: "Utilize the 'Respond to Webhook' node to send specific HTTP status codes (e.g., 200, 201, 202, 400, 500) and custom JSON response bodies back to the webhook caller.",
    descriptionForLLM: "NODE USAGE (Respond to Webhook): When using this node: 1. Set the 'Response Code' parameter appropriately (e.g., `200` for generic success, `201` for resource created, `202` for accepted if processing asynchronously). 2. If the caller expects a response body, set 'Response Data' to 'JSON' and provide the JSON data in the 'Response Body' parameter (e.g., `{{ ({ \"status\": \"success\", \"trackingId\": $json.id }) }}`). An empty response is also valid if the caller doesn't expect a body.",
    type: 'ALWAYS_APPLY', // When this node is being composed
    priority: 'medium',
    category: 'node-respondwebhook',
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.respondToWebhook']
    },
    recommendation: "Set 'Response Code' (e.g., 200). For 'Response Data', choose 'JSON' and provide a body like `{{ ({ \"message\": \"Request received successfully\" }) }}` or use 'No Body'.",
    example: "Respond to Webhook: Response Code: 201, Response Data: JSON, Response Body: `{{ ({ \"itemId\": $json.newItemId, \"status\": \"created\" }) }}`",
    reasoning: "Customizing response codes and bodies provides clear feedback to the system calling the webhook, adhering to HTTP best practices and enabling better integration. [Ref: General HTTP best practices, implied by n8n examples]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.respondtowebhook/",
    relatedRules: ['BR_ERR_008_WebhooksRespondPromptly'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },

  // --- Set Node / Edit Fields Node (Further Nuances) ---
  // BR_DATA_001 (Set for Simple) and BR_NODE_Set_001 (Keep Only Set) already exist.
  {
    id: 'BR_NODE_SetEdit_001',
    name: "Set/Edit Fields: Use 'Execute Once' for Initial Setup or Constants",
    description: "If a Set or Edit Fields node is used to define initial constants or setup data that only needs to be established once per workflow execution (not per item), enable its 'Execute Once' setting.",
    descriptionForLLM: "NODE USAGE (Set/Edit Fields for Setup): If a Set or Edit Fields node is used at the beginning of a workflow to define constants, initial configuration, or test data that should only be computed once for the entire workflow run (not for each item if items are processed later): Go to the node's 'Settings' tab and enable 'Execute Once'. This prevents it from re-running for every item if the workflow processes multiple items.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-set', // or node-editfields
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      keywords: ['constants', 'initial config', 'setup data', 'execute once', 'workflow start'],
      nodeTypes: ['n8n-nodes-base.set', 'n8n-nodes-base.editFields']
      // Typically relevant if these nodes are very early in the flow, before any item looping
    },
    recommendation: "For a Set/Edit Fields node defining global constants: Settings tab -> Enable 'Execute Once'.",
    example: "Start -> Set (Constants: API_URL='...', API_KEY_REF='{{$env.KEY}}', Execute Once: true) -> [Rest of workflow]",
    reasoning: "'Execute Once' ensures that setup operations are performed only a single time per workflow execution, improving efficiency and preventing unintended re-computation for each item in a batch. [Ref: Common n8n user tip/pattern, ChatGPT 4.1 (Execute Once for safe testing)]",
    documentationUrl: "https://docs.n8n.io/nodes/core-concepts/execution-modes/#execute-once",
    additionalData: { impact: 'Medium for performance/correctness', effort: 'Low' }
  },

  // --- NoOp Node (No Operation) ---
  {
    id: 'BR_NODE_NoOp_001',
    name: "NoOp Node: Use for Branch Clarity or as a Pass-Through",
    description: "The NoOp (No Operation) node can be used to explicitly mark the end of a conditional branch that does nothing, to provide a clear visual connection point, or as a simple pass-through node for data without modification.",
    descriptionForLLM: "NODE USAGE (NoOp): Consider using a NoOp node in these scenarios: 1. **Branch Termination:** If an IF or Switch branch requires no action, connect it to a NoOp node for visual clarity that the path is intentionally empty, rather than leaving it dangling. 2. **Merge Point Prep:** As a clean merge point before a Merge node if branches have complex preceding logic. 3. **Simple Pass-Through/Renaming:** Can be used to simply pass data through if you want to rename the node for documentation purposes in a specific part of the flow (though a Set node might be clearer for explicit data passthrough).",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low',
    category: 'node-noop',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['empty branch', 'no action', 'visual clarity', 'merge point', 'pass through'],
      nodeTypes: ['n8n-nodes-base.ifV2', 'n8n-nodes-base.switch', 'n8n-nodes-base.noOp']
    },
    recommendation: "If an IF branch does nothing, connect its output to a NoOp node labeled, e.g., 'No Action Needed'. This makes the workflow's intent explicit.",
    example: "IF (Condition) -> [True] -> Process Data; [False] -> NoOp (Label: 'Condition False - No Action'). Both then might connect to a common next step.",
    reasoning: "NoOp nodes improve workflow readability by making intentional empty paths or pass-through points explicit, aiding in understanding and debugging. [Ref: Gemini III.A (IF Node - handle both branches), ChatGPT 4.1 (NoOp for dummy output)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.noop/",
    additionalData: { impact: 'Low (readability)', effort: 'Low' }
  },

  // --- Switch Node ---
  {
    id: 'BR_NODE_Switch_001',
    name: "Switch Node: Prefer for Multi-Value Conditional Routing",
    description: "For routing based on multiple distinct values of a single field, prefer the Switch node over multiple chained IF nodes for better clarity and maintainability.",
    descriptionForLLM: "NODE USAGE (Switch): If the workflow logic needs to branch based on several specific values of a single input field (e.g., `status` can be 'pending', 'approved', 'rejected', 'archived'), use a 'Switch' node. Configure its 'Field to Route By' to the input field (e.g., `{{$json.status}}`). Then, for each possible value, add a 'Routing Rule' and connect its output to the appropriate branch. This is generally cleaner than multiple nested or sequential IF nodes checking for equality.",
    type: 'CONTEXTUAL_SUGGESTION', // When Architect is planning complex routing
    priority: 'medium',
    category: 'node-switch', // Also workflow-design
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['multiple conditions', 'route by value', 'many if statements', 'decision tree simple'],
      workflowPatterns: ['multi_path_routing_by_value']
    },
    recommendation: "If checking `{{$json.type}}` for values 'A', 'B', 'C', 'D': Use a Switch node with Field to Route By: `{{$json.type}}`. Add rules for 'A', 'B', 'C', 'D', each routing to a different output. Provide a default 'fallback' output if none match.",
    example: "Switch (Field: `{{$json.orderStatus}}`) -> [Output 'shipped'] -> Notify Customer; [Output 'pending'] -> Check Inventory; [Output 'cancelled'] -> Update DB.",
    reasoning: "The Switch node provides a more organized and readable way to implement multi-way branching based on the value of a single field compared to deeply nested or chained IF nodes. [Ref: Gemini III.A (IF Node - stack IFs or use Switch), ChatGPT 2.2 (Switch node)]",
    documentationUrl: "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.switch/",
    relatedRules: ['BR_NODE_IF_001_KeepIFConditionsSimple'],
    additionalData: { impact: 'Medium (readability)', effort: 'Low' }
  },

  // --- General Node Settings/Options ---
  {
    id: 'BR_NODE_GEN_001',
    name: "Review and Utilize Node 'Settings' and 'Options' Tabs",
    description: "Many n8n nodes have a 'Settings' tab (for behavior like 'Continue on Fail', 'Execute Once', 'Always Output Data') and an 'Options' or 'Add Option' section (for parameters like 'Timeout', 'Simplify Output', 'Batching'). Always review these for relevant configurations.",
    descriptionForLLM: "NODE CONFIGURATION (General): When composing or planning any node, especially integration nodes (HTTP, DB, specific services) or flow control nodes (IF, Merge, Loops): Check its 'Settings' tab and 'Options' (or 'Add option' dropdown) for important behavioral controls. Key settings often found here include: 'Continue on Fail', 'Execute Once', 'Always Output Data', 'Timeout', 'Retry on Fail', 'Batching Options', 'Simplify Output', 'Include/Exclude Fields'. Utilize these to build more robust and performant workflows.",
    type: 'ALWAYS_APPLY', // Core knowledge for Architect and Composer
    priority: 'high',
    category: 'node-usage',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies to virtually all nodes being configured
    },
    recommendation: "Before finalizing a node's configuration, always open its 'Settings' tab and explore the 'Add option' dropdown (if present) to see what behavioral and performance tuning parameters are available. Configure them appropriately for your use case.",
    reasoning: "Many powerful n8n features for error handling, performance, and data control are located in these often-overlooked node settings and options. Using them effectively is key to building professional-grade workflows. [Ref: Gemini 1.1 (Unoptimized Node Settings), 4.2 (Config Params), ChatGPT 2.2, 4.1 (Node Settings)]",
    additionalData: { impact: 'High', effort: 'Low (awareness)' }
  },
  {
    id: 'BR_NODE_GEN_002',
    name: "Utilize 'Simplify Output' Option When Available and Appropriate",
    description: "Many nodes offer a 'Simplify Output' option (often under 'Options' or 'Settings') which returns a cleaner, flatter, or reduced set of data. Enable this if the full, complex raw output of the node is not needed downstream.",
    descriptionForLLM: "NODE OPTION (Simplify Output): For nodes that retrieve data (e.g., HTTP Request, many integration nodes), check their 'Options' or 'Settings' for a 'Simplify Output' (or similarly named) toggle. If you only need the primary data and not extensive metadata or deeply nested structures, enabling this option can provide a much cleaner and easier-to-use output object for downstream nodes. This is a specific instance of 'Filter and Reduce Data Payload Early'.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-usage', // Also performance, data-flow
    appliesToAgents: ['n8n-node-composer-agent', 'n8n-architect-agent'],
    triggerContext: {
      keywords: ['simplify output', 'clean data', 'reduce payload', 'api response handling'],
      // Applies to many integration nodes
      nodeTypes: ['n8n-nodes-base.httpRequest', /* many specific service integration nodes */]
    },
    recommendation: "Check node's 'Options' or 'Settings' for 'Simplify Output'. Enable it if downstream nodes don't require the full raw output. Review the simplified output structure to ensure it still contains all necessary fields for your logic.",
    reasoning: "'Simplify Output' makes workflow data easier to work with in expressions, reduces item size (improving performance), and often provides data in a more directly usable format. [Ref: Gemini 1.1, 2.3, 4.1; ChatGPT 2.3]",
    documentationUrl: "https://docs.n8n.io/integrations/creating-nodes/build/reference/ux-guidelines/#simplify-output",
    relatedRules: ['BR_PERF_003_FilterDataEarly', 'BR_PERF_015_MinimizeDataPassed'],
    additionalData: { impact: 'Medium', effort: 'Low' }
  },
  {
    id: 'BR_NODE_StopError_001',
    name: "Stop and Error Node: Use for Deliberate Workflow Halts",
    description: "The 'Stop and Error' node intentionally halts a workflow path and marks the entire execution as failed. It's useful for kill switches or unrecoverable error conditions.",
    descriptionForLLM: "NODE USAGE (Stop and Error): Use the 'Stop and Error' node when you need to deliberately stop the current workflow path and ensure the overall workflow execution is marked as an error. Configure its 'Message' parameter to provide a clear reason for the halt. Common uses include: 1. Implementing a 'kill switch' (IF 'kill_active' -> Stop and Error). 2. The end of an error handling path for an unrecoverable error. 3. Guarding against unexpected logical paths.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium', // Important for control flow and error design
    category: 'node-flowcontrol',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['stop workflow', 'halt execution', 'kill switch', 'fatal error', 'unrecoverable', 'abort path'],
      nodeTypes: ['n8n-nodes-base.stopAndError']
    },
    recommendation: "If a condition indicates a critical failure or a need to immediately stop, route to a 'Stop and Error' node. Set a descriptive 'Message' in its parameters for logging.",
    example: "IF (Critical System Offline?) -> True: Stop and Error (Message: 'Critical dependent system is offline. Halting workflow.')",
    reasoning: "The 'Stop and Error' node provides an explicit and clear way to terminate a workflow path due to a defined error condition, ensuring the execution is correctly flagged as failed. [Ref: ChatGPT 2.2, 4.1 (Kill Switch, testing error paths)]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.stopanderror/", // Actual node name may vary slightly
    
    
  },
  {
    id: 'BR_NODE_SetEdit_002',
    name: "Use Set/Edit Fields for Explicit Data Staging or Renaming",
    description: "Employ Set or Edit Fields nodes to explicitly create intermediate variables, rename fields for clarity before passing to sub-workflows or specific integrations, or to stage data for complex expressions in subsequent nodes.",
    descriptionForLLM: "NODE USAGE (Set/Edit Fields for Clarity): Beyond simple transformations, consider using a Set or Edit Fields node to: 1. Create an explicitly named intermediate variable if a piece of data is used in many subsequent expressions (`intermediateValue = {{$json.deeply.nested.value}}`). 2. Rename fields to match the expected input schema of a sub-workflow or a specific integration node if they differ from your current data structure. 3. Pre-calculate parts of a complex expression and store them in fields, then use these simpler fields in the final complex expression node. This improves readability.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low',
    category: 'node-set', // or node-editfields
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['intermediate variable', 'rename field', 'data staging', 'sub-workflow input mapping', 'complex expression prep'],
      nodeTypes: ['n8n-nodes-base.set', 'n8n-nodes-base.editFields']
    },
    recommendation: "If data paths are long or field names are misaligned for a specific subsequent step, insert a Set/Edit Fields node to create clearly named, readily accessible fields.",
    example: "// Before calling a sub-workflow that expects 'customer_email': Set node: `customer_email = {{$json.contact.primaryEmailAddress}}`",
    reasoning: "Using Set/Edit Fields for explicit data staging or renaming improves workflow readability, makes expressions in subsequent nodes simpler, and helps manage data contracts between workflow segments or with external systems. [Ref: Gemini (Set node for explicit vars), Manus (Progressive Transformation), ChatGPT (High-level nodes for simple transforms)]",
    
    
  },
  {
    id: 'BR_NODE_Filter_001',
    name: "Use Filter Node for Conditional Item Routing/Discarding",
    description: "The Filter node is used to conditionally pass or discard items based on defined criteria, effectively splitting a data stream.",
    descriptionForLLM: "NODE USAGE (Filter): If you need to process only a subset of items based on their data, use the 'Filter' node. Define conditions. Items matching ALL conditions pass through the 'true' output (default). Items not matching pass through the 'false' output (if 'Send Non-Matching Items to Output 2' is enabled). This is a primary tool for implementing 'early filtering' for performance.",
    type: 'CONTEXTUAL_SUGGESTION', // When filtering is needed
    priority: 'medium',
    category: 'node-filter',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['filter items', 'select subset', 'conditional processing', 'discard items'],
      nodeTypes: ['n8n-nodes-base.filter']
    },
    recommendation: "After fetching a list of items, if only some are relevant, add a Filter node. Define conditions based on item properties. Connect the 'true' output to further processing. Consider what to do with 'false' output items (log, ignore, or different path).",
    example: "Get All Orders -> Filter (Conditions: `status` equals `completed` AND `totalAmount` greater than `100`) -> [True Output] Process High-Value Completed Orders.",
    reasoning: "The Filter node provides a dedicated, efficient way to selectively process items based on conditions, crucial for focusing logic and optimizing performance by not processing irrelevant data. [Ref: Gemini 2.3 (Early Filtering)]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.filter/",
    relatedRules: ['BR_PERF_003_FilterDataEarly'],
    
    
  },
  {
    id: 'BR_NODE_Wait_001',
    name: "Use Wait Node for Delays and Throttling",
    description: "The Wait node introduces a fixed or dynamic delay in workflow execution, essential for handling API rate limits, sequencing time-sensitive operations, or implementing custom retry backoffs.",
    descriptionForLLM: "NODE USAGE (Wait): If you need to pause workflow execution for a specific duration: 1. To respect API rate limits: Place a Wait node (e.g., 500ms-2000ms) inside loops that make API calls. 2. For custom retry logic: Use a Wait node with a dynamically calculated delay (e.g., from a Set node: `{{ $json.delayTime }}`) as part of an exponential backoff strategy. 3. To sequence operations that depend on external system timing, though polling is generally discouraged if event-based triggers are available.",
    type: 'CONTEXTUAL_SUGGESTION', // When delays/throttling are needed
    priority: 'medium',
    category: 'node-wait',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['delay', 'wait', 'throttle', 'rate limit', 'pause', 'backoff', 'sequence timing'],
      nodeTypes: ['n8n-nodes-base.wait']
    },
    recommendation: "Set 'Wait Time' and 'Unit' (milliseconds, seconds, etc.). For dynamic delays, use an expression for 'Wait Time' sourcing the value from a previous node.",
    example: "HTTP Request -> Wait (Wait Time: `{{ $env.API_CALL_DELAY_MS || 1000 }}`, Unit: Milliseconds) -> Next HTTP Request in loop.",
    reasoning: "The Wait node is the standard n8n tool for introducing controlled delays, crucial for managing interactions with rate-limited external services and implementing robust retry patterns. [Ref: Gemini 2.2, 4.3; ChatGPT 2.1, 2.2]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.wait/",
    relatedRules: ['BR_PERF_004_RespectRateLimits', 'BR_ERR_003_APIRetries'],
    
    
  },
  {
    id: 'BR_NODE_ItemLists_001',
    name: "Utilize Item Lists Node for Complex Array Operations",
    description: "For complex operations on arrays of items (e.g., summing values across items, finding unique items, filtering based on aggregated properties, complex mapping), prefer the 'Item Lists' node over custom Code node logic if it can achieve the task.",
    descriptionForLLM: "NODE USAGE (Item Lists): If you need to perform operations across an entire array of items, such as: calculating a sum/average of a field from all items, filtering for unique items based on a key, creating a new list by mapping/transforming all items, or splitting/joining arrays based on conditions: Investigate using the 'Item Lists' node. It offers various powerful operations on item arrays that can be more declarative and maintainable than custom JavaScript in a Code node for these specific tasks.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-itemlists', // Specific category
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['array operation', 'list manipulation', 'sum items', 'unique items', 'aggregate list', 'map array', 'filter array'],
      workflowPatterns: ['data_aggregation_from_list', 'complex_array_transform']
    },
    recommendation: "Explore operations in 'Item Lists' like 'Sum', 'Unique', 'Filter', 'Map Items/Properties'. For example, to get a unique list of email domains from a list of user items: Operation 'Unique', Field to Check '{{$json.email.split(\"@\")[1]}}'.",
    example: "Get All Users (Array) -> Item Lists (Operation: Sum, Field to Sum: `{{$json.orderValue}}`) -> Output: Single item with `totalOrderValue`.",
    reasoning: "The Item Lists node provides optimized, built-in functionalities for many common array operations, often being more efficient and easier to understand than custom Code node solutions for these specific tasks. [Ref: Manus 2.4, 4.1.4]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.itemlists/",
    relatedRules: ['BR_DATA_003_LeverageSpecializedNodes', 'BR_DATA_002_CodeNodeForComplexLogicOnly'],
    
    
  },
  {
    id: 'BR_ITEM_001', // New category prefix for Item Handling
    name: "Understand Item Passing and Immutability (Conceptual)",
    description: "n8n nodes typically receive a copy of the previous node's output item(s). While some nodes modify these items, many pass through original data alongside their new additions. Be mindful of what data is carried forward.",
    descriptionForLLM: "N8N CORE MECHANIC (Item Flow): By default, each node receives the JSON data from its immediate predecessor(s). Most nodes add their own output to this incoming JSON data, or transform it. The *entire item* (original input + node's own changes/additions) is then passed to the next node. This means data from early in the flow can persist unless explicitly removed (see BR_PERF_015). This is not true immutability in a programming sense, but rather a passthrough-and-augment model.",
    type: 'ALWAYS_APPLY', // Fundamental understanding for Architect/Composer
    priority: 'medium',
    category: 'item-handling', // New category
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-reflection-agent'],
    triggerContext: {
      // Applies generally when planning/composing any sequence of nodes
    },
    recommendation: "Be aware that data from earlier nodes is often available in `$json` unless a node explicitly overwrites the entire `$json` object or uses a mode like 'Keep Only Set' (Set node) or 'Simplify Output'. This can be useful but also lead to large item payloads if not managed.",
    reasoning: "Understanding n8n's item data propagation model is crucial for writing correct expressions, managing data size, and debugging data flow issues. [Ref: ChatGPT 1 (Excessive intermediate data), n8n Docs (Data Flow basic concept)]",
    relatedRules: ['BR_PERF_015_MinimizeDataPassed', 'BR_NODE_Set_001_KeepOnlySet'],
    
    
  },
  {
    id: 'BR_ITEM_002',
    name: "Explicitly Handle Array vs. Single Item Inputs/Outputs",
    description: "Be clear whether a node expects a single item or an array of items as input, and what it outputs (single item or array). Use SplitInBatches/Loop Over Items or Code node logic to transition between single item processing and array/batch processing.",
    descriptionForLLM: "DATA STRUCTURE AWARENESS: When connecting nodes, pay close attention to whether a node consumes/produces a single item or an array of items. 1. If Node A outputs an array but Node B expects a single item, you'll need to loop (e.g., using 'Loop Over Items' or a sub-workflow called for each item) to process each item with Node B. 2. If Node A outputs single items sequentially but Node B expects an array (e.g., for a batch API call), you need to accumulate these items (e.g., using a Code node with a static variable or an 'Item Lists' node with an append operation if within a single execution path that can see all items). Misaligned item-vs-array assumptions are a common error source.",
    type: 'ALWAYS_APPLY', // Fundamental for Architect/Composer
    priority: 'high',
    category: 'item-handling',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      // Applies when any nodes are being connected or data flow is planned
    },
    recommendation: "Before connecting nodes, check their documentation or test to see if they operate on single items or arrays. Use 'Loop Over Items' to process array items individually. Use Code Node (Run Once for All Items, `items.push(...)`) or Item Lists node to aggregate individual items into an array if needed by a downstream node.",
    example: "HTTP Request (gets array of users) -> Loop Over Items -> Set (process one user) -> ... OR ... Trigger (one item) -> Code Node ( `staticData.users.push($json); return [];` ) -> Function (processes `staticData.users` when all collected).",
    reasoning: "n8n's item-based processing requires careful management of whether a node is dealing with a single item or a collection. Mismatches are a frequent cause of unexpected behavior or errors. [Ref: Implicit in all loop/batching discussions and data transformation examples]",
    relatedRules: ['BR_NODE_SIB_001_LoopOverItems', 'BR_NODE_Code_002_ExecutionMode', 'BR_NODE_ItemLists_001_ArrayOps'],
    
    
  },
  {
    id: 'BR_DATA_006', // New Data rule
    name: "Use Code Node for Complex Data Aggregation (Run Once for All Items)",
    description: "For complex aggregations across multiple items (e.g., summing, averaging, grouping by key, building nested structures from a flat list), the Code node in 'Run Once for All Items' mode is often the most powerful and flexible tool.",
    descriptionForLLM: "DATA AGGREGATION: If you need to perform calculations or restructuring based on an entire dataset of items (e.g., calculate total sales from all order items, group products by category from a list of products): Use a Code node set to 'Run Once for All Items'. Your script will receive all items in the `items` array. You can then iterate this array to perform aggregations and `return [{json: { aggregated_results }}];`.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'data-flow', // Also node-code
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['aggregate', 'summarize data', 'group by', 'total', 'average', 'calculate across items', 'build nested json from list'],
      workflowPatterns: ['data_aggregation_from_list', 'reporting']
    },
    recommendation: "Set Code node to 'Run Mode: Run Once for All Items'. Script: `const aggregation = {}; for (const item of items) { /* ... your aggregation logic ... */ } return [{json: aggregation}];`",
    example: "// Code node (Run Once for All Items) to group items by category:\n// const grouped = {}; \n// for (const item of items) { \n//   const category = item.json.category || 'Uncategorized'; \n//   if (!grouped[category]) grouped[category] = []; \n//   grouped[category].push(item.json); \n// } \n// return [{json: grouped}];",
    reasoning: "While Item Lists node can do some aggregations, the Code node offers ultimate flexibility for complex multi-item calculations, grouping, and restructuring that would be difficult or impossible with other nodes. [Ref: Gemini 2.4 (Code Node for custom aggregation), Manus 2.4 (Code for sophisticated logic), ChatGPT 2.4 (Function for whole-list transforms)]",
    relatedRules: ['BR_NODE_Code_001_ReturnFormat', 'BR_NODE_Code_002_ExecutionMode', 'BR_NODE_ItemLists_001_ArrayOps'],
    additionalData: { impact: 'High for capability', effort: 'Medium' }
  },
  {
    id: 'BR_DATA_007',
    name: "Ensure Consistent Data Structure for Merge Node Inputs",
    description: "When using Merge node in modes like 'Combine by Matching Fields' or 'Combine by Position', ensure that items from different input branches have consistent data structures and field names for the keys/properties being used for merging to avoid unexpected results or missed merges.",
    descriptionForLLM: "MERGE NODE (Data Integrity): For reliable merging, especially with 'Combine by Matching Fields' or 'Combine by Position' modes: 1. Ensure the fields used as keys for matching (`Key Input 1/2`) exist and have consistent naming and data types in items from both input streams. 2. If merging by position, ensure both input streams will have the same number of items in the correct corresponding order. Discrepancies can lead to items not being merged or being merged incorrectly.",
    type: 'ALWAYS_APPLY', // When Merge node is used with these modes
    priority: 'high',
    category: 'data-flow', // Also node-merge
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.merge'],
      // Trigger when Merge node mode is 'Combine by Matching Fields' or 'Combine by Position'
      conditions: { mergeNodeMode_is_CombineByKeyOrPosition: true } 
    },
    recommendation: "Before a Merge node (Combine by Key/Position): Use Set or Edit Fields nodes on one or both input branches to standardize key field names and data types if they differ. For position-based merging, ensure preceding logic guarantees item count and order synchronicity.",
    example: "Input 1: `[{id:1, name:'A'}]`, Input 2: `[{productId:1, stock:10}]`. Before Merge (Combine by Key): Add Set node to Input 2: `id = {{$json.productId}}`. Then Merge on 'id'.",
    reasoning: "The Merge node relies heavily on consistent data structures and key field values/item order for its 'Combine' modes. Inconsistencies are a common reason for merge operations not producing the expected combined items. [Ref: Gemini III.A (Merge Node - common issues)]",
    relatedRules: ['BR_NODE_Merge_001_UnderstandModes'],
    additionalData: { impact: 'High for correctness', effort: 'Medium' }
  },
  {
    id: 'BR_NODE_Trigger_001',
    name: "Understand Trigger Node Execution and Data Output",
    description: "Each trigger node type (Cron, Interval, specific app triggers like Gmail, Google Sheets etc.) has unique initial data output and execution characteristics. Always inspect a trigger's output with a test run to understand the data it provides.",
    descriptionForLLM: "NODE USAGE (All Triggers): When planning or composing a workflow starting with any Trigger node (e.g., Cron, Interval, app-specific triggers like Gmail Trigger): 1. Understand that the trigger is the entry point and initiates each workflow execution. 2. The data output by the trigger (available as `{{$json}}` in the first connected node) varies significantly based on the trigger type and event. 3. ALWAYS perform a test execution of the trigger (or use its 'Fetch Test Event' feature if available) to inspect its actual output JSON structure. This is crucial for correctly referencing its data in subsequent nodes.",
    type: 'ALWAYS_APPLY', // When using any trigger node
    priority: 'high',
    category: 'node-trigger', // New category for general trigger advice
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent' /* for nodes consuming trigger output */],
    triggerContext: {
      // This rule applies when any node that IS a trigger is being planned or its output consumed.
      // The RuleSelectorService would need a way to identify if a nodeTypeActive is a trigger.
      // NodeSpecService could potentially provide this metadata (e.g., from node's 'subtitle').
      workflowPatterns: ['is_trigger_node_present'] 
    },
    recommendation: "After adding any trigger node, use 'Fetch Test Event' or perform a manual execution (if possible) and inspect the output data in the execution log. Use this to accurately map fields in downstream nodes. Document key fields provided by the trigger with a Sticky Note.",
    reasoning: "The data provided by trigger nodes is the starting point for the entire workflow. Misunderstanding or incorrectly assuming its structure is a common source of errors. Direct inspection is key. [Ref: Implicit in all workflow examples and debugging discussions]",
    additionalData: { impact: 'High for correctness', effort: 'Low (testing trigger)' }
  },
  {
    id: 'BR_NODE_Trigger_002',
    name: "Configure Polling Triggers with Appropriate Intervals and Filters",
    description: "For polling triggers (e.g., Interval, Cron in combination with data-fetching nodes, some app triggers like Gmail), set reasonable polling intervals to avoid excessive API calls and use any available filtering options within the trigger to fetch only relevant data.",
    descriptionForLLM: "NODE USAGE (Polling Triggers): When configuring a trigger that polls an external service for updates (e.g., Gmail Trigger, or a Cron node followed by an HTTP Request): 1. Set a polling 'Interval' or 'Schedule' that is appropriate for the data's update frequency and respects the service's API limits (avoid polling every few seconds unless absolutely critical and supported). 2. Utilize any built-in filtering capabilities of the trigger node itself (e.g., Gmail Trigger can filter by labels, sender, subject) to minimize the amount of data fetched and processed by n8n.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-trigger', // Also performance
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent'],
    triggerContext: {
      keywords: ['polling', 'interval', 'schedule', 'cron', 'check for updates', 'gmail trigger', 'rss trigger'],
      // This implies the RuleSelectorService can identify polling triggers based on nodeType or keywords.
      workflowPatterns: ['polling_trigger_used'] 
    },
    recommendation: "For polling triggers: Set interval (e.g., 5 minutes, 1 hour, daily) based on need. Use specific filters in the trigger's parameters (e.g., Gmail 'Match By Labels'). Avoid very frequent polling (e.g., every second) as it's resource-intensive and can lead to rate limiting.",
    reasoning: "Optimizing polling triggers reduces load on both n8n and external services, improves efficiency, and prevents API rate limit issues. Fetching only relevant data minimizes downstream processing. [Ref: Manus 1.1 (Excessive Polling), Gemini 2.3 (Selective DB Queries - concept applies)]",
    relatedRules: ['BR_PERF_004_RespectRateLimits'],
    additionalData: { impact: 'Medium to High', effort: 'Low to Medium' }
  },

  // --- Execute Workflow Node ---
  // BR_DSGN_001, BR_DSGN_003, BR_DSGN_009, BR_DSGN_012, BR_ERR_013 already cover many aspects.
  {
    id: 'BR_NODE_ExecuteWorkflow_001',
    name: "Execute Workflow: Choose 'Wait' Option Based on Synchronicity Needs",
    description: "Decide whether the parent workflow should wait for a sub-workflow to complete ('Wait for Sub-Workflow to Finish' enabled) or continue asynchronously (disabled). This impacts data availability and error propagation.",
    descriptionForLLM: "NODE USAGE (Execute Workflow): The 'Wait for Sub-Workflow to Finish' setting is critical: 1. **Enabled (Default - Synchronous):** The parent workflow pauses until the sub-workflow completes. The output of the sub-workflow's last node(s) is then available as the output of the Execute Workflow node in the parent. Errors in the sub-workflow (if unhandled) will typically cause this node to fail. Use this when the parent needs the sub-workflow's result to continue. 2. **Disabled (Asynchronous):** The parent workflow triggers the sub-workflow and continues immediately *without* waiting for it to finish and *without* receiving its direct output. This is for 'fire and forget' tasks or when implementing parallel processing where results are aggregated externally. Error handling for the sub-workflow must be independent or use external signaling.",
    type: 'ALWAYS_APPLY', // When this node is used
    priority: 'high',
    category: 'node-executeworkflow', // Specific category
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.executeWorkflow']
    },
    recommendation: "Enable 'Wait for Sub-Workflow to Finish' if the parent workflow depends on the sub-workflow's output or completion status. Disable it for asynchronous offloading or true parallel execution patterns (requires careful design for results/error handling).",
    reasoning: "The 'Wait' setting fundamentally changes the interaction model with sub-workflows, affecting data flow, error propagation, and potential for parallelism. Choosing incorrectly leads to unexpected behavior. [Ref: Gemini 1.2, 2.1; ChatGPT 2.5]",
    documentationUrl: "https://docs.n8n.io/nodes/n8n-nodes-base.executeworkflow/#wait-for-sub-workflow-to-finish",
    relatedRules: ['BR_ERR_013_SubWorkflowErrorPropagation', 'BR_PERF_006_SubWorkflowOffloading'],
    additionalData: { impact: 'High', effort: 'Low (selection)' }
  },
  {
    id: 'BR_NODE_ExecuteWorkflow_002',
    name: "Execute Workflow: Reference Sub-Workflows Robustly",
    description: "When referencing sub-workflows in the 'Execute Workflow' node, be mindful that referencing by ID can be brittle across n8n instances/environments. Using names is often more stable if names are unique and maintained.",
    descriptionForLLM: "NODE USAGE (Execute Workflow - Referencing): You can specify the sub-workflow to execute by its ID or its Name. 1. **By ID:** Precise, but the ID can change if the sub-workflow is imported/exported to a different n8n instance or duplicated. 2. **By Name:** More portable across instances, BUT requires the sub-workflow name to be unique and stable. If multiple sub-workflows have the same name, behavior is undefined or might pick the first one found. For V1, if Architect/Composer suggest a sub-workflow that doesn't exist yet, they should plan for its creation and a consistent name.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'medium',
    category: 'node-executeworkflow',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.executeWorkflow']
    },
    recommendation: "Prefer referencing sub-workflows by a unique, stable Name if portability across n8n instances is a concern. If using IDs, ensure a process is in place to update these IDs if workflows are moved (e.g., via environment variables holding the target sub-workflow ID for different environments).",
    reasoning: "Sub-workflow ID changes during export/import are a common source of broken 'Execute Workflow' calls. Naming (if unique) can offer more resilience, though it also requires discipline. [Ref: Gemini 2.5 (Deployment and Maintenance Considerations)]",
    additionalData: { impact: 'Medium for portability', effort: 'Low to Medium (process discipline)' }
  },
  {
    id: 'BR_SEC_001', // Canonical ID for this critical rule
    name: "Never Hardcode Secrets or Credentials; Use n8n Credential Manager",
    description: "Sensitive information (API keys, passwords, tokens, private certificates) MUST NEVER be hardcoded into node parameters or workflow JSON. ALWAYS use n8n's built-in, encrypted Credential Manager.",
    descriptionForLLM: "CRITICAL SECURITY RULE: Under no circumstances should API keys, passwords, access tokens, or any other sensitive credentials be typed or pasted directly into node parameters. ALWAYS instruct the user to store these in n8n's Credential Manager and then select the appropriate credential within the node's authentication settings. For configurations that are environment-specific but not strictly secret (e.g., non-sensitive URLs, account IDs), use environment variables (`{{$env.VAR_NAME}}`).",
    type: 'ALWAYS_APPLY', // This is a non-negotiable principle.
    priority: 'critical',
    category: 'security',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent', 'n8n-reflection-agent'],
    triggerContext: {
      // Applies whenever a node might require authentication or sensitive configuration.
      keywords: ['api key', 'password', 'token', 'secret', 'credential', 'authentication', 'username', 'access key'],
      // Validator can scan parameters for suspicious string patterns.
    },
    validation: {
      customFunctionKey: 'checkForHardcodedSecretsInParams', // Scans node parameters for high-entropy strings or common key patterns.
      operator: 'equals',
      expectedValue: true // Function returns true if hardcoded secrets are suspected.
    },
    recommendation: "When a node requires an API key, token, or password: 1. Guide the user to n8n's 'Credentials' section. 2. Instruct them to 'Add credential' of the appropriate type (e.g., 'Header Auth Credential', 'OAuth2 API Credential', specific app credential). 3. In the node, select the created credential from the 'Credential' dropdown. For environment-specific non-secrets, use `{{$env.MY_CONFIG_VALUE}}`.",
    example: "HTTP Request Node: Authentication: 'Header Auth', Credential: 'MyServiceApiKey' (where 'MyServiceApiKey' is an n8n credential storing the actual key). Parameter: `{{$env.TARGET_API_URL}}`",
    reasoning: "Hardcoding secrets is a major security vulnerability, exposing sensitive information in workflow JSON and potentially in logs or UIs. n8n's Credential Manager encrypts secrets at rest and provides a secure way to manage them. [Ref: Gemini 1.1, 4.1; Manus 1.1, 4.1.5; ChatGPT 1, 4.1]",
    documentationUrl: "https://docs.n8n.io/credentials/",
    additionalData: { impact: 'Critical', securityCriticality: 'Very High', effortToImplement: 'Low' }
  },
  {
    id: 'BR_SEC_002',
    name: "Validate and Sanitize All External Inputs",
    description: "Rigorously validate and sanitize any data received from external sources (Webhooks, API responses consumed, user inputs, files) before using it in downstream operations, especially in expressions, Code nodes, or database queries.",
    descriptionForLLM: "CRITICAL SECURITY & RELIABILITY: Immediately after any node that ingests data from an external, untrusted, or unpredictable source (e.g., Webhook trigger, HTTP Request node getting data from a public API, File Upload): Implement robust validation. Check for: 1. Presence of required fields. 2. Correct data types. 3. Expected formats (e.g., email, URL). 4. Reasonable value ranges/lengths. Sanitize inputs to prevent injection attacks if data is used in constructing Code node scripts, SQL queries, or shell commands (though direct shell command execution should be avoided). Route invalid/suspicious data to an error path or respond with an error (e.g., 400 Bad Request for webhooks).",
    type: 'ALWAYS_APPLY', // For relevant nodes
    priority: 'critical',
    category: 'security', // Also data-flow, error-handling
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['webhook input', 'api response data', 'user form input', 'file content', 'external data', 'input validation', 'sanitize', 'security'],
      nodeTypes: ['n8n-nodes-base.webhook', 'n8n-nodes-base.httpRequest', /* File Read Nodes, Form Trigger Nodes */]
    },
    recommendation: "After an input node: 1. Use IF nodes for basic checks (e.g., `{{ $json.body.expectedField !== undefined }}`). 2. Use a Code node with try-catch for complex validation (e.g., using a validation library like Zod if available, or custom regex/type checks). 3. For webhooks, if validation fails, use 'Respond to Webhook' node to send a 400 error. For other inputs, route to an error handling path.",
    example: "Webhook -> Code Node (Script: `if (!parsedBody.user_id || typeof parsedBody.user_id !== 'string') throw new Error('Invalid user_id'); item.json.validatedData = parsedBody; return item;`, Continue on Fail: true) -> IF (error from Code?) -> Respond 400 / Else -> Process validatedData.",
    reasoning: "Validating and sanitizing external inputs is crucial for preventing data corruption, workflow errors, and security vulnerabilities such as injection attacks (XSS, SQLi, command injection if data is ever used in such contexts). [Ref: Gemini 1.4, 4.1.2; Manus 1.1, 4.1.5; ChatGPT (Unchecked assumptions)]",
    relatedRules: ['BR_ERR_004_ValidateExternalInputDataRigorously'], // This rule adds more security emphasis
    additionalData: { impact: 'Critical', securityCriticality: 'Very High', effortToImplement: 'Medium' }
  },
  {
    id: 'BR_SEC_003',
    name: "Use HTTPS for All External HTTP Communications",
    description: "Ensure all HTTP Request nodes and any other nodes making external network calls are configured to use HTTPS URLs, not plain HTTP, to encrypt data in transit.",
    descriptionForLLM: "SECURITY (Data in Transit): When configuring HTTP Request nodes or any integration that involves calling external URLs: Verify that the URL uses `https://` (HTTPS) and not `http://`. Using HTTPS encrypts the data exchanged between n8n and the external service, protecting it from eavesdropping.",
    type: 'ALWAYS_APPLY',
    priority: 'high',
    category: 'security',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.httpRequest', /* Any node with a URL parameter for external comms */]
    },
    validation: {
      pattern: "^http://", // Looks for http:// in URL fields
      jsonPath: "nodes[*].parameters.url" // Example path, depends on node
    },
    recommendation: "Always use `https://` URLs for external API calls and webhooks. If a service only offers HTTP, evaluate the risk or use a secure proxy.",
    reasoning: "HTTPS encrypts data in transit, preventing sensitive information from being intercepted. Using unencrypted HTTP is a significant security risk, especially for APIs handling personal or financial data. [Ref: Gemini 4.1.5, Manus 4.1.5]",
    additionalData: { impact: 'High', securityCriticality: 'High', effortToImplement: 'Low' }
  },
  {
    id: 'BR_SEC_004',
    name: "Limit Data Exposure in Responses and Logs",
    description: "Be mindful of what data is included in workflow outputs, webhook responses, and especially error messages or logs. Avoid exposing excessive or sensitive information.",
    descriptionForLLM: "SECURITY (Data Exposure): When a workflow sends data externally (e.g., in an HTTP response, email, Slack message) or logs information (especially errors): Ensure that only necessary information is included. Avoid returning or logging entire large objects, internal system details, or sensitive user data if not strictly required for that specific output or log entry. Specifically, error messages logged or sent in notifications should be informative but not expose full raw input data if it contains PII/secrets.",
    type: 'ALWAYS_APPLY',
    priority: 'high',
    category: 'security',
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      keywords: ['response data', 'error message', 'log content', 'notification data', 'sensitive data'],
      nodeTypes: ['n8n-nodes-base.respondToWebhook', 'n8n-nodes-base.sendEmail', 'n8n-nodes-base.slack', 'n8n-nodes-base.set'] // Plus logging nodes, set for error messages
    },
    recommendation: "Use Set or Edit Fields nodes to explicitly construct response payloads or log messages, selecting only necessary fields. For error messages, log a summary and error code/type, but avoid dumping full input items if they contain sensitive data. If sensitive data must be logged for debugging, ensure logs are secure and access-controlled.",
    example: "Error path: Set (Message: `Failed to process order {{ $json.orderId }}. Error: {{ $json.error.type }}. User: {{ $json.user.id }}. NOT {{ $json.user.fullCreditCardDetails }}`)",
    reasoning: "Minimizing data exposure in outputs, logs, and error messages reduces the risk of sensitive information leaks and adheres to data privacy principles. [Ref: Gemini 4.1.5, Manus 4.1.5 (Nettoyer les données sensibles)]",
    relatedRules: ['BR_ERR_017_LogSufficientContext' /* Balance between detail and security */],
    additionalData: { impact: 'High', securityCriticality: 'High', effortToImplement: 'Medium' }
  },
  {
    id: 'BR_SEC_005',
    name: "Apply Principle of Least Privilege for Credentials",
    description: "When configuring n8n Credentials for external services, ensure the associated account/API key has only the minimum necessary permissions required for the workflow's operations.",
    descriptionForLLM: "SECURITY (Credentials - Least Privilege): When guiding a user to create or use an n8n Credential for a service (e.g., database, cloud provider API, SaaS app): Advise them to ensure that the API key or user account associated with that credential has ONLY the permissions strictly necessary for the tasks this workflow (or set of workflows using this credential) will perform. Avoid using admin-level or overly broad permissions if only read access or specific write access is needed.",
    type: 'CONTEXTUAL_SUGGESTION', // Advice for user when setting up creds
    priority: 'high',
    category: 'security',
    appliesToAgents: ['n8n-architect-agent' /* When discussing integrations */, 'n8n-workflow-documenter-agent' /* Can add notes about creds */],
    triggerContext: {
      keywords: ['credential setup', 'api key permissions', 'database user', 'service account', 'access control']
    },
    recommendation: "Review the operations the workflow will perform with a credential. In the external service, create a dedicated user/API key with scoped-down permissions (e.g., read-only for a database if only fetching data; specific API scopes for SaaS apps). Use this restricted credential in n8n.",
    reasoning: "The principle of least privilege minimizes the potential damage if a credential is compromised or if a workflow behaves unexpectedly. Overly permissive credentials increase the attack surface. [Ref: Gemini 4.1.5, Manus 4.1.5]",
    additionalData: { impact: 'High', securityCriticality: 'High', effortToImplement: 'Medium (requires action in external service)' }
  },
  {
    id: 'BR_SEC_006', // Was BR_NODE_Webhook_001, now generalized and enhanced
    name: "Secure Webhook Triggers with Authentication",
    description: "For Webhook triggers, especially those handling sensitive data or initiating critical actions, ALWAYS use an authentication method (e.g., Basic Auth, Header Auth, or custom signature validation) to prevent unauthorized access and ensure data integrity.",
    descriptionForLLM: "WEBHOOK SECURITY (Authentication): If a workflow uses a Webhook trigger: 1. If it's for internal trusted calls, it might be okay without auth if network is isolated. 2. If it's publicly accessible or receives data from external third parties, IT MUST be authenticated. In the Webhook node settings: Choose an 'Authentication' method ('Basic Auth', 'Header Auth'). Configure the associated n8n Credential. The calling system must then provide these credentials/headers. For services like Stripe that use signature validation, plan a Code node or sub-workflow immediately after the Webhook trigger to verify the incoming request signature against a stored secret.",
    type: 'ALWAYS_APPLY', // When Webhook is used and public/sensitive
    priority: 'critical',
    category: 'security', // also node-webhook
    appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
    triggerContext: {
      nodeTypes: ['n8n-nodes-base.webhook'],
      // Contextual trigger might be if webhook is not from localhost or if keywords like 'public', 'external', 'sensitive' are present
      keywords: ['public webhook', 'external trigger', 'sensitive data input']
    },
    validation: {
      customFunctionKey: 'checkWebhookAuthentication',
      // Function checks if Webhook node has auth method set, if path is not default test path, etc.
    },
    recommendation: "In Webhook node: Set 'Authentication' to 'Basic Auth' or 'Header Auth' and select/create a credential. Alternatively, implement signature validation (e.g., for Stripe, GitHub webhooks) in a Code node as the first step after the trigger, using a secret stored in n8n Credentials.",
    example: "Webhook (Authentication: 'Header Auth', Credential: 'MyWebhookAuth') -> Process Data.",
    reasoning: "Unauthenticated public webhooks are a major security risk, allowing DoS, unauthorized data submission, or triggering of unintended actions. Signature validation ensures data integrity and authenticity. [Ref: Gemini 1.1, 4.1.5; Manus 1.1, 4.1.5; ChatGPT (Secure Webhook Exposure)]",
    relatedRules: ['BR_SEC_001_NoHardcodedSecrets', 'BR_ERR_004_ValidateExternalInputDataRigorously'],
    additionalData: { impact: 'Critical', securityCriticality: 'Very High', effortToImplement: 'Medium' }
  },
  {
    id: 'BR_SEC_007',
    name: "Use n8n Encryption Key for Self-Hosted Instances (Instance Security)",
    description: "For self-hosted n8n instances, a unique N8N_ENCRYPTION_KEY MUST be set as an environment variable to ensure all credentials stored in the database are properly encrypted.",
    descriptionForLLM: "INSTANCE SECURITY (Self-Hosted): If the user is self-hosting n8n, remind them of the critical importance of setting a strong, unique `N8N_ENCRYPTION_KEY` environment variable for their n8n instance. Without this key, credentials stored in the database may not be adequately protected. This key is used to encrypt/decrypt sensitive credential data.",
    type: 'CONTEXTUAL_SUGGESTION', // AI can't enforce this, but can strongly advise if self-hosting is known/suspected.
    priority: 'critical',
    category: 'security', // Instance-level configuration
    appliesToAgents: ['n8n-architect-agent' /* when discussing deployment/security for self-host */, 'n8n-workflow-documenter-agent' /* could be part of a setup checklist */],
    triggerContext: {
      keywords: ['self-host', 'docker', 'n8n setup', 'encryption key', 'instance security'],
      workflowPatterns: ['self_hosted_environment_context'] // If AI can know this
    },
    recommendation: "Ensure `N8N_ENCRYPTION_KEY` is set to a long, random, unique string in your n8n environment configuration (e.g., `.env` file, `docker-compose.yml`). This key MUST be backed up securely; loss of this key means loss of access to encrypted credentials.",
    reasoning: "The N8N_ENCRYPTION_KEY is fundamental to securing credentials at rest in self-hosted n8n instances. Failing to set it or using a weak/default key severely compromises credential security. [Ref: Gemini 4.2 (Config Params), n8n Docs]",
    documentationUrl: "https://docs.n8n.io/hosting/configuration/environment-variables/#n8nencryptionkey",
    additionalData: { impact: 'Critical', securityCriticality: 'Very High', effortToImplement: 'Low (set env var)', applicability: 'self-hosted' }
  },
  {
    id: 'BR_TEST_001', // New category prefix for Testing
    name: "Design Workflows for Testability (Parameterize Key Inputs)",
    description: "When designing workflows, especially those with hardcoded critical values used in logic (e.g., specific IDs, thresholds), consider parameterizing these via initial Set nodes (using environment variables or easily updatable values) to facilitate easier testing with different scenarios without altering core logic nodes.",
    descriptionForLLM: "DESIGN FOR TESTABILITY: If the workflow logic depends on specific values (e.g., a particular account ID for an API call, a specific processing threshold for an IF node) that might need to change for different test scenarios: Instead of hardcoding these values deep within operational nodes, plan to define them in an initial 'Set' node (or a group of Set nodes) at the beginning of the workflow. This 'Configuration Block' can then be easily modified with test values (or use expressions to pull from `$env` variables for different environments). This makes the workflow more testable and configurable.",
    type: 'CONTEXTUAL_SUGGESTION',
    priority: 'low', // More advanced design consideration
    category: 'testing',
    appliesToAgents: ['n8n-architect-agent', 'n8n-reflection-agent'],
    triggerContext: {
      keywords: ['testability', 'parameterize', 'configuration block', 'test values', 'hardcoded logic value'],
      workflowPatterns: ['complex_logic_with_constants', 'needs_varied_test_scenarios']
    },
    recommendation: "At the start of the workflow, use a Set node (potentially with 'Execute Once' enabled) to define key variables or constants used by the workflow's core logic (e.g., `testCustomerID = '{{$env.TEST_CUSTOMER_ID || 'defaultTestCust123'}}'`). Subsequent nodes then reference `{{$json.testCustomerID}}`. This allows easy modification of test inputs without changing the main flow.",
    example: "Start -> Set (Execute Once: True, Values: `processingThreshold = 100`, `targetApiEndpoint = '{{$env.API_ENDPOINT || 'https://api.dev.example.com'}}'`) -> [Rest of workflow uses `{{$json.processingThreshold}}` and `{{$json.targetApiEndpoint}}`]",
    reasoning: "Parameterizing key inputs or decision thresholds at the beginning of a workflow (or in a dedicated configuration section) makes it significantly easier to run tests with different scenarios and values without modifying the core operational logic of the workflow, enhancing testability and reusability. [Ref: Implicit in testing discussions, general software best practice]",
    
    
  },
  // In baseN8nRules.ts
// Theme: Deployment & Configuration Management

{
  id: 'BR_DEPLOY_001', // ID remains the same
  name: "Identify and Abstract Environment-Specific Configurations",
  description: "When designing workflows, identify all parameters that are likely to change between different environments (dev, staging, production) - such as URLs, user IDs, folder paths, email addresses etc. - and ensure these are configured via a centralized, easily updatable mechanism rather than being hardcoded throughout the workflow.",
  descriptionForLLM: "DESIGN FOR DEPLOYMENT: As you plan the workflow, for any parameter that will differ across environments (development, staging, production – e.g., API base URLs, specific account IDs, file paths, test vs. live email recipients): These MUST NOT be hardcoded in multiple places. Plan for them to be defined ONCE at the beginning of the workflow or in a dedicated configuration section, and then referenced throughout. This makes switching environments much easier. Advise using n8n Credentials for secrets, and for non-secret configurations, suggest either Environment Variables (if available to the user) OR a 'Workflow Configuration Block' pattern.",
  type: 'ALWAYS_APPLY', // For Architect and Composer
  priority: 'high',
  category: 'deployment', // Also workflow-design
  appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
  triggerContext: {
    keywords: ['environment', 'deploy', 'production', 'staging', 'dev', 'configuration', 'url', 'path', 'email recipient', 'account id'],
    workflowPatterns: ['production_deployable', 'multi_environment_setup']
  },
  recommendation: "For values that change per environment: 1. **Secrets:** ALWAYS use n8n Credentials. 2. **Non-Secrets:** If user's n8n setup supports Environment Variables, use expressions like `{{$env.API_BASE_URL}}`. 3. **Universal Alternative (Workflow Configuration Block):** Use one or more 'Set' nodes at the very beginning of the workflow, explicitly labeled (e.g., 'CONFIG: Environment Settings'), to define all environment-specific values. Downstream nodes then reference values from this 'CONFIG' node (e.g., `{{ $('CONFIG: Environment Settings').item.json.apiUrl }}`). This block is then the single place to change values when moving between environments or for testing.",
  example: "Set Node (Name: 'CONFIG_EnvSettings', Execute Once: True) -> Parameters: `apiUrl = 'https://api.dev.example.com'`, `targetEmail = '<EMAIL>'`. Downstream HTTP Node URL: `{{ $('CONFIG_EnvSettings').item.json.apiUrl }}/users`.",
  reasoning: "Abstracting environment-specific configurations into a single, easily modifiable location (Credentials, Environment Variables, or a dedicated Set node block) is essential for smooth, error-free deployment and management of workflows across different stages (dev, test, prod). It prevents manual, error-prone changes in multiple nodes. [Ref: Gemini 4.1, Manus 1.4, ChatGPT 4.1]",
  relatedRules: ['BR_SEC_001_NoHardcodedSecrets', 'BR_TEST_001_DesignForTestability'],
   // Version update due to significant recommendation change
  
},
// In baseN8nRules.ts
// Theme: Deployment & Configuration Management (or Workflow Design)

{
  id: 'BR_DSGN_017', // New ID, categorized under Design for structure
  name: "Implement a Clear 'Workflow Configuration Block' Pattern",
  description: "For managing non-secret, environment-specific configurations or frequently changed parameters within a workflow (especially if n8n Environment Variables are not used/available), use a dedicated 'Set' node (or group of Set nodes) at the beginning of the workflow, clearly labeled as a configuration block.",
  descriptionForLLM: "DESIGN PATTERN (Workflow Configuration Block): If a workflow requires multiple configuration values that might change (e.g., API endpoints for different environments, processing thresholds, target email lists for dev vs. prod), and if n8n Environment Variables are not the primary way these are managed: 1. Create a 'Set' node at the VERY START of the workflow. 2. Name it clearly (e.g., 'CONFIG: Workflow Settings', 'ENV_CONFIG'). 3. Enable its 'Execute Once' setting (in Settings tab). 4. In this Set node, define all such configuration parameters as key-value pairs. 5. All other nodes in the workflow MUST then reference these values using expressions like `{{ $('CONFIG: Workflow Settings').item.json.myParameter }}`. This centralizes all configuration in one easily editable place.",
  type: 'CONTEXTUAL_SUGGESTION', // For Architect when configs are needed
  priority: 'medium',
  category: 'workflow-design', // Also deployment
  appliesToAgents: ['n8n-architect-agent', 'n8n-node-composer-agent', 'n8n-best-practice-validator-agent'],
  triggerContext: {
    keywords: ['configuration', 'settings', 'parameters', 'environment values', 'changeable values', 'no environment variables'],
    workflowPatterns: ['needs_configurable_params', 'multi_environment_setup_without_env_vars']
  },
  recommendation: "1. Add a Set node as one of the first nodes. Name it e.g., 'Workflow_Config'. 2. In its 'Settings' tab, enable 'Execute Once'. 3. In 'Parameters', add values: e.g., `key: 'apiUrl', value: 'https://dev.api.com'`, `key: 'retryAttempts', value: 3`. 4. Downstream, reference these: `{{ $('Workflow_Config').item.json.apiUrl }}`.",
  example: "Start -> Set (Name: 'CONFIG', Execute Once: True, Values: { apiUrl: '...', recipient: '...' }) -> HTTP Request (URL: `{{ $('CONFIG').item.json.apiUrl }}`) -> Email (To: `{{ $('CONFIG').item.json.recipient }}`).",
  reasoning: "A dedicated configuration block at the start of a workflow provides a clear, centralized, and easily updatable way to manage parameters that vary across environments or need frequent adjustment, especially when n8n instance-level Environment Variables are not available or suitable for all parameters. This greatly improves maintainability and reduces errors from hardcoding. [Ref: Inspired by general programming best practices for config management, adapted from env var discussions]",
  relatedRules: ['BR_DEPLOY_001_AbstractEnvConfigs', 'BR_NODE_SetEdit_001_ExecuteOnce'],
  
  
},
{
  id: 'BR_META_001', // Meta-level principle
  name: "Advise Iterative Development for Complex Requests",
  description: "For very complex or vaguely defined user requests, the AI should plan an initial, simpler core solution (MVP) and suggest iterating with the user to add more complex features or handle more edge cases later.",
  descriptionForLLM: "DESIGN APPROACH (for very complex/vague requests): If the user's request is exceptionally broad or involves many complex, potentially independent features: 1. Focus on designing a core, functional Minimum Viable Product (MVP) that addresses the primary goal. 2. In your plan's summary or open questions, explicitly state that this is a foundational version and suggest that advanced features (list them) or more nuanced edge case handling can be added in subsequent iterations with further user feedback. Avoid trying to build everything perfectly in one go if the scope is massive.",
  type: 'CONTEXTUAL_SUGGESTION', // For Architect when faced with high complexity
  priority: 'medium',
  category: 'workflow-design', // Meta-level strategy
  appliesToAgents: ['n8n-architect-agent', 'n8n-requirements-gathering-agent' /* can also use this to scope */],
  triggerContext: {
    keywords: ['very complex', 'large scope', 'many features', 'vague requirements', 'build everything'],
    conditions: { estimatedOverallComplexity_is_VeryHigh: true }
  },
  recommendation: "When Architect estimates high complexity: Propose a phased approach to the user (via the plan summary). 'Let's start by building X, Y, Z. Once that's working, we can add A, B, C.'",
  reasoning: "Iterative development (starting with an MVP and refining) is a proven software engineering practice that manages complexity, allows for earlier user feedback, and reduces the risk of building an overly complicated, incorrect solution. This is especially true for AI-assisted generation. [Ref: General software principles, implied by 'start small' advice in research]",
  
  
}
];

/**
 * Cached base rules with database-like structure
 */
let loadedBaseRules: N8nRule[] | null = null;

/**
 * Loads and returns the base n8n rules with generated IDs and metadata
 * @returns Array of base N8nRule objects
 */
export function loadBaseN8nRules(): N8nRule[] {
  if (loadedBaseRules) {
    return loadedBaseRules;
  }

  console.log('Loading base n8n rules...');
  
  // Transform base rule data to complete N8nRule objects with proper defaults
  loadedBaseRules = baseRulesData.map((rule, index) => ({
    id: `base-rule-${(index + 1).toString().padStart(3, '0')}`, // base-rule-001, base-rule-002, etc.
    name: rule.name,
    description: rule.description,
    descriptionForLLM: rule.descriptionForLLM,
    type: rule.type,
    priority: rule.priority,
    category: rule.category,
    appliesToAgents: rule.appliesToAgents,
    appliesToNodeTypes: rule.appliesToNodeTypes || [], // Default to empty array
    triggerContext: rule.triggerContext,
    validation: rule.validation,
    recommendation: rule.recommendation,
    example: rule.example,
    reasoning: rule.reasoning,
    source: 'builtin' as const,
    version: rule.version || 1, // Default to 1
    isActive: rule.isActive !== undefined ? rule.isActive : true, // Default to true
    usageCount: rule.usageCount || 0, // Default to 0
    lastUsedAt: rule.lastUsedAt,
    documentationUrl: rule.documentationUrl,
    relatedRules: rule.relatedRules || [], // Default to empty array
    createdAt: new Date('2024-01-01T00:00:00.000Z'), // Fixed date for base rules
    updatedAt: new Date('2024-01-01T00:00:00.000Z'),
    additionalData: rule.additionalData || {}, // Default to empty object
  }));

  console.log(`Loaded ${loadedBaseRules.length} base n8n rules`);
  return loadedBaseRules;
}

/**
 * Gets base rules filtered by agent
 * @param agentSlug - The agent slug to filter rules for
 * @returns Array of base rules applicable to the agent
 */
export function getBaseRulesForAgent(agentSlug: string): N8nRule[] {
  const allRules = loadBaseN8nRules();
  return allRules.filter(rule => 
    rule.isActive && rule.appliesToAgents.includes(agentSlug)
  );
}

/**
 * Gets base rules by type
 * @param type - Rule type to filter by
 * @returns Array of base rules of the specified type
 */
export function getBaseRulesByType(type: N8nRule['type']): N8nRule[] {
  const allRules = loadBaseN8nRules();
  return allRules.filter(rule => 
    rule.isActive && rule.type === type
  );
}

/**
 * Resets the loaded rules cache (useful for testing)
 * @internal
 */
export function resetBaseRulesCache(): void {
  loadedBaseRules = null;
} 