/**
 * Configuration Module Index
 * 
 * Central export point for all configuration-related functionality including
 * model configurations, rules, and system constants.
 */

// Export LLM model configurations
export { 
  createEnhancedModelPricingConfig,
  getModelInfo,
  anthropicDefaultModelId,
  openAiDefaultModelId,
  googleDefaultModelId,
  REASONING_MODELS,
  COMPUTER_USE_MODELS,
  PROMPT_CACHING_MODELS
} from './llmModels.config';

// Export base N8n rules
export { loadBaseN8nRules, getBaseRulesForAgent, getBaseRulesByType } from './baseN8nRules.config';

// Export user settings defaults
export { 
  DEFAULT_FALLBACK_MODELS_FOR_SERVICE,
  DEFAULT_API_KEYS_TEMPLATE,
  DEFAULT_USER_PREFERENCES,
  getDefaultModelForTaskCategory,
  validateDefaultModelConfiguration
} from './userSettings.defaults';

// TODO: Add other config exports as they are implemented
// export { SYSTEM_CONSTANTS } from './systemConstants.config';
// export { FEATURE_FLAGS } from './featureFlags.config'; 