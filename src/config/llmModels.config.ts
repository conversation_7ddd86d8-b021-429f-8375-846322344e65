/**
 * Comprehensive Model Configurations
 * 
 * This file contains detailed information about LLM models from various providers,
 * including pricing, capabilities, and constraints. Based on Roo Code's comprehensive
 * model definitions with enhancements for the n8n AI Assistant project.
 */

export interface ModelInfo {
  maxTokens: number;
  contextWindow: number;
  supportsImages: boolean;
  supportsComputerUse?: boolean;
  supportsPromptCache: boolean;
  inputPrice: number; // Price per million input tokens in USD
  outputPrice: number; // Price per million output tokens in USD
  cacheWritesPrice?: number; // Price per million cache write tokens
  cacheReadsPrice?: number; // Price per million cache read tokens
  supportsReasoningBudget?: boolean;
  requiredReasoningBudget?: boolean;
  supportsReasoningEffort?: boolean;
  reasoningEffort?: 'low' | 'medium' | 'high';
  maxThinkingTokens?: number;
  description?: string;
  tiers?: ModelPricingTier[];
}

export interface ModelPricingTier {
  contextWindow: number;
  inputPrice: number;
  outputPrice: number;
  cacheReadsPrice?: number;
  cacheWritesPrice?: number;
}

// Anthropic Models
export type AnthropicModelId = keyof typeof anthropicModels;
export const anthropicDefaultModelId: AnthropicModelId = "claude-3-7-sonnet-20250219";

export const anthropicModels = {
  "claude-sonnet-4-20250514": {
    maxTokens: 64_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3.0,
    outputPrice: 15.0,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true,
  },
  "claude-opus-4-20250514": {
    maxTokens: 32_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15.0,
    outputPrice: 75.0,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5,
    supportsReasoningBudget: true,
  },
  "claude-3-7-sonnet-20250219:thinking": {
    maxTokens: 128_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3.0,
    outputPrice: 15.0,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true,
  },
  "claude-3-7-sonnet-20250219": {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3.0,
    outputPrice: 15.0,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
  },
  "claude-3-5-sonnet-20241022": {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3.0,
    outputPrice: 15.0,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
  },
  "claude-3-5-haiku-20241022": {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.0,
    outputPrice: 5.0,
    cacheWritesPrice: 1.25,
    cacheReadsPrice: 0.1,
  },
  "claude-3-opus-20240229": {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15.0,
    outputPrice: 75.0,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5,
  },
  "claude-3-haiku-20240307": {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03,
  },
} as const satisfies Record<string, ModelInfo>;

// OpenAI Models
export type OpenAiModelId = keyof typeof openAiModels;
export const openAiDefaultModelId: OpenAiModelId = "gpt-4.1";

export const openAiModels = {
  "gpt-4.1": {
    maxTokens: 32_768,
    contextWindow: 1_047_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2,
    outputPrice: 8,
    cacheReadsPrice: 0.5,
  },
  "gpt-4.1-mini": {
    maxTokens: 32_768,
    contextWindow: 1_047_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.4,
    outputPrice: 1.6,
    cacheReadsPrice: 0.1,
  },
  "gpt-4.1-nano": {
    maxTokens: 32_768,
    contextWindow: 1_047_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025,
  },
  "o3": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10.0,
    outputPrice: 40.0,
    cacheReadsPrice: 2.5,
    supportsReasoningEffort: true,
    reasoningEffort: "medium",
  },
  "o3-high": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10.0,
    outputPrice: 40.0,
    cacheReadsPrice: 2.5,
    reasoningEffort: "high",
  },
  "o3-low": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10.0,
    outputPrice: 40.0,
    cacheReadsPrice: 2.5,
    reasoningEffort: "low",
  },
  "o4-mini": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    supportsReasoningEffort: true,
    reasoningEffort: "medium",
  },
  "o4-mini-high": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "high",
  },
  "o4-mini-low": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "low",
  },
  "o3-mini": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    supportsReasoningEffort: true,
    reasoningEffort: "medium",
  },
  "o3-mini-high": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "high",
  },
  "o3-mini-low": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "low",
  },
  "o1": {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5,
  },
  "o1-preview": {
    maxTokens: 32_768,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5,
  },
  "o1-mini": {
    maxTokens: 65_536,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
  },
  "gpt-4.5-preview": {
    maxTokens: 16_384,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 75,
    outputPrice: 150,
    cacheReadsPrice: 37.5,
  },
  "gpt-4o": {
    maxTokens: 16_384,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 10,
    cacheReadsPrice: 1.25,
  },
  "gpt-4o-mini": {
    maxTokens: 16_384,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.075,
  },
  // Legacy models for compatibility
  "gpt-4-turbo": {
    maxTokens: 4096,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 10.0,
    outputPrice: 30.0,
  },
  "gpt-4": {
    maxTokens: 4096,
    contextWindow: 8_192,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 30.0,
    outputPrice: 60.0,
  },
  "gpt-3.5-turbo": {
    maxTokens: 4096,
    contextWindow: 16_385,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 1.5,
    outputPrice: 2.0,
  },
} as const satisfies Record<string, ModelInfo>;

// Google Models
export type GoogleModelId = keyof typeof googleModels;
export const googleDefaultModelId: GoogleModelId = "gemini-2.0-flash-001";

export const googleModels = {
  "gemini-2.5-flash-preview-05-20:thinking": {
    maxTokens: 65_535,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24_576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true,
  },
  "gemini-2.5-flash-preview-05-20": {
    maxTokens: 65_535,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6,
  },
  "gemini-2.5-pro-preview-03-25": {
    maxTokens: 65_535,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15,
    cacheReadsPrice: 0.625,
    cacheWritesPrice: 4.5,
    tiers: [
      {
        contextWindow: 200_000,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31,
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625,
      },
    ],
  },
  "gemini-2.0-flash-001": {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025,
    cacheWritesPrice: 1.0,
  },
  "gemini-2.0-pro-exp-02-05": {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  "gemini-1.5-flash-002": {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1.0,
    tiers: [
      {
        contextWindow: 128_000,
        inputPrice: 0.075,
        outputPrice: 0.3,
        cacheReadsPrice: 0.01875,
      },
      {
        contextWindow: Infinity,
        inputPrice: 0.15,
        outputPrice: 0.6,
        cacheReadsPrice: 0.0375,
      },
    ],
  },
  "gemini-1.5-pro-002": {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 1.25,
    outputPrice: 5.0,
  },
  // Legacy models for compatibility
  "gemini-pro": {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.5,
    outputPrice: 1.5,
  },
} as const satisfies Record<string, ModelInfo>;

// Model capability sets
export const PROMPT_CACHING_MODELS = new Set([
  "claude-3-haiku-20240307",
  "claude-3-opus-20240229", 
  "claude-3-5-sonnet-20241022",
  "claude-3-5-haiku-20241022",
  "claude-3-7-sonnet-20250219",
  "claude-3-7-sonnet-20250219:thinking",
  "claude-sonnet-4-20250514",
  "claude-opus-4-20250514",
  "gemini-2.5-pro-preview-03-25",
  "gemini-2.0-flash-001",
  "gemini-1.5-flash-002",
  "gpt-4.1",
  "gpt-4.1-mini",
  "gpt-4.1-nano",
  "gpt-4o",
  "gpt-4o-mini",
  "o1",
  "o1-preview",
  "o1-mini",
  "o3",
  "o3-mini",
  "o4-mini",
]);

export const COMPUTER_USE_MODELS = new Set([
  "claude-3-5-sonnet-20241022",
  "claude-3-7-sonnet-20250219", 
  "claude-3-7-sonnet-20250219:thinking",
  "claude-sonnet-4-20250514",
  "claude-opus-4-20250514",
]);

export const REASONING_MODELS = new Set([
  "claude-3-7-sonnet-20250219:thinking",
  "claude-sonnet-4-20250514",
  "claude-opus-4-20250514",
  "gemini-2.5-flash-preview-05-20:thinking",
  "o1",
  "o1-preview", 
  "o1-mini",
  "o3",
  "o3-mini",
  "o3-high",
  "o3-low",
  "o4-mini",
  "o4-mini-high",
  "o4-mini-low",
]);

// Enhanced model configuration for LLMService
export interface EnhancedModelPricingConfig {
  [provider: string]: {
    [modelId: string]: {
      inputCostPerToken: number;    // Cost per input token in USD
      outputCostPerToken: number;   // Cost per output token in USD
      capabilities?: {
        maxTokens: number;
        contextWindow: number;
        supportsImages: boolean;
        supportsComputerUse?: boolean;
        supportsPromptCache: boolean;
        supportsReasoningBudget?: boolean;
        reasoningEffort?: 'low' | 'medium' | 'high';
      };
    };
  };
}

/**
 * Convert comprehensive model data to LLMService-compatible format
 */
export function createEnhancedModelPricingConfig(): EnhancedModelPricingConfig {
  const config: EnhancedModelPricingConfig = {
    openai: {},
    anthropic: {},
    google: {},
  };

  // Convert OpenAI models
  for (const [modelId, modelInfo] of Object.entries(openAiModels)) {
    const model = modelInfo as ModelInfo;
    config.openai[modelId] = {
      inputCostPerToken: model.inputPrice / 1_000_000,
      outputCostPerToken: model.outputPrice / 1_000_000,
      capabilities: {
        maxTokens: model.maxTokens,
        contextWindow: model.contextWindow,
        supportsImages: model.supportsImages,
        supportsPromptCache: model.supportsPromptCache,
        supportsReasoningBudget: model.supportsReasoningBudget,
        reasoningEffort: model.reasoningEffort,
      },
    };
  }

  // Convert Anthropic models
  for (const [modelId, modelInfo] of Object.entries(anthropicModels)) {
    const model = modelInfo as ModelInfo;
    config.anthropic[modelId] = {
      inputCostPerToken: model.inputPrice / 1_000_000,
      outputCostPerToken: model.outputPrice / 1_000_000,
      capabilities: {
        maxTokens: model.maxTokens,
        contextWindow: model.contextWindow,
        supportsImages: model.supportsImages,
        supportsComputerUse: model.supportsComputerUse,
        supportsPromptCache: model.supportsPromptCache,
        supportsReasoningBudget: model.supportsReasoningBudget,
      },
    };
  }

  // Convert Google models
  for (const [modelId, modelInfo] of Object.entries(googleModels)) {
    const model = modelInfo as ModelInfo;
    config.google[modelId] = {
      inputCostPerToken: model.inputPrice / 1_000_000,
      outputCostPerToken: model.outputPrice / 1_000_000,
      capabilities: {
        maxTokens: model.maxTokens,
        contextWindow: model.contextWindow,
        supportsImages: model.supportsImages,
        supportsPromptCache: model.supportsPromptCache,
        supportsReasoningBudget: model.supportsReasoningBudget,
      },
    };
  }

  return config;
}

// Helper functions
export const shouldUseReasoningBudget = (model: ModelInfo, enableReasoningEffort?: boolean): boolean => 
  !!model.requiredReasoningBudget || (!!model.supportsReasoningBudget && !!enableReasoningEffort);

export const shouldUseReasoningEffort = (model: ModelInfo, reasoningEffort?: string): boolean => 
  (!!model.supportsReasoningEffort && !!reasoningEffort) || !!model.reasoningEffort;

export const DEFAULT_HYBRID_REASONING_MODEL_MAX_TOKENS = 16_384;
export const DEFAULT_HYBRID_REASONING_MODEL_THINKING_TOKENS = 8_192;
export const ANTHROPIC_DEFAULT_MAX_TOKENS = 8_192;

export const getModelMaxOutputTokens = (
  modelId: string,
  model: ModelInfo,
  enableReasoningEffort?: boolean,
  modelMaxTokens?: number
): number | undefined => {
  if (shouldUseReasoningBudget(model, enableReasoningEffort)) {
    return modelMaxTokens || DEFAULT_HYBRID_REASONING_MODEL_MAX_TOKENS;
  }

  const isAnthropicModel = modelId.includes("claude");

  // For "Hybrid" reasoning models, we should discard the model's actual
  // `maxTokens` value if we're not using reasoning. We do this for Anthropic
  // models only for now.
  if (model.supportsReasoningBudget && isAnthropicModel) {
    return ANTHROPIC_DEFAULT_MAX_TOKENS;
  }

  return model.maxTokens ?? undefined;
};

/**
 * Get model info by provider and model ID
 */
export function getModelInfo(provider: string, modelId: string): ModelInfo | undefined {
  switch (provider) {
    case 'openai':
      return openAiModels[modelId as OpenAiModelId];
    case 'anthropic':
      return anthropicModels[modelId as AnthropicModelId];
    case 'google':
      return googleModels[modelId as GoogleModelId];
    default:
      return undefined;
  }
}

/**
 * Get all available models for a provider
 */
export function getProviderModels(provider: string): Record<string, ModelInfo> {
  switch (provider) {
    case 'openai':
      return openAiModels;
    case 'anthropic':
      return anthropicModels;
    case 'google':
      return googleModels;
    default:
      return {};
  }
} 