/**
 * User Settings Configuration
 * 
 * Defines default fallback configurations for user settings, particularly model preferences
 * for different LLM task categories. These defaults are used when users haven't configured
 * their own preferences yet.
 * 
 * This configuration provides sensible defaults while allowing the system to function
 * immediately without requiring user configuration.
 */

import { LLMTaskCategory } from '../engine/graph/types/graph.types';
import { ModelProviderConfig } from '../types/userSettings.types';

/**
 * Default fallback models if user hasn't configured any for a specific category
 * These provide sensible defaults for different types of AI tasks
 */
export const DEFAULT_FALLBACK_MODELS_FOR_SERVICE: Record<LLMTaskCategory, ModelProviderConfig> = {
  [LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-********',
    temperature: 0.1, // Lower temperature for more consistent planning
  },
  
  [LLMTaskCategory.N8N_CODE_GENERATION_COMPOSITION]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-********',
    temperature: 0.0, // Deterministic for code generation
  },
  
  [LLMTaskCategory.KNOWLEDGE_AUGMENTED_EXPLANATION]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-********',
    temperature: 0.3, // Slightly higher for more natural explanations
  },
  
  [LLMTaskCategory.ANALYTICAL_VALIDATION_CRITIQUE]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-sonnet-********',
    temperature: 0.1, // Low temperature for analytical precision
  },
  
  [LLMTaskCategory.UTILITY_CLASSIFICATION_ROUTING]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-haiku-********',
    temperature: 0.0, // Deterministic for routing decisions
  },
  
  [LLMTaskCategory.UTILITY_CONVERSATION_SUMMARIZATION]: {
    provider: 'anthropic',
    modelId: 'claude-3-5-haiku-********',
    temperature: 0.2, // Slightly creative for natural summaries
  },
};

/**
 * Default API key configuration template
 * Used to initialize the apiKeys object with undefined values for all supported providers
 */
export const DEFAULT_API_KEYS_TEMPLATE = {
  openai: undefined,
  anthropic: undefined,
  google: undefined,
} as const;

/**
 * Default user preferences configuration
 * These values align with the Zod schema defaults in userSettings.types.ts
 */
export const DEFAULT_USER_PREFERENCES = {
  enableChromeAI: true,
  enableAutoSummarization: true,
  maxContextWindowTokens: 8000,
  debugMode: false,
  autoSaveWorkflows: true,
  defaultWorkflowTags: [],
} as const;

/**
 * Gets the default model configuration for a specific task category
 * @param taskCategory - The LLM task category to get defaults for
 * @returns The default model configuration for the category
 */
export function getDefaultModelForTaskCategory(taskCategory: LLMTaskCategory): ModelProviderConfig {
  const defaultModel = DEFAULT_FALLBACK_MODELS_FOR_SERVICE[taskCategory];
  
  if (!defaultModel) {
    // Fallback to the core orchestration model if somehow the category isn't defined
    console.warn(`UserSettingsConfig: No default model found for task category '${taskCategory}', falling back to core orchestration model`);
    return DEFAULT_FALLBACK_MODELS_FOR_SERVICE[LLMTaskCategory.CORE_ORCHESTRATION_AND_PLANNING];
  }
  
  return defaultModel;
}

/**
 * Validates that all LLM task categories have default models defined
 * This function can be used in tests or initialization to ensure configuration completeness
 */
export function validateDefaultModelConfiguration(): boolean {
  const categories = Object.values(LLMTaskCategory);
  const missingCategories: string[] = [];
  
  for (const category of categories) {
    if (!DEFAULT_FALLBACK_MODELS_FOR_SERVICE[category]) {
      missingCategories.push(category);
    }
  }
  
  if (missingCategories.length > 0) {
    console.error('UserSettingsConfig: Missing default models for categories:', missingCategories);
    return false;
  }
  
  return true;
} 